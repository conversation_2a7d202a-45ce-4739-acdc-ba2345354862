/**
 * Allowed email interface
 */
export interface AllowedEmail {
  id: number;
  email: string;
  invited_by: number | null;
  ae_company_id: number; // Changed from company_id
  default_role_id: number;
  created_at: string | null;
  expires_at: string | null;
}

/**
 * Allowed email filter interface
 */
export interface AllowedEmailFilter {
  ae_company_id?: number; // Changed from company_id
  search?: string;
}

/**
 * Allowed email pagination interface
 */
export interface AllowedEmailPagination {
  page: number;
  rowsPerPage: number;
  rowsNumber: number;
  sortBy?: string;
  descending?: boolean;
}
