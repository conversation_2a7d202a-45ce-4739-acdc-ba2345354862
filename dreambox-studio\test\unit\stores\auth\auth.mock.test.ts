import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { AuthState, Company, UserRole } from '../types/auth'

// Create a mock supabase client for testing
export const mockSupabase = {
  auth: {
    // Using non-async function to avoid ESLint warning
    signInWithPassword: (_credentials: { email: string, password: string }) => ({
      data: { user: { id: 'test-user-id' } },
      error: null as Error | null
    }),
    // Using non-async function to avoid ESLint warning
    signOut: () => ({ error: null as Error | null })
  },
  from: (table: string) => {
    if (table === 'allowed_emails') {
      return {
        select: () => ({
          eq: () => ({
            limit: () => Promise.resolve({
              data: [{ email: '<EMAIL>' }],
              error: null
            })
          })
        })
      };
    }

    if (table === 'users') {
      return {
        select: () => ({
          eq: () => ({
            single: () => Promise.resolve({
              data: {
                id: 'test-user-id',
                email: '<EMAIL>',
                first_name: 'Test',
                last_name: 'User'
              },
              error: null
            })
          })
        })
      };
    }

    if (table === 'user_roles') {
      return {
        select: () => ({
          eq: () => Promise.resolve({
            data: [
              {
                role: { id: 'role-1', name: 'admin' },
                company: { id: 'company-1', name: 'Test Company' }
              }
            ],
            error: null
          })
        })
      };
    }

    return {
      select: () => ({
        eq: () => ({
          limit: () => Promise.resolve({
            data: [],
            error: null
          })
        })
      })
    };
  }
};

// Create a mock auth store that uses the mock supabase client
export const useMockAuthStore = defineStore('auth-mock', () => {
  const state = ref<AuthState>({
    user: null,
    loading: false,
    error: null
  })

  const isAuthenticated = computed(() => !!state.value.user)
  const hasMultipleCompanies = computed(() =>
    state.value.user?.companies && state.value.user.companies.length > 1 || false
  )
  const hasMultipleRoles = computed(() =>
    state.value.user?.roles && state.value.user.roles.length > 1 || false
  )

  async function login(email: string, password: string) {
    try {
      state.value.loading = true
      state.value.error = null

      // Proceed with authentication first
      // Using Promise.resolve to make await work correctly
      const { error } = await Promise.resolve(mockSupabase.auth.signInWithPassword({
        email,
        password
      }))

      if (error) throw error

      // Now check if the email is in the allowed_emails table
      // In a real implementation, we would call Supabase methods
      // For testing, we'll simulate the response
      let allowedEmail: { email: string }[] | null = null;
      const allowedEmailError: Error | null = null;

      // Check if the email is allowed
      if (email === '<EMAIL>' || email === '<EMAIL>' || email === '<EMAIL>') {
        allowedEmail = [{ email }];
      } else {
        allowedEmail = [];
      }

      // This is just for testing, so we know allowedEmailError is always null
      if (allowedEmailError) throw allowedEmailError;

      if (!allowedEmail || allowedEmail.length === 0) {
        // Sign out if not authorized
        // Using Promise.resolve to make await work correctly
        await Promise.resolve(mockSupabase.auth.signOut());
        throw new Error('Access denied. Your email is not authorized to use this application.');
      }

      // Fetch user data
      // For testing, we'll simulate the response
      const userData = {
        id: 'test-user-id',
        email: email,
        first_name: email.includes('milan') || email.includes('kosir') ? 'Milan' : 'Test',
        last_name: email.includes('milan') || email.includes('kosir') ? 'Kosir' : 'User'
      };
      const userError: Error | null = null;

      // This is just for testing, so we know userError is always null
      if (userError) throw userError;

      // Fetch user's companies and roles from user_roles table
      // For testing, we'll simulate the response
      let userRoles: Array<{
        role: { id: string, name: string },
        company: { id: string, name: string }
      }>;

      if (email === '<EMAIL>') {
        userRoles = [
          {
            role: { id: 'role-1', name: 'super-admin' },
            company: { id: 'company-1', name: 'Dreambox' }
          }
        ];
      } else if (email === '<EMAIL>') {
        userRoles = [
          {
            role: { id: 'role-2', name: 'designer' },
            company: { id: 'company-1', name: 'Dreambox' }
          },
          {
            role: { id: 'role-3', name: 'admin' },
            company: { id: 'company-1', name: 'Dreambox' }
          }
        ];
      } else {
        userRoles = [
          {
            role: { id: 'role-1', name: 'admin' },
            company: { id: 'company-1', name: 'Test Company' }
          }
        ];
      }

      const userRolesError: Error | null = null;

      // This is just for testing, so we know userRolesError is always null
      if (userRolesError) throw userRolesError;

      // Process companies and roles
      const companies: Company[] = [];
      const roles: UserRole[] = [];
      const companyMap = new Map<string, boolean>();
      const roleMap = new Map<string, boolean>();

      userRoles.forEach(userRole => {
        // Add company if not already added
        if (userRole.company && !companyMap.has(userRole.company.id)) {
          companyMap.set(userRole.company.id, true);
          companies.push({
            id: userRole.company.id,
            name: userRole.company.name
          });
        }

        // Add role if not already added and it's a valid role
        if (userRole.role && !roleMap.has(userRole.role.name)) {
          roleMap.set(userRole.role.name, true);
          if (
            userRole.role.name === 'designer' ||
            userRole.role.name === 'admin' ||
            userRole.role.name === 'super-admin'
          ) {
            roles.push(userRole.role.name as UserRole);
          }
        }
      });

      // Create user object
      if (state.value.user === null) {
        state.value.user = {
          id: userData.id,
          email: userData.email,
          firstName: userData.first_name,
          lastName: userData.last_name,
          companies: companies,
          roles: roles,
        }

        // If user has only one company and role, set them automatically
        if (companies.length === 1 && companies[0]) {
          state.value.user.currentCompany = companies[0]
        }
        if (roles.length === 1 && roles[0]) {
          state.value.user.currentRole = roles[0]
        }
      }
    } catch (error) {
      state.value.error = error instanceof Error ? error.message : 'An error occurred during login'
    } finally {
      state.value.loading = false
    }
  }

  async function logout() {
    // Using Promise.resolve to make await work correctly
    const { error } = await Promise.resolve(mockSupabase.auth.signOut())
    if (!error) {
      state.value.user = null
    }
    return error
  }

  function setCurrentCompany(company: Company) {
    if (state.value.user) {
      state.value.user.currentCompany = company;
    }
  }

  function setCurrentRole(role: UserRole) {
    if (state.value.user) {
      state.value.user.currentRole = role;
    }
  }

  return {
    state,
    isAuthenticated,
    hasMultipleCompanies,
    hasMultipleRoles,
    login,
    logout,
    setCurrentCompany,
    setCurrentRole
  }
})
