# Context Store Implementation

The Context Store is the central piece of the Context Manager. It manages the current context and provides components for each UI area.

## File: `stores/contextStore.ts`

```typescript
import { defineStore } from 'pinia';
import { ref, computed, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useAuthStore } from './authStore';
import { matchContextPattern, extractContextVariables } from '@/utils/contextMatcher';
import { resolveProperties } from '@/utils/propertyResolver';
import { componentMappings } from './contextMappings';
import { DeviceType, detectDeviceType } from '@/utils/deviceDetection';

export interface ComponentConfig {
  component: string;
  props: Record<string, any>;
}

export const useContextStore = defineStore('context', () => {
  // Current state
  const currentContext = ref('default');
  const currentSubContext = ref('default');
  const contextData = ref<Record<string, any>>({});
  const deviceType = ref<DeviceType>(detectDeviceType());
  
  // Auth store for role information
  const authStore = useAuthStore();
  const userRole = computed(() => authStore.currentRole);
  
  // Get the full context string
  const fullContext = computed(() => {
    if (currentSubContext.value === 'default') {
      return currentContext.value;
    }
    return `${currentContext.value}.${currentSubContext.value}`;
  });
  
  // Get components for a specific area
  function getComponentsForArea(area: string): ComponentConfig[] {
    const components: ComponentConfig[] = [];
    const context = fullContext.value;
    const role = userRole.value;
    const device = deviceType.value;
    
    // Loop through all component mappings
    for (const mapping of componentMappings) {
      if (mapping.area !== area) continue;
      
      // Check if role matches
      if (mapping.role !== 'ALL' && mapping.role !== role) continue;
      
      // Check if device matches
      if (mapping.device !== 'ALL' && mapping.device !== device) continue;
      
      // Check if context pattern matches
      const match = matchContextPattern(mapping.context, context);
      if (match) {
        // Extract variables from the context
        const variables = extractContextVariables(mapping.context, context);
        
        // Add context data to variables
        const allVariables = { ...variables, ...contextData.value };
        
        // Create component with resolved properties
        const component: ComponentConfig = {
          component: mapping.component,
          props: resolveProperties(mapping.properties, allVariables)
        };
        
        components.push(component);
      }
    }
    
    return components;
  }
  
  // Computed properties for each UI area
  const leftDrawerComponents = computed(() => getComponentsForArea('left_drawer'));
  const rightDrawerComponents = computed(() => getComponentsForArea('right_drawer'));
  const topBarComponents = computed(() => getComponentsForArea('top_bar'));
  const bottomBarComponents = computed(() => getComponentsForArea('bottom_bar'));
  const mainContentComponents = computed(() => getComponentsForArea('main_content'));
  
  // Set context and update contextual data
  function setContext(context: string, subContext: string = 'default', data: Record<string, any> = {}) {
    currentContext.value = context;
    currentSubContext.value = subContext;
    contextData.value = data;
    
    // Emit event for components to react
    document.dispatchEvent(new CustomEvent('context-changed', {
      detail: { context, subContext, data }
    }));
  }
  
  // Update device type (useful for testing)
  function setDeviceType(device: DeviceType) {
    deviceType.value = device;
  }
  
  // Watch for route changes to set context automatically
  const route = useRoute();
  watch(() => route.path, (newPath) => {
    // Determine context from route
    if (newPath.includes('/templates')) {
      setContext('templates_table', 'table_view');
    } else if (newPath.includes('/products')) {
      setContext('products_table', 'table_view');
    } else if (newPath.includes('/users')) {
      setContext('users_table', 'table_view');
    } else if (newPath === '/' || newPath.includes('/dashboard')) {
      setContext('dashboard');
    } else {
      setContext('default');
    }
  }, { immediate: true });
  
  return {
    currentContext,
    currentSubContext,
    fullContext,
    contextData,
    deviceType,
    userRole,
    
    leftDrawerComponents,
    rightDrawerComponents,
    topBarComponents,
    bottomBarComponents,
    mainContentComponents,
    
    setContext,
    setDeviceType
  };
});
```

## Context Mappings

The context mappings define which components appear in which areas based on context, role, and device.

## File: `stores/contextMappings.ts`

```typescript
export interface ComponentMapping {
  component: string;
  area: string;
  context: string;
  role: string;
  device: string;
  properties: Record<string, any>;
}

export const componentMappings: ComponentMapping[] = [
  // Navigation components
  {
    component: 'Navigation',
    area: 'left_drawer',
    context: 'ALL',
    role: 'super-admin',
    device: 'ALL',
    properties: {
      items: 'super_admin_navigation_items',
      expanded: true,
      initiallySelected: true
    }
  },
  {
    component: 'Navigation',
    area: 'left_drawer',
    context: 'ALL',
    role: 'admin',
    device: 'ALL',
    properties: {
      items: 'admin_navigation_items',
      expanded: true,
      initiallySelected: true
    }
  },
  {
    component: 'Navigation',
    area: 'left_drawer',
    context: 'ALL',
    role: 'designer',
    device: 'ALL',
    properties: {
      items: 'designer_navigation_items',
      expanded: true,
      initiallySelected: true
    }
  },
  
  // Chat component
  {
    component: 'ChatAssistant',
    area: 'right_drawer',
    context: 'ALL',
    role: 'ALL',
    device: 'ALL',
    properties: {
      initiallySelected: true,
      placeholder: 'Type a message...',
      showVoiceInput: true
    }
  },
  
  // Search component
  {
    component: 'SearchBar',
    area: 'top_bar',
    context: '[table_type].*_view',
    role: 'ALL',
    device: 'ALL',
    properties: {
      placeholder: 'Search [table_type]...',
      targetCollection: '[table_type]',
      debounce: 300
    }
  },
  
  // Filter component
  {
    component: 'FilterPanel',
    area: 'left_drawer',
    context: '[table_type].*_view',
    role: 'ALL',
    device: 'ALL',
    properties: {
      targetCollection: '[table_type]',
      initiallySelected: false
    }
  },
  
  // View toggle component
  {
    component: 'ViewToggle',
    area: 'top_bar',
    context: '[table_type].*_view',
    role: 'ALL',
    device: 'Desktop',
    properties: {
      currentView: '[wildcard0]',
      views: ['table_view', 'grid_view']
    }
  },
  
  // Prompt elements component
  {
    component: 'PromptElementsPanel',
    area: 'left_drawer',
    context: 'templates_table.*_view.expanded.elements',
    role: 'Designer,Admin',
    device: 'ALL',
    properties: {
      mode: 'selection',
      targetTemplate: '[templateId]',
      initiallySelected: true
    }
  },
  
  // Element values component
  {
    component: 'ElementValuesEditor',
    area: 'left_drawer',
    context: 'templates_table.*_view.expanded.elements.element',
    role: 'Designer,Admin',
    device: 'ALL',
    properties: {
      mode: 'edit',
      elementId: '[elementId]',
      templateId: '[templateId]'
    }
  },
  
  // Preview component
  {
    component: 'TemplatePreview',
    area: 'right_drawer',
    context: 'templates_table.*',
    role: 'ALL',
    device: 'ALL',
    properties: {
      templateId: '[templateId]',
      initiallySelected: false,
      showControls: true
    }
  },
  
  // Mobile action buttons
  {
    component: 'ActionButton',
    area: 'bottom_bar',
    context: '[table_type].*_view',
    role: 'Admin',
    device: 'Mobile',
    properties: {
      action: 'new',
      label: 'New',
      icon: 'add',
      color: 'primary'
    }
  },
  {
    component: 'ActionButton',
    area: 'bottom_bar',
    context: '[table_type].*_view.selected',
    role: 'Admin',
    device: 'Mobile',
    properties: {
      action: 'edit',
      label: 'Edit',
      icon: 'edit',
      color: 'primary'
    }
  },
  {
    component: 'ActionButton',
    area: 'bottom_bar',
    context: '[table_type].*_view.selected',
    role: 'Admin',
    device: 'Mobile',
    properties: {
      action: 'delete',
      label: 'Delete',
      icon: 'delete',
      color: 'negative'
    }
  }
];
```
