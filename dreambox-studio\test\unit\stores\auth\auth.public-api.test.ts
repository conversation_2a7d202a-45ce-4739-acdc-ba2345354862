import { describe, test, expect, beforeEach, afterEach, mock, spyOn } from 'bun:test';
import { setActive<PERSON><PERSON>, createP<PERSON> } from 'pinia';
import { useAuthStore } from '../../../../src/stores/auth';
import type { Company, UserRole } from '../../../../src/types/auth';

// Mock localStorage
const mockLocalStorage = {
  store: {} as Record<string, string>,
  getItem: function(key: string) {
    return this.store[key] || null;
  },
  setItem: function(key: string, value: string) {
    this.store[key] = value;
  },
  removeItem: function(key: string) {
    delete this.store[key];
  },
  clear: function() {
    this.store = {};
  }
};

// Mock console methods to avoid cluttering test output
const originalConsoleLog = console.log;
const originalConsoleWarn = console.warn;
const originalConsoleError = console.error;

describe('Auth Store - Public API', () => {
  beforeEach(() => {
    // Create a fresh pinia instance
    setActivePinia(createPinia());

    // Reset localStorage mock
    mockLocalStorage.clear();

    // Replace global localStorage with our mock
    Object.defineProperty(globalThis, 'localStorage', {
      value: mockLocalStorage,
      writable: true
    });

    // Silence console output during tests
    console.log = mock(() => {});
    console.warn = mock(() => {});
    console.error = mock(() => {});
  });

  afterEach(() => {
    // Restore console methods
    console.log = originalConsoleLog;
    console.warn = originalConsoleWarn;
    console.error = originalConsoleError;
  });

  test('isAuthenticated returns true when user is set', () => {
    const authStore = useAuthStore();

    // Initially not authenticated
    expect(authStore.isAuthenticated).toBe(false);

    // Set a user
    authStore.state.user = {
      id: 'test-user-id',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      companies: [{ id: '1', name: 'Company A' }],
      roles: ['admin'],
      userRoles: [
        {
          role: { id: '1', name: 'admin' },
          company: { id: '1', name: 'Company A' }
        }
      ],
      currentCompany: { id: '1', name: 'Company A' },
      currentRole: 'admin'
    };

    // Now should be authenticated
    expect(authStore.isAuthenticated).toBe(true);
  });

  test('hasMultipleCompanies returns true when user has multiple companies', () => {
    const authStore = useAuthStore();

    // Set a user with multiple companies
    authStore.state.user = {
      id: 'test-user-id',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      companies: [
        { id: '1', name: 'Company A' },
        { id: '2', name: 'Company B' }
      ],
      roles: ['admin'],
      userRoles: [
        {
          role: { id: '1', name: 'admin' },
          company: { id: '1', name: 'Company A' }
        },
        {
          role: { id: '1', name: 'admin' },
          company: { id: '2', name: 'Company B' }
        }
      ],
      currentCompany: { id: '1', name: 'Company A' },
      currentRole: 'admin'
    };

    expect(authStore.hasMultipleCompanies).toBe(true);

    // Change to single company
    authStore.state.user.companies = [{ id: '1', name: 'Company A' }];

    expect(authStore.hasMultipleCompanies).toBe(false);
  });

  test('hasMultipleRoles returns true when user has multiple roles', () => {
    const authStore = useAuthStore();

    // Set a user with multiple roles
    authStore.state.user = {
      id: 'test-user-id',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      companies: [{ id: '1', name: 'Company A' }],
      roles: ['admin', 'designer'],
      userRoles: [
        {
          role: { id: '1', name: 'admin' },
          company: { id: '1', name: 'Company A' }
        },
        {
          role: { id: '2', name: 'designer' },
          company: { id: '1', name: 'Company A' }
        }
      ],
      currentCompany: { id: '1', name: 'Company A' },
      currentRole: 'admin'
    };

    expect(authStore.hasMultipleRoles).toBe(true);

    // Change to single role
    authStore.state.user.roles = ['admin'];

    expect(authStore.hasMultipleRoles).toBe(false);
  });

  test('setCurrentCompany updates the current company', () => {
    const authStore = useAuthStore();

    // Set a user
    authStore.state.user = {
      id: 'test-user-id',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      companies: [
        { id: '1', name: 'Company A' },
        { id: '2', name: 'Company B' }
      ],
      roles: ['admin'],
      userRoles: [
        {
          role: { id: '1', name: 'admin' },
          company: { id: '1', name: 'Company A' }
        },
        {
          role: { id: '1', name: 'admin' },
          company: { id: '2', name: 'Company B' }
        }
      ],
      currentCompany: { id: '1', name: 'Company A' },
      currentRole: 'admin'
    };

    // Change current company
    const newCompany: Company = { id: '2', name: 'Company B' };
    authStore.setCurrentCompany(newCompany);

    expect(authStore.state.user?.currentCompany).toEqual(newCompany);
  });

  test('setCurrentRole updates the current role', () => {
    const authStore = useAuthStore();

    // Set a user
    authStore.state.user = {
      id: 'test-user-id',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      companies: [{ id: '1', name: 'Company A' }],
      roles: ['admin', 'designer'],
      userRoles: [
        {
          role: { id: '1', name: 'admin' },
          company: { id: '1', name: 'Company A' }
        },
        {
          role: { id: '2', name: 'designer' },
          company: { id: '1', name: 'Company A' }
        }
      ],
      currentCompany: { id: '1', name: 'Company A' },
      currentRole: 'admin'
    };

    // Change current role
    const newRole: UserRole = 'designer';
    authStore.setCurrentRole(newRole);

    expect(authStore.state.user?.currentRole).toBe(newRole);
  });

  test('setCurrentCompany does nothing if user is null', () => {
    const authStore = useAuthStore();

    // Ensure user is null
    authStore.state.user = null;

    // Try to change current company
    const newCompany: Company = { id: '2', name: 'Company B' };
    authStore.setCurrentCompany(newCompany);

    // Should still be null
    expect(authStore.state.user).toBeNull();
  });

  test('setCurrentRole does nothing if user is null', () => {
    const authStore = useAuthStore();

    // Ensure user is null
    authStore.state.user = null;

    // Try to change current role
    const newRole: UserRole = 'designer';
    authStore.setCurrentRole(newRole);

    // Should still be null
    expect(authStore.state.user).toBeNull();
  });

  test('login and logout flow', async () => {
    const authStore = useAuthStore();

    // Manually set the user state to simulate a successful login
    authStore.state.user = {
      id: 'test-user-id',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      companies: [{ id: '1', name: 'Test Company' }],
      roles: ['admin'],
      userRoles: [
        {
          role: { id: '1', name: 'admin' },
          company: { id: '1', name: 'Test Company' }
        }
      ],
      currentCompany: { id: '1', name: 'Test Company' },
      currentRole: 'admin'
    };

    // Should be authenticated
    expect(authStore.isAuthenticated).toBe(true);
    expect(authStore.state.user).not.toBeNull();

    // Manually clear the user state to simulate a logout
    authStore.state.user = null;

    // Should not be authenticated
    expect(authStore.isAuthenticated).toBe(false);
    expect(authStore.state.user).toBeNull();
  });
});
