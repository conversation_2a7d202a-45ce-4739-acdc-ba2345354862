<template>
  <div class="prompt-elements-filter-section">
    <!-- Element Types as direct selects -->
    <div class="element-list-container rounded-borders">
      <div style="padding: 12px 12px 0 12px;">
        <div
          v-for="type in elementTypes"
          :key="type.id"
          class="q-mb-md"
        >
          <div v-if="hasElementsForType(type.id)">
            <div class="filter-container">
              <q-select
                v-model="selectedElementValues[type.id]"
                :options="getElementsForType(type.id)"
                option-label="value"
                option-value="id"
                multiple
                outlined
                dense
                use-chips
                stack-label
                use-input
                hide-selected
                fill-input
                input-debounce="300"
                popup-content-class="custom-scrollbar"
                :label="getFilterLabel(type)"
                :class="isFilterActive(type.id) ? 'active-filter-select' : ''"
                @update:model-value="updateElementFilter(type.id, $event)"
                @filter="(val, update) => filterOptions(val, update, type.id)"
              >
                <template v-slot:selected-item="scope">
                  <q-chip
                    removable
                    dense
                    @remove="scope.removeAtIndex(scope.index)"
                    :tabindex="scope.tabindex"
                    color="primary"
                    text-color="white"
                  >
                    {{ scope.opt.value }}
                  </q-chip>
                </template>
              </q-select>

              <!-- Clear button for this filter type -->
              <q-btn
                v-if="isFilterActive(type.id)"
                flat
                round
                dense
                size="sm"
                icon="clear"
                color="grey-7"
                class="clear-type-btn"
                @click="clearFilterType(type.id)"
              >
                <q-tooltip>Clear {{ formatElementTypeName(type.name) }} filters</q-tooltip>
              </q-btn>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { notify } from 'src/boot/notifications';
import { usePromptElementsStore } from 'src/stores/promptElements';

// Types
interface ElementType {
  id: number;
  name: string;
  description: string | null;
}

interface ElementValue {
  id: number;
  type_id: number;
  value: string;
  description: string | null;
}

// Prompt Elements Store
const promptElementsStore = usePromptElementsStore();

// State
const elementTypes = computed(() => promptElementsStore.elementTypes as ElementType[]);
const elementValuesByType = ref<Record<number, ElementValue[]>>({});
// Keep a separate copy of the original values for filtering
const originalElementValuesByType = ref<Record<number, ElementValue[]>>({});
const selectedElementValues = ref<Record<number, number[]>>({});

// Computed
const activeElementFilters = computed(() => {
  const filters: Record<string, number[]> = {};

  for (const typeId in selectedElementValues.value) {
    const values = selectedElementValues.value[typeId] || [];
    if (values.length > 0) {
      // Get the type name for this typeId
      const type = elementTypes.value.find(t => t.id === parseInt(typeId));
      if (type) {
        // Use the format expected by the filter system
        filters[`element_${type.name}`] = values;
      }
    }
  }

  return filters;
});

// Watch for changes in active filters
watch(activeElementFilters, (newFilters) => {
  // Dispatch a global filter event with the updated filters
  dispatchFilterEvent(newFilters);
});

// Initialize
onMounted(async () => {
  await loadElementTypes();

  // Add event listeners for loading and clearing filters
  document.addEventListener('load-prompt-element-filters', handleLoadFilters as EventListener);
  document.addEventListener('clear-prompt-element-filters', handleClearFilters as EventListener);
  document.addEventListener('get-prompt-element-filters', handleGetFilters as EventListener);
});

// Clean up event listeners on unmount
onUnmounted(() => {
  document.removeEventListener('load-prompt-element-filters', handleLoadFilters as EventListener);
  document.removeEventListener('clear-prompt-element-filters', handleClearFilters as EventListener);
  document.removeEventListener('get-prompt-element-filters', handleGetFilters as EventListener);
});

// Expose methods to parent components
defineExpose({
  clearAllFilters
});

// Load element types from the store
async function loadElementTypes() {
  try {
    // Load element types from the store
    await promptElementsStore.loadElementTypes();

    // Initialize selectedElementValues for each type
    elementTypes.value.forEach(type => {
      selectedElementValues.value[type.id] = [];
    });

    // Fetch values for each type
    for (const type of elementTypes.value) {
      await loadElementValuesForType(type.id);
    }
  } catch (error) {
    console.error('Error loading element types:', error);
    void notify({
      color: 'negative',
      message: 'Failed to load element types',
      icon: 'error'
    });
  }
}

// Load element values for a type from the store
async function loadElementValuesForType(typeId: number) {
  try {
    // Load values from the store
    const values = await promptElementsStore.loadElementValuesForType(typeId);

    // Update local state
    elementValuesByType.value[typeId] = values as ElementValue[];
    // Store a copy of the original data for filtering
    originalElementValuesByType.value[typeId] = [...values] as ElementValue[];
  } catch (error) {
    console.error(`Error loading element values for type ${typeId}:`, error);
  }
}

// Format element type name (convert snake_case to Title Case)
function formatElementTypeName(name: string): string {
  return name
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// Get filter label with selected count if any
function getFilterLabel(type: ElementType): string {
  const baseLabel = formatElementTypeName(type.name);
  const selectedValues = selectedElementValues.value[type.id];
  const selectedCount = selectedValues ? selectedValues.length : 0;

  if (selectedCount > 0) {
    return `${baseLabel} (${selectedCount} selected)`;
  }

  return baseLabel;
}

// Helper methods for element access
function hasElementsForType(typeId: number): boolean {
  return !!elementValuesByType.value[typeId] && elementValuesByType.value[typeId].length > 0;
}

function getElementsForType(typeId: number): ElementValue[] {
  return elementValuesByType.value[typeId] || [];
}

// Check if a filter has active selections
function isFilterActive(typeId: number): boolean {
  const selectedValues = selectedElementValues.value[typeId];
  return !!selectedValues && selectedValues.length > 0;
}

// This function is no longer needed as we're using CSS classes
// function getActiveFilterStyle(typeId: number): string {
//   if (!isFilterActive(typeId)) {
//     return '';
//   }
//
//   // Check if dark mode is active
//   const isDarkMode = document.body.classList.contains('body--dark');
//   const color = isDarkMode ? '#2196f3' : '#1976d2';
//
//   return `color: ${color}; font-weight: 500;`;
// }

// Update element filter
function updateElementFilter(typeId: number, selectedIds: number[]) {
  // Update the selected values
  selectedElementValues.value[typeId] = selectedIds;

  // Trigger the computed property to update the active filters
  // This will automatically call dispatchFilterEvent via the watcher
}

// Clear all filters - exposed for parent components to call
function clearAllFilters() {
  // Reset all selected values to empty arrays
  for (const typeId in selectedElementValues.value) {
    selectedElementValues.value[typeId] = [];
  }

  // Explicitly dispatch an empty filter event
  console.log('Dispatching empty prompt elements filter event');
  document.dispatchEvent(new CustomEvent('global-filter-changed', {
    detail: {
      name: 'prompt_elements',
      filters: {}
    }
  }));

  // The watcher on activeElementFilters will also be triggered
  // but it's important to dispatch an explicit empty event
}

// Clear filters for a specific element type
function clearFilterType(typeId: number) {
  // Reset selected values for this type to an empty array
  selectedElementValues.value[typeId] = [];

  // Get the type name for this typeId
  const type = elementTypes.value.find(t => t.id === typeId);
  const typeName = type ? type.name : '';

  // Show notification
  void notify({
    color: 'info',
    message: `${formatElementTypeName(typeName || '')} filters cleared`,
    icon: 'clear',
    position: 'top-right',
    timeout: 2000
  });

  // Explicitly dispatch a filter event with the current state
  // This ensures the filter is immediately applied
  console.log('Dispatching updated prompt elements filter event after clearing type:', typeId);
  document.dispatchEvent(new CustomEvent('global-filter-changed', {
    detail: {
      name: 'prompt_elements',
      filters: activeElementFilters.value
    }
  }));

  // The watcher on activeElementFilters will also be triggered
  // but it's important to dispatch an explicit event
}

// Filter options based on input
function filterOptions(val: string, update: (callback: () => void) => void, typeId: number) {
  update(() => {
    // If search is empty, restore original values
    if (val === '') {
      elementValuesByType.value[typeId] = [...originalElementValuesByType.value[typeId] || []];
      return;
    }

    const needle = val.toLowerCase();
    // Filter the options for this type based on the input value
    const filteredOptions = originalElementValuesByType.value[typeId]?.filter(
      (element) => element.value.toLowerCase().indexOf(needle) > -1
    ) || [];

    // Update the filtered options for this type
    elementValuesByType.value[typeId] = filteredOptions;
  });
}

// Handle loading filters from a saved filter
function handleLoadFilters(event: CustomEvent) {
  console.log('Received load-prompt-element-filters event:', event.detail);
  const { filters } = event.detail;

  if (!filters) {
    console.warn('No filters provided in load-prompt-element-filters event');
    return;
  }

  // Reset all selected values first
  for (const typeId in selectedElementValues.value) {
    selectedElementValues.value[typeId] = [];
  }

  // Process each filter that starts with 'element_'
  for (const key in filters) {
    if (key.startsWith('element_')) {
      // Extract the type name from the key (remove 'element_' prefix)
      const typeName = key.substring(8); // 'element_'.length = 8

      // Find the type ID for this type name
      const type = elementTypes.value.find(t => t.name === typeName);
      if (type) {
        console.log(`Found type for ${typeName}:`, type);

        // Get the filter value
        const filterValue = filters[key];

        // Handle different formats of filter values
        if (Array.isArray(filterValue)) {
          // If it's already an array of IDs, use it directly
          selectedElementValues.value[type.id] = filterValue;
          console.log(`Set ${typeName} filter to:`, filterValue);
        } else if (typeof filterValue === 'string') {
          // If it's a string, try to parse it as JSON
          try {
            const parsedValue = JSON.parse(filterValue);
            if (Array.isArray(parsedValue)) {
              selectedElementValues.value[type.id] = parsedValue;
              console.log(`Set ${typeName} filter to parsed value:`, parsedValue);
            }
          } catch (err) {
            console.error(`Error parsing filter value for ${typeName}:`, err);
          }
        }
      } else {
        console.warn(`No type found for ${typeName}`);
      }
    }
  }

  // Dispatch a filter event with the updated filters
  dispatchFilterEvent(activeElementFilters.value);
}

// Handle clearing all prompt element filters
function handleClearFilters() {
  console.log('Received clear-prompt-element-filters event');
  clearAllFilters();
}

// Handle getting prompt element filters for saving
function handleGetFilters(event: CustomEvent) {
  console.log('Received get-prompt-element-filters event');
  const { callback } = event.detail;

  if (typeof callback === 'function') {
    // Get the current active element filters
    const filters = activeElementFilters.value;
    console.log('Returning prompt element filters:', filters);

    // Call the callback with the filters
    callback(filters);
  } else {
    console.warn('No callback provided in get-prompt-element-filters event');
  }
}

// Dispatch a global filter event with the current filters
function dispatchFilterEvent(filters: Record<string, number[]>) {
  console.log('Dispatching element filter event with filters:', filters);

  // The filters are already in the correct format (element_type_name -> element_ids)
  // Just pass them directly to the event

  // Check if there are any non-empty filters
  const hasActiveFilters = Object.values(filters).some(values => values.length > 0);

  // If there are no active filters, dispatch an empty object to clear all filters
  const filtersToSend = hasActiveFilters ? filters : {};

  console.log('Filters to send:', filtersToSend, 'Has active filters:', hasActiveFilters);

  // Dispatch the event with the filters
  document.dispatchEvent(new CustomEvent('global-filter-changed', {
    detail: {
      name: 'prompt_elements',
      filters: filtersToSend
    }
  }));
}
</script>

<style lang="scss" scoped>
.prompt-elements-filter-section {
  .section-header {
    padding: 0 16px;
  }

  // Custom padding is now applied directly to the container

  // Add border around the element list
  .element-list-container {
    border: 1px solid rgba(0, 0, 0, 0.12);
    background-color: white;
    margin-bottom: 16px;
    margin-top: 8px;

    // Empty state styling
    &:empty {
      padding: 16px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #999;
      font-style: italic;

      &::after {
        content: 'No prompt elements available';
      }
    }
  }

  // Style for active filters
  .active-filter-select {
    :deep(.q-field__native) {
      color: var(--q-primary) !important;
      font-weight: 500 !important;
    }

    :deep(.q-field__label) {
      color: var(--q-primary) !important;
      font-weight: 500 !important;
    }
  }
}

// Global styles for all q-select components in this component
:deep(.q-field) {
  // Set a fixed height for the control
  .q-field__control {
    height: 40px !important;
  }

  // Center the content vertically
  .q-field__control-container {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    display: flex !important;
    align-items: center !important;
  }

  // Style the label (shown when dropdown is closed)
  .q-field__label {
    line-height: 40px !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    transform: none !important;
    font-size: 14px !important;
    top: 0 !important;
  }

  // Style the native input (shown when dropdown is open)
  .q-field__native {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    min-height: unset !important;
    line-height: 40px !important;
    display: flex !important;
    align-items: center !important;
  }
}

// Filter container with clear button
.filter-container {
  position: relative;
  margin-bottom: 8px;

  // Position the clear button
  .clear-type-btn {
    position: absolute;
    right: 48px; // Position to the left of the dropdown arrow
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.7;
    transition: opacity 0.3s;
    z-index: 1;

    &:hover {
      opacity: 1;
    }
  }
}

// Dark mode support
.q-dark {
  .element-list-container {
    border-color: rgba(255, 255, 255, 0.12);
    background-color: #1d1d1d;
  }

  // Dark mode styles are now handled with inline styles
}

// Scrollbar styling is now handled by the global .custom-scrollbar class
</style>
