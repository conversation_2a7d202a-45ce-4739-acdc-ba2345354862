# Table Implementation with Row Expansion and Selection

This document describes how to implement a Quasar table with both row expansion and row selection functionality, as used in the TemplateTable component.

## Key Features

- Row selection with checkboxes
- Expandable rows with detailed content
- Custom cell formatting
- Action buttons
- Pagination
- Sorting

## Implementation

### 1. Table Setup

```vue
<q-table
  :rows="templates"
  :columns="columns"
  :loading="loading"
  v-model:pagination="pagination"
  v-model:selected="selectedRows"
  v-model:expanded="expandedRows"
  selection="multiple"
  row-key="id"
  binary-state-sort
  @request="onRequest"
  :rows-per-page-options="[10, 20, 50, 100]"
>
  <!-- Slots go here -->
</q-table>
```

### 2. Required Data Properties

```typescript
// Selected rows
const selectedRows = ref<Template[]>([]);

// Expanded rows
const expandedRows = ref<number[]>([]);

// Watch for selection changes
watch(() => selectedRows.value, (newSelected) => {
  emit('selection-change', newSelected);
});
```

### 3. Custom Body Slot

The key to implementing both row selection and expansion is using the `body` slot instead of individual cell slots:

```vue
<!-- Custom body template for rows and expanded content -->
<template v-slot:body="props">
  <q-tr :props="props" :class="{ 'selected-row': props.selected }">
    <!-- Selection checkbox column -->
    <q-td auto-width>
      <q-checkbox v-model="props.selected" />
    </q-td>
    
    <!-- Expand button column -->
    <q-td auto-width>
      <q-btn
        size="sm"
        color="accent"
        round
        dense
        :icon="props.expand ? 'remove' : 'add'"
        @click.stop="props.expand = !props.expand"
      />
    </q-td>
    
    <!-- Regular columns -->
    <q-td
      v-for="col in props.cols"
      :key="col.name"
      :props="props"
      @click="toggleRowSelection(props.row)"
    >
      <!-- Custom cell formatting based on column name -->
      <!-- ... -->
    </q-td>
  </q-tr>
  
  <!-- Expanded row content -->
  <q-tr v-show="props.expand" :props="props">
    <q-td colspan="100%">
      <!-- Expanded content goes here -->
    </q-td>
  </q-tr>
</template>
```

### 4. Custom Cell Formatting

Within the body slot, you can format cells based on their column name:

```vue
<!-- Format status with badge -->
<template v-if="col.name === 'status'">
  <q-badge :color="getStatusColor(col.value)">
    {{ col.value }}
  </q-badge>
</template>

<!-- Format elements count -->
<template v-else-if="col.name === 'elements'">
  <q-badge color="blue-grey" v-if="getElementCount(props.row) > 0">
    {{ getElementCount(props.row) }}
  </q-badge>
  <span v-else>-</span>
</template>

<!-- Default display for other columns -->
<template v-else>
  {{ col.value }}
</template>
```

### 5. Row Selection Toggle

```typescript
// Toggle row selection
function toggleRowSelection(row: Template) {
  // Find if the row is already selected
  const index = selectedRows.value.findIndex((item: Template) => item.id === row.id);

  // If row is already selected, remove it from selection
  if (index !== -1) {
    selectedRows.value = selectedRows.value.filter((item: Template) => item.id !== row.id);
  }
  // Otherwise add it to selection
  else {
    selectedRows.value = [...selectedRows.value, row];
  }
}
```

## Important Notes

1. **Don't use the `expand` column in your columns definition** - we add the expand button manually in the body slot.

2. **Use `@click.stop` on the expand button** to prevent it from triggering row selection.

3. **Use `colspan="100%"` on the expanded row's td** to make it span all columns.

4. **Add styling for selected rows**:
   ```scss
   :deep(.selected) {
     background-color: rgba(var(--q-primary), 0.1) !important;
   }
   ```

5. **Handle both single and multiple row selection** in your parent component based on the selection-change event.

## Reference

This implementation is based on the Quasar documentation example for expandable rows:
https://quasar.dev/vue-components/table/#example--internal-expansion-model

## Example

For a complete implementation, see the TemplateTable component in:
`dreambox-studio/src/components/templates/TemplateTable.vue`
