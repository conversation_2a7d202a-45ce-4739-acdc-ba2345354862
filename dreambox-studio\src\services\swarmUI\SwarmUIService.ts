import { supabase } from 'src/boot/supabase';
import type {
  SwarmUIGenerationParams,
  SwarmUIGenerationResponse,
  SwarmUIModel,
  SwarmUIServerInfo,
  ServerStatus,
  ServerStartOptions,
  ServerStartResult
} from './types';

class SwarmUIService {
  public statusCheckInterval: number | null = null;
  private serverStatus: ServerStatus = { status: 'no-server' };

  constructor() {
    // Start checking server status periodically
    this.startStatusCheck();
  }

  /**
   * Start periodic server status check
   */
  startStatusCheck(intervalMs = 10000) {
    if (this.statusCheckInterval) {
      clearInterval(this.statusCheckInterval);
    }

    // Initial check
    void this.checkServerStatus();

    // Set up interval for periodic checks
    this.statusCheckInterval = window.setInterval(() => {
      void this.checkServerStatus();
    }, intervalMs);
  }

  /**
   * Stop periodic server status check
   */
  stopStatusCheck() {
    if (this.statusCheckInterval) {
      clearInterval(this.statusCheckInterval);
      this.statusCheckInterval = null;
    }
  }

  /**
   * Check the current server status
   */
  async checkServerStatus(): Promise<ServerStatus> {
    try {
      // Use URL parameters for GET requests instead of body
      const { data, error } = await supabase.functions.invoke('swarmui-server?action=server-status', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });

      if (error) {
        console.error('Error checking server status:', error);
        this.serverStatus = {
          status: 'error',
          message: error.message
        };
        return this.serverStatus;
      }

      this.serverStatus = data;
      return data;
    } catch (error) {
      console.error('Error checking server status:', error);
      this.serverStatus = {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
      return this.serverStatus;
    }
  }

  /**
   * Get the current server status without making an API call
   */
  getCurrentStatus(): ServerStatus {
    return this.serverStatus;
  }

  /**
   * Start a SwarmUI server
   */
  async startServer(options: ServerStartOptions = {}): Promise<ServerStartResult> {
    try {
      const { data, error } = await supabase.functions.invoke('swarmui-server', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: { ...options, action: 'start-server' }
      });

      if (error) {
        console.error('Error starting server:', error);
        return {
          status: 'error',
          message: error.message
        };
      }

      // Trigger an immediate status check
      setTimeout(() => { void this.checkServerStatus(); }, 2000);

      return data;
    } catch (error) {
      console.error('Error starting server:', error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Stop a SwarmUI server
   */
  async stopServer(): Promise<boolean> {
    if (!this.serverStatus.podId) {
      console.error('No active server to stop');
      return false;
    }

    try {
      const { error } = await supabase.functions.invoke('swarmui-server', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: {
          action: 'stop-server',
          podId: this.serverStatus.podId
        }
      });

      if (error) {
        console.error('Error stopping server:', error);
        return false;
      }

      // Trigger an immediate status check
      setTimeout(() => { void this.checkServerStatus(); }, 2000);

      return true;
    } catch (error) {
      console.error('Error stopping server:', error);
      return false;
    }
  }

  /**
   * Get SwarmUI server info
   */
  async getServerInfo(): Promise<SwarmUIServerInfo | null> {
    if (this.serverStatus.status !== 'running') {
      console.error('Server is not running');
      return null;
    }

    try {
      const { data, error } = await supabase.functions.invoke('swarmui-generation?path=info', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });

      if (error) {
        console.error('Error getting server info:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error getting server info:', error);
      return null;
    }
  }

  /**
   * Get available models
   */
  async getModels(): Promise<SwarmUIModel[]> {
    if (this.serverStatus.status !== 'running') {
      console.error('Server is not running');
      return [];
    }

    try {
      const { data, error } = await supabase.functions.invoke('swarmui-generation?path=models', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });

      if (error) {
        console.error('Error getting models:', error);
        return [];
      }

      return data.models || [];
    } catch (error) {
      console.error('Error getting models:', error);
      return [];
    }
  }

  /**
   * Generate an image
   */
  async generateImage(params: SwarmUIGenerationParams): Promise<SwarmUIGenerationResponse | null> {
    if (this.serverStatus.status !== 'running') {
      console.error('Server is not running');
      return null;
    }

    try {
      const { data, error } = await supabase.functions.invoke('swarmui-generation', {
        method: 'POST',
        body: params
      });

      if (error) {
        console.error('Error generating image:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error generating image:', error);
      return null;
    }
  }

  /**
   * Get generation status
   */
  async getGenerationStatus(generationId: string): Promise<SwarmUIGenerationResponse | null> {
    if (this.serverStatus.status !== 'running') {
      console.error('Server is not running');
      return null;
    }

    try {
      const { data, error } = await supabase.functions.invoke(`swarmui-generation?path=generations/${generationId}`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });

      if (error) {
        console.error('Error getting generation status:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error getting generation status:', error);
      return null;
    }
  }

  /**
   * Upload generated image to S3 and save record in database
   */
  async saveGeneratedImage(imageUrl: string, metadata: Record<string, unknown>): Promise<string | null> {
    try {
      const { data, error } = await supabase.functions.invoke('image-management?action=save-generated-image', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: {
          imageUrl,
          metadata
        }
      });

      if (error) {
        console.error('Error saving generated image:', error);
        return null;
      }

      return data.image?.id || null;
    } catch (error) {
      console.error('Error saving generated image:', error);
      return null;
    }
  }
}

// Create a singleton instance
export const swarmUIService = new SwarmUIService();
