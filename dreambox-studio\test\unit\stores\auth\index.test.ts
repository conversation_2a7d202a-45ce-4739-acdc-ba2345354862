// @ts-ignore - Bun test types
import { describe, test, expect, beforeEach } from 'bun:test';
// No need to import setActivePinia and createPinia as they're used in createFreshPinia
// @ts-ignore - Path resolution
import { useAuthStore } from '../../../../src/stores/auth';
// @ts-ignore - Path resolution
import { createFreshPinia } from '../../../helpers/test-utils';

describe('AuthStore', () => {
  beforeEach(() => {
    createFreshPinia();
  });

  test('initial state', async () => {
    const authStore = useAuthStore();

    // Wait for initialization to complete
    await new Promise(resolve => setTimeout(resolve, 100));

    expect(authStore.state.user).toBeNull();
    expect(authStore.state.loading).toBe(false);
    expect(authStore.state.error).toBeNull();
  });
});
