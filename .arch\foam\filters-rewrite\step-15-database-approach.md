# Step 15: Database Approach for Pre-Production Environment

## Overview

This document outlines our approach to handling database components (tables, functions, and views) for the new filter engine in a pre-production environment. Since the application is not yet in production and we're using a free Supabase instance without staging capabilities, we'll use a parallel implementation approach rather than in-place migration.

## Principles

1. **Create new components** rather than modifying existing ones
2. **Maintain clear separation** between old and new implementations
3. **Use naming conventions** to distinguish between systems
4. **Remove old components** only after the new system is fully tested and working

## Database Tables

### Current Tables

- `user_filters`: Stores saved filters in the current implementation

### New Tables

We'll create the following new tables for the filter engine:

```sql
-- New table for saved filters
CREATE TABLE filter_engine_filters (
  id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
  user_id BIGINT REFERENCES app_users(id),
  name TEXT NOT NULL,
  description TEXT,
  table_name TEXT NOT NULL,
  context TEXT NOT NULL,
  configuration JSONB NOT NULL,
  is_default BOOLEAN DEFAULT false,
  contexts TEXT[] DEFAULT '{}',
  is_context_default BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- New table for context-filter mappings
CREATE TABLE filter_engine_context_mappings (
  id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
  user_id BIGINT REFERENCES app_users(id),
  context_filters JSONB NOT NULL DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  CONSTRAINT filter_engine_context_mappings_user_id_key UNIQUE (user_id)
);
```

## Database Functions

### Current Functions

- `filter_templates`: Current function for filtering templates
- Other filter-related functions

### New Functions

We'll create new functions with a `filter_engine_` prefix:

```sql
-- New function for filtering templates
CREATE OR REPLACE FUNCTION filter_engine_templates(
  search TEXT DEFAULT NULL,
  status TEXT[] DEFAULT NULL,
  created_from TIMESTAMP DEFAULT NULL,
  created_to TIMESTAMP DEFAULT NULL,
  -- Other standard filters
  prompt_elements JSONB DEFAULT NULL, -- For specialized filters
  related_items JSONB DEFAULT NULL,   -- For specialized filters
  sort_by TEXT DEFAULT 'updated_at',
  sort_desc BOOLEAN DEFAULT TRUE,
  limit_val INTEGER DEFAULT 10,
  offset_val INTEGER DEFAULT 0
) RETURNS TABLE (
  -- Template fields
  id BIGINT,
  name TEXT,
  description TEXT,
  status TEXT,
  -- Other fields
  total_count BIGINT
) AS $$
DECLARE
  query TEXT;
  count_query TEXT;
  total BIGINT;
BEGIN
  -- Implementation of the new filter function
  -- This will be similar to the current function but with improvements
  -- ...
END;
$$ LANGUAGE plpgsql;

-- Helper function for prompt elements filtering
CREATE OR REPLACE FUNCTION filter_engine_process_prompt_elements(
  template_id BIGINT,
  filter_json JSONB
) RETURNS BOOLEAN AS $$
DECLARE
  -- Implementation
  -- ...
END;
$$ LANGUAGE plpgsql;

-- Helper function for related items filtering
CREATE OR REPLACE FUNCTION filter_engine_process_related_items(
  template_id BIGINT,
  filter_json JSONB
) RETURNS BOOLEAN AS $$
DECLARE
  -- Implementation
  -- ...
END;
$$ LANGUAGE plpgsql;
```

## Database Views

### Current Views

- `template_view`: Current view for templates
- Other filter-related views

### New Views

We'll create new views with a `filter_engine_` prefix:

```sql
-- New view for templates
CREATE OR REPLACE VIEW filter_engine_template_view AS
SELECT
  t.id,
  t.name,
  t.description,
  t.status,
  -- Other fields
  -- Include any joins or calculations needed for filtering
FROM templates t
LEFT JOIN -- other joins as needed
;

-- Other specialized views as needed
CREATE OR REPLACE VIEW filter_engine_template_elements_view AS
SELECT
  -- Fields needed for prompt elements filtering
FROM template_elements te
JOIN element_values ev ON te.element_value_id = ev.id
JOIN element_types et ON ev.element_type_id = et.id
;
```

## Client-Side Integration

On the client side, we'll update our code to use the new database components:

```typescript
// src/lib/filters/supabase/FilterClient.ts
export class FilterClient {
  // Save a filter
  async saveFilter(filter: SavedFilter): Promise<number | null> {
    try {
      const { data, error } = await supabase
        .from('filter_engine_filters')
        .insert({
          user_id: filter.user_id,
          name: filter.name,
          description: filter.description,
          table_name: filter.table_name,
          context: filter.context,
          configuration: filter.configuration,
          is_default: filter.is_default,
          contexts: filter.contexts,
          is_context_default: filter.is_context_default
        })
        .select();
      
      if (error) throw error;
      
      return data?.[0]?.id || null;
    } catch (err) {
      console.error('Error saving filter:', err);
      return null;
    }
  }
  
  // Load filters for a user
  async loadFilters(userId: number): Promise<SavedFilter[]> {
    try {
      const { data, error } = await supabase
        .from('filter_engine_filters')
        .select('*')
        .eq('user_id', userId);
      
      if (error) throw error;
      
      return data as SavedFilter[];
    } catch (err) {
      console.error('Error loading filters:', err);
      return [];
    }
  }
  
  // Apply filters to templates
  async filterTemplates(filters: TemplateFilters, pagination: Pagination): Promise<TemplateResult> {
    try {
      const { data, error } = await supabase
        .rpc('filter_engine_templates', {
          search: filters.search,
          status: filters.status,
          created_from: filters.created_from,
          created_to: filters.created_to,
          prompt_elements: filters.prompt_elements,
          related_items: filters.related_items,
          sort_by: pagination.sortBy || 'updated_at',
          sort_desc: pagination.sortDesc !== undefined ? pagination.sortDesc : true,
          limit_val: pagination.limit,
          offset_val: pagination.offset
        });
      
      if (error) throw error;
      
      return {
        items: data,
        total: data.length > 0 ? data[0].total_count : 0
      };
    } catch (err) {
      console.error('Error filtering templates:', err);
      return { items: [], total: 0 };
    }
  }
}
```

## Implementation Approach

1. **Create Database Components**:
   - Create new tables for the filter engine
   - Create new functions with the `filter_engine_` prefix
   - Create new views with the `filter_engine_` prefix

2. **Implement Client-Side Code**:
   - Use the new database components in the filter engine code
   - Keep the old implementation working alongside the new one

3. **Testing**:
   - Test the new implementation thoroughly
   - Verify that both old and new implementations work correctly

4. **Cleanup**:
   - Once the new implementation is fully tested and working:
     - Remove the old client-side code
     - Drop the old database components (tables, functions, views)
     - Optionally rename the new components to remove the `filter_engine_` prefix

## SQL Migration Scripts

### Create New Tables

```sql
-- Create new tables for the filter engine
BEGIN;

-- New table for saved filters
CREATE TABLE filter_engine_filters (
  id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
  user_id BIGINT REFERENCES app_users(id),
  name TEXT NOT NULL,
  description TEXT,
  table_name TEXT NOT NULL,
  context TEXT NOT NULL,
  configuration JSONB NOT NULL,
  is_default BOOLEAN DEFAULT false,
  contexts TEXT[] DEFAULT '{}',
  is_context_default BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- New table for context-filter mappings
CREATE TABLE filter_engine_context_mappings (
  id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
  user_id BIGINT REFERENCES app_users(id),
  context_filters JSONB NOT NULL DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  CONSTRAINT filter_engine_context_mappings_user_id_key UNIQUE (user_id)
);

COMMIT;
```

### Create New Views

```sql
-- Create new views for the filter engine
BEGIN;

-- New view for templates
CREATE OR REPLACE VIEW filter_engine_template_view AS
SELECT
  t.id,
  t.name,
  t.description,
  t.status,
  t.version,
  t.created_at,
  t.updated_at,
  c.name AS company_name,
  u1.name AS initiated_by_name,
  u2.name AS approved_by_name,
  t.approval_date,
  a.name AS agent_name
FROM templates t
LEFT JOIN companies c ON t.company_id = c.id
LEFT JOIN app_users u1 ON t.initiated_by = u1.id
LEFT JOIN app_users u2 ON t.approved_by = u2.id
LEFT JOIN agents a ON t.agent_id = a.id;

-- View for template elements
CREATE OR REPLACE VIEW filter_engine_template_elements_view AS
SELECT
  te.template_id,
  te.element_value_id,
  et.name AS element_type,
  ev.value AS element_value
FROM template_elements te
JOIN element_values ev ON te.element_value_id = ev.id
JOIN element_types et ON ev.element_type_id = et.id;

-- View for template collections
CREATE OR REPLACE VIEW filter_engine_template_collections_view AS
SELECT
  tc.template_id,
  tc.collection_id,
  c.name AS collection_name
FROM template_collections tc
JOIN collections c ON tc.collection_id = c.id;

-- View for template products
CREATE OR REPLACE VIEW filter_engine_template_products_view AS
SELECT
  tp.template_id,
  tp.product_id,
  p.name AS product_name
FROM template_products tp
JOIN products p ON tp.product_id = p.id;

COMMIT;
```

### Create New Functions

```sql
-- Create new functions for the filter engine
BEGIN;

-- Helper function for prompt elements filtering
CREATE OR REPLACE FUNCTION filter_engine_process_prompt_elements(
  template_id BIGINT,
  filter_json JSONB
) RETURNS BOOLEAN AS $$
DECLARE
  element_type TEXT;
  element_values BIGINT[];
  matches BOOLEAN := TRUE;
BEGIN
  -- For each element type in the filter
  FOR element_type, element_values IN SELECT * FROM jsonb_each(filter_json)
  LOOP
    -- Check if the template has any of the specified element values for this type
    -- The element_type will be in format 'element_TYPE_NAME'
    -- We need to extract the actual type name
    DECLARE
      actual_type TEXT := substring(element_type FROM 9); -- Remove 'element_' prefix
      has_match BOOLEAN;
    BEGIN
      SELECT EXISTS (
        SELECT 1 FROM filter_engine_template_elements_view te
        WHERE te.template_id = filter_engine_process_prompt_elements.template_id
          AND te.element_type = actual_type
          AND te.element_value_id = ANY(element_values::BIGINT[])
      ) INTO has_match;
      
      -- If no match for this element type, the template doesn't match the filter
      IF NOT has_match THEN
        matches := FALSE;
        EXIT; -- No need to check other element types
      END IF;
    END;
  END LOOP;
  
  RETURN matches;
END;
$$ LANGUAGE plpgsql;

-- Helper function for related items filtering
CREATE OR REPLACE FUNCTION filter_engine_process_related_items(
  template_id BIGINT,
  filter_json JSONB
) RETURNS BOOLEAN AS $$
DECLARE
  matches BOOLEAN := TRUE;
  collection_ids BIGINT[];
  product_ids BIGINT[];
BEGIN
  -- Extract collection IDs if present
  IF filter_json ? 'collections' THEN
    collection_ids := (SELECT array_agg(value::BIGINT) FROM jsonb_array_elements_text(filter_json->'collections'));
    
    -- Check if the template is in any of the specified collections
    IF NOT EXISTS (
      SELECT 1 FROM filter_engine_template_collections_view tc
      WHERE tc.template_id = filter_engine_process_related_items.template_id
        AND tc.collection_id = ANY(collection_ids)
    ) THEN
      matches := FALSE;
    END IF;
  END IF;
  
  -- Extract product IDs if present and we still have a match
  IF matches AND filter_json ? 'products' THEN
    product_ids := (SELECT array_agg(value::BIGINT) FROM jsonb_array_elements_text(filter_json->'products'));
    
    -- Check if the template is associated with any of the specified products
    IF NOT EXISTS (
      SELECT 1 FROM filter_engine_template_products_view tp
      WHERE tp.template_id = filter_engine_process_related_items.template_id
        AND tp.product_id = ANY(product_ids)
    ) THEN
      matches := FALSE;
    END IF;
  END IF;
  
  RETURN matches;
END;
$$ LANGUAGE plpgsql;

-- Main function for filtering templates
CREATE OR REPLACE FUNCTION filter_engine_templates(
  search TEXT DEFAULT NULL,
  status TEXT[] DEFAULT NULL,
  created_from TIMESTAMP DEFAULT NULL,
  created_to TIMESTAMP DEFAULT NULL,
  prompt_elements JSONB DEFAULT NULL,
  related_items JSONB DEFAULT NULL,
  sort_by TEXT DEFAULT 'updated_at',
  sort_desc BOOLEAN DEFAULT TRUE,
  limit_val INTEGER DEFAULT 10,
  offset_val INTEGER DEFAULT 0
) RETURNS TABLE (
  id BIGINT,
  name TEXT,
  description TEXT,
  status TEXT,
  version INTEGER,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  company_name TEXT,
  initiated_by_name TEXT,
  approved_by_name TEXT,
  approval_date TIMESTAMP WITH TIME ZONE,
  agent_name TEXT,
  total_count BIGINT
) AS $$
DECLARE
  query TEXT;
  count_query TEXT;
  total BIGINT;
BEGIN
  -- Build the base query
  query := 'SELECT t.* FROM filter_engine_template_view t WHERE 1=1';
  
  -- Add standard filters
  IF search IS NOT NULL THEN
    query := query || ' AND (t.name ILIKE ''%' || search || '%'' OR t.description ILIKE ''%' || search || '%'')';
  END IF;
  
  IF status IS NOT NULL AND array_length(status, 1) > 0 THEN
    query := query || ' AND t.status = ANY($1)';
  END IF;
  
  -- Add date range filters
  IF created_from IS NOT NULL THEN
    query := query || ' AND t.created_at >= $2';
  END IF;
  
  IF created_to IS NOT NULL THEN
    query := query || ' AND t.created_at <= $3';
  END IF;
  
  -- Add prompt elements filter
  IF prompt_elements IS NOT NULL AND jsonb_typeof(prompt_elements) = 'object' THEN
    query := query || ' AND EXISTS (
      SELECT 1 FROM templates t2
      WHERE t2.id = t.id
      AND filter_engine_process_prompt_elements(t2.id, $4)
    )';
  END IF;
  
  -- Add related items filter
  IF related_items IS NOT NULL AND jsonb_typeof(related_items) = 'object' THEN
    query := query || ' AND EXISTS (
      SELECT 1 FROM templates t3
      WHERE t3.id = t.id
      AND filter_engine_process_related_items(t3.id, $5)
    )';
  END IF;
  
  -- Get total count before applying limit/offset
  count_query := 'SELECT COUNT(*) FROM (' || query || ') AS count_query';
  EXECUTE count_query INTO total USING status, created_from, created_to, prompt_elements, related_items;
  
  -- Add sorting
  query := query || ' ORDER BY t.' || sort_by;
  IF sort_desc THEN
    query := query || ' DESC';
  ELSE
    query := query || ' ASC';
  END IF;
  
  -- Add pagination
  query := query || ' LIMIT ' || limit_val || ' OFFSET ' || offset_val;
  
  -- Return the results with the total count
  RETURN QUERY EXECUTE query USING status, created_from, created_to, prompt_elements, related_items;
END;
$$ LANGUAGE plpgsql;

COMMIT;
```

### Cleanup Script (Run After Migration)

```sql
-- Cleanup script to run after migration is complete
BEGIN;

-- Drop old tables
DROP TABLE IF EXISTS user_filters;

-- Drop old views
DROP VIEW IF EXISTS template_view;
DROP VIEW IF EXISTS template_view_with_collections;

-- Drop old functions
DROP FUNCTION IF EXISTS filter_templates;
DROP FUNCTION IF EXISTS get_templates;

-- Optionally rename new components to remove prefix
ALTER TABLE filter_engine_filters RENAME TO filters;
ALTER TABLE filter_engine_context_mappings RENAME TO filter_context_mappings;

ALTER VIEW filter_engine_template_view RENAME TO template_view;
ALTER VIEW filter_engine_template_elements_view RENAME TO template_elements_view;
ALTER VIEW filter_engine_template_collections_view RENAME TO template_collections_view;
ALTER VIEW filter_engine_template_products_view RENAME TO template_products_view;

ALTER FUNCTION filter_engine_templates RENAME TO filter_templates;
ALTER FUNCTION filter_engine_process_prompt_elements RENAME TO process_prompt_elements;
ALTER FUNCTION filter_engine_process_related_items RENAME TO process_related_items;

COMMIT;
```

## Benefits of This Approach

1. **Clear separation**: The new and old implementations are clearly separated, making development and testing easier.

2. **No migration complexity**: We don't need to worry about migrating existing data or maintaining backward compatibility.

3. **Easy rollback**: If issues arise, we can easily continue using the old implementation.

4. **Simplified testing**: We can test the new implementation thoroughly without affecting the current functionality.

5. **Clean slate**: We can design the new database components optimally without being constrained by the existing structure.

## Conclusion

This approach provides a clean and safe way to implement the new filter engine in a pre-production environment. By creating new database components with a distinct naming convention, we maintain a clear separation between the old and new implementations, making development, testing, and eventual migration much simpler.
