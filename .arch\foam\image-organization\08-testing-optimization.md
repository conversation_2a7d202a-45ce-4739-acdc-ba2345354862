# Testing and Optimization

This document outlines the testing and optimization strategies for the image organization system.

## Testing Strategy

### 1. Unit Testing

Test individual components and services:

```typescript
// tests/unit/services/imageService.spec.js
import { describe, it, expect, vi } from 'vitest'
import { imageService } from '@/services/imageService'

// Mock Supabase client
vi.mock('@/boot/supabase', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn(() => ({
            data: { id: 1, s3_url: 'https://example.com/image.jpg' },
            error: null
          }))
        }))
      })),
      delete: vi.fn(() => ({
        eq: vi.fn(() => ({
          data: null,
          error: null
        }))
      }))
    })),
    functions: {
      invoke: vi.fn(() => ({
        data: { success: true },
        error: null
      }))
    }
  }
}))

describe('imageService', () => {
  it('should get an image by ID', async () => {
    const image = await imageService.getImage(1)
    expect(image).toEqual({ id: 1, s3_url: 'https://example.com/image.jpg' })
  })
  
  it('should delete an image', async () => {
    const result = await imageService.deleteImage(1)
    expect(result).toBe(true)
  })
})
```

### 2. Integration Testing

Test the interaction between components:

```typescript
// tests/integration/imageUpload.spec.js
import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import ImageUploader from '@/components/ImageUploader.vue'
import { imageService } from '@/services/imageService'

// Mock imageService
vi.mock('@/services/imageService', () => ({
  imageService: {
    uploadImage: vi.fn(() => Promise.resolve({
      id: 1,
      s3_url: 'https://example.com/image.jpg',
      filename: 'test.jpg'
    })),
    getImageDimensions: vi.fn(() => Promise.resolve({
      width: 800,
      height: 600
    }))
  }
}))

describe('ImageUploader', () => {
  it('should upload an image when a file is selected', async () => {
    const wrapper = mount(ImageUploader)
    
    // Create a mock file
    const file = new File(['dummy content'], 'test.jpg', { type: 'image/jpeg' })
    
    // Trigger file input change
    const input = wrapper.find('input[type="file"]')
    await input.trigger('change', { target: { files: [file] } })
    
    // Check if uploadImage was called
    expect(imageService.uploadImage).toHaveBeenCalledWith(file, expect.any(Object))
    
    // Wait for the upload to complete
    await vi.waitFor(() => {
      expect(wrapper.emitted('upload-success')).toBeTruthy()
    })
    
    // Check emitted event data
    const emittedData = wrapper.emitted('upload-success')[0][0]
    expect(emittedData).toEqual({
      id: 1,
      s3_url: 'https://example.com/image.jpg',
      filename: 'test.jpg'
    })
  })
})
```

### 3. End-to-End Testing

Test the complete user flow:

```typescript
// tests/e2e/imageManagement.spec.js
import { test, expect } from '@playwright/test'

test('should upload and manage images', async ({ page }) => {
  // Login
  await page.goto('/login')
  await page.fill('input[name="email"]', '<EMAIL>')
  await page.fill('input[name="password"]', 'password')
  await page.click('button[type="submit"]')
  
  // Navigate to image management
  await page.goto('/images')
  
  // Upload an image
  await page.setInputFiles('input[type="file"]', 'tests/fixtures/test-image.jpg')
  await page.waitForSelector('.preview-image')
  
  // Verify image appears in gallery
  await expect(page.locator('.image-card')).toHaveCount(1)
  
  // Open image details
  await page.click('.image-card')
  await expect(page.locator('.q-dialog')).toBeVisible()
  
  // Delete the image
  await page.click('.q-dialog button[icon="delete"]')
  await page.click('text=Delete')
  
  // Verify image is removed
  await expect(page.locator('.image-card')).toHaveCount(0)
})
```

## Performance Optimization

### 1. Database Optimization

Ensure database queries are efficient:

1. **Indexing**: Verify that all necessary indexes are created
2. **Query Analysis**: Use `EXPLAIN ANALYZE` to identify slow queries
3. **Pagination**: Ensure all list queries use proper pagination

Example of analyzing a query:

```sql
EXPLAIN ANALYZE
SELECT * FROM s3_images
WHERE company_id = 1
AND tags @> ARRAY['summer', 'beach']
ORDER BY created_at DESC
LIMIT 20 OFFSET 0;
```

### 2. S3 Optimization

Optimize S3 storage and access:

1. **Object Lifecycle Management**: Set up lifecycle rules to transition older objects to cheaper storage classes
2. **CloudFront Integration**: Use CloudFront CDN for faster image delivery
3. **Image Compression**: Implement server-side compression for uploaded images

Example CloudFront setup:

```json
{
  "Origin": {
    "DomainName": "dreambox-studio-images.s3.amazonaws.com",
    "OriginPath": "",
    "S3OriginConfig": {
      "OriginAccessIdentity": "origin-access-identity/cloudfront/EXXXXXXXXXXXXX"
    }
  },
  "DefaultCacheBehavior": {
    "AllowedMethods": ["GET", "HEAD", "OPTIONS"],
    "CachedMethods": ["GET", "HEAD", "OPTIONS"],
    "Compress": true,
    "DefaultTTL": 86400,
    "MaxTTL": 31536000,
    "MinTTL": 0,
    "ViewerProtocolPolicy": "redirect-to-https"
  }
}
```

### 3. Frontend Optimization

Optimize frontend performance:

1. **Lazy Loading**: Implement lazy loading for images in galleries
2. **Image Resizing**: Use appropriately sized images for different contexts
3. **Caching**: Implement client-side caching for frequently accessed images
4. **Compression**: Use WebP or AVIF formats for better compression

Example of lazy loading in the gallery component:

```vue
<q-img
  :src="image.s3_url"
  :ratio="1"
  spinner-color="primary"
  spinner-size="42px"
  loading="lazy"
/>
```

### 4. Edge Function Optimization

Optimize Edge Functions:

1. **Caching**: Implement caching for frequently used data
2. **Error Handling**: Ensure robust error handling and retries
3. **Monitoring**: Set up monitoring to identify performance issues

## Monitoring and Maintenance

### 1. Set Up Monitoring

Monitor the image system's performance:

1. **S3 Metrics**: Monitor storage usage, request counts, and error rates
2. **Database Metrics**: Monitor query performance and table sizes
3. **Edge Function Metrics**: Monitor execution time and error rates

### 2. Regular Maintenance

Implement regular maintenance tasks:

1. **Database Cleanup**: Remove orphaned image records
2. **S3 Cleanup**: Remove unused objects
3. **Performance Review**: Regularly review performance metrics and optimize as needed

Example cleanup function:

```sql
-- Function to clean up orphaned image records
CREATE OR REPLACE FUNCTION cleanup_orphaned_images()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_count INTEGER;
BEGIN
  -- Find and delete orphaned records
  WITH deleted AS (
    DELETE FROM s3_images
    WHERE template_id IS NOT NULL
    AND NOT EXISTS (
      SELECT 1 FROM templates WHERE id = s3_images.template_id
    )
    RETURNING id
  )
  SELECT COUNT(*) INTO v_count FROM deleted;
  
  RETURN v_count;
END;
$$;
```

## Next Steps

With the image organization system fully implemented and optimized, you can now:

1. **Integrate with Other Systems**: Connect the image system with other parts of the application
2. **Expand Functionality**: Add additional features like image editing, AI tagging, etc.
3. **User Training**: Provide documentation and training for users on how to effectively use the image system
