/**
 * Boot file for initializing the Filter Engine
 */
import { boot } from 'quasar/wrappers';
import { FilterPlugin } from '../lib/filters/FilterPlugin';
import { featureFlags, FEATURES } from '../config/features';
import { filterEngine } from '../lib/filters/FilterEngine';
import { registerAllPlugins } from '../lib/filters/plugins';

export default boot(({ app }) => {
  console.log('Initializing Filter Engine...');

  // Register the Filter Plugin (UI components)
  app.use(FilterPlugin);

  // Register all filter engine plugins
  registerAllPlugins(filterEngine);

  // Enable the feature flag for development
  if (process.env.DEV) {
    featureFlags.enable(FEATURES.NEW_FILTER_ENGINE);
    console.log('Filter Engine feature flag enabled for development');
  }

  console.log('Filter Engine initialized');
});
