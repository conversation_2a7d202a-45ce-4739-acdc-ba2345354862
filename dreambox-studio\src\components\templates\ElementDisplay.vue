<template>
  <div class="element-display">
    <!-- Desktop view: Cards -->
    <div class="row q-col-gutter-md gt-xs">
      <div
        v-for="typeId in selectedElementTypes"
        :key="typeId"
        class="col-12 col-sm-6 col-md-4"
      >
        <q-card class="element-type-card">
          <q-card-section class="bg-primary text-white">
            <div class="text-subtitle1">{{ getElementTypeName(typeId) }}</div>
          </q-card-section>
          <q-card-section>
            <q-list dense>
              <q-item v-for="value in getSelectedValuesForType(typeId)" :key="value.id">
                <q-item-section>
                  <q-item-label>{{ value.value }}</q-item-label>
                  <q-item-label caption v-if="value.description">{{ value.description }}</q-item-label>
                </q-item-section>
              </q-item>
              <q-item v-if="getSelectedValuesForType(typeId).length === 0">
                <q-item-section>
                  <q-item-label class="text-grey">No values selected</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- Mobile view: Tree -->
    <div class="xs-only">
      <q-tree
        :nodes="elementTreeData"
        node-key="id"
        no-connectors
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { supabase } from 'src/boot/supabase';

const props = defineProps({
  selectedElementTypes: {
    type: Array as () => number[],
    required: true
  },
  selectedElementValues: {
    type: Array as () => number[],
    required: true
  }
});

// Element type names
const elementTypeNames = ref<Record<number, string>>({});

// Element values by type
const elementValuesByType = ref<Record<number, { id: number, value: string, description: string | null }[]>>({});

// Format element type name (convert snake_case to Title Case)
function formatElementTypeName(name: string): string {
  return name
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// Get element type name
function getElementTypeName(typeId: number): string {
  return elementTypeNames.value[typeId] || `Type ${typeId}`;
}

// Get selected values for a type
function getSelectedValuesForType(typeId: number) {
  if (!elementValuesByType.value[typeId]) return [];

  return elementValuesByType.value[typeId].filter(value =>
    props.selectedElementValues.includes(value.id)
  );
}

// Tree data for mobile view
const elementTreeData = computed(() => {
  return props.selectedElementTypes.map(typeId => {
    const selectedValues = getSelectedValuesForType(typeId);

    return {
      id: `type-${typeId}`,
      label: getElementTypeName(typeId),
      children: selectedValues.length > 0
        ? selectedValues.map(value => ({
            id: `value-${value.id}`,
            label: value.value,
            caption: value.description
          }))
        : [{ id: `empty-${typeId}`, label: 'No values selected', disabled: true }]
    };
  });
});

// Fetch element type names
async function fetchElementTypeNames() {
  try {
    const { data, error } = await supabase
      .from('prompt_element_types')
      .select('id, name');

    if (error) throw error;

    if (data) {
      data.forEach((type: { id: number, name: string }) => {
        elementTypeNames.value[type.id] = formatElementTypeName(type.name);
      });
    }
  } catch (error) {
    console.error('Error fetching element type names:', error);
  }
}

// Fetch element values
async function fetchElementValues() {
  try {
    // Only fetch for selected types
    if (props.selectedElementTypes.length === 0) return;

    const { data, error } = await supabase
      .from('prompt_elements')
      .select('id, type_id, value, description')
      .in('type_id', props.selectedElementTypes);

    if (error) throw error;

    if (data) {
      // Group by type_id
      const valuesByType: Record<number, typeof data> = {};

      data.forEach((item) => {
        const typeId = item.type_id;
        if (!valuesByType[typeId]) {
          valuesByType[typeId] = [];
        }
        valuesByType[typeId].push(item);
      });

      // Update the ref
      elementValuesByType.value = valuesByType;
    }
  } catch (error) {
    console.error('Error fetching element values:', error);
  }
}

// Watch for changes in selected types
watch(() => props.selectedElementTypes, () => {
  void fetchElementValues();
}, { deep: true });

// Initialize
onMounted(() => {
  void fetchElementTypeNames();
  void fetchElementValues();
});
</script>

<style lang="scss" scoped>
.element-type-card {
  height: 100%;
}
</style>
