<template>
  <q-card class="mock-voice-panel q-pa-md" v-if="modelValue">
    <q-card-section>
      <div class="row items-center justify-between q-mb-md">
        <div class="text-h6">{{ $t('voiceCommands.title') }}</div>
        <q-btn flat round dense icon="close" @click="updateModelValue(false)" />
      </div>

      <q-select
        v-model="commandText"
        :options="filteredCommands"
        use-input
        hide-selected
        fill-input
        input-debounce="0"
        :label="$t('voiceCommands.enterCommand')"
        outlined
        @filter="filterCommands"
        @keyup.enter="executeCommand"
        class="q-mb-sm"
        popup-content-class="custom-scrollbar"
      >
        <template v-slot:no-option>
          <q-item>
            <q-item-section class="text-grey">
              {{ $t('voiceCommands.noCommandDetected') }}
            </q-item-section>
          </q-item>
        </template>

        <template v-slot:option="scope">
          <q-item v-bind="scope.itemProps">
            <q-item-section>
              <q-item-label>{{ scope.opt }}</q-item-label>
              <q-item-label caption v-if="getCommandDescription(scope.opt)">
                {{ getCommandDescription(scope.opt) }}
              </q-item-label>
              <q-item-label caption v-if="getCommandExample(scope.opt)">
                {{ $t('voiceCommands.examples') }}: "{{ getCommandExample(scope.opt) }}"
              </q-item-label>
            </q-item-section>
          </q-item>
        </template>

        <template v-slot:append>
          <q-btn
            round
            dense
            flat
            :icon="isListening ? 'mic_off' : 'mic'"
            :color="isListening ? 'negative' : 'primary'"
            @click="simulateVoiceInput"
          />
        </template>
      </q-select>

      <q-btn
        color="primary"
        :label="$t('voiceCommands.execute')"
        class="q-mb-md"
        @click="executeCommand"
      />

      <div v-if="lastResult" class="q-mb-md">
        <div class="text-subtitle2">{{ $t('voiceCommands.lastResult') }}:</div>
        <q-card bordered flat :class="$q.dark.isActive ? 'bg-dark q-pa-sm' : 'bg-grey-2 q-pa-sm'">
          <pre class="text-wrap">{{ JSON.stringify(lastResult, null, 2) }}</pre>
        </q-card>
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { commandRegistry } from 'src/services/commands/registry';
import { mockWebSocket } from 'src/services/commands/mock-websocket';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import { notify } from 'src/boot/notifications';

const $q = useQuasar();
const { t } = useI18n();
const commandText = ref('');

// Props
const props = defineProps<{
  modelValue: boolean;
}>();

// Emits
const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void;
}>();

// Update model value
function updateModelValue(value: boolean) {
  emit('update:modelValue', value);

  // Refresh available commands when opening the panel
  if (value) {
    refreshAvailableCommands();
  }
}

// Refresh available commands
function refreshAvailableCommands() {
  availableCommands.value = commandRegistry.getAvailableCommands() as typeof availableCommands.value;
  filteredCommands.value = availableCommands.value.map(cmd => cmd.name);
  // console.log('Refreshed available commands:', availableCommands.value);
}

const lastResult = ref<null | Record<string, unknown>>(null);
const availableCommands = ref<Array<{
  name: string;
  description: string;
  parameters?: Array<{
    name: string;
    type: string;
    required: boolean;
    description: string;
  }>;
  examples?: string[];
}>>([]);

// Filtered commands for the dropdown
const filteredCommands = ref<string[]>([]);

// Filter commands based on user input
function filterCommands(val: string, update: (callback: () => void) => void) {
  if (val === '') {
    update(() => {
      filteredCommands.value = availableCommands.value.map(cmd => cmd.name);
    });
    return;
  }

  update(() => {
    const needle = val.toLowerCase();
    filteredCommands.value = availableCommands.value
      .filter(cmd => {
        // Check command name
        if (cmd.name.toLowerCase().includes(needle)) return true;

        // Check normalized name
        if (normalizeCommandName(cmd.name).includes(needle)) return true;

        // Check examples
        if (cmd.examples && cmd.examples.some(ex => ex.toLowerCase().includes(needle))) return true;

        // Check aliases
        const aliasMatch = Object.entries(commandAliases)
          .some(([alias, cmdName]) => alias.includes(needle) && cmdName === cmd.name);
        if (aliasMatch) return true;

        return false;
      })
      .map(cmd => cmd.name);
  });
}

// Helper functions to get command details
function getCommandDescription(commandName: string): string {
  const cmd = availableCommands.value.find(c => c.name === commandName);
  return cmd?.description || '';
}

function getCommandExample(commandName: string): string {
  const cmd = availableCommands.value.find(c => c.name === commandName);
  return cmd?.examples?.[0] || '';
}

const isListening = ref(false);
const commandHistory = ref<string[]>([]);
const historyIndex = ref(-1);

// Command history navigation
function addToHistory(command: string) {
  if (command.trim() === '') return;

  // Don't add duplicate consecutive commands
  if (commandHistory.value.length > 0 && commandHistory.value[0] === command) {
    return;
  }

  commandHistory.value.unshift(command);
  if (commandHistory.value.length > 10) {
    commandHistory.value.pop();
  }
  historyIndex.value = -1;
}

function navigateHistory(direction: 'up' | 'down') {
  if (commandHistory.value.length === 0) return;

  if (direction === 'up') {
    historyIndex.value = Math.min(historyIndex.value + 1, commandHistory.value.length - 1);
  } else {
    historyIndex.value = Math.max(historyIndex.value - 1, -1);
  }

  if (historyIndex.value >= 0) {
    commandText.value = commandHistory.value[historyIndex.value] || '';
  } else {
    commandText.value = '';
  }
}

// Helper function to normalize command names for matching
function normalizeCommandName(name: string): string {
  // Convert camelCase to space-separated words
  return name.replace(/([a-z])([A-Z])/g, '$1 $2').toLowerCase();
}

// Map of common command variations to actual command names
const commandAliases: Record<string, string> = {
  'toggle dark mode': 'toggleDarkMode',
  'switch theme': 'toggleDarkMode',
  'change theme': 'toggleDarkMode',
  'dark mode': 'toggleDarkMode',
  'light mode': 'toggleDarkMode',
  'toggle theme': 'toggleDarkMode',
  'open dashboard': 'openDashboard',
  'go to dashboard': 'openDashboard',
  'show dashboard': 'openDashboard',
  'open settings': 'openSettings',
  'go to settings': 'openSettings',
  'show settings': 'openSettings',
  'toggle left drawer': 'toggleLeftDrawer',
  'open menu': 'toggleLeftDrawer',
  'close menu': 'toggleLeftDrawer',
  'toggle navigation': 'toggleLeftDrawer',
  'toggle right drawer': 'toggleRightDrawer',
  'open preview': 'toggleRightDrawer',
  'close preview': 'toggleRightDrawer',
  'log out': 'logout',
  'sign out': 'logout'
};

function executeCommand() {
  const trimmedCommand = commandText.value.trim();
  if (!trimmedCommand) return;

  try {
    // Log the command being executed
    console.log('Executing command:', trimmedCommand);
    console.log('Available commands:', availableCommands.value);

    // Special case for toggleDarkMode
    if (trimmedCommand.toLowerCase() === 'toggledarkmode' ||
        trimmedCommand.toLowerCase() === 'toggle dark mode') {
      console.log('Executing toggleDarkMode command');
      mockWebSocket.simulateCommand('toggleDarkMode');
      addToHistory('toggleDarkMode');
      commandText.value = '';
      return;
    }

    // Check if the command is in the available commands list
    const isExactCommand = availableCommands.value.some(cmd => cmd.name === trimmedCommand);

    if (isExactCommand) {
      // If it's an exact command, execute it directly
      console.log('Executing exact command:', trimmedCommand);
      mockWebSocket.simulateCommand(trimmedCommand);
      addToHistory(trimmedCommand);
      commandText.value = '';
    } else {
      // If it's not an exact command, try to find a matching command
      const matchingCommand = availableCommands.value.find(cmd =>
        cmd.name.toLowerCase() === trimmedCommand.toLowerCase() ||
        cmd.examples?.some(ex => ex.toLowerCase() === trimmedCommand.toLowerCase())
      );

      if (matchingCommand) {
        console.log('Executing matching command:', matchingCommand.name);
        mockWebSocket.simulateCommand(matchingCommand.name);
        addToHistory(matchingCommand.name);
        commandText.value = '';
      } else {
        // If no matching command is found, execute as is
        console.log('No matching command found, executing as is:', trimmedCommand);
        mockWebSocket.simulateCommand(trimmedCommand);
        addToHistory(trimmedCommand);
        commandText.value = '';
      }
    }

    // Refresh available commands list
    refreshAvailableCommands();
  } catch (error) {
    lastResult.value = {
      error: error instanceof Error ? error.message : 'Unknown error',
      input: trimmedCommand
    };

    void notify({
      color: 'negative',
      message: error instanceof Error ? error.message : 'Unknown error',
      icon: 'error'
    });
  }
}

function simulateVoiceInput() {
  isListening.value = true;

  // Show notification
  void notify({
    color: 'info',
    message: t('voiceCommands.listening'),
    icon: 'mic'
  });

  // Simulate processing time
  setTimeout(() => {
    isListening.value = false;

    // If there's text, execute the command
    if (commandText.value) {
      void executeCommand();
    } else {
      void notify({
        color: 'warning',
        message: t('voiceCommands.noCommandDetected'),
        icon: 'mic_off'
      });
    }
  }, 1500);
}

// Initialize
onMounted(() => {
  // Set up a small delay to ensure commands are registered before we try to get them
  setTimeout(() => {
    refreshAvailableCommands();
    console.log('Available commands on mount:', availableCommands.value.map(cmd => ({
      name: cmd.name,
      normalized: normalizeCommandName(cmd.name),
      examples: cmd.examples
    })));
  }, 500);

  // Set up keyboard shortcuts
  window.addEventListener('keydown', (e) => {
    if (props.modelValue) {
      if (e.key === 'ArrowUp') {
        navigateHistory('up');
      } else if (e.key === 'ArrowDown') {
        navigateHistory('down');
      }
    }
  });

  // Listen for WebSocket results
  mockWebSocket.on('result', (data) => {
    lastResult.value = data as Record<string, unknown>;
  });
});
</script>

<style scoped>
.mock-voice-panel {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 350px;
  z-index: 1000;
  max-height: 80vh;
  overflow-y: auto;
}

pre {
  white-space: pre-wrap;
  word-break: break-word;
}
</style>
