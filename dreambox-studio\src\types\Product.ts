/**
 * Product interface
 */
export interface Product {
  id: number;
  name: string;
  description: string | null;
  sku: string;
  base_price: number;
  prod_company_id: number; // Changed from company_id
  company_name?: string;
  template_id: number | null;
  template_name?: string;
  image_id: number | null;
  image_url?: string;
  agent_id: number | null;
  agent_name?: string;
  category: string;
  active: boolean;
  created_at: string;
  updated_at: string;
}

/**
 * Product filter interface
 */
export interface ProductFilter {
  prod_company_id?: number; // Changed from company_id
  template_id?: number;
  collection_id?: number;
  active?: boolean;
  search?: string;
}

/**
 * Product pagination interface
 */
export interface ProductPagination {
  page: number;
  rowsPerPage: number;
  rowsNumber: number;
  sortBy?: string;
  descending?: boolean;
}
