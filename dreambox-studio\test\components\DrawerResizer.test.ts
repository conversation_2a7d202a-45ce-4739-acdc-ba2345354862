import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import DrawerResizer from '../../src/components/common/DrawerResizer.vue';

// Mock Quasar's platform detection
// Note: We're using a manual mock instead of vi.mock since it's not working in this environment
const originalUseQuasar = globalThis.useQuasar;
globalThis.useQuasar = () => ({
  platform: {
    is: {
      mobile: false // Default to desktop for tests
    }
  }
});

describe.skip('DrawerResizer', () => {
  it('renders correctly on desktop', () => {
    const wrapper = mount(DrawerResizer);
    expect(wrapper.exists()).toBe(true);
    expect(wrapper.classes()).toContain('drawer-resizer');
  });

  it('applies right side class when side prop is right', () => {
    const wrapper = mount(DrawerResizer, {
      props: {
        side: 'right'
      }
    });
    expect(wrapper.classes()).toContain('drawer-resizer--right');
  });

  it('emits resize event with correct delta when dragged', async () => {
    const wrapper = mount(DrawerResizer);

    // Mock the touch-pan event with Quasar's TouchPanDetails structure
    const event = {
      delta: { x: 50, y: 0 },
      direction: 'right',
      isFirst: false,
      isFinal: false
    };

    // Call the handleResize method directly
    await wrapper.vm.handleResize(event);

    // Check that the resize event was emitted with the correct value
    expect(wrapper.emitted('resize')).toBeTruthy();
    expect(wrapper.emitted('resize')![0]).toEqual([50]);
  });

  it('emits negative delta for right drawer when dragged', async () => {
    const wrapper = mount(DrawerResizer, {
      props: {
        side: 'right'
      }
    });

    // Mock the touch-pan event with Quasar's TouchPanDetails structure
    const event = {
      delta: { x: 50, y: 0 },
      direction: 'right',
      isFirst: false,
      isFinal: false
    };

    // Call the handleResize method directly
    await wrapper.vm.handleResize(event);

    // For right drawer, the delta should be negated
    expect(wrapper.emitted('resize')).toBeTruthy();
    expect(wrapper.emitted('resize')![0]).toEqual([-50]);
  });

  // Add a test for mobile behavior
  it('does not render on mobile devices', () => {
    // Temporarily change the mock to simulate mobile device
    const originalMock = globalThis.useQuasar;
    globalThis.useQuasar = () => ({
      platform: {
        is: {
          mobile: true
        }
      }
    });

    // Test mobile behavior
    const wrapper = mount(DrawerResizer);
    expect(wrapper.html()).toBe('');

    // Restore the original mock
    globalThis.useQuasar = originalMock;
  });
});