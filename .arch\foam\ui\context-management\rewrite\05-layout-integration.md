# Layout Integration

This file shows how to integrate the Context Manager with the main layout of the application.

## File: `layouts/MainLayout.vue`

```vue
<template>
  <q-layout view="hHh lpR fFf" :class="platformClass">
    <!-- Top Bar -->
    <q-header elevated class="bg-primary text-white" :style="{ height: 'auto' }">
      <q-toolbar>
        <!-- Standard UI elements -->
        <q-btn
          flat
          round
          dense
          icon="menu"
          aria-label="Menu"
          @click="leftDrawerOpen = !leftDrawerOpen"
        />
        
        <!-- Dynamic components from context -->
        <dynamic-component
          v-for="(comp, index) in contextStore.topBarComponents"
          :key="index"
          :name="comp.component"
          v-bind="comp.props"
        />
      </q-toolbar>
    </q-header>
    
    <!-- Left Drawer -->
    <q-drawer
      v-model="leftDrawerOpen"
      :behavior="drawerBehavior"
      show-if-above
      bordered
      :width="leftDrawerWidth"
      :breakpoint="768"
      side="left"
      class="drawer-with-tabs"
      content-class="no-scroll"
    >
      <!-- Drawer tabs -->
      <q-tabs
        v-model="activeLeftDrawerTab"
        dense
        class="bg-primary text-white drawer-tabs drawer-tabs-height"
        indicator-color="secondary"
        narrow-indicator
      >
        <!-- Main navigation tab - always present -->
        <q-tab name="nav" icon="home" />
        
        <!-- Dynamic tabs based on context -->
        <q-tab
          v-for="tab in leftDrawerTabs"
          :key="tab.name"
          :name="tab.name"
          :icon="tab.icon"
        >
          <q-tooltip>{{ tab.label }}</q-tooltip>
        </q-tab>
      </q-tabs>
      
      <!-- Tab panels -->
      <q-tab-panels
        v-model="activeLeftDrawerTab"
        animated
        transition-prev="slide-right"
        transition-next="slide-left"
        class="drawer-panels"
        style="height: calc(100% - 50px); overflow: hidden;"
      >
        <!-- Navigation Panel -->
        <q-tab-panel name="nav" class="q-pa-none">
          <q-scroll-area class="fit">
            <!-- Find the navigation component -->
            <dynamic-component
              v-for="(comp, index) in navigationComponents"
              :key="index"
              :name="comp.component"
              v-bind="comp.props"
            />
          </q-scroll-area>
        </q-tab-panel>
        
        <!-- Dynamic tab panels -->
        <q-tab-panel
          v-for="tab in leftDrawerTabs"
          :key="tab.name"
          :name="tab.name"
          class="q-pa-none"
        >
          <q-scroll-area class="fit">
            <!-- Components for this tab -->
            <dynamic-component
              v-for="(comp, index) in getComponentsForTab(tab.name)"
              :key="index"
              :name="comp.component"
              v-bind="comp.props"
            />
          </q-scroll-area>
        </q-tab-panel>
      </q-tab-panels>
    </q-drawer>
    
    <!-- Right Drawer (similar structure to Left Drawer) -->
    <q-drawer
      v-model="rightDrawerOpen"
      :behavior="drawerBehavior"
      bordered
      :width="rightDrawerWidth"
      side="right"
      content-class="no-scroll"
      class="drawer-with-tabs"
    >
      <!-- Similar structure to left drawer -->
      <!-- ... -->
    </q-drawer>
    
    <!-- Main Content -->
    <q-page-container>
      <!-- Router view for main content -->
      <router-view />
    </q-page-container>
    
    <!-- Bottom Bar -->
    <q-footer
      v-if="contextStore.bottomBarComponents.length > 0"
      elevated
      class="bg-primary text-white"
      :class="{ 'footer-expanded': footerExpanded }"
      :style="{ height: 'auto', transition: 'height 0.3s ease' }"
    >
      <q-toolbar>
        <!-- Dynamic components from context -->
        <dynamic-component
          v-for="(comp, index) in contextStore.bottomBarComponents"
          :key="index"
          :name="comp.component"
          v-bind="comp.props"
        />
        
        <!-- Footer expand toggle -->
        <div class="absolute-center footer-icon-wrapper" @click="toggleFooterExpand">
          <q-icon
            :name="footerExpanded ? 'expand_more' : 'expand_less'"
            color="white"
            size="24px"
          >
            <q-tooltip>{{ footerExpanded ? 'Collapse' : 'Expand' }}</q-tooltip>
          </q-icon>
        </div>
      </q-toolbar>
      
      <!-- Expanded footer content -->
      <div v-if="footerExpanded" class="footer-expanded-content q-px-md q-pt-md q-pb-md">
        <!-- Additional footer content -->
      </div>
    </q-footer>
  </q-layout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useContextStore } from 'src/stores/contextStore';
import { useAuthStore } from 'src/stores/authStore';
import DynamicComponent from 'src/components/common/DynamicComponent.vue';
import { PlatformUtil } from 'src/utils/platformUtil';

// Stores
const contextStore = useContextStore();
const authStore = useAuthStore();
const route = useRoute();

// Drawer state
const leftDrawerOpen = ref(false);
const rightDrawerOpen = ref(false);
const activeLeftDrawerTab = ref('nav');
const activeRightDrawerTab = ref('chat');
const leftDrawerWidth = ref(280);
const rightDrawerWidth = ref(280);
const footerExpanded = ref(false);

// Platform detection
const platformClass = computed(() => PlatformUtil.getPlatformClass());
const drawerBehavior = computed(() => PlatformUtil.isMobile() ? 'mobile' : 'desktop');

// Get navigation components
const navigationComponents = computed(() => {
  return contextStore.leftDrawerComponents.filter(comp => 
    comp.component === 'Navigation'
  );
});

// Define left drawer tabs based on components
const leftDrawerTabs = computed(() => {
  const tabs = [];
  
  // Check for filter components
  if (contextStore.leftDrawerComponents.some(comp => comp.component === 'FilterPanel')) {
    tabs.push({ name: 'filters', icon: 'filter_list', label: 'Filters' });
  }
  
  // Check for prompt elements components
  if (contextStore.leftDrawerComponents.some(comp => comp.component === 'PromptElementsPanel')) {
    tabs.push({ name: 'elements', icon: 'widgets', label: 'Prompt Elements' });
  }
  
  return tabs;
});

// Get components for a specific tab
function getComponentsForTab(tabName: string) {
  switch (tabName) {
    case 'filters':
      return contextStore.leftDrawerComponents.filter(comp => 
        comp.component === 'FilterPanel'
      );
    case 'elements':
      return contextStore.leftDrawerComponents.filter(comp => 
        comp.component === 'PromptElementsPanel' || 
        comp.component === 'ElementValuesEditor'
      );
    default:
      return [];
  }
}

// Toggle footer expansion
function toggleFooterExpand() {
  footerExpanded.value = !footerExpanded.value;
}

// Listen for context changes to update active tabs
onMounted(() => {
  document.addEventListener('context-changed', handleContextChange);
});

// Clean up
onUnmounted(() => {
  document.removeEventListener('context-changed', handleContextChange);
});

// Handle context changes
function handleContextChange(event: CustomEvent) {
  const { context, subContext } = event.detail;
  
  // Set active tab based on context
  if (context.includes('templates_table') && subContext.includes('elements')) {
    activeLeftDrawerTab.value = 'elements';
  } else if (context.includes('_table')) {
    activeLeftDrawerTab.value = 'filters';
  }
}

// Set initial drawer state based on screen size
onMounted(() => {
  leftDrawerOpen.value = !PlatformUtil.isMobile();
});
</script>
```
