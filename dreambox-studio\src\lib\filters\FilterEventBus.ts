/**
 * FilterEventBus.ts
 *
 * This module provides a standardized event system for filter-related communication.
 * It defines event types and provides methods for emitting and subscribing to events.
 */

import mitt from 'mitt';

/**
 * Filter event types
 */
export const FILTER_EVENTS = {
  FILTER_CHANGED: 'filter:changed',
  FILTER_CLEARED: 'filter:cleared',
  FILTER_SAVED: 'filter:saved',
  FILTER_LOADED: 'filter:loaded',
  FILTER_DELETED: 'filter:deleted',
  COLUMN_VISIBILITY_CHANGED: 'filter:column-visibility-changed',
  COLUMN_ORDER_CHANGED: 'filter:column-order-changed',
  SPECIALIZED_FILTER_CHANGED: 'filter:specialized-changed'
} as const;

/**
 * Filter event payload types
 */
export type FilterEventPayload = {
  'filter:changed': {
    tableName: string;
    filterId?: number | null;
    columnId?: string;
    value: unknown;
  };
  'filter:cleared': {
    tableName: string;
    filterId?: number | null;
    columnId?: string;
  };
  'filter:saved': {
    tableName: string;
    filterId: number;
    name: string;
  };
  'filter:loaded': {
    tableName: string;
    filterId: number;
  };
  'filter:deleted': {
    tableName: string;
    filterId: number;
  };
  'filter:column-visibility-changed': {
    tableName: string;
    columnId: string;
    visible: boolean;
    section: string;
  };
  'filter:column-order-changed': {
    tableName: string;
    columnId: string;
    order: number;
    section: string;
  };
  'filter:specialized-changed': {
    tableName: string;
    filterId: string;
    value: unknown;
  };
};

// Create a typed event emitter
const emitter = mitt<FilterEventPayload>();

/**
 * Filter Event Bus
 */
export const filterEventBus = {
  /**
   * Subscribe to a filter event
   */
  on<K extends keyof FilterEventPayload>(
    event: K,
    handler: (payload: FilterEventPayload[K]) => void
  ) {
    // Type assertion needed because mitt doesn't have the same generic structure
    emitter.on(event, handler as (payload: unknown) => void);
    return () => emitter.off(event, handler as (payload: unknown) => void);
  },

  /**
   * Emit a filter event
   */
  emit<K extends keyof FilterEventPayload>(
    event: K,
    payload: FilterEventPayload[K]
  ) {
    emitter.emit(event, payload);

    // Also dispatch a DOM event for components that listen via addEventListener
    const domEvent = new CustomEvent('filter-event', {
      detail: {
        type: event.replace('filter:', ''),
        ...payload
      }
    });

    document.dispatchEvent(domEvent);

    console.log('FilterEventBus: Emitted event:', {
      event,
      payload,
      domEventType: 'filter-event',
      domEventDetail: {
        type: event.replace('filter:', ''),
        ...payload
      }
    });
  },

  /**
   * Unsubscribe from a filter event
   */
  off<K extends keyof FilterEventPayload>(
    event: K,
    handler: (payload: FilterEventPayload[K]) => void
  ) {
    // Type assertion needed because mitt doesn't have the same generic structure
    emitter.off(event, handler as (payload: unknown) => void);
  }
};

// Export default for module imports
export default filterEventBus;
