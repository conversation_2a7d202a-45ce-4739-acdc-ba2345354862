# Context Manager Rewrite: Overview

## Core Concept

The Context Manager is a central system that controls which UI components appear in which areas of the application based on the current context, user role, and device type. It also passes contextual information to components so they can adapt their behavior accordingly.

## Key Features

1. **Dynamic Component Placement**: Components are placed in UI areas based on context, role, and device
2. **Context-Aware Components**: Components receive information about the current context
3. **Pattern Matching**: Support for wildcards and placeholders in context definitions
4. **Property Resolution**: Dynamic resolution of component properties based on context variables
5. **Event-Based Communication**: Components can communicate through events

## Architecture

The Context Manager consists of several parts:

1. **Context Store**: A Pinia store that manages the current context and provides components for each UI area
2. **Component Registry**: A system for registering and retrieving UI components
3. **Context Matcher**: A utility for matching context patterns against the current context
4. **Property Resolver**: A utility for resolving component properties based on context variables
5. **Event Bus**: A system for component communication

## Implementation Files

1. `stores/contextStore.ts`: The main Context Manager store
2. `boot/componentRegistry.ts`: Component registration system
3. `utils/contextMatcher.ts`: Utilities for context pattern matching
4. `utils/propertyResolver.ts`: Utilities for resolving component properties
5. `components/layout/ContextAwareLayout.vue`: A layout component that uses the Context Manager

## Integration Points

1. **Main Layout**: Uses the Context Manager to render components in each UI area
2. **Route Components**: Set the context when they are mounted
3. **UI Components**: Receive and use contextual information
4. **Router**: Updates the context when routes change
