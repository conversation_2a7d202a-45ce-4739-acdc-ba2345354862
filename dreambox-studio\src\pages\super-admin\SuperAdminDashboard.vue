<template>
  <q-page class="flex flex-center">
    <div class="column items-center q-gutter-y-md">
      <h1 class="text-h4">Super Admin Dashboard</h1>
      <p class="text-subtitle1">
        Welcome to the Super Administrator interface
      </p>
      <p v-if="authStore.state.user?.currentCompany">
        Company: {{ authStore.state.user.currentCompany.name }}
      </p>
      <div class="row q-gutter-md">
        <q-btn color="primary" label="User Management" @click="navigateToUserManagement" />
        <q-btn color="primary" label="Administrator Mode" @click="switchToAdmin" v-if="hasAdminRole" />
        <q-btn color="secondary" label="Designer Mode" @click="switchToDesigner" v-if="hasDesignerRole" />
        <q-btn color="negative" label="Logout" @click="handleLogout" />
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { useAuthStore } from 'src/stores/auth'
import { useRouter } from 'vue-router'
import { computed } from 'vue'

const authStore = useAuthStore()
const router = useRouter()

const hasAdminRole = computed(() =>
  authStore.state.user?.roles.includes('admin')
)

const hasDesignerRole = computed(() =>
  authStore.state.user?.roles.includes('designer')
)

async function switchToAdmin() {
  if (authStore.state.user) {
    authStore.setCurrentRole('admin')
    try {
      await router.push('/admin')
    } catch (error) {
      console.error('Navigation error:', error)
    }
  }
}

async function switchToDesigner() {
  if (authStore.state.user) {
    authStore.setCurrentRole('designer')
    try {
      await router.push('/designer')
    } catch (error) {
      console.error('Navigation error:', error)
    }
  }
}

async function navigateToUserManagement() {
  try {
    await router.push('/super-admin/user-management')
  } catch (error) {
    console.error('Navigation error:', error)
  }
}

async function handleLogout() {
  await authStore.logout()
  try {
    await router.push('/auth/login')
  } catch (error) {
    console.error('Navigation error:', error)
  }
}
</script>
