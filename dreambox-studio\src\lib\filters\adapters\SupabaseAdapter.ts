/**
 * SupabaseAdapter.ts
 *
 * This module provides an adapter for Supabase to handle server-side filtering.
 * It converts filter definitions to Supabase RPC calls.
 */

import { supabase } from '../../../boot/supabase';
import { FilterQueryBuilder } from '../FilterQueryBuilder';

/**
 * Pagination interface
 */
export interface Pagination {
  limit: number;
  offset: number;
  total?: number;
}

/**
 * Sorting interface
 */
export interface Sorting {
  sort_by: string;
  sort_desc: boolean;
}

/**
 * Filter result interface
 */
export interface FilterResult<T> {
  items: T[];
  pagination: Pagination;
}

/**
 * Supabase Adapter class
 */
export class SupabaseAdapter {
  /**
   * Filter templates
   *
   * @param standardFilters Standard column filters
   * @param specializedFilters Specialized filters
   * @param pagination Pagination parameters
   * @param sorting Sorting parameters
   * @returns Filtered templates and pagination info
   */
  static async filterTemplates(
    standardFilters: Record<string, unknown> = {},
    specializedFilters: Record<string, unknown> = {},
    pagination: Pagination = { limit: 10, offset: 0 },
    sorting: Sorting = { sort_by: 'updated_at', sort_desc: true }
  ): Promise<FilterResult<unknown>> {
    try {
      // Build query parameters
      const params = FilterQueryBuilder.buildTemplatesQueryParams(
        standardFilters,
        specializedFilters,
        pagination,
        sorting
      );

      // Call the appropriate filter function based on parameters
      // Determine if we need to use the new function with element value filtering
      // Check if prompt_element_values exists and is not empty
      const hasElementValues = params.prompt_element_values !== undefined &&
        Array.isArray(params.prompt_element_values) &&
        params.prompt_element_values.length > 0;

      // Also check for prompt_elements_filter as a fallback
      const elementValues = params.prompt_element_values ||
        (params.prompt_elements_filter && typeof params.prompt_elements_filter === 'string'
          ? JSON.parse(params.prompt_elements_filter)
          : null);

      // Check if we have element values to filter by
      const useElementValues = hasElementValues || (elementValues && elementValues.length > 0);

      // console.log('DEBUG - Element values check:', {
      //   useElementValues,
      //   elementValues
      // });

      // Convert params to match the function signature
      const rpcParams = {
        p_company_id: null, // Set this based on user context if needed
        p_designer_id: params.designer_id_filter ? Number(params.designer_id_filter) : null,
        p_status: params.status_filter || null,
        p_published: null, // Set this if needed
        p_search: params.name_filter || params.description_filter || params.search_text || null,
        p_limit: params.limit_val || 10,
        p_offset: params.offset_val || 0,
        p_template_id: null, // Set this if needed for single template view
        p_sort_by: params.sort_by || 'updated_at',
        p_sort_desc: params.sort_desc !== undefined ? params.sort_desc : true,
        // Extract element type IDs from prompt_elements_filter if available
        p_element_types: params.prompt_elements_filter && typeof params.prompt_elements_filter === 'string' ?
          JSON.parse(params.prompt_elements_filter) : null,
        p_include_elements: true, // Always include elements by default
        // Add element value filtering parameters if available
        ...(useElementValues ? {
          p_element_values: elementValues,
          p_element_match_type: params.prompt_element_match_type || 'any'
        } : {})
      };

      console.log('SupabaseAdapter: Calling RPC with pagination:', {
        limit: rpcParams.p_limit,
        offset: rpcParams.p_offset
      });

      // Determine which function to call based on whether we have element values
      const functionName = useElementValues
        ? 'filter_engine_templates_with_elements_alternative' // Use the alternative function
        : 'filter_engine_templates_optimized';

      // console.log('DEBUG - Using function:', {
      //   functionName,
      //   useElementValues,
      //   elementValues,
      //   rpcParams: {
      //     ...rpcParams,
      //     p_element_values: rpcParams.p_element_values ? [...rpcParams.p_element_values] : null
      //   }
      // });

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const { data, error } = await (supabase.rpc as any)(
        functionName,
        rpcParams
      );

      if (error) {
        console.error(`Error from ${functionName}:`, error);
      } else {
        // Check if we have debug info in the first row
        if (data && Array.isArray(data) && data.length > 0 && data[0].debug_info) {
          // console.log(`DEBUG - ${functionName} debug info:`, data[0].debug_info);

          // Remove debug_info from the data to avoid issues with the rest of the code
          data.forEach(item => {
            if (item.debug_info) {
              delete item.debug_info;
            }
          });
        }
      }

      if (error) throw error;

      // Extract total count from the first row
      const total = data && Array.isArray(data) && data.length > 0 ?
        (data[0] as { total_count?: number }).total_count || 0 : 0;

      // console.log(`DEBUG - Total templates found: ${total}`);

      return {
        items: Array.isArray(data) ? data : [],
        pagination: {
          ...pagination,
          total: total || 0
        }
      };
    } catch (err) {
      console.error('Error filtering templates:', err);
      return {
        items: [],
        pagination: {
          ...pagination,
          total: 0
        }
      };
    }
  }

  /**
   * Filter any table
   *
   * @param tableName Table name
   * @param standardFilters Standard column filters
   * @param specializedFilters Specialized filters
   * @param pagination Pagination parameters
   * @param sorting Sorting parameters
   * @returns Filtered items and pagination info
   */
  static async filter(
    tableName: string,
    standardFilters: Record<string, unknown> = {},
    specializedFilters: Record<string, unknown> = {},
    pagination: Pagination = { limit: 10, offset: 0 },
    sorting: Sorting = { sort_by: 'updated_at', sort_desc: true }
  ): Promise<FilterResult<unknown>> {
    // Call the appropriate filter method based on table name
    if (tableName === 'templates') {
      return this.filterTemplates(
        standardFilters,
        specializedFilters,
        pagination,
        sorting
      );
    }

    // Default to an empty result for unsupported tables
    return {
      items: [],
      pagination: {
        ...pagination,
        total: 0
      }
    };
  }
}

// Export default for module imports
export default SupabaseAdapter;
