<template>
  <div class="settings-panel">
    <q-tabs
      v-model="activeTab"
      dense
      class="text-grey"
      active-color="primary"
      indicator-color="primary"
      align="left"
      narrow-indicator
    >
      <q-tab name="general" :label="$t('settings.tabs.general')" />
      <q-tab name="appearance" :label="$t('settings.tabs.appearance')" />
      <q-tab v-if="!isMobile" name="shortcuts" :label="$t('settings.tabs.shortcuts')" />
      <!-- Other tabs can be added here -->
    </q-tabs>

    <q-separator />

    <q-tab-panels v-model="activeTab" animated>
      <!-- General settings panel -->
      <q-tab-panel name="general">
        <div class="text-h6">{{ $t('settings.tabs.general') }}</div>
        <div class="q-gutter-lg">
          <!-- Language Toggle -->
          <toggle-option
            v-model="language"
            :label="$t('settings.language')"
            :option1="{ label: 'English', value: 'en-US' }"
            :option2="{ label: 'Hrvatski', value: 'hr-HR' }"
          />

          <!-- Toast Notifications Toggle -->
          <toggle-option
            v-model="toastNotificationsEnabled"
            :label="$t('settings.toastNotifications')"
            :option1="{ label: $t('common.off'), value: false, icon: 'notifications_off' }"
            :option2="{ label: $t('common.on'), value: true, icon: 'notifications' }"
          />

          <!-- Image Server Configuration -->
          <div class="text-h6 q-mt-lg q-mb-md">{{ $t('settings.imageServerConfiguration') }}</div>

          <!-- Server Termination on Reload Toggle -->
          <toggle-option
            v-model="terminateServerOnReload"
            :label="$t('settings.terminateServerOnReload')"
            :option1="{ label: $t('settings.keepRunning'), value: false, icon: 'play_arrow' }"
            :option2="{ label: $t('settings.terminate'), value: true, icon: 'stop' }"
          />

          <!-- Server Auto-Termination Timer -->
          <div class="q-mt-lg">
            <div class="text-subtitle2 q-mb-sm">{{ $t('settings.autoTerminationTimer') }}</div>
            <div class="text-caption text-grey-6 q-mb-md">
              {{ $t('settings.autoTerminationDescription') }}
            </div>
            <div class="q-pr-md">
              <q-slider
                v-model="serverAutoTerminationMinutes"
                :min="5"
                :max="60"
                :step="5"
                :label-value="`${serverAutoTerminationMinutes} ${$t('settings.minutes')}`"
                label-always
                color="primary"
                class="q-mb-sm"
                style="max-width: calc(100% - 20px);"
              />
            </div>
            <div class="text-caption text-grey-6">
              {{ $t('settings.currentSetting') }}: {{ serverAutoTerminationMinutes }} {{ $t('settings.minutes') }}
            </div>
          </div>
        </div>
      </q-tab-panel>

      <!-- Appearance settings panel -->
      <q-tab-panel name="appearance">
        <div class="text-h6">{{ $t('settings.tabs.appearance') }}</div>
        <div class="q-gutter-lg">
          <!-- UI Theme Toggle -->
          <toggle-option
            v-model="darkMode"
            :label="$t('settings.uiTheme')"
            :option1="{ label: $t('settings.lightMode'), value: false, icon: 'light_mode' }"
            :option2="{ label: $t('settings.darkMode'), value: true, icon: 'dark_mode' }"
          />

          <!-- Table Text Wrapping Toggle -->
          <toggle-option
            v-model="tableTextWrapMode"
            :label="$t('settings.tableTextWrapMode')"
            :option1="{ label: $t('settings.wrap'), value: 'wrap' }"
            :option2="{ label: $t('settings.ellipsis'), value: 'ellipsis' }"
          />
        </div>
      </q-tab-panel>

      <!-- Keyboard shortcuts panel (hidden on mobile) -->
      <q-tab-panel v-if="!isMobile" name="shortcuts">
        <keyboard-shortcuts-manager />
      </q-tab-panel>
    </q-tab-panels>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import KeyboardShortcutsManager from './KeyboardShortcutsManager.vue';
import ToggleOption from 'src/components/common/ToggleOption.vue';
import { notificationService } from 'src/services/notificationService';
import { notify } from 'src/boot/notifications';
import { TableTextWrapMode, TABLE_SETTINGS_KEY } from 'src/types/TableSettings';
import { userSettingsService } from 'src/services/userSettingsService';
import PlatformUtil from 'src/utils/platform';

const $q = useQuasar();
const { locale, t } = useI18n();

const activeTab = ref('general');

// Check if the device is mobile
const isMobile = computed(() => PlatformUtil.isMobile() || $q.screen.lt.sm);

// Watch for changes in isMobile and activeTab
watch([isMobile, activeTab], ([newIsMobile, newActiveTab]) => {
  // If device is mobile and shortcuts tab is selected, switch to general tab
  if (newIsMobile && newActiveTab === 'shortcuts') {
    activeTab.value = 'general';
  }
});

// Toast notifications setting
const notificationsEnabled = ref(true);

// Server termination on reload setting
const serverTerminationOnReload = ref(true); // Default to terminate (safer option)

// Server auto-termination timer setting (in minutes)
const serverAutoTerminationTimer = ref(30); // Default to 30 minutes

// Load settings on mount
onMounted(async () => {
  try {
    // Load toast notifications setting
    const enabled = await notificationService.areToastNotificationsEnabled();
    notificationsEnabled.value = enabled;

    // Load table text wrap mode setting
    const savedWrapMode = await userSettingsService.getSetting(TABLE_SETTINGS_KEY);
    if (savedWrapMode) {
      const settings = JSON.parse(savedWrapMode);
      wrapMode.value = settings.textWrapMode || TableTextWrapMode.WRAP;
    }

    // Load theme setting from database
    const savedTheme = await userSettingsService.getSetting('theme');
    if (savedTheme) {
      // Update the theme without triggering the computed setter
      $q.dark.set(savedTheme === 'dark');
      // Also update localStorage for consistency
      localStorage.setItem('dreambox-theme', savedTheme);
    }

    // Load language setting from database
    const savedLanguage = await userSettingsService.getSetting('language');
    if (savedLanguage && (savedLanguage === 'en-US' || savedLanguage === 'hr-HR')) {
      // Update the language without triggering the computed setter
      locale.value = savedLanguage;
      // Also update localStorage for consistency
      localStorage.setItem('dreambox-locale', savedLanguage);
    }

    // Load server termination on reload setting
    const savedTerminationSetting = await userSettingsService.getSetting('comfyui-terminate-on-reload');
    if (savedTerminationSetting !== null) {
      serverTerminationOnReload.value = savedTerminationSetting === 'true';
    }

    // Load server auto-termination timer setting
    const savedAutoTerminationTimer = await userSettingsService.getSetting('comfyui-auto-termination-minutes');
    if (savedAutoTerminationTimer !== null) {
      const minutes = parseInt(savedAutoTerminationTimer, 10);
      if (!isNaN(minutes) && minutes >= 5 && minutes <= 60) {
        serverAutoTerminationTimer.value = minutes;
      }
    }
  } catch (error) {
    console.error('Error loading settings:', error);
    void notify({
      color: 'negative',
      message: t('settings.errorLoadingSettings'),
      icon: 'error'
    });
  }
});

// Toast notifications toggle
const toastNotificationsEnabled = computed({
  get: () => notificationsEnabled.value,
  set: (value) => {
    notificationsEnabled.value = value;

    // Save toast notifications setting - use void to handle the promise
    void (async () => {
      try {
        await notificationService.setToastNotificationsEnabled(value);
        void notify({
          color: 'positive',
          message: value
            ? t('settings.toastNotificationsEnabled')
            : t('settings.toastNotificationsDisabled'),
          icon: 'check_circle'
        });
      } catch (error) {
        console.error('Error saving toast notifications setting:', error);
        void notify({
          color: 'negative',
          message: t('settings.errorSavingSettings'),
          icon: 'error'
        });
      }
    })();
  }
});

// Dark mode toggle
const darkMode = computed({
  get: () => $q.dark.isActive,
  set: (value) => {
    $q.dark.set(value);

    // Save theme setting to localStorage
    localStorage.setItem('dreambox-theme', value ? 'dark' : 'light');

    // Save theme setting to database - use void to handle the promise
    void (async () => {
      try {
        await userSettingsService.saveSetting('theme', value ? 'dark' : 'light');
        void notify({
          color: 'positive',
          message: t('settings.themeSaved'),
          icon: 'check_circle'
        });
      } catch (error) {
        console.error('Error saving theme setting:', error);
        void notify({
          color: 'negative',
          message: t('settings.errorSavingSettings'),
          icon: 'error'
        });
      }
    })();
  }
});

// Language selection
const language = computed({
  get: () => locale.value,
  set: (value) => {
    locale.value = value;

    // Save language setting to database - use void to handle the promise
    void (async () => {
      try {
        await userSettingsService.saveSetting('language', value);
        void notify({
          color: 'positive',
          message: t('settings.languageSaved'),
          icon: 'check_circle'
        });
      } catch (error) {
        console.error('Error saving language setting:', error);
        void notify({
          color: 'negative',
          message: t('settings.errorSavingSettings'),
          icon: 'error'
        });
      }
    })();
  }
});

// Language options are now directly in the ToggleOption component

// Server termination on reload toggle
const terminateServerOnReload = computed({
  get: () => serverTerminationOnReload.value,
  set: (value) => {
    serverTerminationOnReload.value = value;

    // Save server termination setting - use void to handle the promise
    void (async () => {
      try {
        await userSettingsService.saveSetting('comfyui-terminate-on-reload', value.toString());
        void notify({
          color: 'positive',
          message: value
            ? t('settings.serverWillTerminateOnReload')
            : t('settings.serverWillKeepRunningOnReload'),
          icon: 'check_circle'
        });
      } catch (error) {
        console.error('Error saving server termination setting:', error);
        void notify({
          color: 'negative',
          message: t('settings.errorSavingSettings'),
          icon: 'error'
        });
      }
    })();
  }
});

// Server auto-termination timer setting
const serverAutoTerminationMinutes = computed({
  get: () => serverAutoTerminationTimer.value,
  set: (value) => {
    serverAutoTerminationTimer.value = value;

    // Save auto-termination timer setting - use void to handle the promise
    void (async () => {
      try {
        await userSettingsService.saveSetting('comfyui-auto-termination-minutes', value.toString());
        void notify({
          color: 'positive',
          message: t('settings.autoTerminationTimerUpdated', { minutes: value }),
          icon: 'check_circle'
        });
      } catch (error) {
        console.error('Error saving auto-termination timer setting:', error);
        void notify({
          color: 'negative',
          message: t('settings.errorSavingSettings'),
          icon: 'error'
        });
      }
    })();
  }
});

// Table text wrapping mode
const wrapMode = ref<TableTextWrapMode>(TableTextWrapMode.WRAP);

// Table text wrapping toggle
const tableTextWrapMode = computed({
  get: () => wrapMode.value,
  set: (value) => {
    wrapMode.value = value;

    // Save table text wrap mode setting - use void to handle the promise
    void (async () => {
      try {
        // Save the setting
        const settings = {
          textWrapMode: value
        };
        await userSettingsService.saveSetting(TABLE_SETTINGS_KEY, JSON.stringify(settings));

        // Notify the user
        void notify({
          color: 'positive',
          message: t('settings.tableTextWrapModeChanged', {
            mode: value === TableTextWrapMode.WRAP ? t('settings.wrap') : t('settings.ellipsis')
          }),
          icon: 'check_circle'
        });

        // Dispatch an event to notify components
        document.dispatchEvent(new CustomEvent('table-text-wrap-mode-changed', {
          detail: { mode: value }
        }));
      } catch (error) {
        console.error('Error saving table text wrap mode:', error);
        void notify({
          color: 'negative',
          message: t('settings.errorSavingSettings'),
          icon: 'error'
        });
      }
    })();
  }
});
</script>

<style lang="scss" scoped>
.settings-panel {
  max-width: 1000px;
  margin: 0 auto;
}
</style>
