/**
 * contextManager.ts
 *
 * This module provides a centralized way to manage application contexts.
 * It loads context configurations and provides them to the application.
 */

import { ref, computed } from 'vue';
import type { Component, DefineComponent } from 'vue';
import { DeviceType } from 'src/types/deviceTypes';
import type { DeviceTypeValue } from 'src/types/deviceTypes';

// Import context configurations
import { defaultContext } from './contexts/default';
import { dashboardContext } from './contexts/dashboard';
import { settingsContext } from './contexts/settings';
import { templatesBrowseContext } from './contexts/templates';

// Component type definitions
export type ComponentConfig = string | null | {
  component: string;
  props?: Record<string, unknown>;
} | Component | DefineComponent<Record<string, unknown>, unknown, unknown>;

export type ComponentObject = {
  id: string;
  component: string;
  props: Record<string, unknown>;
};

// Context configuration type
export type ContextConfig = {
  id: string;
  name: string;
  leftDrawer: {
    tabs: {
      filters?: boolean;
      tools?: boolean;
      columns?: boolean;
      elements?: boolean;
    };
    components: Record<string, ComponentConfig>;
  };
  rightDrawer: {
    tabs: {
      preview?: boolean;
      settings?: boolean;
      chat?: boolean;
      values?: boolean;
    };
    components: Record<string, ComponentConfig>;
  };
  search: {
    visible: boolean;
    placeholder: string;
    targetCollection: string;
    filters?: Array<{
      name: string;
      label: string;
      options: Array<{
        label: string;
        value: string;
      }>;
    }>;
  };
  bottomBarActions: Array<{
    id: string;
    icon?: string;
    label?: string;
    action?: (...args: unknown[]) => void;
    condition?: (state: unknown) => boolean;
  }>;
  deviceOverrides: {
    [DeviceType.DESKTOP_BROWSER]: {
      leftDrawer?: {
        tabs?: Record<string, boolean>;
        components?: Record<string, ComponentConfig>;
      };
      rightDrawer?: {
        tabs?: Record<string, boolean>;
        components?: Record<string, ComponentConfig>;
      };
      search?: {
        visible?: boolean;
        placeholder?: string;
        targetCollection?: string;
      };
      bottomBarActions?: Array<{
        id: string;
        icon?: string;
        label?: string;
        action?: (...args: unknown[]) => void;
        condition?: (state: unknown) => boolean;
      }>;
    };
    [DeviceType.MOBILE_BROWSER]: {
      leftDrawer?: {
        tabs?: Record<string, boolean>;
        components?: Record<string, ComponentConfig>;
      };
      rightDrawer?: {
        tabs?: Record<string, boolean>;
        components?: Record<string, ComponentConfig>;
      };
      search?: {
        visible?: boolean;
        placeholder?: string;
        targetCollection?: string;
      };
      bottomBarActions?: Array<{
        id: string;
        icon?: string;
        label?: string;
        action?: (...args: unknown[]) => void;
        condition?: (state: unknown) => boolean;
      }>;
    };
    [DeviceType.DESKTOP_ELECTRON]: {
      leftDrawer?: {
        tabs?: Record<string, boolean>;
        components?: Record<string, ComponentConfig>;
      };
      rightDrawer?: {
        tabs?: Record<string, boolean>;
        components?: Record<string, ComponentConfig>;
      };
      search?: {
        visible?: boolean;
        placeholder?: string;
        targetCollection?: string;
      };
      bottomBarActions?: Array<{
        id: string;
        icon?: string;
        label?: string;
        action?: (...args: unknown[]) => void;
        condition?: (state: unknown) => boolean;
      }>;
    };
  };
}

// Context manager state
const currentContext = ref<string | null>(null);
const currentSubContext = ref<string | null>(null);
const contextData = ref<Record<string, unknown>>({});
const deviceType = ref<DeviceTypeValue>(DeviceType.DESKTOP_BROWSER);

// Map of context configurations
const contextConfigs: Record<string, ContextConfig> = {
  default: defaultContext,
  dashboard: dashboardContext,
  settings: settingsContext,
  templates_table: templatesBrowseContext
};

// Computed properties for components
const leftDrawerComponents = computed<ComponentObject[]>(() => {
  if (!currentContext.value) return [];
  const config = contextConfigs[currentContext.value] || contextConfigs.default;

  // console.log('Computing leftDrawerComponents for context:', currentContext.value);
  // console.log('Context config:', config);

  // Convert components object to array format
  const components: ComponentObject[] = [];
  if (config && config.leftDrawer && config.leftDrawer.components) {
    for (const [key, component] of Object.entries(config.leftDrawer.components)) {
      if (component) {
        const componentObj: ComponentObject = {
          id: key,
          component: typeof component === 'string' ? component :
                    (component && typeof component === 'object' && 'component' in component) ?
                    component.component :
                    // If it's a Vue component, use the key as the component name
                    key,
          props: typeof component === 'object' && component !== null ?
                (component && 'props' in component && component.props) ?
                component.props : {} : { tableName: 'templates' }
        };

        // console.log(`Adding component for ${key}:`, componentObj);
        components.push(componentObj);
      }
    }
  }

  // console.log('Final components array:', components);
  return components;
});

const rightDrawerComponents = computed<ComponentObject[]>(() => {
  if (!currentContext.value) return [];
  const config = contextConfigs[currentContext.value] || contextConfigs.default;

  // Convert components object to array format
  const components: ComponentObject[] = [];
  if (config && config.rightDrawer && config.rightDrawer.components) {
    for (const [key, component] of Object.entries(config.rightDrawer.components)) {
      if (component) {
        const componentObj: ComponentObject = {
          id: key,
          component: typeof component === 'string' ? component :
                    (component && typeof component === 'object' && 'component' in component) ?
                    component.component :
                    // If it's a Vue component, use the key as the component name
                    key,
          props: typeof component === 'object' && component !== null ?
                (component && 'props' in component && component.props) ?
                component.props : {} : {}
        };

        components.push(componentObj);
      }
    }
  }

  return components;
});

const topBarComponents = computed<ComponentObject[]>(() => {
  if (!currentContext.value) return [];

  // For now, return empty array as we don't have top bar components in the new structure
  return [];
});

// Bottom bar components removed as we're not using the bottom bar anymore

const expandedComponents = computed<ComponentObject[]>(() => {
  if (!currentContext.value) return [];

  // For now, return empty array as we don't have expanded components in the new structure
  return [];
});

// Computed properties for tabs
const leftDrawerTabs = computed(() => {
  if (!currentContext.value) return { nav: true };
  const config = contextConfigs[currentContext.value] || contextConfigs.default;

  // Add nav tab which is always true
  if (config && config.leftDrawer && config.leftDrawer.tabs) {
    return {
      nav: true,
      ...config.leftDrawer.tabs
    };
  }

  return { nav: true };
});

const rightDrawerTabs = computed(() => {
  if (!currentContext.value) return { chat: true };
  const config = contextConfigs[currentContext.value] || contextConfigs.default;

  if (config && config.rightDrawer && config.rightDrawer.tabs) {
    return config.rightDrawer.tabs;
  }

  return { chat: true };
});

// Device configuration
const deviceConfig = computed(() => {
  if (!currentContext.value) return {
    leftDrawerOpen: true,
    rightDrawerOpen: false,
    leftDrawerWidth: 300,
    rightDrawerWidth: 300
  };

  // For now, return default values as we don't have device config in the new structure
  return {
    leftDrawerOpen: true,
    rightDrawerOpen: false,
    leftDrawerWidth: 300,
    rightDrawerWidth: 300
  };
});

/**
 * Set the current context
 */
function setContext(context: string, subContext: string | null = null, data: Record<string, unknown> = {}) {
  // console.log(`Setting context: ${context}, subContext: ${subContext}, data:`, data);

  // Check if the context exists
  if (!contextConfigs[context]) {
    console.warn(`Context ${context} not found in contextConfigs!`);
  }

  currentContext.value = context;
  currentSubContext.value = subContext;
  contextData.value = data;

  // console.log('Context set, current components:', leftDrawerComponents.value);
}

/**
 * Get the current device type
 */
function getCurrentDeviceType(): DeviceTypeValue {
  return deviceType.value;
}

/**
 * Set the device type
 */
function setDeviceType(type: DeviceTypeValue) {
  deviceType.value = type;
}

// Export context manager
export const contextManager = {
  // State
  currentContext,
  currentSubContext,
  contextData,
  deviceType,

  // Computed
  leftDrawerComponents,
  rightDrawerComponents,
  topBarComponents,
  // bottomBarComponents removed
  expandedComponents,
  leftDrawerTabs,
  rightDrawerTabs,
  deviceConfig,

  // Actions
  setContext,
  getCurrentDeviceType,
  setDeviceType
};
