import { defineStore } from 'pinia';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import type { NavItem } from 'src/components/navigation/TreeNavigation.vue';

export const useNavigationStore = defineStore('navigation', () => {
  const { t } = useI18n();

  // Designer Navigation Tree
  const designerNavigation = computed<NavItem[]>(() => [
    {
      id: 'dashboard',
      label: t('nav.dashboard'),
      icon: 'dashboard',
      route: '/'
    },
    {
      id: 'templates',
      label: t('nav.templates'),
      icon: 'article',
      children: [
        {
          id: 'templates-browse',
          label: t('nav.browse'),
          icon: 'grid_view',
          route: '/designer/templates'
        },
        {
          id: 'templates-builder',
          label: t('nav.builder'),
          icon: 'build',
          route: '/designer/templates/builder/new'
        },

      ]
    },
    {
      id: 'products',
      label: t('nav.products'),
      icon: 'inventory_2',
      route: '/designer/products'
    },
    {
      id: 'images',
      label: t('nav.images'),
      icon: 'image',
      route: '/designer/images'
    }
  ]);

  // Admin Navigation Tree
  const adminNavigation = computed<NavItem[]>(() => [
    {
      id: 'dashboard',
      label: t('nav.dashboard'),
      icon: 'dashboard',
      route: '/'
    },
    {
      id: 'templates',
      label: t('nav.templates'),
      icon: 'article',
      children: [
        {
          id: 'templates-browse',
          label: t('nav.browse'),
          icon: 'grid_view',
          route: '/admin/templates'
        },
        {
          id: 'templates-builder',
          label: t('nav.builder'),
          icon: 'build',
          route: '/admin/templates/builder/new'
        },

      ]
    },
    {
      id: 'products',
      label: t('nav.products'),
      icon: 'inventory_2',
      route: '/admin/products'
    },
    {
      id: 'marketing',
      label: t('nav.marketing'),
      icon: 'campaign',
      route: '/admin/marketing'
    },
    {
      id: 'sales',
      label: t('nav.sales'),
      icon: 'point_of_sale',
      route: '/admin/sales'
    },
    {
      id: 'customers',
      label: t('nav.customers'),
      icon: 'people',
      route: '/admin/customers'
    },
    {
      id: 'statistics',
      label: t('nav.statistics'),
      icon: 'bar_chart',
      route: '/admin/statistics'
    },
    {
      id: 'utilities',
      label: t('nav.utilities'),
      icon: 'build_circle',
      children: [
        {
          id: 'events',
          label: t('nav.events'),
          icon: 'event',
          route: '/admin/events'
        },
        {
          id: 'collections',
          label: t('nav.collections'),
          icon: 'collections_bookmark',
          route: '/admin/collections'
        }
      ]
    },
    {
      id: 'company',
      label: t('nav.company'),
      icon: 'business',
      children: [
        {
          id: 'company-settings',
          label: t('nav.settings'),
          icon: 'settings',
          route: '/admin/company/settings'
        },
        {
          id: 'company-users',
          label: t('nav.users'),
          icon: 'people',
          route: '/admin/company/users'
        },
        {
          id: 'company-branding',
          label: t('nav.branding'),
          icon: 'brush',
          route: '/admin/company/branding'
        }
      ]
    }
  ]);

  // Super Admin Navigation Tree
  const superAdminNavigation = computed<NavItem[]>(() => [
    {
      id: 'dashboard',
      label: t('nav.dashboard'),
      icon: 'dashboard',
      route: '/'
    },
    {
      id: 'app',
      label: t('nav.app'),
      icon: 'apps',
      route: '/super-admin/app'
    },

    {
      id: 'users',
      label: t('nav.users'),
      icon: 'people',
      route: '/super-admin/user-management'
    },
    {
      id: 'companies',
      label: t('nav.companies'),
      icon: 'business',
      route: '/super-admin/companies'
    },
    {
      id: 'servers',
      label: t('nav.servers'),
      icon: 'dns',
      route: '/super-admin/servers'
    },
    {
      id: 'database',
      label: t('nav.database'),
      icon: 'storage',
      route: '/super-admin/database'
    },
    {
      id: 'statistics',
      label: t('nav.statistics'),
      icon: 'bar_chart',
      route: '/super-admin/statistics'
    }
  ]);

  // Get navigation based on role
  const getNavigationForRole = (role: string | null): NavItem[] => {
    console.log('Getting navigation for role:', role);

    // For testing/development, if no role is set, default to designer
    if (!role && process.env.DEV) {
      console.log('DEV mode: Defaulting to designer navigation');
      return designerNavigation.value;
    }

    switch (role) {
      case 'designer':
        return designerNavigation.value;
      case 'admin':
        return adminNavigation.value;
      case 'super-admin':
        return superAdminNavigation.value;
      default:
        console.warn('No navigation found for role:', role);
        return [];
    }
  };

  return {
    designerNavigation,
    adminNavigation,
    superAdminNavigation,
    getNavigationForRole
  };
});
