<template>
  <div class="image-upload-test q-pa-md">
    <h2>Image Upload Test</h2>
    
    <div class="q-mb-md">
      <q-file
        v-model="file"
        label="Select an image"
        accept="image/*"
        outlined
        :loading="uploading"
      >
        <template v-slot:prepend>
          <q-icon name="photo" />
        </template>
      </q-file>
    </div>
    
    <div class="q-mb-md">
      <q-btn
        color="primary"
        label="Upload"
        :loading="uploading"
        :disabled="!file"
        @click="uploadImage"
      />
    </div>
    
    <div v-if="imageUrl" class="q-mb-md">
      <h3>Uploaded Image:</h3>
      <img :src="imageUrl" style="max-width: 100%; max-height: 300px;" />
      <p>URL: {{ imageUrl }}</p>
      
      <q-btn
        color="negative"
        label="Delete"
        :loading="deleting"
        @click="deleteImage"
        class="q-mt-sm"
      />
    </div>
    
    <div v-if="error" class="text-negative q-mt-md">
      <p>Error: {{ error }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { imageStorage } from '../services/image-storage';

const file = ref<File | null>(null);
const imageUrl = ref<string | null>(null);
const uploading = ref(false);
const deleting = ref(false);
const error = ref<string | null>(null);

async function uploadImage() {
  if (!file.value) return;
  
  uploading.value = true;
  error.value = null;
  
  try {
    // Upload the image
    const url = await imageStorage.uploadImage(file.value, 'test');
    imageUrl.value = url;
    
    // Note: We're not saving the image record to the database yet
    // This will be implemented later when the images table is properly defined
    
    console.log('Image uploaded successfully:', url);
  } catch (err) {
    console.error('Error uploading image:', err);
    error.value = err instanceof Error ? err.message : 'An error occurred during upload';
  } finally {
    uploading.value = false;
  }
}

async function deleteImage() {
  if (!imageUrl.value) return;
  
  deleting.value = true;
  error.value = null;
  
  try {
    // Delete the image
    await imageStorage.deleteImage(imageUrl.value);
    
    console.log('Image deleted successfully');
    imageUrl.value = null;
    file.value = null;
  } catch (err) {
    console.error('Error deleting image:', err);
    error.value = err instanceof Error ? err.message : 'An error occurred during deletion';
  } finally {
    deleting.value = false;
  }
}
</script>

<style scoped>
.image-upload-test {
  max-width: 600px;
  margin: 0 auto;
}
</style>
