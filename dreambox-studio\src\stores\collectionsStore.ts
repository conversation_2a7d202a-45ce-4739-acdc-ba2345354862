/**
 * collectionsStore.ts
 *
 * This store manages collections data.
 * It provides methods for loading, filtering, and manipulating collections.
 */

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { supabase } from '../boot/supabase';
import { useAuthStore } from './auth';

/**
 * Collection interface
 */
export interface Collection {
  id: number;
  name: string;
  description: string | null;
  user_id: string;
  created_at: string;
  updated_at: string;
}

/**
 * Collections store definition
 */
export const useCollectionsStore = defineStore('collections', () => {
  // State
  const authStore = useAuthStore();
  const collections = ref<Collection[]>([]);
  const loading = ref(false);

  // Getters
  const userCollections = computed(() => {
    const user = authStore.getUser();
    return collections.value.filter(col => user && col.user_id === user.id);
  });

  // Actions
  /**
   * Load all collections
   */
  async function loadCollections() {
    const user = authStore.getUser();
    if (!user) return;

    loading.value = true;

    try {
      // Use type assertion to tell TypeScript this table exists
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const { data, error } = await supabase.from('collections' as any).select('*').order('name');

      if (error) throw error;

      collections.value = data as unknown as Collection[];
    } catch (err) {
      console.error('Error loading collections:', err);
      collections.value = [];
    } finally {
      loading.value = false;
    }
  }

  /**
   * Get collections by IDs
   */
  function getCollectionsByIds(ids: number[]): Collection[] {
    return collections.value.filter(col => ids.includes(col.id));
  }

  return {
    // State
    collections,
    loading,

    // Getters
    userCollections,

    // Actions
    loadCollections,
    getCollectionsByIds
  };
});
