{"BackendUrl": "http://0.0.0.0:7801", "FrontendUrl": "http://0.0.0.0:7800", "ModelRoot": "/workspace/models", "OutputsPath": "/workspace/outputs", "DisableUpdateCheck": true, "AutoInstall": {"Torch": false, "Comfy": false, "Models": false}, "Auth": {"Enabled": true, "ApiKey": "REPLACE_WITH_GENERATED_API_KEY"}, "ComfyUI": {"Path": "/workspace/ComfyUI", "Url": "http://127.0.0.1:7860", "EnableWorkflows": true, "ModelPaths": {"checkpoints": "/workspace/ComfyUI/models/checkpoints", "clip": "/workspace/models/clip", "vae": "/workspace/models/vae"}}, "Backend": {"Type": "ComfyUI-SelfStarting", "ComfyUIPath": "/workspace/ComfyUI", "ComfyUIPort": 7860, "EnableWorkflows": true}}