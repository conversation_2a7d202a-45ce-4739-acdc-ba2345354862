<template>
  <q-page class="flex flex-center">
    <q-card class="login-card">
      <q-card-section class="bg-primary text-white">
        <div class="text-h6">Login to Dreambox Studio</div>
      </q-card-section>

      <q-card-section>
        <q-form @submit.prevent="handleLogin" class="q-gutter-md">
          <q-input
            v-model="email"
            label="Email"
            type="email"
            outlined
            :rules="[val => !!val || 'Email is required']"
            :disable="authStore.state.loading"
          />

          <q-input
            v-model="password"
            label="Password"
            :type="showPassword ? 'text' : 'password'"
            outlined
            :rules="[val => !!val || 'Password is required']"
            :disable="authStore.state.loading"
          >
            <template v-slot:append>
              <q-icon
                :name="showPassword ? 'visibility_off' : 'visibility'"
                class="cursor-pointer"
                @click="showPassword = !showPassword"
              />
            </template>
          </q-input>

          <div>
            <q-btn
              label="Login"
              type="submit"
              color="primary"
              :loading="authStore.state.loading"
              :disable="!email || !password"
            />
          </div>

          <q-banner v-if="authStore.state.error" class="bg-negative text-white">
            {{ authStore.state.error }}
          </q-banner>
        </q-form>
      </q-card-section>

      <q-card-section class="text-center q-pt-none">
        <p class="text-caption text-grey-8">
          Access is restricted to authorized users only.
        </p>
      </q-card-section>
    </q-card>

    <!-- Company Selection Dialog -->
    <q-dialog v-model="showCompanyDialog" persistent>
      <q-card style="min-width: 350px">
        <q-card-section class="bg-primary text-white">
          <div class="text-h6">Select Company</div>
        </q-card-section>

        <q-card-section>
          <p class="text-body2 q-mb-md">You have access to multiple companies. Please select one:</p>
          <q-select
            v-model="selectedCompany"
            :options="authStore.state.user?.companies || []"
            option-label="name"
            label="Select your company"
            outlined
            map-options
            emit-value
            @update:model-value="handleCompanySelection"
            use-input
            hide-selected
            fill-input
            input-debounce="0"
          >
            <template v-slot:no-option>
              <q-item>
                <q-item-section class="text-grey">
                  No results
                </q-item-section>
              </q-item>
            </template>
          </q-select>
        </q-card-section>
      </q-card>
    </q-dialog>

    <!-- Role Selection Dialog -->
    <q-dialog v-model="showRoleDialog" persistent>
      <q-card style="min-width: 350px">
        <q-card-section class="bg-primary text-white">
          <div class="text-h6">Select Role</div>
        </q-card-section>

        <q-card-section>
          <p class="text-body2 q-mb-md">You have multiple roles in this company. Please select one:</p>
          <q-select
            v-model="selectedRole"
            :options="roleOptions"
            option-label="label"
            option-value="value"
            label="Select your role"
            outlined
            @update:model-value="handleRoleSelection"
            use-input
            hide-selected
            fill-input
            input-debounce="0"
          >
            <template v-slot:no-option>
              <q-item>
                <q-item-section class="text-grey">
                  No results
                </q-item-section>
              </q-item>
            </template>
          </q-select>
        </q-card-section>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
// No need to import useQuasar as we're using notify directly
import { notify } from 'src/boot/notifications'
import { useAuthStore } from 'src/stores/auth'
import type { Company, UserRole } from 'src/types/auth'

const router = useRouter()
const authStore = useAuthStore()

const email = ref('')
const password = ref('')
const showPassword = ref(false)
const showCompanyDialog = ref(false)
const showRoleDialog = ref(false)
const selectedCompany = ref<Company | null>(null)
const selectedRole = ref<UserRole | null>(null)
const isCompanySelecting = ref(false)
const isRoleSelecting = ref(false)

// For testing
onMounted(() => {
  // Uncomment for testing
  // email.value = '<EMAIL>'
  // password.value = 'your-password'
})

const roleOptions = computed(() => {
  const roles = authStore.state.user?.roles || []
  return roles.map(role => ({
    label: formatRoleLabel(role),
    value: role
  }))
})

function formatRoleLabel(role: UserRole): string {
  switch (role) {
    case 'super-admin':
      return 'Super Administrator'
    case 'admin':
      return 'Administrator'
    case 'designer':
      return 'Designer'
    default:
      return role
  }
}

async function handleLogin() {
  if (!email.value || !password.value) return

  // console.log('Logging in with:', email.value)
  await authStore.login(email.value, password.value)

  // console.log('Login result - isAuthenticated:', authStore.isAuthenticated)
  // console.log('User state after login:', authStore.state.user)
  // console.log('Has multiple companies:', authStore.hasMultipleCompanies)
  // console.log('Has multiple roles:', authStore.hasMultipleRoles)

  if (authStore.isAuthenticated) {
    if (authStore.hasMultipleCompanies) {
      // console.log('Showing company dialog, companies:', authStore.state.user?.companies)
      showCompanyDialog.value = true
    } else if (authStore.hasMultipleRoles) {
      // console.log('Showing role dialog, roles:', authStore.state.user?.roles)
      showRoleDialog.value = true
    } else {
      // console.log('Navigating directly to app')
      await navigateToApp()
    }
  }
}

async function handleCompanySelection() {
  // console.log('Selected company:', selectedCompany.value)
  if (selectedCompany.value && !isCompanySelecting.value) {
    try {
      isCompanySelecting.value = true

      // Show a notification
      void notify({
        message: `Selected company: ${selectedCompany.value.name}`,
        color: 'positive',
        position: 'top',
        timeout: 1000
      })

      authStore.setCurrentCompany(selectedCompany.value)
      showCompanyDialog.value = false

      // Add a small delay to allow the UI to update
      await new Promise(resolve => setTimeout(resolve, 300))

      // console.log('After setting company, user state:', authStore.state.user)
      // console.log('Has multiple roles:', authStore.hasMultipleRoles)

      if (authStore.hasMultipleRoles) {
        showRoleDialog.value = true
      } else {
        // console.log('Navigating to app with role:', authStore.state.user?.currentRole)
        await navigateToApp()
      }
    } finally {
      isCompanySelecting.value = false
    }
  }
}

async function handleRoleSelection() {
  // console.log('Selected role:', selectedRole.value)
  if (selectedRole.value && !isRoleSelecting.value) {
    try {
      isRoleSelecting.value = true

      // Extract the role value if it's an object
      let roleValue: string;
      let roleLabel: string;

      if (typeof selectedRole.value === 'object' && selectedRole.value !== null) {
        const roleObj = selectedRole.value as { value?: string, label?: string };
        roleValue = roleObj.value || '';
        roleLabel = roleObj.label || roleValue;
      } else {
        roleValue = selectedRole.value as string;
        roleLabel = roleValue;
      }

      // Show a notification
      void notify({
        message: `Selected role: ${roleLabel}`,
        color: 'positive',
        position: 'top',
        timeout: 1000
      })

      // console.log('Extracted role value:', roleValue);
      authStore.setCurrentRole(roleValue as UserRole)
      // console.log('After setting role, user state:', authStore.state.user)
      showRoleDialog.value = false

      // Add a small delay to allow the UI to update
      await new Promise(resolve => setTimeout(resolve, 300))

      await navigateToApp()
    } finally {
      isRoleSelecting.value = false
    }
  }
}

async function navigateToApp() {
  const role = authStore.state.user?.currentRole
  // console.log('Navigating to app with role:', role)
  // console.log('Current user state:', authStore.state.user)

  // Extract the role value if it's an object
  let roleValue: string | undefined;
  if (typeof role === 'object' && role !== null) {
    const roleObj = role as { value?: string };
    roleValue = roleObj.value;
  } else {
    roleValue = role as string | undefined;
  }

  // console.log('Extracted role value for navigation:', roleValue);

  try {
    if (roleValue === 'super-admin') {
      // console.log('Navigating to super-admin dashboard')
      await router.push('/super-admin')
    } else if (roleValue === 'admin') {
      // console.log('Navigating to admin dashboard')
      await router.push('/admin')
    } else {
      // console.log('Navigating to designer dashboard')
      await router.push('/designer')
    }
  } catch (error) {
    console.error('Navigation error:', error)
  }
}
</script>

<style lang="scss" scoped>
.login-card {
  width: 100%;
  max-width: 400px;
  padding: 0;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
</style>
