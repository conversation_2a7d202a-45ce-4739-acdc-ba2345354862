import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import chalk from 'chalk';
import { Command } from 'commander';
import { Parser } from 'json2csv';

// Get the directory name of the current module
const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Load environment variables from the parent directory's .env file
dotenv.config({ path: path.join(__dirname, '..', '.env') });

// Directory to save exports
const exportsDir = path.join(__dirname, 'exports');

// Create Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error(chalk.red('Supabase credentials not found in environment variables'));
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Parse command line arguments
const program = new Command();
program
  .option('-t, --table <name>', 'Table to export')
  .option('-q, --query <sql>', 'SQL query to execute')
  .option('-f, --format <format>', 'Output format (json or csv)', 'json')
  .option('-o, --output <file>', 'Output file name')
  .option('-l, --limit <number>', 'Limit number of rows', '1000')
  .parse(process.argv);

const options = program.opts();

/**
 * Exports data to a file
 * @param {Array} data - The data to export
 * @param {string} format - The output format (json or csv)
 * @param {string} filename - The output filename
 */
function exportData(data, format, filename) {
  if (!data || data.length === 0) {
    console.log(chalk.yellow('No data to export'));
    return;
  }
  
  // Create the exports directory if it doesn't exist
  if (!fs.existsSync(exportsDir)) {
    fs.mkdirSync(exportsDir, { recursive: true });
  }
  
  // Generate filename if not provided
  if (!filename) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    filename = `export_${timestamp}`;
  }
  
  // Add extension if not present
  if (!filename.endsWith(`.${format}`)) {
    filename = `${filename}.${format}`;
  }
  
  const filePath = path.join(exportsDir, filename);
  
  if (format === 'csv') {
    // Convert to CSV
    const parser = new Parser();
    const csv = parser.parse(data);
    
    fs.writeFileSync(filePath, csv);
  } else {
    // Save as JSON
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
  }
  
  console.log(chalk.green(`Data exported to ${filePath}`));
  console.log(chalk.gray(`${data.length} rows exported`));
}

/**
 * Executes a SQL query
 * @param {string} query - The SQL query to execute
 * @returns {Promise<Array>} The query results
 */
async function executeQuery(query) {
  try {
    console.log(chalk.blue('Executing query...'));
    
    const { data, error } = await supabase.rpc('execute_sql', { sql_query: query });
    
    if (error) {
      throw new Error(`Query execution failed: ${error.message}`);
    }
    
    return data;
  } catch (error) {
    console.error(chalk.red(`Error executing query: ${error.message}`));
    console.log(chalk.yellow('To enable arbitrary SQL queries, create the following function in your Supabase project:'));
    console.log(`
CREATE OR REPLACE FUNCTION execute_sql(sql_query text)
RETURNS JSONB LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  result JSONB;
BEGIN
  EXECUTE 'WITH query_result AS (' || sql_query || ') SELECT jsonb_agg(row_to_json(query_result)) FROM query_result' INTO result;
  RETURN COALESCE(result, '[]'::jsonb);
END;
$$;
    `);
    return [];
  }
}

/**
 * Fetches data from a table
 * @param {string} tableName - The name of the table
 * @param {number} limit - The maximum number of rows to fetch
 * @returns {Promise<Array>} The table data
 */
async function fetchTableData(tableName, limit) {
  try {
    console.log(chalk.blue(`Fetching data from table ${tableName}...`));
    
    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .limit(limit);
    
    if (error) {
      throw new Error(`Error fetching data: ${error.message}`);
    }
    
    return data;
  } catch (error) {
    console.error(chalk.red(`Error: ${error.message}`));
    return [];
  }
}

/**
 * Main function
 */
async function main() {
  try {
    // Check if table or query is provided
    if (!options.table && !options.query) {
      console.error(chalk.red('Error: Either --table or --query must be provided'));
      program.help();
      return;
    }
    
    let data;
    
    if (options.query) {
      // Execute SQL query
      data = await executeQuery(options.query);
    } else {
      // Fetch table data
      data = await fetchTableData(options.table, parseInt(options.limit));
    }
    
    // Export the data
    exportData(data, options.format, options.output);
  } catch (error) {
    console.error(chalk.red(`Error: ${error.message}`));
    process.exit(1);
  }
}

// Run the main function
main();
