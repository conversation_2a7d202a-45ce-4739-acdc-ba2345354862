import { supabase } from 'src/boot/supabase';
import type { Json } from 'src/types/supabase';
import type { TemplateStatusHistory } from 'src/types/Template';
import type { ProductTypeBasic } from 'src/types/ProductType';

// Create a custom type for Supabase RPC functions to avoid ESLint errors
// This is necessary because the Supabase TypeScript definitions are very strict
// eslint-disable-next-line @typescript-eslint/no-explicit-any
type CustomRpcFunction = any;

// We're using 'any' for RPC parameters due to TypeScript's strict type checking
// This would be the ideal type definition, but it causes compatibility issues:
// type GetTemplatesParams = {
//   p_company_id?: number | undefined;
//   p_designer_id?: number | undefined;
//   p_status?: string | undefined;
//   p_published?: boolean | undefined;
//   p_search?: string | undefined;
//   p_limit?: number | undefined;
//   p_offset?: number | undefined;
// };

// Define type for template data from RPC function
type TemplateRpcResult = {
  id: number;
  name: string;
  description: string | null;
  version: number;
  created_at: string;
  updated_at: string;
  tmpl_company_id: number;
  company_name: string;
  initiated_by: number;
  initiated_by_name: string;
  approved_by: number | null;
  approved_by_name: string | null;
  approval_date: string | null;
  agent_id: number | null;
  agent_name: string | null;
  status: 'draft' | 'pending' | 'active' | 'archived';
  published: boolean;
  image_server_data: Json | null;
  elements: Json | null;
  images: Json | null;
  events: Json | null;
  products: Json | null;
  collections: Json | null;
  element_types?: Json | null;
  element_values?: Json | null;
  total_count: number;
};
import { ref, computed } from 'vue';
import { useAuthStore } from 'src/stores/auth';

// Template types
export interface TemplateElement {
  id: number;
  value: string;
  description: string | null;
}

export interface TemplateElementsMap {
  [key: string]: TemplateElement[];
}

export interface TemplateImage {
  id: number;
  title: string;
  image_url: string;
  status: string;
  metadata: Record<string, unknown>;
}

export interface TemplateEvent {
  id: number;
  title: string;
  start_date: string;
  end_date: string | null;
  status: string;
}

export interface TemplateProduct {
  id: number;
  name: string;
  sku: string;
  base_price: number;
  active: boolean;
}

export interface TemplateCollection {
  id: number;
  name: string;
  description: string | null;
}

export interface Template {
  id: number;
  name: string;
  description: string | null;
  version: number;
  created_at: string;
  updated_at: string;
  tmpl_company_id: number;
  company_name: string;
  initiated_by: number;
  initiated_by_name: string;
  approved_by: number | null;
  approved_by_name: string | null;
  approval_date: string | null;
  agent_id: number | null;
  agent_name: string | null;
  status: 'draft' | 'pending' | 'active' | 'archived';
  published: boolean;
  image_server_data: Record<string, unknown> | null;
  elements: TemplateElementsMap;
  element_types?: number[];
  element_values?: number[];
  images: TemplateImage[];
  events: TemplateEvent[];
  products: TemplateProduct[];
  collections: TemplateCollection[];
  product_types?: ProductTypeBasic[]; // New field for product types
}

export interface TemplateFilter {
  tmpl_company_id?: number | undefined;
  designer_id?: number | undefined;
  status?: string | string[] | undefined;

  // Basic search and pagination
  search?: string | undefined;
  limit?: number | undefined;
  offset?: number | undefined;

  // Sorting parameters
  sort_by?: string | undefined;
  sort_desc?: boolean | undefined;

  // Prompt Elements filters
  element_angles?: number[] | undefined;
  element_artistic_reference?: number[] | undefined;
  element_blend_concepts?: number[] | undefined;
  element_camera_settings?: number[] | undefined;
  element_color_scheme?: number[] | undefined;
  element_composition?: number[] | undefined;
  element_lighting?: number[] | undefined;
  element_mood?: number[] | undefined;
  element_style?: number[] | undefined;
  element_subject?: number[] | undefined;

  // Related Items filters
  image_count?: string | undefined;
  has_images?: boolean | undefined;
  product_count?: string | undefined;
  has_products?: boolean | undefined;
  product_ids?: number[] | undefined;
  event_count?: string | undefined;
  has_events?: boolean | undefined;
  event_ids?: number[] | undefined;
}

export interface TemplatePagination {
  page: number;
  rowsPerPage: number;
  rowsNumber: number;
  sortBy?: string;
  descending?: boolean;
}

/**
 * Template service for managing templates
 *
 * Access control:
 * - Designers can only see, edit, and delete their own templates
 * - Admins and super-admins can see, edit, and delete all templates
 */
export const useTemplateService = () => {
  const authStore = useAuthStore();
  const loading = ref(false);
  const error = ref<string | null>(null);
  const templates = ref<Template[]>([]);
  const totalCount = ref(0);

  // Get current user's company ID
  const currentCompanyId = computed(() =>
    authStore.state.user?.currentCompany?.id
  );

  /**
   * Fetch templates with filtering and pagination
   */
  const getTemplates = async (filter: TemplateFilter = {}, pagination: TemplatePagination = { page: 1, rowsPerPage: 10, rowsNumber: 0 }) => {
    try {
      loading.value = true;
      error.value = null;

      // Calculate offset and ensure pagination parameters are numbers
      const page = Number(pagination.page);
      const rowsPerPage = Number(pagination.rowsPerPage);
      const offset = (page - 1) * rowsPerPage;

      console.log('Pagination parameters:', { page, rowsPerPage, offset });

      // Get the auth store
      const authStore = useAuthStore();

      // Set company ID from current user if not specified
      if (!filter.tmpl_company_id && currentCompanyId.value) {
        filter.tmpl_company_id = Number(currentCompanyId.value);
      }

      // If user is a designer, restrict to only their templates
      // Admin and super-admin can see all templates
      if (authStore.state.user?.currentRole === 'designer' && !filter.designer_id && authStore.state.user?.id) {
        filter.designer_id = Number(authStore.state.user.id);
      }

      // Define element filter keys for reuse
      const elementFilterKeys = [
        'element_angles', 'element_artistic_reference', 'element_blend_concepts',
        'element_camera_settings', 'element_color_scheme', 'element_composition',
        'element_lighting', 'element_mood', 'element_style', 'element_subject'
      ];

      // Check if any prompt element filters are present
      const hasElementFilters = elementFilterKeys.some(key =>
        filter[key as keyof typeof filter] !== undefined &&
        Array.isArray(filter[key as keyof typeof filter]) &&
        (filter[key as keyof typeof filter] as number[]).length > 0
      );

      // Prepare parameters for the filter_templates function
      // Only include parameters that the existing function accepts
      const params: Record<string, unknown> = {
        p_company_id: filter.tmpl_company_id,
        p_designer_id: filter.designer_id,
        // Handle multiple status values by joining them with commas
        // The updated SQL function will handle this format
        p_status: Array.isArray(filter.status) ? (
          // Log the array values for debugging
          console.log('Status filter array:', filter.status),
          filter.status.join(',')
        ) : filter.status,
        p_published: null,
        p_search: filter.search,
        p_limit: rowsPerPage,
        p_offset: offset,
        // Add sorting parameters if provided
        p_sort_by: filter.sort_by || 'updated_at',
        p_sort_desc: filter.sort_desc !== undefined ? filter.sort_desc : true
      };

      // Add prompt element filters to the params if they exist
      if (hasElementFilters) {
        console.log('Prompt element filters detected:', filter);

        // Add each element filter to the params
        elementFilterKeys.forEach(key => {
          const filterValues = filter[key as keyof typeof filter];
          if (filterValues && Array.isArray(filterValues) && filterValues.length > 0) {
            // Extract the IDs from the objects in the array
            const elementIds = filterValues.map(item => {
              // Check if the item is an object with an id property
              if (typeof item === 'object' && item !== null && 'id' in item) {
                return (item as { id: number }).id;
              }
              // If it's already a number, use it directly
              if (typeof item === 'number') {
                return item;
              }
              // Otherwise, try to convert it to a number
              return Number(item);
            }).filter(id => !isNaN(id)); // Filter out any NaN values

            // Only add the filter if we have valid IDs
            if (elementIds.length > 0) {
              // Convert the key to the parameter name format (p_element_angles, etc.)
              const paramName = `p_${key}`;
              console.log(`Adding element filter: ${paramName} = ${JSON.stringify(elementIds)}`);
              params[paramName] = elementIds;
            }
          }
        });
      }

      // Log any additional filter parameters that we're not sending to the SQL function
      if (Object.keys(filter).some(key => [
        'image_count', 'has_images', 'product_count', 'has_products', 'product_ids',
        'event_count', 'has_events', 'event_ids'
      ].includes(key))) {
        console.log('Note: Some filter parameters are not being sent to the SQL function:', filter);
      }

      // Log the parameters being sent to the filter_templates function
      console.log('Calling filter_templates with params:', params);
      console.log('Current filter:', JSON.stringify(filter));
      console.log('Current pagination:', JSON.stringify(pagination));

      // Special handling for status filter
      if ('status' in filter) {
        console.log('STATUS FILTER in getTemplates:', filter.status);

        // If status is empty string, null, undefined, or empty array, remove it
        if (filter.status === '' ||
            filter.status === null ||
            filter.status === undefined ||
            (Array.isArray(filter.status) && filter.status.length === 0)) {
          console.log('STATUS FILTER is empty, removing from params');
          delete params.p_status;
          delete filter.status;
        }
      } else {
        console.log('STATUS FILTER NOT PRESENT in getTemplates');
      }

      // IMPORTANT: Store the current filter in localStorage to ensure it's preserved across page changes
      // This is a backup mechanism in case the filter is lost in the UI state
      localStorage.setItem('lastTemplateFilter', JSON.stringify(filter));

      // Call the filter_templates function (which supports multiple status values and element filters)
      const { data, error: err } = await supabase.rpc(
        'filter_templates' as CustomRpcFunction,
        params
      );

      // Log the response
      console.log('filter_templates response:', data, 'error:', err);
      console.log('Response data length:', data ? data.length : 0);

      // Log the total count from the first item
      if (data && Array.isArray(data) && data.length > 0 && 'total_count' in data[0]) {
        console.log('Total count from filter_templates:', data[0].total_count);

        // Check if all items have the same total_count
        const allSameCount = data.every(item => item.total_count === data[0].total_count);
        console.log('All items have same total_count:', allSameCount);

        if (!allSameCount) {
          console.warn('Different total_count values found in response:');
          data.forEach((item, index) => {
            console.log(`Item ${index}: total_count = ${item.total_count}`);
          });
        }
      }

      if (err) {
        throw err;
      }

      if (data && Array.isArray(data) && data.length > 0) {
        // Process the templates
        const processedTemplates = (data as TemplateRpcResult[]).map((template: TemplateRpcResult) => {
          // Process JSON fields
          let elements = {} as Record<string, unknown>;
          let images: TemplateImage[] = [];
          let events: TemplateEvent[] = [];
          let products: TemplateProduct[] = [];
          let collections: TemplateCollection[] = [];
          let imageServerData: Record<string, unknown> | null = null;
          let elementTypes: number[] = [];
          let elementValues: number[] = [];

          try {
            // Parse elements
            if (template.elements) {
              elements = typeof template.elements === 'string'
                ? JSON.parse(template.elements)
                : template.elements as Record<string, unknown>;
            }

            // Parse images
            if (template.images) {
              const parsedImages = typeof template.images === 'string'
                ? JSON.parse(template.images)
                : template.images;

              if (Array.isArray(parsedImages)) {
                images = parsedImages.map(img => ({
                  id: img.id || 0,
                  title: img.title || '',
                  image_url: img.image_url || '',
                  status: img.status || '',
                  metadata: img.metadata || {}
                }));
              }
            }

            // Parse events
            if (template.events) {
              const parsedEvents = typeof template.events === 'string'
                ? JSON.parse(template.events)
                : template.events;

              if (Array.isArray(parsedEvents)) {
                events = parsedEvents.map(evt => ({
                  id: evt.id || 0,
                  title: evt.title || '',
                  start_date: evt.start_date || '',
                  end_date: evt.end_date || null,
                  status: evt.status || ''
                }));
              }
            }

            // Parse products
            if (template.products) {
              const parsedProducts = typeof template.products === 'string'
                ? JSON.parse(template.products)
                : template.products;

              if (Array.isArray(parsedProducts)) {
                products = parsedProducts.map(prod => ({
                  id: prod.id || 0,
                  name: prod.name || '',
                  sku: prod.sku || '',
                  base_price: prod.base_price || 0,
                  active: !!prod.active
                }));
              }
            }

            // Parse collections
            if (template.collections) {
              const parsedCollections = typeof template.collections === 'string'
                ? JSON.parse(template.collections)
                : template.collections;

              if (Array.isArray(parsedCollections)) {
                collections = parsedCollections.map(coll => ({
                  id: coll.id || 0,
                  name: coll.name || '',
                  description: coll.description || null
                }));
              }
            }

            // Parse image_server_data
            if (template.image_server_data) {
              imageServerData = typeof template.image_server_data === 'string'
                ? JSON.parse(template.image_server_data)
                : template.image_server_data as Record<string, unknown>;
            }

            // Parse element_types
            if (template.element_types) {
              const parsedTypes = typeof template.element_types === 'string'
                ? JSON.parse(template.element_types)
                : template.element_types;

              if (Array.isArray(parsedTypes)) {
                elementTypes = parsedTypes.map(t => Number(t.id || t)).filter(id => !isNaN(id));
              }
            }

            // Parse element_values
            if (template.element_values) {
              const parsedValues = typeof template.element_values === 'string'
                ? JSON.parse(template.element_values)
                : template.element_values;

              if (Array.isArray(parsedValues)) {
                elementValues = parsedValues.map(v => Number(v.id || v)).filter(id => !isNaN(id));
              }
            }
          } catch (parseErr) {
            console.error('Error parsing template JSON fields:', parseErr);
          }

          // Return properly typed template object
          return {
            id: template.id,
            name: template.name,
            description: template.description,
            version: template.version,
            created_at: template.created_at,
            updated_at: template.updated_at,
            tmpl_company_id: template.tmpl_company_id,
            company_name: template.company_name,
            initiated_by: template.initiated_by,
            initiated_by_name: template.initiated_by_name,
            approved_by: template.approved_by,
            approved_by_name: template.approved_by_name,
            approval_date: template.approval_date,
            agent_id: template.agent_id,
            agent_name: template.agent_name,
            status: template.status,
            published: template.published,
            image_server_data: imageServerData,
            elements: elements as TemplateElementsMap,
            images,
            events,
            products,
            collections,
            element_types: elementTypes,
            element_values: elementValues
          } as Template;
        });

        // No need for client-side filtering anymore as we're using server-side filtering
        // The SQL function now handles multiple status values and prompt element filters

        templates.value = processedTemplates;

        // Set total count for pagination
        if (Array.isArray(data) && data.length > 0) {
          const firstItem = data[0];
          console.log('First item from API:', firstItem);
          if (firstItem && 'total_count' in firstItem && firstItem.total_count) {
            // IMPORTANT: The total_count should reflect the filtered results, not all records
            // This is set by the SQL function based on the applied filters
            totalCount.value = (firstItem as { total_count: number }).total_count;
            console.log('Total count set to:', totalCount.value);

            // Debug logging for pagination
            console.log('Current pagination:', {
              page,
              rowsPerPage,
              totalItems: totalCount.value
            });

            // Calculate expected page range
            const startItem = (page - 1) * rowsPerPage + 1;
            const endItem = Math.min(startItem + rowsPerPage - 1, totalCount.value);
            console.log(`Expected pagination display: ${startItem}-${endItem} of ${totalCount.value}`);

            // Update pagination.rowsNumber with the total count
            pagination.rowsNumber = totalCount.value;

            // Verify that we got the expected number of records
            const expectedRecords = Math.min(rowsPerPage, totalCount.value - (page - 1) * rowsPerPage);
            console.log(`Expected ${expectedRecords} records, got ${data.length} records`);

            if (data.length < expectedRecords && data.length > 0) {
              console.warn('Warning: Received fewer records than expected. This may indicate a pagination issue.');
            }
          } else {
            console.warn('Total count not found in API response');
          }
        } else {
          console.warn('No data returned from API');
          templates.value = [];
          totalCount.value = 0;
        }
      } else {
        templates.value = [];
        totalCount.value = 0;
      }

      return templates.value;
    } catch (err) {
      console.error('Error fetching templates:', err);
      error.value = err instanceof Error ? err.message : 'Failed to fetch templates';
      return [];
    } finally {
      loading.value = false;
    }
  };

  /**
   * Get a single template by ID
   */
  const getTemplate = async (id: number): Promise<Template | null> => {
    try {
      loading.value = true;
      error.value = null;

      // Get the auth store
      const authStore = useAuthStore();

      // Define a base template structure with default values
      const templateBase = {
        id: 0,
        name: '',
        description: null as string | null,
        version: 1,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        tmpl_company_id: 0,
        company_name: '',
        initiated_by: 0,
        initiated_by_name: '',
        approved_by: null as number | null,
        approved_by_name: null as string | null,
        approval_date: null as string | null,
        agent_id: null as number | null,
        agent_name: null as string | null,
        status: 'draft' as 'draft' | 'pending' | 'active' | 'archived',
        published: false,
        image_server_data: null as Record<string, unknown> | null,
        elements: {} as Record<string, unknown>,
        images: [] as Record<string, unknown>[],
        events: [] as Record<string, unknown>[],
        products: [] as Record<string, unknown>[],
        collections: [] as Record<string, unknown>[],
        element_values: [] as number[],
        element_types: [] as number[]
      };

      // First, try to get the template directly from the templates table
      // This is more reliable than using the RPC function with a template_id parameter
      const { data: directTemplateData, error: templateErr } = await supabase
        .from('templates')
        .select(`
          id, name, description, version, created_at, updated_at,
          tmpl_company_id, status, published, image_server_data,
          designer_id, agent_id
        `)
        .eq('id', id)
        .single();

      let templateData = { ...templateBase };

      if (templateErr) {
        console.error(`Error fetching template with ID ${id} from templates table:`, templateErr);

        // Fall back to the RPC function if direct query fails
        const params: Record<string, unknown> = {
          p_template_id: id,
          p_limit: 1,
          p_offset: 0,
          p_published: null
        };

        // If user is a designer, restrict to only their templates
        if (authStore.state.user?.currentRole === 'designer' && authStore.state.user?.id) {
          params.p_designer_id = Number(authStore.state.user.id);
        }

        // Log the parameters being sent to the filter_templates function
        console.log('Calling filter_templates for single template with params:', params);

        // Use filter_templates instead of get_templates
        const { data, error: err } = await supabase.rpc('filter_templates' as CustomRpcFunction, params);

        // Log the response
        console.log('filter_templates response for single template:', data, 'error:', err);

        if (err) {
          throw err;
        }

        // Get the first template from the results
        if (data && Array.isArray(data) && data.length > 0) {
          const rpcResult = data[0] as TemplateRpcResult;

          // Process JSON fields
          let elements = {} as Record<string, unknown>;
          let images: Record<string, unknown>[] = [];
          let events: Record<string, unknown>[] = [];
          let products: Record<string, unknown>[] = [];
          let collections: Record<string, unknown>[] = [];
          let imageServerData: Record<string, unknown> | null = null;

          try {
            if (rpcResult.elements) {
              elements = typeof rpcResult.elements === 'string'
                ? JSON.parse(rpcResult.elements)
                : rpcResult.elements as Record<string, unknown>;
            }

            if (rpcResult.images) {
              images = typeof rpcResult.images === 'string'
                ? JSON.parse(rpcResult.images)
                : rpcResult.images as Record<string, unknown>[];
            }

            if (rpcResult.events) {
              events = typeof rpcResult.events === 'string'
                ? JSON.parse(rpcResult.events)
                : rpcResult.events as Record<string, unknown>[];
            }

            if (rpcResult.products) {
              products = typeof rpcResult.products === 'string'
                ? JSON.parse(rpcResult.products)
                : rpcResult.products as Record<string, unknown>[];
            }

            if (rpcResult.collections) {
              collections = typeof rpcResult.collections === 'string'
                ? JSON.parse(rpcResult.collections)
                : rpcResult.collections as Record<string, unknown>[];
            }

            if (rpcResult.image_server_data) {
              imageServerData = typeof rpcResult.image_server_data === 'string'
                ? JSON.parse(rpcResult.image_server_data)
                : rpcResult.image_server_data as Record<string, unknown>;
            }
          } catch (parseErr) {
            console.error('Error parsing JSON fields:', parseErr);
          }

          // Determine status
          let status: 'draft' | 'pending' | 'active' | 'archived' = 'draft';
          if (typeof rpcResult.status === 'string') {
            if (rpcResult.status === 'draft' ||
                rpcResult.status === 'pending' ||
                rpcResult.status === 'active' ||
                rpcResult.status === 'archived') {
              status = rpcResult.status;
            }
          }

          // Update template data with proper type handling
          templateData = {
            ...templateBase,
            id: rpcResult.id,
            name: rpcResult.name || '',
            description: rpcResult.description,
            version: rpcResult.version || 1,
            created_at: rpcResult.created_at || new Date().toISOString(),
            updated_at: rpcResult.updated_at || new Date().toISOString(),
            tmpl_company_id: rpcResult.tmpl_company_id || 0,
            company_name: rpcResult.company_name || '',
            initiated_by: rpcResult.initiated_by || 0,
            initiated_by_name: rpcResult.initiated_by_name || '',
            approved_by: rpcResult.approved_by,
            approved_by_name: rpcResult.approved_by_name,
            approval_date: rpcResult.approval_date,
            agent_id: rpcResult.agent_id,
            agent_name: rpcResult.agent_name,
            status,
            published: !!rpcResult.published,
            image_server_data: imageServerData,
            elements,
            images,
            events,
            products,
            collections
          };
        }
      } else if (directTemplateData) {
        // We got data from the direct query
        // Process the image_server_data field
        let imageServerData: Record<string, unknown> | null = null;
        try {
          if (directTemplateData.image_server_data) {
            imageServerData = typeof directTemplateData.image_server_data === 'string'
              ? JSON.parse(directTemplateData.image_server_data)
              : directTemplateData.image_server_data as Record<string, unknown>;
          }
        } catch (parseErr) {
          console.error('Error parsing image_server_data:', parseErr);
        }

        // Determine status
        let status: 'draft' | 'pending' | 'active' | 'archived' = 'draft';
        if (directTemplateData.status === 'draft' ||
            directTemplateData.status === 'pending' ||
            directTemplateData.status === 'active' ||
            directTemplateData.status === 'archived') {
          status = directTemplateData.status;
        }

        templateData = {
          ...templateBase,
          id: directTemplateData.id,
          name: directTemplateData.name,
          description: directTemplateData.description,
          version: directTemplateData.version,
          created_at: directTemplateData.created_at,
          updated_at: directTemplateData.updated_at,
          tmpl_company_id: directTemplateData.tmpl_company_id,
          initiated_by: directTemplateData.designer_id || 0,
          status,
          published: directTemplateData.published,
          image_server_data: imageServerData,
          agent_id: directTemplateData.agent_id
        };

        // Get company name
        try {
          const { data: companyData } = await supabase
            .from('companies')
            .select('name')
            .eq('id', directTemplateData.tmpl_company_id.toString())
            .single();

          if (companyData && typeof companyData === 'object' && 'name' in companyData) {
            templateData.company_name = String(companyData.name);
          }
        } catch (companyErr) {
          console.error('Error fetching company data:', companyErr);
        }

        // Get user names - use a single query for both users
        try {
          // Create an array of user IDs to fetch
          const userIds: number[] = [];

          if (directTemplateData.designer_id) {
            userIds.push(directTemplateData.designer_id);
          }

          if (directTemplateData.agent_id) {
            userIds.push(directTemplateData.agent_id);
          }

          // Only make the query if we have user IDs to fetch
          if (userIds.length > 0) {
            // Use in() filter instead of eq() to avoid 400 errors
            // Select first_name and last_name instead of full_name
            const { data: userData, error: userErr } = await supabase
              .from('app_users')
              .select('id, first_name, last_name')
              .in('id', userIds);

            if (userErr) {
              console.error('Error fetching user data:', userErr);
            } else if (userData && Array.isArray(userData)) {
              // Process the user data
              userData.forEach(user => {
                // Combine first_name and last_name to create a full name
                const fullName = [user.first_name, user.last_name]
                  .filter(Boolean)  // Remove null/undefined values
                  .join(' ')        // Join with space
                  .trim();          // Trim any extra spaces

                if (user.id === directTemplateData.designer_id) {
                  templateData.initiated_by_name = fullName || '';
                }

                if (user.id === directTemplateData.agent_id) {
                  templateData.agent_name = fullName || '';
                }
              });
            }
          }
        } catch (userErr) {
          console.error('Error fetching user data:', userErr);
        }
      }

      // Get element values for this template
      try {
        const { data: elementValues, error: elementValuesError } = await supabase
          .from('prompt_elements_usage')
          .select('element_id, id')
          .eq('template_id', id);

        if (elementValuesError) {
          console.error('Error fetching template element values:', elementValuesError);
        } else if (elementValues) {
          templateData.element_values = elementValues.map((item: { element_id: number }) => item.element_id);
        }
      } catch (elementErr) {
        console.error('Error fetching element values:', elementErr);
      }

      // Get element types for this template
      try {
        // Check if the table exists by querying it
        const { data: tableCheck } = await supabase
          .from('prompt_element_types')
          .select('id')
          .limit(1);

        if (tableCheck) {
          // If we can query prompt_element_types, we can try to query the usage table
          // Just query the element types directly
          try {
            const { data: elementTypesData } = await supabase
              .from('prompt_element_types')
              .select('id')
              .limit(100);

            if (elementTypesData && Array.isArray(elementTypesData)) {
              templateData.element_types = elementTypesData.map(item => item.id);
            }
          } catch (directErr) {
            console.error('Error with direct query for element types:', directErr);
            // Set default element types if query fails
            templateData.element_types = [1, 2, 3];
          }
        }
      } catch (tableErr) {
        console.error('Error checking for prompt_element_types table:', tableErr);
      }

      // Convert the templateData to a proper Template object
      const result: Template = {
        id: templateData.id,
        name: templateData.name,
        description: templateData.description,
        version: templateData.version,
        created_at: templateData.created_at,
        updated_at: templateData.updated_at,
        tmpl_company_id: templateData.tmpl_company_id,
        company_name: templateData.company_name,
        initiated_by: templateData.initiated_by,
        initiated_by_name: templateData.initiated_by_name,
        approved_by: templateData.approved_by,
        approved_by_name: templateData.approved_by_name,
        approval_date: templateData.approval_date,
        agent_id: templateData.agent_id,
        agent_name: templateData.agent_name,
        status: templateData.status,
        published: templateData.published,
        image_server_data: templateData.image_server_data,
        elements: templateData.elements as TemplateElementsMap,
        images: [] as TemplateImage[],
        events: [] as TemplateEvent[],
        products: [] as TemplateProduct[],
        collections: [] as TemplateCollection[],
        element_types: templateData.element_types,
        element_values: templateData.element_values
      };

      // Return the properly typed template
      return result;
    } catch (err) {
      console.error(`Error fetching template with ID ${id}:`, err);
      error.value = err instanceof Error ? err.message : `Failed to fetch template with ID ${id}`;
      return null;
    } finally {
      loading.value = false;
    }
  };

  /**
   * Create a new template
   */
  const createTemplate = async (template: Partial<Template>): Promise<Template | null> => {
    try {
      loading.value = true;
      error.value = null;

      // Set company ID from current user if not specified
      if (!template.tmpl_company_id && currentCompanyId.value) {
        template.tmpl_company_id = Number(currentCompanyId.value);
      }

      // Set initiated_by (designer_id) from current user
      if (!template.initiated_by && authStore.state.user?.id) {
        template.initiated_by = Number(authStore.state.user.id);
      }

      // Ensure the template is created by the current user if they are a designer
      if (authStore.state.user?.currentRole === 'designer' && authStore.state.user?.id) {
        template.initiated_by = Number(authStore.state.user.id);
      }

      // Insert the template
      // Use a more specific query to avoid column ambiguity
      const { data, error: err } = await supabase
        .from('templates')
        .insert({
          name: template.name,
          description: template.description,
          tmpl_company_id: template.tmpl_company_id,
          designer_id: template.initiated_by, // designer_id is the same as initiated_by
          agent_id: template.agent_id,
          status: template.status || 'draft',
          published: template.published || false,
          image_server_data: template.image_server_data as unknown as Json
        // Using 'any' to bypass TypeScript's strict type checking for Supabase
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } as any)
        .select('id, name, description, tmpl_company_id, designer_id, agent_id, status, published, image_server_data, created_at, updated_at')
        .single();

      // If template was created successfully and has element values, save them
      if (data && !err) {
        if (template.element_values && template.element_values.length > 0) {
          await saveTemplateElementValues(data.id, template.element_values);
        }
      }

      if (err) {
        throw err;
      }

      if (data) {
        // Fetch the complete template with all related data
        return await getTemplate(data.id);
      }

      return null;
    } catch (err) {
      console.error('Error creating template:', err);
      error.value = err instanceof Error ? err.message : 'Failed to create template';
      return null;
    } finally {
      loading.value = false;
    }
  };

  /**
   * Update an existing template
   */
  const updateTemplate = async (id: number, template: Partial<Template>): Promise<Template | null> => {
    try {
      loading.value = true;
      error.value = null;

      // Get the auth store
      const authStore = useAuthStore();

      // For designers, verify they can only update their own templates
      if (authStore.state.user?.currentRole === 'designer' && authStore.state.user?.id) {
        // Get the template to check ownership
        const existingTemplate = await getTemplate(id);

        // If template doesn't exist or doesn't belong to this designer, return null
        if (!existingTemplate || existingTemplate.initiated_by !== Number(authStore.state.user.id)) {
          error.value = 'You do not have permission to update this template';
          return null;
        }
      }

      // Use the admin function to update the template (bypasses RLS)
      const { data: updateResult, error: updateErr } = await supabase.rpc(
        'admin_update_template_fields_v2' as CustomRpcFunction,
        {
          p_template_id: id,
          p_name: template.name,
          p_description: template.description,
          p_status: template.status,
          p_published: template.published,
          p_image_server_data: template.image_server_data
        }
      );

      if (updateErr) {
        throw updateErr;
      }

      if (!updateResult) {
        throw new Error('Failed to update template');
      }

      // If template was updated successfully and has element types/values, save them
      // First, delete existing element values
      await deleteTemplateElementValues(id);

      // Then save new element values
      if (template.element_values && template.element_values.length > 0) {
        await saveTemplateElementValues(id, template.element_values);
      }

      // Fetch the complete template with all related data
      return await getTemplate(id);
    } catch (err) {
      console.error(`Error updating template with ID ${id}:`, err);
      error.value = err instanceof Error ? err.message : `Failed to update template with ID ${id}`;
      return null;
    } finally {
      loading.value = false;
    }
  };

  /**
   * Delete a template
   */
  const deleteTemplate = async (id: number): Promise<boolean> => {
    try {
      loading.value = true;
      error.value = null;

      // Get the auth store
      const authStore = useAuthStore();

      // For designers, verify they can only delete their own templates
      if (authStore.state.user?.currentRole === 'designer' && authStore.state.user?.id) {
        // Get the template to check ownership
        const existingTemplate = await getTemplate(id);

        // If template doesn't exist or doesn't belong to this designer, return false
        if (!existingTemplate || existingTemplate.initiated_by !== Number(authStore.state.user.id)) {
          error.value = 'You do not have permission to delete this template';
          return false;
        }
      }

      // First, delete related element values
      await deleteTemplateElementValues(id);

      // Then delete the template
      const { error: err } = await supabase
        .from('templates')
        .delete()
        .eq('id', id);

      if (err) {
        throw err;
      }

      return true;
    } catch (err) {
      console.error(`Error deleting template with ID ${id}:`, err);
      error.value = err instanceof Error ? err.message : `Failed to delete template with ID ${id}`;
      return false;
    } finally {
      loading.value = false;
    }
  };

  /**
   * Save template element values
   */
  const saveTemplateElementValues = async (templateId: number, elementValueIds: number[]): Promise<void> => {
    try {
      // Create an array of objects to insert
      const data = elementValueIds.map((valueId, index) => ({
        template_id: templateId,
        element_id: valueId,
        order: index + 1 // Add order field with sequential values
      }));

      // Insert the data
      const { error: err } = await supabase
        .from('prompt_elements_usage')
        .insert(data);

      if (err) {
        throw err;
      }
    } catch (err) {
      console.error('Error saving template element values:', err);
      error.value = err instanceof Error ? err.message : 'Failed to save template element values';
    }
  };

  /**
   * Delete template element values
   */
  const deleteTemplateElementValues = async (templateId: number): Promise<void> => {
    try {
      const { error: err } = await supabase
        .from('prompt_elements_usage')
        .delete()
        .eq('template_id', templateId);

      if (err) {
        throw err;
      }
    } catch (err) {
      console.error('Error deleting template element values:', err);
      error.value = err instanceof Error ? err.message : 'Failed to delete template element values';
    }
  };

  /**
   * Update template status using the lifecycle function
   * @param templateId Template ID
   * @param newStatus New status ('draft', 'pending', 'active', 'archived')
   * @param notes Optional notes about the status change
   * @returns Boolean indicating success or failure
   */
  const updateTemplateStatus = async (
    templateId: number,
    newStatus: 'draft' | 'pending' | 'active' | 'archived',
    notes?: string
  ): Promise<boolean> => {
    try {
      loading.value = true;
      error.value = null;

      // Get current user's app_user.id (integer ID)
      const userId = authStore.state.user?.appUserId;

      if (!userId) {
        error.value = 'User ID not found';
        return false;
      }

      // Call the admin database function to update status (bypasses RLS)
      const { data, error: err } = await supabase.rpc(
        'admin_update_template_status' as CustomRpcFunction,
        {
          p_template_id: templateId,
          p_new_status: newStatus,
          p_user_id: userId,
          p_notes: notes || null
        }
      );

      if (err) {
        throw err;
      }

      return !!data; // Convert to boolean
    } catch (err) {
      console.error('Error updating template status:', err);
      error.value = err instanceof Error ? err.message : 'Failed to update template status';
      return false;
    } finally {
      loading.value = false;
    }
  };

  /**
   * Get template status history
   * @param templateId Template ID
   * @returns Array of status history records
   */
  const getTemplateStatusHistory = async (templateId: number): Promise<TemplateStatusHistory[]> => {
    try {
      loading.value = true;
      error.value = null;

      // Call the admin database function to get status history (bypasses RLS)
      const { data, error: err } = await supabase.rpc(
        'admin_get_template_status_history' as CustomRpcFunction,
        {
          p_template_id: templateId
        }
      );

      if (err) {
        throw err;
      }

      // Return the data directly
      return data || [];
    } catch (err) {
      console.error('Error fetching template status history:', err);
      error.value = err instanceof Error ? err.message : 'Failed to fetch template status history';
      return [];
    } finally {
      loading.value = false;
    }
  };

  /**
   * Copy any template to create a new draft
   * @param templateId Template ID to copy
   * @returns ID of the new template or null if failed
   */
  const copyTemplate = async (templateId: number): Promise<number | null> => {
    try {
      loading.value = true;
      error.value = null;

      // Get current user's app_user.id (integer ID)
      const userId = authStore.state.user?.appUserId;

      if (!userId) {
        error.value = 'User ID not found';
        return null;
      }

      // Call the admin database function to copy the template (bypasses RLS)
      const { data, error: err } = await supabase.rpc(
        'admin_copy_template' as CustomRpcFunction,
        {
          p_template_id: templateId,
          p_user_id: userId
        }
      );

      if (err) {
        throw err;
      }

      // Cast the data to the correct type
      return data as number;
    } catch (err) {
      console.error('Error copying template:', err);
      error.value = err instanceof Error ? err.message : 'Failed to copy template';
      return null;
    } finally {
      loading.value = false;
    }
  };

  return {
    loading,
    error,
    templates,
    totalCount,
    getTemplates,
    getTemplate,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    updateTemplateStatus,
    getTemplateStatusHistory,
    copyTemplate
  };
};
