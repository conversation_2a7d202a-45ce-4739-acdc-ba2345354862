# 2025-04-12 (Part 6)

## User Management Table Improvements - Part 2

Today we made additional improvements to the User Management table:

### Improved Add User Role Dialog

1. Reorganized the Add User Role dialog to be more intuitive:
   - User selection first
   - Company selection second
   - Role selection last, filtered to show only roles the user doesn't already have for the selected company

2. Added validation to prevent duplicate role assignments:
   - Roles are filtered based on the selected user and company
   - Only roles that the user doesn't already have for the selected company are shown
   - This prevents the "duplicate key" error that was occurring before

3. Added better error handling:
   - User-friendly message for duplicate role errors
   - Different colors for different types of errors (warning vs. error)
   - More descriptive error messages

### Next Steps

1. Add confirmation dialogs for destructive actions
2. Add filtering options to the table
3. Add pagination controls
4. Add sorting functionality
