import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { usePromptElementsService, type PromptElementType, type PromptElement } from 'src/services/promptElementsService';
import { useAuthStore } from 'src/stores/auth';
import { supabase } from 'src/boot/supabase';

export const usePromptElementsStore = defineStore('promptElements', () => {
  // Services
  const promptElementsService = usePromptElementsService();
  const authStore = useAuthStore();
  const {
    getPromptElementTypes,
    getPromptElements,
    getVisiblePromptElements,
    createCustomPromptElement
  } = promptElementsService;

  // Get current app_user ID (the integer ID, not the UUID)
  const currentUserId = computed(() => {
    // Log the entire user object to see its structure
    console.log('Auth store user object:', authStore.state.user);

    // We need to get the app_user.id (integer) from the auth store
    // This is different from the auth.user.id (UUID)
    const appUserId = authStore.state.user?.appUserId || null;
    console.log('Current app_user ID in promptElementsStore:', appUserId);
    return appUserId;
  });

  // State
  const elementTypes = ref<PromptElementType[]>([]);
  const elementValuesByType = ref<Record<number, PromptElement[]>>({});
  const isLoading = ref(false);
  const isTypesLoaded = ref(false);

  // Getters
  const getElementTypes = computed(() => elementTypes.value);

  const getElementValuesForType = computed(() => {
    return (typeId: number) => elementValuesByType.value[typeId] || [];
  });

  // Actions
  async function loadElementTypes() {
    if (isTypesLoaded.value) {
      // console.log('Using cached element types:', elementTypes.value);
      return elementTypes.value;
    }

    try {
      // console.log('Loading element types from service');
      isLoading.value = true;
      elementTypes.value = await getPromptElementTypes();
      isTypesLoaded.value = true;
      // console.log('Loaded element types:', elementTypes.value);
      return elementTypes.value;
    } catch (error) {
      console.error('Error loading element types:', error);
      return [];
    } finally {
      isLoading.value = false;
    }
  }

  async function loadElementValuesForType(typeId: number) {
    if (elementValuesByType.value[typeId]) {
      return elementValuesByType.value[typeId];
    }

    try {
      isLoading.value = true;

      let values: PromptElement[] = [];

      // Check if we have a current user ID
      if (currentUserId.value) {
        // If we have a user ID, get visible elements (system + user's custom)
        // console.log(`Loading visible elements for type ${typeId} with user ID ${currentUserId.value}`);
        values = await getVisiblePromptElements(typeId, currentUserId.value);
      } else {
        // Fallback: If no user ID, just get all elements (for testing/development)
        console.log(`No user ID available, loading all elements for type ${typeId}`);
        values = await getPromptElements(typeId);
      }
      elementValuesByType.value[typeId] = values;
      return values;
    } catch (error) {
      console.error(`Error loading element values for type ${typeId}:`, error);
      return [];
    } finally {
      isLoading.value = false;
    }
  }

  async function addCustomValue(typeId: number, value: string, description: string | null = null) {
    try {
      isLoading.value = true;

      // Check if we have a current user ID
      if (!currentUserId.value) {
        console.error('No user ID available for adding custom value');
        return null;
      }

      const newElement = await createCustomPromptElement({
        type_id: typeId,
        value,
        description
      });

      // Update the cache
      if (newElement) {
        if (!elementValuesByType.value[typeId]) {
          elementValuesByType.value[typeId] = [];
        }
        elementValuesByType.value[typeId].push(newElement);
      }

      return newElement;
    } catch (error) {
      console.error('Error adding custom value:', error);
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  // Reset store
  function reset() {
    elementTypes.value = [];
    elementValuesByType.value = {};
    isTypesLoaded.value = false;
  }

  // Get elements by their IDs
  async function getElementsByIds(elementIds: number[]): Promise<PromptElement[]> {
    try {
      isLoading.value = true;

      if (elementIds.length === 0) {
        return [];
      }

      const { data, error: err } = await supabase
        .from('prompt_elements')
        .select('*')
        .in('id', elementIds)
        .order('value');

      if (err) {
        throw err;
      }

      // Transform the data to ensure it matches the PromptElement interface
      const elements: PromptElement[] = (data || []).map((item: Record<string, unknown>) => {
        // Define a type that matches the expected structure
        interface PromptElementData {
          id: number;
          type_id: number;
          value: string;
          description: string | null;
          is_system?: boolean;
          designer_id?: number | null;
          created_at: string;
          updated_at: string;
        }

        // First convert to unknown to avoid direct conversion error
        const typedItem = item as unknown as PromptElementData;

        return {
          id: typedItem.id,
          type_id: typedItem.type_id,
          value: typedItem.value,
          description: typedItem.description,
          is_system: typedItem.is_system || false,
          designer_id: typedItem.designer_id || null,
          created_at: typedItem.created_at,
          updated_at: typedItem.updated_at
        };
      });

      return elements;
    } catch (error) {
      console.error('Error fetching elements by IDs:', error);
      return [];
    } finally {
      isLoading.value = false;
    }
  }

  return {
    // State
    elementTypes,
    elementValuesByType,
    isLoading,
    isTypesLoaded,

    // Getters
    getElementTypes,
    getElementValuesForType,

    // Actions
    loadElementTypes,
    loadElementValuesForType,
    addCustomValue,
    getElementsByIds,
    reset
  };
});
