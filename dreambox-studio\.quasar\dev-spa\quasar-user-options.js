/* eslint-disable */
/**
 * THIS FILE IS GENERATED AUTOMATICALLY.
 * DO NOT EDIT.
 *
 * You are probably looking on adding startup/initialization code.
 * Use "quasar new boot <name>" and add it there.
 * One boot file per concern. Then reference the file(s) in quasar.config file > boot:
 * boot: ['file', ...] // do not add ".js" extension to it.
 *
 * Boot files are your "main.js"
 **/



import {<PERSON><PERSON><PERSON>out,QHeader,QDrawer,QPageContainer,QPage,QToolbar,QToolbarTitle,QBtn,QIcon,QList,QItem,QItemSection,QItemLabel,QCard,QCardSection,QCardActions,QInput,QForm,QSelect,QTable,QTh,QTr,QTd,QDialog,QSpace,QSeparator,QBadge,QToggle,QSpinner,QTabs,QTab,QTabPanels,QTabPanel,QRouteTab,<PERSON><PERSON><PERSON>,ClosePopup,Notify,Dialog,Loading,LocalStorage,SessionStorage,Dark,Meta} from 'quasar'



export default { config: {"dark":"auto","brand":{"primary":"#1976D2","secondary":"#26A69A","accent":"#9C27B0","positive":"#21BA45","negative":"#C10015","info":"#31CCEC","warning":"#F2C037","dark":"#1D1D1D"},"loading":{"spinnerColor":"primary","spinnerSize":80},"notify":{"position":"top","timeout":3000,"textColor":"white","actions":[{"icon":"close","color":"white"}]}},components: {QLayout,QHeader,QDrawer,QPageContainer,QPage,QToolbar,QToolbarTitle,QBtn,QIcon,QList,QItem,QItemSection,QItemLabel,QCard,QCardSection,QCardActions,QInput,QForm,QSelect,QTable,QTh,QTr,QTd,QDialog,QSpace,QSeparator,QBadge,QToggle,QSpinner,QTabs,QTab,QTabPanels,QTabPanel,QRouteTab},directives: {Ripple,ClosePopup},plugins: {Notify,Dialog,Loading,LocalStorage,SessionStorage,Dark,Meta} }

