# Filter Engine Plugins

This directory contains plugins for the Filter Engine. Plugins extend the core functionality of the Filter Engine with table-specific filtering logic and specialized filter types.

## Available Plugins

### Templates Plugin

The Templates Plugin provides template-specific filtering functionality:

- Registers template-specific filter types
- Defines template table structure and columns
- Provides template-specific filtering logic
- Connects to the SupabaseAdapter for server-side filtering

## Creating New Plugins

To create a new plugin:

1. Create a new plugin class that implements the `FilterPlugin` interface
2. Register the plugin in the `plugins/index.ts` file
3. Implement the required methods:
   - `initialize`: Set up the plugin
   - `applyFilters`: Apply plugin-specific filters
   - `getComponents`: Return plugin-specific components
   - `getFilterTypes`: Return plugin-specific filter types

Example:

```typescript
import type { FilterPlugin } from '../FilterPlugin';
import type { FilterEngine } from '../FilterEngine';

export class MyPlugin implements FilterPlugin {
  id = 'myPlugin';
  name = 'My Plugin';
  
  initialize(engine: FilterEngine): void {
    // Custom initialization
  }
  
  applyFilters<T>(data: T[], tableName: string, filters: Record<string, unknown>): T[] {
    // Custom filter application
    return data;
  }
  
  getComponents(): Record<string, unknown> {
    // Custom components
    return {};
  }
  
  getFilterTypes(): unknown[] {
    // Custom filter types
    return [];
  }
}
```

Then register the plugin in `plugins/index.ts`:

```typescript
import { MyPlugin } from './MyPlugin';

export function registerAllPlugins(engine: FilterEngine): void {
  // Register existing plugins
  engine.registerPlugin(new TemplatesPlugin());
  
  // Register your new plugin
  engine.registerPlugin(new MyPlugin());
}
```
