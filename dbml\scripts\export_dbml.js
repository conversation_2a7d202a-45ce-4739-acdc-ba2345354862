const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// Load environment variables from .env file if available
require('dotenv').config({ path: path.resolve(__dirname, '../../.env') });

// Create a connection pool using environment variables or fallback to hardcoded values
const pool = new Pool({
    user: process.env.SUPABASE_DB_USER || 'postgres',
    password: process.env.SUPABASE_DB_PASSWORD || 'opfb7oFzck6hbG07', // Replace with your new password
    host: process.env.SUPABASE_DB_HOST || 'db.ppzaqhuaqzjofsorlmbn.supabase.co',
    port: process.env.SUPABASE_DB_PORT || 5432,
    database: process.env.SUPABASE_DB_NAME || 'postgres',
    ssl: { rejectUnauthorized: false }
});

// Query to get tables
const getTablesQuery = `
SELECT
    table_name
FROM
    information_schema.tables
WHERE
    table_schema = 'public'
    AND table_type = 'BASE TABLE'
    AND table_name NOT IN ('schema_migrations', 'spatial_ref_sys')
ORDER BY
    table_name;
`;

// Query to get columns for a table
const getColumnsQuery = `
SELECT
    column_name,
    data_type,
    is_nullable,
    column_default
FROM
    information_schema.columns
WHERE
    table_schema = 'public'
    AND table_name = $1
ORDER BY
    ordinal_position;
`;

// Query to get primary keys
const getPrimaryKeysQuery = `
SELECT
    tc.table_name,
    kc.column_name
FROM
    information_schema.table_constraints tc
    JOIN information_schema.key_column_usage kc ON tc.constraint_name = kc.constraint_name
WHERE
    tc.constraint_type = 'PRIMARY KEY'
    AND tc.table_schema = 'public'
    AND tc.table_name = $1;
`;

// Query to get unique constraints
const getUniqueConstraintsQuery = `
SELECT
    tc.table_name,
    kc.column_name
FROM
    information_schema.table_constraints tc
    JOIN information_schema.key_column_usage kc ON tc.constraint_name = kc.constraint_name
WHERE
    tc.constraint_type = 'UNIQUE'
    AND tc.table_schema = 'public'
    AND tc.table_name = $1;
`;

// Query to get foreign keys
const getForeignKeysQuery = `
SELECT
    tc.table_name,
    kc.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM
    information_schema.table_constraints tc
    JOIN information_schema.key_column_usage kc ON tc.constraint_name = kc.constraint_name
    JOIN information_schema.constraint_column_usage ccu ON ccu.constraint_name = tc.constraint_name
WHERE
    tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_schema = 'public'
    AND tc.table_name = $1;
`;

// Function to convert PostgreSQL data type to DBML data type
function convertDataType(pgType) {
    // Map PostgreSQL data types to DBML data types
    const typeMap = {
        'integer': 'integer',
        'bigint': 'bigint',
        'smallint': 'integer',
        'character varying': 'varchar',
        'varchar': 'varchar',
        'text': 'text',
        'boolean': 'boolean',
        'timestamp with time zone': 'timestamp',
        'timestamp without time zone': 'timestamp',
        'date': 'date',
        'time': 'time',
        'numeric': 'numeric',
        'decimal': 'decimal',
        'real': 'float',
        'double precision': 'float',
        'uuid': 'uuid',
        'jsonb': 'json',
        'json': 'json',
        'ARRAY': 'array',
        'USER-DEFINED': 'text', // Handle custom types
        'citext': 'text', // Case-insensitive text
        'bytea': 'binary', // Binary data
        'interval': 'interval', // Time interval
        'money': 'decimal', // Money type
        'inet': 'varchar', // IP address
        'macaddr': 'varchar', // MAC address
        'bit': 'bit', // Bit string
        'bit varying': 'bit', // Variable-length bit string
        'point': 'point', // Geometric point
        'line': 'line', // Geometric line
        'lseg': 'lseg', // Line segment
        'box': 'box', // Rectangular box
        'path': 'path', // Geometric path
        'polygon': 'polygon', // Geometric polygon
        'circle': 'circle' // Geometric circle
    };

    // Extract the base type from array types
    if (pgType.endsWith('[]')) {
        return 'array';
    }

    // Handle specific types or use the map
    for (const [pgPattern, dbmlType] of Object.entries(typeMap)) {
        if (pgType.includes(pgPattern)) {
            return dbmlType;
        }
    }

    // Default to the original type if no mapping is found
    return pgType;
}

// Main function to generate DBML
async function generateDBML() {
    let dbml = '// Dreambox Studio Database Schema\n';
    dbml += '// Generated on ' + new Date().toISOString() + '\n\n';
    dbml += 'Project dreambox_studio {\n';
    dbml += '  database_type: "PostgreSQL"\n';
    dbml += '  Note: "Dreambox Studio Database Schema"\n';
    dbml += '}\n\n';

    try {
        // Get all tables
        const tablesResult = await pool.query(getTablesQuery);
        const tables = tablesResult.rows;

        console.log(`Found ${tables.length} tables`);

        // Process each table
        for (const table of tables) {
            const tableName = table.table_name;
            console.log(`Processing table: ${tableName}`);
            dbml += `Table ${tableName} {\n`;

            // Get columns for this table
            const columnsResult = await pool.query(getColumnsQuery, [tableName]);
            const columns = columnsResult.rows;

            // Get primary keys for this table
            const primaryKeysResult = await pool.query(getPrimaryKeysQuery, [tableName]);
            const primaryKeys = primaryKeysResult.rows.map(row => row.column_name);

            // Get unique constraints for this table
            const uniqueConstraintsResult = await pool.query(getUniqueConstraintsQuery, [tableName]);
            const uniqueConstraints = uniqueConstraintsResult.rows.map(row => row.column_name);

            // Get foreign keys for this table
            const foreignKeysResult = await pool.query(getForeignKeysQuery, [tableName]);
            const foreignKeys = foreignKeysResult.rows;

            // Process each column
            for (const column of columns) {
                const columnName = column.column_name;
                const dataType = convertDataType(column.data_type);
                const isNullable = column.is_nullable === 'YES' ? 'null' : 'not null';

                // Build column definition
                let columnDef = `  ${columnName} ${dataType}`;

                // Add constraints
                const constraints = [];

                // Add not null/null constraint
                constraints.push(isNullable);

                if (primaryKeys.includes(columnName)) {
                    constraints.push('pk');
                }

                if (uniqueConstraints.includes(columnName)) {
                    constraints.push('unique');
                }

                if (column.column_default) {
                    // Clean up the default value for DBML
                    let defaultValue = column.column_default;
                    if (defaultValue.startsWith('nextval(')) {
                        // For auto-incrementing columns, use 'increment' instead of 'default: auto increment'
                        constraints.push('increment');
                    } else {
                        // Handle specific default values that cause syntax issues
                        if (defaultValue === "'planned'::character varying") {
                            constraints.push('note: "Default: planned"');
                        } else if (defaultValue === "'now()'") {
                            constraints.push('note: "Default: now()"');
                        } else if (defaultValue.includes('::')) {
                            // Remove type cast
                            defaultValue = defaultValue.split('::')[0];
                            // For other default values with type casts, use note
                            constraints.push(`note: "Default: ${defaultValue.replace(/'/g, '')}"`);
                        } else {
                            // For numeric and boolean defaults, use default
                            if (defaultValue.match(/^[0-9.]+$/) || defaultValue === 'true' || defaultValue === 'false' || defaultValue === 'null') {
                                constraints.push(`default: ${defaultValue}`);
                            } else {
                                // For other defaults, use note
                                constraints.push(`note: "Default: ${defaultValue.replace(/'/g, '')}"`);
                            }
                        }
                    }
                }

                // Add foreign key reference
                const foreignKey = foreignKeys.find(fk => fk.column_name === columnName);
                if (foreignKey) {
                    constraints.push(`ref: > ${foreignKey.foreign_table_name}.${foreignKey.foreign_column_name}`);
                }

                // Add all constraints to column definition in a single set of brackets
                columnDef += ` [${constraints.join(', ')}]`;

                dbml += `${columnDef}\n`;
            }

            dbml += '}\n\n';
        }

        // Add note about auth schema tables
        dbml += '// Note: Auth schema tables are not included in this diagram\n';
        dbml += '// They are managed by Supabase Auth service\n\n';

        // Write DBML to file
        const outputDir = path.join(__dirname, '..', 'DBML');
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }

        const outputPath = path.join(outputDir, 'dreambox.dbml');
        fs.writeFileSync(outputPath, dbml);

        console.log(`DBML file has been generated successfully at ${outputPath}`);
    } catch (err) {
        console.error('Error generating DBML:', err);
    } finally {
        await pool.end();
    }
}

// Run the function
generateDBML();
