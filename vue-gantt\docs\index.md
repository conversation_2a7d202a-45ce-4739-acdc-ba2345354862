---
# https://vitepress.dev/reference/default-theme-home-page
layout: home

hero:
  name: Vue-Ganttastic
  text: Gantt chart component for Vue
  tagline: A simple, interactive and highly customizable Gantt chart component for Vue.js 
  image:
    src: https://user-images.githubusercontent.com/28678851/148047714-301f07df-4101-48b8-9e47-1f272b290e80.png
    alt: Vue-Ganttastic logo
  actions:
    - theme: brand
      text: Get started
      link: /introduction
    - theme: alt
      text: API Reference
      link: /GGanttChart

features:
  - title: Vue 3 and TypeScript support
    details: Written in Vue 3 and TypeScript. Ships with out-of-the-box type declarations.
  - title: Interactive
    details: Dynamic Gantt chart with movable bars and numerous event handlers.
  - title:  Customizable
    details: Style the chart and each individual bar to your own liking!
---

