import { supabase } from 'src/boot/supabase';
import { useAuthStore } from 'src/stores/auth';

export interface UserSetting {
  id?: number;
  app_user_id: number;
  setting_key: string;
  setting_value: string;
  created_at?: string;
  updated_at?: string;
}

/**
 * Service for managing user settings in the database
 */
export const userSettingsService = {
  /**
   * Get a user setting by key
   */
  async getSetting(key: string): Promise<string | null> {
    const authStore = useAuthStore();
    const user = authStore.state.user;

    if (!user || !user.id) {
      console.error('Cannot get setting: User not logged in');
      return null;
    }

    try {
      // First, get the app_user_id from the app_users table
      const { data: appUserData, error: appUserError } = await supabase
        .from('app_users')
        .select('id')
        .eq('user_id', user.id)
        .single();

      if (appUserError || !appUserData) {
        console.error('Error getting app_user_id:', appUserError);
        return null;
      }

      const appUserId = appUserData.id;

      // Get the setting using the RPC function
      const { data, error } = await supabase
        .rpc('get_user_setting', {
          p_app_user_id: appUserId,
          p_setting_key: key
        });

      if (error) {
        console.error('Error getting user setting:', error);
        return null;
      }

      return data;
    } catch (err) {
      console.error('Exception getting user setting:', err);
      return null;
    }
  },

  /**
   * Save a user setting
   */
  async saveSetting(key: string, value: string): Promise<boolean> {
    const authStore = useAuthStore();
    const user = authStore.state.user;

    if (!user || !user.id) {
      console.error('Cannot save setting: User not logged in');
      return false;
    }

    try {
      // First, get the app_user_id from the app_users table
      const { data: appUserData, error: appUserError } = await supabase
        .from('app_users')
        .select('id')
        .eq('user_id', user.id)
        .single();

      if (appUserError || !appUserData) {
        console.error('Error getting app_user_id:', appUserError);
        return false;
      }

      const appUserId = appUserData.id;

      // Save the setting using the RPC function
      const { data, error } = await supabase
        .rpc('save_user_setting', {
          p_app_user_id: appUserId,
          p_setting_key: key,
          p_setting_value: value
        });

      if (error) {
        console.error('Error saving user setting:', error);
        return false;
      }

      return data;
    } catch (err) {
      console.error('Exception saving user setting:', err);
      return false;
    }
  },

  /**
   * Delete a user setting
   */
  async deleteSetting(key: string): Promise<boolean> {
    const authStore = useAuthStore();
    const user = authStore.state.user;

    if (!user || !user.id) {
      console.error('Cannot delete setting: User not logged in');
      return false;
    }

    try {
      // First, get the app_user_id from the app_users table
      const { data: appUserData, error: appUserError } = await supabase
        .from('app_users')
        .select('id')
        .eq('user_id', user.id)
        .single();

      if (appUserError || !appUserData) {
        console.error('Error getting app_user_id:', appUserError);
        return false;
      }

      const appUserId = appUserData.id;

      // Delete the setting using the RPC function
      const { data, error } = await supabase
        .rpc('delete_user_setting', {
          p_app_user_id: appUserId,
          p_setting_key: key
        });

      if (error) {
        console.error('Error deleting user setting:', error);
        return false;
      }

      return data;
    } catch (err) {
      console.error('Exception deleting user setting:', err);
      return false;
    }
  }
};
