# Step 6: Migration Plan

## Overview

This document outlines a detailed migration plan for transitioning from the current filter implementation to the new filter engine. The migration will be executed in phases to minimize disruption and ensure a smooth transition for users.

## Migration Principles

1. **Parallel Implementation**: Build the new system alongside the existing one
2. **Feature Parity**: Ensure all existing functionality is supported before switching
3. **Data Preservation**: Maintain all saved filters during the transition
4. **Incremental Rollout**: Use feature flags to control the rollout
5. **Fallback Mechanism**: Provide a way to revert to the old system if issues arise

## Phase 1: Preparation

### 1.1 Create Feature Flag System

```typescript
// src/config/features.ts
export const FEATURES = {
  NEW_FILTER_ENGINE: 'new-filter-engine'
};

export class FeatureFlags {
  private static instance: FeatureFlags;
  private flags: Record<string, boolean> = {};
  
  private constructor() {
    // Initialize default flags
    this.flags[FEATURES.NEW_FILTER_ENGINE] = false;
    
    // Load from localStorage if available
    const savedFlags = localStorage.getItem('feature_flags');
    if (savedFlags) {
      try {
        const parsedFlags = JSON.parse(savedFlags);
        this.flags = { ...this.flags, ...parsedFlags };
      } catch (e) {
        console.error('Error parsing feature flags:', e);
      }
    }
  }
  
  public static getInstance(): FeatureFlags {
    if (!FeatureFlags.instance) {
      FeatureFlags.instance = new FeatureFlags();
    }
    return FeatureFlags.instance;
  }
  
  public isEnabled(feature: string): boolean {
    return this.flags[feature] === true;
  }
  
  public enable(feature: string): void {
    this.flags[feature] = true;
    this.saveFlags();
  }
  
  public disable(feature: string): void {
    this.flags[feature] = false;
    this.saveFlags();
  }
  
  private saveFlags(): void {
    localStorage.setItem('feature_flags', JSON.stringify(this.flags));
  }
}

export const featureFlags = FeatureFlags.getInstance();
```

### 1.2 Create Filter Format Converter

This utility will convert between old and new filter formats:

```typescript
// src/lib/filters/migration/FilterFormatConverter.ts
import type { SavedFilter as OldSavedFilter } from 'src/stores/tableFilterStore';
import type { SavedFilter as NewSavedFilter } from '@/stores/filterStore';

export class FilterFormatConverter {
  /**
   * Convert from old filter format to new filter format
   */
  static oldToNew(oldFilter: OldSavedFilter): NewSavedFilter {
    // Create base structure for new filter
    const newFilter: NewSavedFilter = {
      id: oldFilter.id,
      user_id: oldFilter.user_id,
      name: oldFilter.name,
      description: oldFilter.description || null,
      table_name: oldFilter.table_name || oldFilter.context,
      context: oldFilter.context,
      is_default: oldFilter.is_default,
      created_at: oldFilter.created_at,
      updated_at: oldFilter.updated_at,
      configuration: {
        columns: [],
        specializedFilters: {}
      }
    };
    
    // Convert columns
    if (oldFilter.configuration && oldFilter.configuration.columns) {
      newFilter.configuration.columns = oldFilter.configuration.columns.map(col => ({
        id: col.id,
        visible: col.visible !== undefined ? col.visible : true,
        section: col.section || 'main',
        order: col.order !== undefined ? col.order : 0,
        filterValue: col.filterValue || null
      }));
    }
    
    // Convert specialized filters (additionalFilters in old format)
    if (oldFilter.configuration && oldFilter.configuration.additionalFilters) {
      // Handle prompt elements
      const promptElements: Record<string, number[]> = {};
      
      // Extract prompt element filters
      for (const [key, value] of Object.entries(oldFilter.configuration.additionalFilters)) {
        if (key.startsWith('element_')) {
          promptElements[key] = value as number[];
        }
      }
      
      if (Object.keys(promptElements).length > 0) {
        newFilter.configuration.specializedFilters.promptElements = promptElements;
      }
      
      // Handle collections
      if (oldFilter.configuration.additionalFilters.collections) {
        newFilter.configuration.specializedFilters.relatedItems = {
          collections: oldFilter.configuration.additionalFilters.collections as number[]
        };
      }
      
      // Handle products
      if (oldFilter.configuration.additionalFilters.products) {
        if (!newFilter.configuration.specializedFilters.relatedItems) {
          newFilter.configuration.specializedFilters.relatedItems = {};
        }
        newFilter.configuration.specializedFilters.relatedItems.products = 
          oldFilter.configuration.additionalFilters.products as number[];
      }
    }
    
    return newFilter;
  }
  
  /**
   * Convert from new filter format to old filter format
   */
  static newToOld(newFilter: NewSavedFilter): OldSavedFilter {
    // Create base structure for old filter
    const oldFilter: OldSavedFilter = {
      id: newFilter.id,
      user_id: newFilter.user_id,
      name: newFilter.name,
      description: newFilter.description,
      table_name: newFilter.table_name,
      context: newFilter.context,
      is_default: newFilter.is_default,
      created_at: newFilter.created_at,
      updated_at: newFilter.updated_at,
      configuration: {
        columns: []
      }
    };
    
    // Convert columns
    if (newFilter.configuration && newFilter.configuration.columns) {
      oldFilter.configuration.columns = newFilter.configuration.columns.map(col => ({
        id: col.id,
        visible: col.visible,
        section: col.section,
        order: col.order,
        filterValue: col.filterValue
      }));
    }
    
    // Convert specialized filters
    if (newFilter.configuration && newFilter.configuration.specializedFilters) {
      oldFilter.configuration.additionalFilters = {};
      
      // Handle prompt elements
      if (newFilter.configuration.specializedFilters.promptElements) {
        for (const [key, value] of Object.entries(newFilter.configuration.specializedFilters.promptElements)) {
          oldFilter.configuration.additionalFilters[key] = value;
        }
      }
      
      // Handle related items
      if (newFilter.configuration.specializedFilters.relatedItems) {
        const relatedItems = newFilter.configuration.specializedFilters.relatedItems;
        
        if (relatedItems.collections) {
          oldFilter.configuration.additionalFilters.collections = relatedItems.collections;
        }
        
        if (relatedItems.products) {
          oldFilter.configuration.additionalFilters.products = relatedItems.products;
        }
      }
    }
    
    return oldFilter;
  }
  
  /**
   * Convert active filters from old format to new format
   */
  static convertActiveFilters(
    oldActiveFilters: Record<string, any>,
    oldCombinedFilters: Record<string, any>
  ): {
    activeFilters: Record<string, any>;
    specializedFilters: Record<string, any>;
  } {
    const activeFilters: Record<string, any> = { ...oldActiveFilters };
    const specializedFilters: Record<string, any> = {};
    
    // Extract prompt elements
    const promptElements: Record<string, number[]> = {};
    for (const [key, value] of Object.entries(oldCombinedFilters)) {
      if (key.startsWith('element_')) {
        promptElements[key] = value as number[];
      }
    }
    
    if (Object.keys(promptElements).length > 0) {
      specializedFilters.promptElements = promptElements;
    }
    
    // Extract collections and products
    if (oldCombinedFilters.collections || oldCombinedFilters.products) {
      specializedFilters.relatedItems = {};
      
      if (oldCombinedFilters.collections) {
        specializedFilters.relatedItems.collections = oldCombinedFilters.collections;
      }
      
      if (oldCombinedFilters.products) {
        specializedFilters.relatedItems.products = oldCombinedFilters.products;
      }
    }
    
    return { activeFilters, specializedFilters };
  }
}
```

### 1.3 Create Database Migration Script

```sql
-- Migration script to update user_filters table
-- This script will be run once during the migration

-- First, create a backup of the user_filters table
CREATE TABLE user_filters_backup AS SELECT * FROM user_filters;

-- Add any new columns or indexes needed for the new filter engine
-- For example, if we need to add a new column for specialized filters:
-- ALTER TABLE user_filters ADD COLUMN IF NOT EXISTS specialized_filters JSONB;

-- Update the configuration format if needed
-- This is a placeholder - the actual migration would depend on the specific changes
-- UPDATE user_filters SET configuration = jsonb_set(configuration, '{specializedFilters}', '{}')
-- WHERE configuration ? 'additionalFilters';

-- Create a migration log
CREATE TABLE IF NOT EXISTS migration_log (
  id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
  migration_name TEXT NOT NULL,
  executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  status TEXT NOT NULL,
  details JSONB
);

-- Log the migration
INSERT INTO migration_log (migration_name, status, details)
VALUES (
  'filter_engine_migration',
  'completed',
  jsonb_build_object(
    'backup_table', 'user_filters_backup',
    'records_migrated', (SELECT COUNT(*) FROM user_filters)
  )
);
```

## Phase 2: Implementation

### 2.1 Create Adapter Components

Create adapter components that can work with both old and new filter systems:

```typescript
// src/components/filters/FilterAdapter.vue
<template>
  <div class="filter-adapter">
    <!-- Use new filter panel if feature flag is enabled -->
    <new-filter-panel
      v-if="useNewFilterEngine"
      @filter-changed="handleNewFilterChanged"
    />
    
    <!-- Use old filter panel otherwise -->
    <table-filter-panel
      v-else
      @filter-changed="handleOldFilterChanged"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { featureFlags, FEATURES } from '@/config/features';
import TableFilterPanel from '@/components/filters/TableFilterPanel.vue';
import NewFilterPanel from '@/lib/filters/components/FilterPanel.vue';
import { FilterFormatConverter } from '@/lib/filters/migration/FilterFormatConverter';

// Determine which filter engine to use
const useNewFilterEngine = computed(() => {
  return featureFlags.isEnabled(FEATURES.NEW_FILTER_ENGINE);
});

// Event handlers
function handleNewFilterChanged(event: any) {
  // Convert to old format for backward compatibility
  const oldFormat = FilterFormatConverter.convertActiveFilters(
    event.activeFilters,
    event.specializedFilters
  );
  
  // Dispatch event in old format
  document.dispatchEvent(new CustomEvent('global-filter-changed', {
    detail: {
      filters: oldFormat
    }
  }));
}

function handleOldFilterChanged(event: any) {
  // No conversion needed - old format is already expected by existing components
  // Just re-dispatch the event
  document.dispatchEvent(new CustomEvent('global-filter-changed', {
    detail: event.detail
  }));
}
</script>
```

### 2.2 Update TemplateBrowser Component

Modify the TemplateBrowser component to work with both filter systems:

```typescript
// Partial update to src/pages/templates/TemplateBrowser.vue

// Import the feature flags
import { featureFlags, FEATURES } from '@/config/features';
import { FilterFormatConverter } from '@/lib/filters/migration/FilterFormatConverter';

// Add computed property to determine which filter engine to use
const useNewFilterEngine = computed(() => {
  return featureFlags.isEnabled(FEATURES.NEW_FILTER_ENGINE);
});

// Update the handleGlobalFilter function
function handleGlobalFilter(event: CustomEvent) {
  console.log('Global filter event received:', event.detail);
  const { name, value, filters } = event.detail;

  // Log the current filter before any changes
  console.log('Current filter before update:', JSON.stringify(currentFilter.value));

  if (filters) {
    console.log('Applying filters from global filter event:', filters);

    // Convert filter format if using new filter engine
    const apiFilters = { ...defaultFilter } as Record<string, unknown>;

    if (useNewFilterEngine.value) {
      // Convert from new format to API format
      const { activeFilters, specializedFilters } = filters;
      
      // Handle name filter specially - convert to search parameter
      if (activeFilters.name) {
        apiFilters.search = activeFilters.name;
      }
      
      // Add other standard filters
      for (const [key, value] of Object.entries(activeFilters)) {
        if (key !== 'name') {
          apiFilters[key] = value;
        }
      }
      
      // Handle specialized filters
      if (specializedFilters.promptElements) {
        // Convert prompt elements to API format
        for (const [key, value] of Object.entries(specializedFilters.promptElements)) {
          apiFilters[key] = value;
        }
      }
      
      if (specializedFilters.relatedItems) {
        // Convert related items to API format
        if (specializedFilters.relatedItems.collections) {
          apiFilters.collections = specializedFilters.relatedItems.collections;
        }
        
        if (specializedFilters.relatedItems.products) {
          apiFilters.products = specializedFilters.relatedItems.products;
        }
      }
    } else {
      // Using old format - handle as before
      // Handle name filter specially - convert to search parameter
      if (filters.name) {
        apiFilters.search = filters.name;
      }
      
      // Add other filters
      for (const [key, value] of Object.entries(filters)) {
        if (key !== 'name') {
          apiFilters[key] = value;
        }
      }
    }

    // Update the current filter
    currentFilter.value = {
      ...currentFilter.value,
      ...apiFilters
    };

    // Reset pagination to first page
    currentPagination.value.page = 1;

    // Fetch templates with the updated filter
    void getTemplates(currentFilter.value, currentPagination.value);
  }
}
```

### 2.3 Create Test Route

Create a test route to allow testing the new filter engine without affecting users:

```typescript
// src/router/routes.ts
// Add a new route for testing the new filter engine

const routes = [
  // Existing routes...
  
  // Test route for new filter engine
  {
    path: '/test/templates',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        component: () => import('pages/templates/TemplateBrowserTest.vue'),
        meta: {
          requiresAuth: true,
          title: 'Templates (New Filter Engine)'
        }
      }
    ]
  }
];
```

```typescript
// src/pages/templates/TemplateBrowserTest.vue
// This is a copy of TemplateBrowser.vue with the feature flag forced to true

<script setup>
import { onMounted } from 'vue';
import { featureFlags, FEATURES } from '@/config/features';

// Force the new filter engine to be enabled for this test page
onMounted(() => {
  featureFlags.enable(FEATURES.NEW_FILTER_ENGINE);
});

// Rest of the component is identical to TemplateBrowser.vue
</script>
```

## Phase 3: Rollout

### 3.1 Development Environment

1. **Enable for Developers**
   - Enable the feature flag for all developers
   - Test thoroughly with real data
   - Fix any issues that arise

2. **Update Documentation**
   - Document the new filter engine architecture
   - Create migration guides for developers
   - Update API documentation

### 3.2 Staging Environment

1. **Deploy to Staging**
   - Deploy the new filter engine to the staging environment
   - Enable the feature flag for all staging users
   - Monitor for issues

2. **Conduct User Testing**
   - Have a subset of users test the new filter engine
   - Gather feedback and make improvements
   - Ensure all edge cases are handled

### 3.3 Production Environment

1. **Gradual Rollout**
   - Deploy to production with the feature flag disabled
   - Enable the feature flag for a small percentage of users (e.g., 5%)
   - Monitor for issues and gradually increase the percentage

2. **Full Rollout**
   - Once confident, enable the feature flag for all users
   - Monitor for issues and be prepared to roll back if necessary
   - Provide support for any user questions or issues

### 3.4 Cleanup

1. **Remove Old Code**
   - Once the new filter engine is stable, remove the old filter code
   - Update all components to use the new filter engine directly
   - Remove the feature flag system for this feature

2. **Finalize Documentation**
   - Update all documentation to reflect the new filter engine
   - Remove references to the old filter system
   - Document lessons learned from the migration

## Rollback Plan

In case of critical issues, we need a plan to quickly roll back to the old filter system:

1. **Disable Feature Flag**
   - Set the `NEW_FILTER_ENGINE` feature flag to `false` for all users
   - This will immediately revert to using the old filter system

2. **Restore Database**
   - If database changes were made, restore from the backup table
   - Run the following SQL:
   ```sql
   -- Restore from backup if needed
   DELETE FROM user_filters;
   INSERT INTO user_filters SELECT * FROM user_filters_backup;
   ```

3. **Notify Users**
   - Inform users of the rollback and any actions they need to take
   - Provide support for any issues that arise

## Testing Checklist

Before each phase of the rollout, ensure the following tests pass:

1. **Functionality Tests**
   - All filter types work correctly
   - Saved filters can be loaded and saved
   - Filter combinations work as expected
   - Pagination works correctly with filters applied

2. **Performance Tests**
   - Filter application is performant
   - Large datasets are handled efficiently
   - UI remains responsive during filtering

3. **Migration Tests**
   - Existing saved filters are correctly migrated
   - Filter format conversion works in both directions
   - No data loss during migration

4. **Edge Cases**
   - Empty filters are handled correctly
   - Invalid filter combinations are handled gracefully
   - Error states are properly managed

## Conclusion

This migration plan provides a structured approach to transitioning from the current filter implementation to the new filter engine. By following this plan, we can ensure a smooth transition with minimal disruption to users.

The key to success will be thorough testing at each stage and maintaining backward compatibility during the transition. The feature flag system allows us to control the rollout and quickly roll back if necessary, providing an additional safety net.

Once completed, the new filter engine will provide a more robust, consistent, and extensible filtering system that can be used across all tables in the application.
