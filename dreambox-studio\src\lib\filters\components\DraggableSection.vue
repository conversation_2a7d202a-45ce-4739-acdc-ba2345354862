<template>
  <div class="draggable-section">
    <div class="draggable-section-header q-mb-sm">
      <div class="row items-center justify-between">
        <div class="draggable-section-title">{{ title }}</div>
        <q-btn
          flat
          dense
          round
          icon="add"
          @click="showColumnDialog = true"
        />
      </div>
    </div>

    <div
      class="draggable-section-content"
      :class="{ 'draggable-section-empty': !modelValue || modelValue.length === 0 }"
    >
      <draggable
        v-model="internalValue"
        group="columns"
        item-key="id"
        handle=".drag-handle"
        @change="handleDragChange"
      >
        <template #item="{ element }">
          <div class="draggable-item q-mb-xs">
            <div class="row items-center no-wrap">
              <q-icon name="drag_indicator" class="drag-handle cursor-move q-mr-xs" />
              <div class="col-grow">{{ element.name }}</div>
              <q-btn
                flat
                dense
                round
                size="sm"
                icon="close"
                @click="removeItem(element)"
              />
            </div>
          </div>
        </template>

        <template #header>
          <div v-if="!modelValue || modelValue.length === 0" class="draggable-section-placeholder">
            Drag columns here
          </div>
        </template>
      </draggable>
    </div>

    <!-- Column Selection Dialog -->
    <q-dialog v-model="showColumnDialog">
      <q-card style="min-width: 350px">
        <q-card-section>
          <div class="text-h6">Add Columns to {{ title }}</div>
        </q-card-section>

        <q-card-section>
          <q-list separator>
            <q-item
              v-for="column in availableColumns"
              :key="column.id"
              clickable
              @click="addColumn(column)"
            >
              <q-item-section>
                <q-item-label>{{ column.name }}</q-item-label>
              </q-item-section>

              <q-item-section side>
                <q-icon name="add" color="primary" />
              </q-item-section>
            </q-item>

            <q-item v-if="availableColumns.length === 0">
              <q-item-section class="text-grey">
                No columns available
              </q-item-section>
            </q-item>
          </q-list>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="Close" color="primary" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { VueDraggableNext as draggable } from 'vue-draggable-next';
import type { ColumnDefinition } from '../../../stores/filterStore';

// Props
const props = defineProps<{
  modelValue: ColumnDefinition[];
  title: string;
  allColumns: ColumnDefinition[];
  sectionId: string;
}>();

// Emits
const emit = defineEmits<{
  (e: 'update:modelValue', value: ColumnDefinition[]): void;
  (e: 'move-column', columnId: string, fromSection: string, toSection: string): void;
}>();

// State
const internalValue = ref<ColumnDefinition[]>([]);
const showColumnDialog = ref(false);

// Watch for external changes
watch(() => props.modelValue, (newValue) => {
  internalValue.value = newValue ? [...newValue] : [];
}, { immediate: true, deep: true });

// Watch for internal changes
watch(() => internalValue.value, (newValue) => {
  emit('update:modelValue', newValue);
}, { deep: true });

// Computed
const availableColumns = computed(() => {
  // Get all columns that are not already in this section
  return props.allColumns.filter(col => {
    return !internalValue.value.some((item: ColumnDefinition) => item.id === col.id);
  });
});

// Methods
function handleDragChange(event: {
  added?: { element: { id: string, section: string }, newIndex: number },
  removed?: { element: { id: string, section: string }, oldIndex: number },
  moved?: { element: { id: string, section: string }, oldIndex: number, newIndex: number }
}) {
  // Handle added items
  if (event.added) {
    const column = event.added.element;
    const fromSection = column.section;

    // Emit move event if the column came from another section
    if (fromSection !== props.sectionId) {
      emit('move-column', column.id, fromSection, props.sectionId);
    }
  }
}

function addColumn(column: ColumnDefinition) {
  // Add column to this section
  const fromSection = column.section;
  emit('move-column', column.id, fromSection, props.sectionId);

  // Close dialog
  showColumnDialog.value = false;
}

function removeItem(column: ColumnDefinition) {
  // Remove column from this section
  const index = internalValue.value.findIndex((item: ColumnDefinition) => item.id === column.id);

  if (index >= 0) {
    internalValue.value.splice(index, 1);

    // Move column to the other section
    const toSection = props.sectionId === 'main' ? 'expanded' : 'main';
    emit('move-column', column.id, props.sectionId, toSection);
  }
}
</script>

<style scoped>
.draggable-section {
  width: 100%;
}

.draggable-section-title {
  font-weight: 500;
  font-size: 0.9rem;
}

.draggable-section-content {
  min-height: 50px;
  border: 1px dashed #e0e0e0;
  border-radius: 4px;
  padding: 8px;
}

.draggable-section-empty {
  display: flex;
  align-items: center;
  justify-content: center;
}

.draggable-section-placeholder {
  color: #9e9e9e;
  font-style: italic;
  text-align: center;
  padding: 8px;
}

.draggable-item {
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 8px;
}

.drag-handle {
  cursor: move;
}

/* Dark mode styles */
.body--dark .draggable-section-content {
  border-color: #424242;
}

.body--dark .draggable-item {
  background-color: #333333;
}
</style>
