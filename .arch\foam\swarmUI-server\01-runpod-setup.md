# Step 1: RunPod Account Setup

This document outlines the process for setting up a RunPod account and configuring it for use with our application.

## 1.1 Create a RunPod Account

1. Go to [RunPod.io](https://www.runpod.io/) and click "Sign Up"
2. Complete the registration process with your business email
3. Verify your email address

## 1.2 Add Payment Method

1. Navigate to Account → Billing
2. Add a credit card or other payment method
3. Consider setting up spending limits to prevent unexpected charges

## 1.3 Generate API Keys

API keys are required to programmatically control RunPod instances from our application.

1. Navigate to Account → API Keys
2. Click "Create API Key"
3. Name the key (e.g., "Dreambox Studio SwarmUI")
4. Select the following permissions:
   - `pod:create`
   - `pod:read`
   - `pod:update`
   - `pod:delete`
   - `template:read`
5. Click "Create"
6. Copy the generated API key and store it securely (you won't be able to see it again)

## 1.4 Store API Key in Supabase

For security, we'll store the RunPod API key in Supabase rather than hardcoding it in our application.

1. In your Supabase project, navigate to SQL Editor
2. Create a table for API keys:

```sql
CREATE TABLE api_keys (
  id BIGSERIAL PRIMARY KEY,
  service TEXT NOT NULL,
  key_name TEXT NOT NULL,
  key_value TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(service, key_name)
);

-- Add RLS policies
ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;

-- Only super-admins can access API keys
CREATE POLICY "Super admins can access API keys" ON api_keys
  USING (EXISTS (
    SELECT 1 FROM user_roles_view
    WHERE user_id = auth.uid() AND role_name = 'super-admin'
  ));
```

3. Insert the RunPod API key:

```sql
INSERT INTO api_keys (service, key_name, key_value)
VALUES ('runpod', 'main', 'rpa_ENVUVX1256CB5WT9HZS53RC1S7BA4464KK6VTC1Kwlpeyj');
```

## 1.5 Create a Secure API Key Retrieval Function

Create a secure server-side function to retrieve the API key:

```sql
CREATE OR REPLACE FUNCTION get_api_key(service_name TEXT, key_name TEXT DEFAULT 'main')
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  key_value TEXT;
BEGIN
  -- Check if user has super-admin role
  IF NOT EXISTS (
    SELECT 1 FROM user_roles_view
    WHERE user_id = auth.uid() AND role_name = 'super-admin'
  ) THEN
    RAISE EXCEPTION 'Access denied: Only super-admins can retrieve API keys';
  END IF;
  
  -- Get the API key
  SELECT api_keys.key_value INTO key_value
  FROM api_keys
  WHERE service = service_name AND key_name = key_name;
  
  RETURN key_value;
END;
$$;
```

## 1.6 Test API Access

Before proceeding, verify that you can access the RunPod API with your key:

```bash
curl -X POST "https://api.runpod.io/graphql" \
  -H "Authorization: Bearer rpa_ENVUVX1256CB5WT9HZS53RC1S7BA4464KK6VTC1Kwlpeyj" \
  -H "Content-Type: application/json" \
  -d "{\"query\": \"query { myself { id } }\"}"
```

You should receive a JSON response with your account information.

## 1.7 Explore Available GPU Types

Take note of the GPU types available on RunPod for your region:

1. Navigate to [RunPod Secure Cloud](https://www.runpod.io/console/gpu-cloud)
2. Note the available GPU types and their pricing
3. For SwarmUI, we recommend at least:
   - GPU: NVIDIA RTX A4000 or better
   - RAM: 16GB or more
   - vCPUs: 4 or more
   - Disk: 50GB or more

## 1.8 Set Up Spending Alerts

To avoid unexpected costs:

1. Navigate to Account → Billing → Spending Alerts
2. Create alerts for different thresholds (e.g., $50, $100, $200)
3. Configure email notifications for these alerts

## Next Steps

Once your RunPod account is set up, proceed to [Step 2: SwarmUI Installation and Configuration](./02-swarmui-installation.md) to prepare the SwarmUI environment.
