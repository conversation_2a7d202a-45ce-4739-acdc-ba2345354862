// SwarmUI Server Management Edge Function with permissive CORS
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// Environment variables
const RUNPOD_API_KEY = Deno.env.get('RUNPOD_API_KEY') || ''
const TEMPLATE_ID = Deno.env.get('SWARMUI_TEMPLATE_ID') || ''

// Default GPU type if none specified
const DEFAULT_GPU_TYPE = 'NVIDIA RTX A5000'

// Extend Request type to include parsedBody
declare global {
  interface Request {
    parsedBody?: any;
  }
}

// CORS headers for all responses
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': '*',
  'Access-Control-Allow-Headers': '*',
  'Access-Control-Max-Age': '86400',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  // Create Supabase client
  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
  )

  // Get user from request
  const { data: { user } } = await supabaseClient.auth.getUser()
  if (!user) {
    return new Response(JSON.stringify({ error: 'Unauthorized' }), {
      status: 401,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }

  // Get action from body or URL
  let action: string | null = null;
  let body: any = {};
  
  try {
    if (req.headers.get('content-type')?.includes('application/json')) {
      body = await req.json();
      action = body.action;
      req.parsedBody = body;
    }
  } catch (e) {
    // If JSON parsing fails, try URL params
    console.error('Error parsing JSON body:', e);
  }
  
  // If action not in body, try URL params
  if (!action) {
    const url = new URL(req.url);
    action = url.searchParams.get('action');
  }

  // Handle different actions
  if (req.method === 'POST' && action === 'start-server') {
    // Start a new server
    const { gpuTypeId } = body;

    // Check if user already has a running server
    const { data: existingServers, error: existingError } = await supabaseClient
      .from('swarmui_servers')
      .select('*')
      .eq('user_id', user.id)
      .in('status', ['starting', 'running'])
      .order('created_at', { ascending: false })
      .limit(1)

    if (existingError) {
      return new Response(JSON.stringify({ error: existingError.message }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    if (existingServers.length > 0) {
      return new Response(JSON.stringify({ 
        message: 'Server is already running',
        serverId: existingServers[0].id,
        status: existingServers[0].status
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // Generate a secure API key for SwarmUI
    const apiKey = crypto.randomUUID()

    // Call RunPod API to start a pod
    try {
      const response = await fetch('https://api.runpod.io/v2/pods', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${RUNPOD_API_KEY}`
        },
        body: JSON.stringify({
          name: `SwarmUI-${user.id}`,
          templateId: TEMPLATE_ID,
          gpuCount: 1,
          gpuTypeId: gpuTypeId || DEFAULT_GPU_TYPE,
          containerDiskSizeGB: 50,
          volumeSizeGB: 100,
          env: {
            SWARMUI_API_KEY: apiKey
          }
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        return new Response(JSON.stringify({ 
          error: `RunPod API error: ${errorData.error || response.statusText}` 
        }), {
          status: response.status,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        })
      }

      const data = await response.json()

      // Store server info in database
      if (data.id) {
        const { data: serverData, error: insertError } = await supabaseClient
          .from('swarmui_servers')
          .insert({
            user_id: user.id,
            pod_id: data.id,
            api_endpoint: `https://${data.id}-7801.proxy.runpod.net/api`,
            api_key: apiKey,
            status: 'starting',
            gpu_type: gpuTypeId || DEFAULT_GPU_TYPE,
            started_at: new Date().toISOString(),
            last_activity: new Date().toISOString()
          })
          .select()

        if (insertError) {
          console.error('Error inserting server record:', insertError)
        }

        return new Response(JSON.stringify({
          serverId: serverData?.[0]?.id,
          status: 'starting',
          message: 'Server is starting'
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        })
      } else {
        return new Response(JSON.stringify({ 
          error: 'Failed to start server: No pod ID returned' 
        }), {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        })
      }
    } catch (error) {
      return new Response(JSON.stringify({ 
        error: `Failed to start server: ${error.message}` 
      }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
  } else if (req.method === 'DELETE' && action === 'stop-server') {
    // Stop a server
    const url = new URL(req.url);
    let podId = url.searchParams.get('podId');
    
    // If podId not in URL, try body
    if (!podId && body.podId) {
      podId = body.podId;
    }
    
    if (!podId) {
      return new Response(JSON.stringify({ error: 'Missing podId parameter' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // Call RunPod API to stop the pod
    try {
      const response = await fetch(`https://api.runpod.io/v2/pods/${podId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${RUNPOD_API_KEY}`
        }
      })

      if (!response.ok) {
        const errorData = await response.json()
        return new Response(JSON.stringify({ 
          error: `RunPod API error: ${errorData.error || response.statusText}` 
        }), {
          status: response.status,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        })
      }

      // Update server status in database
      const { error: updateError } = await supabaseClient
        .from('swarmui_servers')
        .update({ 
          status: 'stopping', 
          stopped_at: new Date().toISOString() 
        })
        .eq('pod_id', podId)
        .eq('user_id', user.id)

      if (updateError) {
        console.error('Error updating server record:', updateError)
      }

      return new Response(JSON.stringify({ 
        success: true,
        message: 'Server is stopping'
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    } catch (error) {
      return new Response(JSON.stringify({ 
        error: `Failed to stop server: ${error.message}` 
      }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
  } else if (req.method === 'GET' && action === 'server-status') {
    // Get server status
    try {
      // Get the most recent active server for the user
      const { data: servers, error } = await supabaseClient
        .from('swarmui_servers')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(1)

      if (error) {
        return new Response(JSON.stringify({ error: error.message }), {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        })
      }

      if (servers.length === 0) {
        return new Response(JSON.stringify({ status: 'no-server' }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        })
      }

      const server = servers[0]

      // If server is in a final state, just return its status
      if (['stopped', 'error'].includes(server.status)) {
        return new Response(JSON.stringify(server), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        })
      }

      // Check actual pod status from RunPod API
      const response = await fetch(`https://api.runpod.io/v2/pods/${server.pod_id}`, {
        headers: {
          'Authorization': `Bearer ${RUNPOD_API_KEY}`
        }
      })

      if (!response.ok) {
        // If pod not found, mark as stopped
        if (response.status === 404) {
          const { error: updateError } = await supabaseClient
            .from('swarmui_servers')
            .update({ 
              status: 'stopped', 
              stopped_at: new Date().toISOString() 
            })
            .eq('id', server.id)
          
          server.status = 'stopped'
          
          return new Response(JSON.stringify(server), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          })
        }
        
        return new Response(JSON.stringify({ 
          error: `RunPod API error: ${response.statusText}` 
        }), {
          status: response.status,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        })
      }

      const podData = await response.json()

      // Map RunPod status to our status
      let mappedStatus = server.status
      if (podData.status === 'RUNNING') {
        mappedStatus = 'running'
      } else if (podData.status === 'PENDING' || podData.status === 'INITIALIZING') {
        mappedStatus = 'starting'
      } else if (podData.status === 'STOPPING' || podData.status === 'TERMINATED') {
        mappedStatus = 'stopping'
      } else if (podData.status === 'EXITED') {
        mappedStatus = 'stopped'
      }

      // Update server status if needed
      if (mappedStatus !== server.status) {
        const { error: updateError } = await supabaseClient
          .from('swarmui_servers')
          .update({ status: mappedStatus })
          .eq('id', server.id)
        
        if (updateError) {
          console.error('Error updating server status:', updateError)
        }
        
        server.status = mappedStatus
      }

      return new Response(JSON.stringify(server), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    } catch (error) {
      return new Response(JSON.stringify({ 
        error: `Failed to get server status: ${error.message}` 
      }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
  }

  return new Response(JSON.stringify({ error: 'Invalid action' }), {
    status: 400,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  })
})
