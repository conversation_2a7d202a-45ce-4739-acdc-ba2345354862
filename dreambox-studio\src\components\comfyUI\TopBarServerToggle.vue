<template>
  <div class="top-bar-server-toggle">
    <!-- Server Toggle Component -->
    <div class="server-toggle-container">
      <q-btn
        :color="offButtonColor"
        :text-color="offButtonTextColor"
        label="OFF"
        @click="handleOffClick"
        dense
        no-caps
        unelevated
        class="toggle-btn toggle-btn-off"
        :loading="isTerminating"
        :disable="isCreating"
      >
        <q-tooltip>{{ tooltipText }}</q-tooltip>
      </q-btn>
      <q-btn
        :color="onButtonColor"
        :text-color="onButtonTextColor"
        label="ON"
        @click="handleOnClick"
        dense
        no-caps
        unelevated
        class="toggle-btn toggle-btn-on"
        :loading="isCreating"
        :disable="isTerminating || comfyUIStore.serverExists"
      >
        <q-tooltip>{{ tooltipText }}</q-tooltip>
      </q-btn>
    </div>

    <!-- Auto-termination countdown timer -->
    <div v-if="comfyUIStore.shouldShowCountdown" class="countdown-timer q-ml-xs">
      <q-circular-progress
        :value="comfyUIStore.terminationProgress"
        size="30px"
        :thickness="0.5"
        color="negative"
        track-color="grey-3"
        class="countdown-progress"
        show-value
        font-size="9px"
        :min="0"
        :max="1"
      >
        {{ Math.floor(comfyUIStore.timeUntilTermination || 0) }}
      </q-circular-progress>
      <q-tooltip>
        Auto-termination in {{ Math.floor(comfyUIStore.timeUntilTermination || 0) }} minutes
        <br>Progress: {{ Math.round((comfyUIStore.terminationProgress || 0) * 100) }}% elapsed
        <br>Raw Value: {{ (comfyUIStore.terminationProgress || 0).toFixed(3) }}
        <br>Time left: {{ comfyUIStore.timeUntilTermination?.toFixed(1) }} min
        <br>Max time: {{ comfyUIStore.autoTerminationMinutes }} min
      </q-tooltip>
    </div>

    <!-- Create server dialog -->
    <q-dialog v-model="showCreateDialog" persistent>
      <q-card style="min-width: 350px">
        <q-card-section>
          <div class="text-h6">{{ $t('comfyUI.createComfyUIServer') }}</div>
        </q-card-section>

        <q-card-section>
          <q-select
            v-model="comfyUIStore.selectedGpu"
            :options="comfyUIStore.gpuOptions"
            :label="$t('comfyUI.selectGpu')"
            filled
            emit-value
            map-options
          />
        </q-card-section>

        <q-card-section v-if="comfyUIStore.errorMessage" class="text-negative">
          {{ comfyUIStore.errorMessage }}
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="$t('comfyUI.cancel')" color="primary" v-close-popup />
          <q-btn
            :label="$t('comfyUI.create')"
            color="primary"
            @click="createServer"
            :loading="comfyUIStore.isCreating"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import { useComfyUIStore } from 'src/stores/comfyUIStore';
import { notificationService } from 'src/services/notificationService';

const $q = useQuasar();
const { t } = useI18n();
const comfyUIStore = useComfyUIStore();

// Local state
const showCreateDialog = ref(false);
let statusCheckInterval: number | null = null;

// Tooltip text with server status information
const tooltipText = computed(() => {
  return `Status: ${comfyUIStore.serverStatus.status} | Connected: ${comfyUIStore.isConnected} | Reload: ${comfyUIStore.reloadBehavior} | Auto-stop: ${comfyUIStore.autoTerminationMinutes}min`;
});

// Debug countdown values
const debugCountdown = computed(() => {
  return {
    shouldShow: comfyUIStore.shouldShowCountdown,
    timeLeft: comfyUIStore.timeUntilTermination,
    progress: comfyUIStore.terminationProgress,
    serverRunning: comfyUIStore.isServerRunning,
    hasTimer: comfyUIStore.autoTerminationTimer !== null,
    hasActivity: comfyUIStore.lastActivity !== null
  };
});

// Log debug info when values change
watch(debugCountdown, (newVal) => {
  console.log('🔍 Countdown debug:', newVal);
}, { deep: true });

// Computed properties for button colors based on server state
const offButtonColor = computed(() => {
  if (comfyUIStore.serverExists) {
    return 'negative'; // Red when server is running (ready to terminate)
  }
  return 'grey-4'; // Grey when no server
});

const offButtonTextColor = computed(() => {
  if (comfyUIStore.serverExists) {
    return 'white';
  }
  return 'grey-8';
});

const onButtonColor = computed(() => {
  switch (comfyUIStore.toggleState) {
    case 'creating':
      return 'orange'; // Orange when pod is created
    case 'starting-comfyui':
      return 'orange'; // Orange when ComfyUI is starting
    case 'ready':
      return 'positive'; // Green when ComfyUI is ready
    case 'stopping':
      return 'warning'; // Warning color when stopping
    default:
      return 'primary'; // Blue when no server (default state)
  }
});

const onButtonTextColor = computed(() => {
  return comfyUIStore.serverExists ? 'white' : 'white';
});

// Computed properties for loading states
const isCreating = computed(() => comfyUIStore.isCreating);
const isTerminating = computed(() => comfyUIStore.isTerminating);

// Methods
function handleOffClick() {
  if (comfyUIStore.serverExists) {
    confirmTerminate();
  }
  // Do nothing if no server exists
}

function handleOnClick() {
  if (!comfyUIStore.serverExists) {
    showCreateServerDialog();
  }
  // Do nothing if server already exists
}

function showCreateServerDialog() {
  comfyUIStore.clearError();
  showCreateDialog.value = true;
}

function confirmTerminate() {
  $q.dialog({
    title: t('common.confirm'),
    message: t('comfyUI.terminateServerConfirm'),
    color: 'negative',
    persistent: true,
    ok: {
      label: t('common.confirm'),
      color: 'negative'
    },
    cancel: {
      label: t('common.cancel'),
      color: 'primary'
    }
  }).onOk(() => {
    void terminateServer();
  });
}

async function createServer() {
  // Record user activity when creating server
  comfyUIStore.recordServerActivity();

  const success = await comfyUIStore.startServer();

  if (success) {
    // Use notification service to respect user settings
    await notificationService.notify({
      color: 'positive',
      message: t('comfyUI.serverCreating'),
      icon: 'cloud_queue'
    });
    showCreateDialog.value = false;

    // Start checking status more frequently after creation
    setTimeout(() => {
      void comfyUIStore.updateServerStatus();
    }, 2000);
    setTimeout(() => {
      void comfyUIStore.updateServerStatus();
    }, 5000);
  }
  // Error handling is done in the store
}

async function terminateServer() {
  // Record user activity when terminating server
  comfyUIStore.recordServerActivity();

  const success = await comfyUIStore.terminateServer();

  if (success) {
    // Use notification service to respect user settings
    await notificationService.notify({
      color: 'info',
      message: t('comfyUI.serverTerminating'),
      icon: 'delete'
    });
  } else {
    // Always show error notifications
    $q.notify({
      color: 'negative',
      message: t('comfyUI.errorTerminatingServer'),
      icon: 'error'
    });
  }
}

// Lifecycle
onMounted(() => {
  // Initialize the store (settings will load asynchronously)
  void comfyUIStore.initialize();

  // Wait a moment for initial status check, then check existing connection
  setTimeout(() => {
    void comfyUIStore.checkExistingConnection();
  }, 2000);

  // Start periodic status checks - more frequent to catch state changes faster
  statusCheckInterval = window.setInterval(() => {
    void comfyUIStore.updateServerStatus();
  }, 5000); // Check every 5 seconds for better responsiveness
});

onUnmounted(() => {
  if (statusCheckInterval) {
    clearInterval(statusCheckInterval);
  }
});
</script>

<style lang="scss" scoped>
.top-bar-server-toggle {
  display: flex;
  align-items: center;

  .server-toggle-container {
    display: flex;
    border-radius: 4px;
    overflow: hidden;

    .toggle-btn {
      min-width: 35px;
      border-radius: 0;
      font-size: 11px;
      padding: 4px 8px;

      &.toggle-btn-off {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
      }

      &.toggle-btn-on {
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
      }
    }
  }

  .countdown-timer {
    display: flex;
    align-items: center;

    .countdown-progress {
      // Let Quasar handle the thickness naturally
      :deep(.q-circular-progress__text) {
        fill: #424242 !important;
        font-weight: bold !important;
        font-size: 9px !important;
      }
    }
  }
}
</style>
