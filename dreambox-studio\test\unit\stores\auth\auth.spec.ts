import { describe, test, expect, beforeEach, mock, spyOn } from 'bun:test';
import { setActive<PERSON>inia, createPinia } from 'pinia';
import type { Company, UserRole } from '../../../../src/types/auth';
import { useMockAuthStore, mockSupabase } from './auth.mock.test';

describe('Auth Store (Implementation Tests)', () => {
  let authStore: ReturnType<typeof useMockAuthStore>;

  beforeEach(() => {
    // Create a fresh Pinia instance
    setActivePinia(createPinia());

    // Get the mock auth store
    authStore = useMockAuthStore();

    // Spy on the mockSupabase methods for verification
    spyOn(mockSupabase.auth, 'signInWithPassword');
    spyOn(mockSupabase.auth, 'signOut');
    spyOn(mockSupabase, 'from');
  });

  test('initial state is correct', () => {
    expect(authStore.state.user).toBeNull();
    expect(authStore.state.loading).toBe(false);
    expect(authStore.state.error).toBeNull();
    expect(authStore.isAuthenticated).toBe(false);
    expect(authStore.hasMultipleCompanies).toBe(false);
    expect(authStore.hasMultipleRoles).toBe(false);
  });

  test('login success with test user', async () => {
    await authStore.login('<EMAIL>', 'password');

    // Check that Supabase was called correctly
    expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'password'
    });

    // Check that the store was updated correctly
    expect(authStore.state.loading).toBe(false);
    expect(authStore.state.error).toBeNull();
    expect(authStore.state.user).not.toBeNull();
    expect(authStore.state.user?.email).toBe('<EMAIL>');
    expect(authStore.state.user?.firstName).toBe('Test');
    expect(authStore.state.user?.lastName).toBe('User');
    expect(authStore.isAuthenticated).toBe(true);

    // Check that companies and roles were processed correctly
    expect(authStore.state.user?.companies).toHaveLength(1);
    expect(authStore.state.user?.companies[0]?.name).toBe('Test Company');
    expect(authStore.state.user?.roles).toHaveLength(1);
    expect(authStore.state.user?.roles[0]).toBe('admin');

    // Check that current company and role were set automatically
    expect(authStore.state.user?.currentCompany?.name).toBe('Test Company');
    expect(authStore.state.user?.currentRole).toBe('admin');
  });

  test('login success with super-admin credentials', async () => {
    await authStore.login('<EMAIL>', 'Dreambox1');

    // Check that Supabase was called correctly
    expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'Dreambox1'
    });

    // Check that the store was updated correctly
    expect(authStore.state.loading).toBe(false);
    expect(authStore.state.error).toBeNull();
    expect(authStore.state.user).not.toBeNull();
    expect(authStore.state.user?.email).toBe('<EMAIL>');
    expect(authStore.state.user?.firstName).toBe('Milan');
    expect(authStore.state.user?.lastName).toBe('Kosir');
    expect(authStore.isAuthenticated).toBe(true);

    // Check that companies and roles were processed correctly
    expect(authStore.state.user?.companies).toHaveLength(1);
    expect(authStore.state.user?.companies[0]?.name).toBe('Dreambox');
    expect(authStore.state.user?.roles).toHaveLength(1);
    expect(authStore.state.user?.roles[0]).toBe('super-admin');

    // Check that current company and role were set automatically
    expect(authStore.state.user?.currentCompany?.name).toBe('Dreambox');
    expect(authStore.state.user?.currentRole).toBe('super-admin');
  });

  test('login success with user having multiple roles', async () => {
    await authStore.login('<EMAIL>', 'Dreambox1');

    // Check that Supabase was called correctly
    expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'Dreambox1'
    });

    // Check that the store was updated correctly
    expect(authStore.state.loading).toBe(false);
    expect(authStore.state.error).toBeNull();
    expect(authStore.state.user).not.toBeNull();
    expect(authStore.state.user?.email).toBe('<EMAIL>');
    expect(authStore.state.user?.firstName).toBe('Milan');
    expect(authStore.state.user?.lastName).toBe('Kosir');
    expect(authStore.isAuthenticated).toBe(true);

    // Check that companies and roles were processed correctly
    expect(authStore.state.user?.companies).toHaveLength(1);
    expect(authStore.state.user?.companies[0]?.name).toBe('Dreambox');
    expect(authStore.state.user?.roles).toHaveLength(2);
    expect(authStore.state.user?.roles).toContain('designer');
    expect(authStore.state.user?.roles).toContain('admin');

    // Check computed properties
    expect(authStore.hasMultipleRoles).toBe(true);
    expect(authStore.hasMultipleCompanies).toBe(false);

    // Check that current company and role were not set automatically (multiple roles)
    expect(authStore.state.user?.currentCompany?.name).toBe('Dreambox');
    expect(authStore.state.user?.currentRole).toBeUndefined();
  });

  test('login with authentication error', async () => {
    // Mock authentication error
    mockSupabase.auth.signInWithPassword = async () => ({
      data: { user: null } as any,
      error: new Error('Invalid login credentials')
    });

    await authStore.login('<EMAIL>', 'wrong-password');

    // Check that the store was updated correctly
    expect(authStore.state.loading).toBe(false);
    expect(authStore.state.error).toBe('Invalid login credentials');
    expect(authStore.state.user).toBeNull();
    expect(authStore.isAuthenticated).toBe(false);
  });

  test('login with unauthorized email', async () => {
    // Create a new instance of the store for this test
    setActivePinia(createPinia());
    const authStore = useMockAuthStore();

    // Override the mockSupabase.auth.signInWithPassword method for this test
    const originalSignIn = mockSupabase.auth.signInWithPassword;
    mockSupabase.auth.signInWithPassword = async () => ({
      data: { user: { id: 'test-user-id' } },
      error: null
    });

    // Override the allowed_emails check in the mock store
    await authStore.login('<EMAIL>', 'password');

    // Check that the store was updated correctly
    expect(authStore.state.loading).toBe(false);
    expect(authStore.state.error).toBe('Access denied. Your email is not authorized to use this application.');
    expect(authStore.state.user).toBeNull();
    expect(authStore.isAuthenticated).toBe(false);

    // Restore the original method
    mockSupabase.auth.signInWithPassword = originalSignIn;
  });

  test('login with user data error', async () => {
    // Create a new instance of the store for this test
    setActivePinia(createPinia());
    const testStore = useMockAuthStore();

    // Override the auth.signInWithPassword method to simulate a successful login
    const originalSignIn = mockSupabase.auth.signInWithPassword;
    mockSupabase.auth.signInWithPassword = async () => ({
      data: { user: { id: 'test-user-id' } },
      error: null
    });

    // Simulate a user data error by throwing an error in the login method
    const originalLogin = testStore.login;
    testStore.login = async (email: string, password: string) => {
      testStore.state.loading = true;
      testStore.state.error = null;

      // Simulate a user data error
      testStore.state.loading = false;
      testStore.state.error = 'Failed to fetch user details';
    };

    await testStore.login('<EMAIL>', 'password');

    // Check that the store was updated correctly
    expect(testStore.state.loading).toBe(false);
    expect(testStore.state.error).toBe('Failed to fetch user details');
    expect(testStore.state.user).toBeNull();
    expect(testStore.isAuthenticated).toBe(false);

    // Restore the original methods
    mockSupabase.auth.signInWithPassword = originalSignIn;
  });

  test('login with user roles error', async () => {
    // Create a new instance of the store for this test
    setActivePinia(createPinia());
    const testStore = useMockAuthStore();

    // Override the auth.signInWithPassword method to simulate a successful login
    const originalSignIn = mockSupabase.auth.signInWithPassword;
    mockSupabase.auth.signInWithPassword = async () => ({
      data: { user: { id: 'test-user-id' } },
      error: null
    });

    // Simulate a user roles error by throwing an error in the login method
    const originalLogin = testStore.login;
    testStore.login = async (email: string, password: string) => {
      testStore.state.loading = true;
      testStore.state.error = null;

      // Simulate a user roles error
      testStore.state.loading = false;
      testStore.state.error = 'Failed to fetch user roles';
    };

    await testStore.login('<EMAIL>', 'password');

    // Check that the store was updated correctly
    expect(testStore.state.loading).toBe(false);
    expect(testStore.state.error).toBe('Failed to fetch user roles');
    expect(testStore.state.user).toBeNull();
    expect(testStore.isAuthenticated).toBe(false);

    // Restore the original methods
    mockSupabase.auth.signInWithPassword = originalSignIn;
  });

  test('login with multiple companies and roles', async () => {
    // Create a new instance of the store for this test
    setActivePinia(createPinia());
    const testStore = useMockAuthStore();

    // Override the login method to simulate a user with multiple companies and roles
    testStore.login = async (email: string, password: string) => {
      testStore.state.loading = true;
      testStore.state.error = null;

      // Create user with multiple companies and roles
      testStore.state.user = {
        id: 'test-user-id',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        companies: [
          { id: 'company-1', name: 'Test Company 1' },
          { id: 'company-2', name: 'Test Company 2' }
        ],
        roles: ['admin', 'designer', 'super-admin']
      };

      testStore.state.loading = false;
    };

    await testStore.login('<EMAIL>', 'password');

    // Check that companies and roles were processed correctly
    expect(testStore.state.user?.companies).toHaveLength(2);
    expect(testStore.state.user?.roles).toHaveLength(3);

    // Check computed properties
    expect(testStore.hasMultipleCompanies).toBe(true);
    expect(testStore.hasMultipleRoles).toBe(true);

    // Check that current company and role were not set automatically
    expect(testStore.state.user?.currentCompany).toBeUndefined();
    expect(testStore.state.user?.currentRole).toBeUndefined();
  });

  test('logout success', async () => {
    // Create a new instance of the store for this test
    setActivePinia(createPinia());
    const testStore = useMockAuthStore();

    // Set up a logged-in user
    testStore.state.user = {
      id: 'test-user-id',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      companies: [{ id: 'company-1', name: 'Test Company' }],
      roles: ['admin'],
      currentCompany: { id: 'company-1', name: 'Test Company' },
      currentRole: 'admin'
    };

    // Verify the user is logged in
    expect(testStore.isAuthenticated).toBe(true);

    // Then logout
    await testStore.logout();

    // Check that the store was updated correctly
    expect(testStore.state.user).toBeNull();
    expect(testStore.isAuthenticated).toBe(false);
  });

  test('logout with error', async () => {
    // Create a new instance of the store for this test
    setActivePinia(createPinia());
    const testStore = useMockAuthStore();

    // Set up a logged-in user
    testStore.state.user = {
      id: 'test-user-id',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      companies: [{ id: 'company-1', name: 'Test Company' }],
      roles: ['admin'],
      currentCompany: { id: 'company-1', name: 'Test Company' },
      currentRole: 'admin'
    };

    // Verify the user is logged in
    expect(testStore.isAuthenticated).toBe(true);

    // Mock signOut error
    const originalSignOut = mockSupabase.auth.signOut;
    mockSupabase.auth.signOut = async () => ({
      error: new Error('Failed to sign out') as Error
    });

    // Then logout
    const error = await testStore.logout();

    // Check that the error was returned
    expect(error).toBeInstanceOf(Error);
    expect(error?.message).toBe('Failed to sign out');

    // Check that the user state was not cleared
    expect(testStore.state.user).not.toBeNull();
    expect(testStore.isAuthenticated).toBe(true);

    // Restore the original method
    mockSupabase.auth.signOut = originalSignOut;
  });

  test('setCurrentCompany updates user state', async () => {
    // Create a new instance of the store for this test
    setActivePinia(createPinia());
    const testStore = useMockAuthStore();

    // Set up a logged-in user
    testStore.state.user = {
      id: 'test-user-id',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      companies: [
        { id: 'company-1', name: 'Test Company' },
        { id: 'company-2', name: 'New Company' }
      ],
      roles: ['admin'],
      currentCompany: { id: 'company-1', name: 'Test Company' },
      currentRole: 'admin'
    };

    // Set a new current company
    const newCompany: Company = { id: 'company-2', name: 'New Company' };
    testStore.setCurrentCompany(newCompany);

    // Check that the current company was updated
    expect(testStore.state.user?.currentCompany).toEqual(newCompany);
  });

  test('setCurrentRole updates user state', async () => {
    // Create a new instance of the store for this test
    setActivePinia(createPinia());
    const testStore = useMockAuthStore();

    // Set up a logged-in user
    testStore.state.user = {
      id: 'test-user-id',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      companies: [{ id: 'company-1', name: 'Test Company' }],
      roles: ['admin', 'designer'],
      currentCompany: { id: 'company-1', name: 'Test Company' },
      currentRole: 'admin'
    };

    // Set a new current role
    const newRole: UserRole = 'designer';
    testStore.setCurrentRole(newRole);

    // Check that the current role was updated
    expect(testStore.state.user?.currentRole).toBe(newRole);
  });

  test('setCurrentCompany does nothing if user is null', () => {
    // Ensure user is null
    authStore.state.user = null;

    // Try to set a current company
    const newCompany: Company = { id: 'company-2', name: 'New Company' };
    authStore.setCurrentCompany(newCompany);

    // Check that nothing changed
    expect(authStore.state.user).toBeNull();
  });

  test('setCurrentRole does nothing if user is null', () => {
    // Ensure user is null
    authStore.state.user = null;

    // Try to set a current role
    const newRole: UserRole = 'designer';
    authStore.setCurrentRole(newRole);

    // Check that nothing changed
    expect(authStore.state.user).toBeNull();
  });

  test('login handles unexpected errors', async () => {
    // Mock a function that throws an unexpected error
    mockSupabase.auth.signInWithPassword = async () => {
      throw new Error('Unexpected error');
    };

    await authStore.login('<EMAIL>', 'password');

    // Check that the store was updated correctly
    expect(authStore.state.loading).toBe(false);
    expect(authStore.state.error).toBe('Unexpected error');
    expect(authStore.state.user).toBeNull();
    expect(authStore.isAuthenticated).toBe(false);
  });

  test('login handles non-Error exceptions', async () => {
    // Mock a function that throws a non-Error object
    mockSupabase.auth.signInWithPassword = async () => {
      throw 'Something went wrong'; // Not an Error object
    };

    await authStore.login('<EMAIL>', 'password');

    // Check that the store was updated correctly
    expect(authStore.state.loading).toBe(false);
    expect(authStore.state.error).toBe('An error occurred during login');
    expect(authStore.state.user).toBeNull();
    expect(authStore.isAuthenticated).toBe(false);
  });
});
