<template>
  <div
    v-if="!isMobile"
    v-touch-pan.preserveCursor.prevent.mouse.horizontal="handleResize"
    class="drawer-resizer"
    :class="{ 'drawer-resizer--right': side === 'right' }"
  ></div>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';
import { computed } from 'vue';

const $q = useQuasar();

// Check if we're on a mobile device
const isMobile = computed(() => $q.platform.is.mobile);

const props = defineProps({
  side: {
    type: String,
    default: 'left',
    validator: (value: string) => ['left', 'right'].includes(value)
  },
  minWidth: {
    type: Number,
    default: 200
  },
  maxWidth: {
    type: Number,
    default: 500
  }
});

const emit = defineEmits(['resize']);

// Define the type for Quasar's TouchPanDetails
interface TouchPanDetails {
  evt?: Event;
  touch?: boolean;
  mouse?: boolean;
  position?: { top?: number; left?: number };
  direction?: 'left' | 'right' | 'up' | 'down';
  isFirst?: boolean;
  isFinal?: boolean;
  duration?: number;
  distance?: { x?: number; y?: number };
  offset?: { x?: number; y?: number };
  delta?: { x?: number; y?: number };
}

function handleResize(details: TouchPanDetails) {
  // Make sure delta.x exists before using it
  if (details.delta?.x !== undefined) {
    const delta = props.side === 'left' ? details.delta.x : -details.delta.x;
    emit('resize', delta);
  }
}
</script>

<style lang="scss">
.drawer-resizer {
  width: 4px;
  background-color: transparent;
  cursor: ew-resize;
  z-index: 10;
  transition: background-color 0.3s;

  &:hover {
    background-color: var(--q-primary);
  }

  &:active {
    background-color: var(--q-primary);
  }

  // Visual indicator in the middle
  &:after {
    content: '';
    position: absolute;
    top: 50%;
    height: 30px;
    width: 4px;
    transform: translateY(-50%);
    background-color: var(--q-primary);
    opacity: 0.7;
    border-radius: 4px;
  }
}
</style>
