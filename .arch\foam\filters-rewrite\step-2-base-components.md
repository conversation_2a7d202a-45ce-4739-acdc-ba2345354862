# Step 2: Base Components

## Overview
In this step, we'll implement the base UI components that will form the foundation of our filter system. These components will be reusable across all tables and provide a consistent user experience.

## Tasks

### 2.1 Create the FilterPanel Component

The FilterPanel will be the main container for all filter-related UI elements.

```typescript
// src/components/filters/FilterPanel.vue
<template>
  <div class="filter-panel">
    <!-- Saved Filter Selector -->
    <saved-filter-selector
      :filters="filterStore.filtersByTable"
      :current-filter-id="filterStore.currentFilterId"
      :has-unsaved-changes="filterStore.hasUnsavedChanges"
      @select="loadFilter"
      @save="saveFilter"
      @delete="deleteFilter"
      @clear="clearAllFilters"
    />

    <!-- Main Filters Section -->
    <filter-section
      title="Main Filters"
      :columns="filterStore.mainTableColumns"
      @update-filter="updateColumnFilter"
      @clear-filter="clearColumnFilter"
      @toggle-visibility="toggleColumnVisibility"
    />

    <!-- Expanded Filters Section -->
    <filter-section
      title="Expanded Filters"
      :columns="filterStore.expandedViewColumns"
      :collapsible="true"
      :default-collapsed="true"
      @update-filter="updateColumnFilter"
      @clear-filter="clearColumnFilter"
      @toggle-visibility="toggleColumnVisibility"
    />

    <!-- Specialized Filters Section -->
    <div v-for="filterType in filterStore.specializedFilterTypes" :key="filterType.id">
      <component
        :is="filterType.component"
        :filter-type="filterType"
        :filter-value="filterStore.specializedFilters[filterType.id]"
        @update="updateSpecializedFilter(filterType.id, $event)"
        @clear="clearSpecializedFilter(filterType.id)"
      />
    </div>

    <!-- Filter Actions -->
    <filter-action-bar
      :has-unsaved-changes="filterStore.hasUnsavedChanges"
      :has-active-filters="hasActiveFilters"
      @save="showSaveFilterDialog"
      @clear="clearAllFilters"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useFilterStore } from '@/stores/filterStore';
import SavedFilterSelector from './SavedFilterSelector.vue';
import FilterSection from './FilterSection.vue';
import FilterActionBar from './FilterActionBar.vue';
import { filterEventBus } from '@/lib/filters/FilterEventBus';

// Store
const filterStore = useFilterStore();

// Computed
const hasActiveFilters = computed(() => {
  return Object.keys(filterStore.activeFilters).length > 0 || 
         Object.keys(filterStore.specializedFilters).length > 0;
});

// Save filter dialog
const saveFilterDialog = ref(false);
const newFilterName = ref('');
const newFilterDescription = ref('');
const makeDefault = ref(false);

// Methods
function loadFilter(filterId: number) {
  filterStore.loadFilter(filterId);
  filterEventBus.emit(FilterEventBus.EVENTS.FILTER_LOADED, {
    filterId,
    tableName: filterStore.currentTable
  });
}

function saveFilter() {
  if (!newFilterName.value) return;
  
  filterStore.saveCurrentFilter(
    newFilterName.value,
    newFilterDescription.value,
    makeDefault.value
  );
  
  // Reset form
  saveFilterDialog.value = false;
  newFilterName.value = '';
  newFilterDescription.value = '';
  makeDefault.value = false;
  
  filterEventBus.emit(FilterEventBus.EVENTS.FILTER_SAVED, {
    filterId: filterStore.currentFilterId,
    tableName: filterStore.currentTable
  });
}

function deleteFilter(filterId: number) {
  filterStore.deleteFilter(filterId);
  filterEventBus.emit(FilterEventBus.EVENTS.FILTER_DELETED, {
    filterId,
    tableName: filterStore.currentTable
  });
}

function showSaveFilterDialog() {
  // If editing existing filter, pre-fill the form
  if (filterStore.currentFilterId) {
    const currentFilter = filterStore.currentFilter;
    if (currentFilter) {
      newFilterName.value = currentFilter.name;
      newFilterDescription.value = currentFilter.description || '';
      makeDefault.value = currentFilter.is_default;
    }
  } else {
    newFilterName.value = '';
    newFilterDescription.value = '';
    makeDefault.value = false;
  }
  
  saveFilterDialog.value = true;
}

function updateColumnFilter(columnId: string, value: any) {
  filterStore.setColumnFilter(columnId, value);
  filterEventBus.emit(FilterEventBus.EVENTS.FILTER_CHANGED, {
    filterId: columnId,
    value,
    tableName: filterStore.currentTable
  });
}

function clearColumnFilter(columnId: string) {
  filterStore.clearFilter(columnId);
  filterEventBus.emit(FilterEventBus.EVENTS.FILTER_CLEARED, {
    filterId: columnId,
    tableName: filterStore.currentTable
  });
}

function updateSpecializedFilter(filterId: string, value: any) {
  filterStore.setSpecializedFilter(filterId, value);
  filterEventBus.emit(FilterEventBus.EVENTS.SPECIALIZED_FILTER_CHANGED, {
    filterId,
    value,
    tableName: filterStore.currentTable
  });
}

function clearSpecializedFilter(filterId: string) {
  filterStore.clearSpecializedFilter(filterId);
  filterEventBus.emit(FilterEventBus.EVENTS.SPECIALIZED_FILTER_CLEARED, {
    filterId,
    tableName: filterStore.currentTable
  });
}

function clearAllFilters() {
  filterStore.clearAllFilters();
  filterEventBus.emit(FilterEventBus.EVENTS.FILTER_CLEARED, {
    tableName: filterStore.currentTable
  });
}

function toggleColumnVisibility(columnId: string, visible: boolean) {
  filterStore.toggleColumnVisibility(columnId, visible);
}
</script>

<style lang="scss" scoped>
.filter-panel {
  padding: 16px;
  
  > * {
    margin-bottom: 16px;
  }
}
</style>
```

### 2.2 Implement the SavedFilterSelector Component

This component will allow users to select, save, and manage their filters.

```typescript
// src/components/filters/SavedFilterSelector.vue
<template>
  <div class="saved-filter-selector">
    <div class="row items-center q-col-gutter-sm">
      <div class="col">
        <q-select
          v-model="selectedFilter"
          :options="filters"
          option-label="name"
          option-value="id"
          outlined
          dense
          emit-value
          map-options
          label="Saved Filters"
          :loading="loading"
          :disable="loading"
          @update:model-value="onFilterSelected"
        >
          <template v-slot:option="scope">
            <q-item v-bind="scope.itemProps">
              <q-item-section>
                <q-item-label>{{ scope.opt.name }}</q-item-label>
                <q-item-label caption v-if="scope.opt.description">
                  {{ scope.opt.description }}
                </q-item-label>
              </q-item-section>
              <q-item-section side v-if="scope.opt.is_default">
                <q-badge color="primary" label="Default" />
              </q-item-section>
            </q-item>
          </template>
          
          <template v-slot:no-option>
            <q-item>
              <q-item-section class="text-grey">
                No saved filters
              </q-item-section>
            </q-item>
          </template>
          
          <template v-slot:after>
            <q-btn
              flat
              round
              dense
              icon="save"
              color="primary"
              :disable="!hasUnsavedChanges"
              @click.stop="$emit('save')"
            >
              <q-tooltip>Save filter</q-tooltip>
            </q-btn>
            
            <q-btn
              flat
              round
              dense
              icon="delete"
              color="negative"
              :disable="!selectedFilter"
              @click.stop="confirmDelete"
            >
              <q-tooltip>Delete filter</q-tooltip>
            </q-btn>
            
            <q-btn
              flat
              round
              dense
              icon="clear_all"
              color="grey-7"
              @click.stop="$emit('clear')"
            >
              <q-tooltip>Clear all filters</q-tooltip>
            </q-btn>
          </template>
        </q-select>
      </div>
    </div>
    
    <!-- Delete confirmation dialog -->
    <q-dialog v-model="deleteDialog" persistent>
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar icon="delete" color="negative" text-color="white" />
          <span class="q-ml-sm">Are you sure you want to delete this filter?</span>
        </q-card-section>
        
        <q-card-actions align="right">
          <q-btn flat label="Cancel" color="primary" v-close-popup />
          <q-btn flat label="Delete" color="negative" @click="deleteFilter" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import type { SavedFilter } from '@/stores/filterStore';

// Props
const props = defineProps<{
  filters: SavedFilter[];
  currentFilterId: number | null;
  hasUnsavedChanges: boolean;
  loading?: boolean;
}>();

// Emits
const emit = defineEmits<{
  (e: 'select', filterId: number): void;
  (e: 'save'): void;
  (e: 'delete', filterId: number): void;
  (e: 'clear'): void;
}>();

// State
const selectedFilter = ref<number | null>(props.currentFilterId);
const deleteDialog = ref(false);
const filterToDelete = ref<number | null>(null);

// Watch for changes in currentFilterId
watch(() => props.currentFilterId, (newId) => {
  selectedFilter.value = newId;
});

// Methods
function onFilterSelected(filterId: number) {
  if (filterId !== props.currentFilterId) {
    emit('select', filterId);
  }
}

function confirmDelete() {
  if (selectedFilter.value) {
    filterToDelete.value = selectedFilter.value;
    deleteDialog.value = true;
  }
}

function deleteFilter() {
  if (filterToDelete.value) {
    emit('delete', filterToDelete.value);
    filterToDelete.value = null;
  }
}
</script>

<style lang="scss" scoped>
.saved-filter-selector {
  margin-bottom: 16px;
}
</style>
```

### 2.3 Create the FilterSection Component

This component will contain a group of related filters.

```typescript
// src/components/filters/FilterSection.vue
<template>
  <div class="filter-section">
    <q-expansion-item
      :label="title"
      :default-opened="!defaultCollapsed"
      :disable="!collapsible"
      header-class="filter-section-header"
      expand-icon-class="text-primary"
    >
      <template v-slot:header>
        <q-item-section>
          <div class="row items-center">
            <div class="col">{{ title }}</div>
            <div class="col-auto">
              <q-btn
                flat
                round
                dense
                icon="clear_all"
                color="grey-7"
                size="sm"
                @click.stop="clearAllFilters"
              >
                <q-tooltip>Clear all {{ title.toLowerCase() }}</q-tooltip>
              </q-btn>
            </div>
          </div>
        </q-item-section>
      </template>
      
      <div class="filter-section-content">
        <filter-control
          v-for="column in columns"
          :key="column.id"
          :column="column"
          @update="(value) => $emit('update-filter', column.id, value)"
          @clear="() => $emit('clear-filter', column.id)"
          @toggle-visibility="(visible) => $emit('toggle-visibility', column.id, visible)"
        />
      </div>
    </q-expansion-item>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import FilterControl from './FilterControl.vue';
import type { FilterDefinition } from '@/stores/filterStore';

// Props
const props = defineProps<{
  title: string;
  columns: FilterDefinition[];
  collapsible?: boolean;
  defaultCollapsed?: boolean;
}>();

// Emits
const emit = defineEmits<{
  (e: 'update-filter', columnId: string, value: any): void;
  (e: 'clear-filter', columnId: string): void;
  (e: 'toggle-visibility', columnId: string, visible: boolean): void;
}>();

// Methods
function clearAllFilters() {
  props.columns.forEach(column => {
    emit('clear-filter', column.id);
  });
}
</script>

<style lang="scss" scoped>
.filter-section {
  margin-bottom: 16px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  
  .filter-section-header {
    font-weight: 500;
    background-color: rgba(0, 0, 0, 0.03);
  }
  
  .filter-section-content {
    padding: 8px;
  }
}
</style>
```

### 2.4 Develop the FilterControl Component

This component will handle individual filter controls based on their type.

```typescript
// src/components/filters/FilterControl.vue
<template>
  <div class="filter-control q-mb-sm">
    <div class="row items-center q-col-gutter-sm">
      <div class="col-auto">
        <q-btn
          flat
          round
          dense
          :icon="column.visible ? 'visibility' : 'visibility_off'"
          :color="column.visible ? 'primary' : 'grey'"
          @click="toggleVisibility"
        >
          <q-tooltip>{{ column.visible ? 'Hide column' : 'Show column' }}</q-tooltip>
        </q-btn>
      </div>
      
      <div class="col">
        <div class="filter-label">{{ column.name }}</div>
      </div>
      
      <div class="col-12 col-sm-6">
        <!-- Text filter -->
        <q-input
          v-if="column.filterType === 'text'"
          v-model="filterValue"
          dense
          outlined
          placeholder="Filter..."
          clearable
          @update:model-value="updateFilter"
        />
        
        <!-- Number filter -->
        <q-input
          v-else-if="column.filterType === 'number'"
          v-model="filterValue"
          dense
          outlined
          placeholder="Filter..."
          type="number"
          clearable
          @update:model-value="updateFilter"
        />
        
        <!-- Date filter -->
        <q-input
          v-else-if="column.filterType === 'date'"
          v-model="filterValue"
          dense
          outlined
          placeholder="Filter..."
          clearable
          @update:model-value="updateFilter"
        >
          <template v-slot:append>
            <q-icon name="event" class="cursor-pointer">
              <q-popup-proxy cover transition-show="scale" transition-hide="scale">
                <q-date v-model="filterValue" mask="YYYY-MM-DD" />
              </q-popup-proxy>
            </q-icon>
          </template>
        </q-input>
        
        <!-- Select filter -->
        <q-select
          v-else-if="column.filterType === 'select' && column.filterOptions"
          v-model="filterValue"
          dense
          outlined
          placeholder="Filter..."
          :options="column.filterOptions"
          emit-value
          map-options
          clearable
          @update:model-value="updateFilter"
        />
        
        <!-- Multi-select filter -->
        <q-select
          v-else-if="column.filterType === 'multiselect' && column.filterOptions"
          v-model="filterValue"
          dense
          outlined
          placeholder="Filter..."
          :options="column.filterOptions"
          multiple
          emit-value
          map-options
          use-chips
          clearable
          @update:model-value="updateFilter"
        />
        
        <!-- Boolean filter -->
        <q-select
          v-else-if="column.filterType === 'boolean'"
          v-model="filterValue"
          dense
          outlined
          placeholder="Filter..."
          :options="[
            { label: 'Yes', value: true },
            { label: 'No', value: false }
          ]"
          emit-value
          map-options
          clearable
          @update:model-value="updateFilter"
        />
        
        <!-- Default fallback -->
        <q-input
          v-else
          v-model="filterValue"
          dense
          outlined
          placeholder="Filter..."
          clearable
          @update:model-value="updateFilter"
        />
      </div>
      
      <div class="col-auto">
        <q-btn
          flat
          round
          dense
          icon="clear"
          color="grey-7"
          :disable="!hasValue"
          @click="clearFilter"
        >
          <q-tooltip>Clear filter</q-tooltip>
        </q-btn>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import type { FilterDefinition } from '@/stores/filterStore';

// Props
const props = defineProps<{
  column: FilterDefinition;
}>();

// Emits
const emit = defineEmits<{
  (e: 'update', value: any): void;
  (e: 'clear'): void;
  (e: 'toggle-visibility', visible: boolean): void;
}>();

// State
const filterValue = ref(props.column.filterValue);

// Computed
const hasValue = computed(() => {
  return filterValue.value !== null && filterValue.value !== undefined && filterValue.value !== '';
});

// Watch for changes in column.filterValue
watch(() => props.column.filterValue, (newValue) => {
  filterValue.value = newValue;
});

// Methods
function updateFilter(value: any) {
  emit('update', value);
}

function clearFilter() {
  filterValue.value = null;
  emit('clear');
}

function toggleVisibility() {
  emit('toggle-visibility', !props.column.visible);
}
</script>

<style lang="scss" scoped>
.filter-control {
  padding: 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  
  &:last-child {
    border-bottom: none;
  }
  
  .filter-label {
    font-weight: 500;
  }
}
</style>
```

### 2.5 Create the FilterActionBar Component

This component will provide actions for saving and clearing filters.

```typescript
// src/components/filters/FilterActionBar.vue
<template>
  <div class="filter-action-bar">
    <div class="row q-col-gutter-sm">
      <div class="col">
        <q-btn
          outline
          color="primary"
          icon="save"
          label="Save Filter"
          :disable="!hasUnsavedChanges"
          @click="$emit('save')"
        />
      </div>
      
      <div class="col">
        <q-btn
          outline
          color="grey-7"
          icon="clear_all"
          label="Clear All"
          :disable="!hasActiveFilters"
          @click="$emit('clear')"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Props
const props = defineProps<{
  hasUnsavedChanges: boolean;
  hasActiveFilters: boolean;
}>();

// Emits
const emit = defineEmits<{
  (e: 'save'): void;
  (e: 'clear'): void;
}>();
</script>

<style lang="scss" scoped>
.filter-action-bar {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}
</style>
```

### 2.6 Create a useFilter Composable

This composable will provide a convenient way to use the filter system in components.

```typescript
// src/composables/useFilter.ts
import { computed, ref, watch, onMounted, onUnmounted } from 'vue';
import { useFilterStore } from '@/stores/filterStore';
import { filterEngine } from '@/lib/filters/FilterEngine';
import { filterEventBus } from '@/lib/filters/FilterEventBus';
import type { FilterTypeDefinition } from '@/lib/filters/FilterRegistry';

export function useFilter<T>(tableName: string, data: T[] | Ref<T[]>) {
  const filterStore = useFilterStore();
  const isInitialized = ref(false);
  
  // Initialize the filter store with the table name
  async function initialize() {
    if (!isInitialized.value) {
      await filterStore.setTable(tableName);
      isInitialized.value = true;
    }
  }
  
  // Call initialize immediately
  void initialize();
  
  // Computed properties
  const filteredData = computed(() => {
    if (!isInitialized.value) return [];
    
    const dataValue = Array.isArray(data) ? data : data.value;
    
    return filterEngine.applyFilters(
      dataValue,
      tableName,
      filterStore.activeFilters,
      filterStore.specializedFilters
    );
  });
  
  const visibleColumns = computed(() => {
    return filterStore.mainTableColumns
      .filter(col => col.visible)
      .sort((a, b) => a.order - b.order);
  });
  
  const expandedColumns = computed(() => {
    return filterStore.expandedViewColumns
      .filter(col => col.visible)
      .sort((a, b) => a.order - b.order);
  });
  
  const specializedFilterTypes = computed(() => {
    return filterStore.specializedFilterTypes;
  });
  
  // Event handlers
  function handleFilterChanged(payload: any) {
    // Handle filter changed event if needed
    console.log('Filter changed:', payload);
  }
  
  function handleFilterLoaded(payload: any) {
    // Handle filter loaded event if needed
    console.log('Filter loaded:', payload);
  }
  
  // Set up event listeners
  onMounted(() => {
    filterEventBus.on(FilterEventBus.EVENTS.FILTER_CHANGED, handleFilterChanged);
    filterEventBus.on(FilterEventBus.EVENTS.FILTER_LOADED, handleFilterLoaded);
  });
  
  // Clean up event listeners
  onUnmounted(() => {
    filterEventBus.off(FilterEventBus.EVENTS.FILTER_CHANGED, handleFilterChanged);
    filterEventBus.off(FilterEventBus.EVENTS.FILTER_LOADED, handleFilterLoaded);
  });
  
  return {
    filteredData,
    visibleColumns,
    expandedColumns,
    specializedFilterTypes,
    isInitialized,
    activeFilters: computed(() => filterStore.activeFilters),
    specializedFilters: computed(() => filterStore.specializedFilters),
    currentFilterId: computed(() => filterStore.currentFilterId),
    hasUnsavedChanges: computed(() => filterStore.hasUnsavedChanges)
  };
}
```

## Expected Outcome

After completing this step, we will have:

1. A complete set of reusable filter components
2. A consistent UI for filter management
3. A composable for easy integration with components
4. Event-based communication between filter components

These components will provide a solid foundation for the filter system and can be used across all tables in the application.

## Next Steps

In the next step, we'll implement specialized filter types for specific use cases, such as the PromptElementsFilter.
