<template>
  <div class="table-filter-panel">
    <!-- Saved Filters Section -->
    <div class="saved-filters-section q-mb-md">
      <div class="row items-center justify-between q-mb-sm">
        <div class="text-subtitle1">{{ t('filters.savedFilters') }}</div>
        <div class="row no-wrap">
          <!-- Show Cancel and Save buttons when there are unsaved changes -->
          <q-btn
            v-if="filterStore.hasUnsavedChanges"
            flat
            dense
            :icon="$q.screen.lt.sm ? 'close' : undefined"
            :label="$q.screen.lt.sm ? undefined : t('common.cancel')"
            color="grey-7"
            @click="dismissChanges()"
            class="q-mr-xs"
          >
            <q-tooltip>{{ t('common.dismissNotification') }}</q-tooltip>
          </q-btn>
          <q-btn
            v-if="filterStore.hasUnsavedChanges"
            flat
            dense
            :icon="$q.screen.lt.sm ? 'save' : undefined"
            :label="$q.screen.lt.sm ? undefined : t('common.save')"
            color="primary"
            @click="saveCurrentFilterChanges()"
            class="q-mr-xs"
          >
            <q-tooltip>{{ t('common.saveCurrentFilter') }}</q-tooltip>
          </q-btn>
          <!-- New filter button -->
          <q-btn
            flat
            dense
            :icon="$q.screen.lt.sm ? 'add' : 'add'"
            :label="$q.screen.lt.sm ? undefined : t('app.new')"
            color="primary"
            @click="showSaveFilterDialog()"
          >
            <q-tooltip>{{ t('common.createNewFilter') }}</q-tooltip>
          </q-btn>
        </div>
      </div>

      <div class="row q-col-gutter-sm">
        <div class="col">
          <q-select
            v-model="selectedFilter"
            :options="filterOptions"
            outlined
            dense
            emit-value
            map-options
            option-label="name"
            option-value="id"
            :loading="filterStore.isLoading"
            @update:model-value="loadSelectedFilter"
            @clear="clearSelectedFilter"
            :disable="!filterStore.currentTable"
            clearable
            clear-icon="close"
            :key="'filter-select-' + (selectedFilter || 'none')"
          >
            <template v-slot:prepend>
              <q-icon name="filter_list" />
            </template>

            <template v-slot:no-option>
              <q-item>
                <q-item-section class="text-grey">
                  {{ t('common.noSavedFilters') }}
                </q-item-section>
              </q-item>
            </template>

            <template v-slot:option="{ opt, selected, toggleOption }">
              <q-item clickable @click="toggleOption(opt)" :active="selected">
                <q-item-section>
                  <q-item-label>{{ opt.name }}</q-item-label>
                  <q-item-label caption v-if="opt.description">{{ opt.description }}</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <div class="row items-center">
                    <q-badge v-if="opt.is_default" color="primary" :label="t('common.default')" class="q-mr-xs" />
                    <q-btn flat dense round icon="more_vert" @click.stop="showFilterMenu(opt)">
                      <q-menu>
                        <q-list style="min-width: 150px">
                          <q-item clickable v-close-popup @click="showSaveFilterDialog(opt)">
                            <q-item-section>{{ t('common.edit') }}</q-item-section>
                          </q-item>
                          <q-item clickable v-close-popup @click="setDefaultFilter(opt.id)">
                            <q-item-section>{{ t('common.setAsDefault') }}</q-item-section>
                          </q-item>
                          <q-item clickable v-close-popup @click="confirmDeleteFilter(opt.id)">
                            <q-item-section>{{ t('common.delete') }}</q-item-section>
                          </q-item>
                        </q-list>
                      </q-menu>
                    </q-btn>
                  </div>
                </q-item-section>
              </q-item>
            </template>
          </q-select>
        </div>
        <div class="col-auto">
          <q-btn
            outline
            dense
            icon="clear_all"
            color="grey-7"
            @click="clearSelectedFilter"
            :disable="!selectedFilter || !filterStore.currentTable"
            class="clear-filter-btn"
          >
            <q-tooltip>{{ $t('common.clearFilter') }}</q-tooltip>
          </q-btn>
        </div>
      </div>
    </div>

    <!-- No unsaved changes indicator - buttons at the top are sufficient -->

    <!-- No table selected message -->
    <div v-if="!filterStore.currentTable" class="no-table-message q-pa-md">
      <q-banner class="bg-grey-3">
        <template v-slot:avatar>
          <q-icon name="info" color="primary" />
        </template>
        {{ $t('filters.filtersWillAppear') }}
      </q-banner>
    </div>

    <!-- Column Sections -->
    <div v-else class="column-sections">
      <div class="section-header q-mb-sm">
        <div class="row items-center justify-between">
          <div>
            <div class="text-subtitle1">{{ $t('filters.main') }}</div>
          </div>
          <q-btn
            flat
            dense
            :icon="selectAllMainColumns ? 'visibility' : 'visibility_off'"
            :color="selectAllMainColumns ? 'primary' : 'grey'"
            :label="selectAllMainColumns ? $t('app.hideAll') : $t('app.showAll')"
            @click="toggleAllMainColumns(!selectAllMainColumns)"
          />
        </div>
      </div>

      <draggable
        v-model="mainColumns"
        group="columns"
        item-key="id"
        @end="onDragEnd"
        class="column-list q-mb-md"
        handle=".q-item"
        data-section="main"
        :delay="200"
        :delayOnTouchOnly="true"
      >
        <template #item="{element}">
          <column-filter-item
            :column="element"
            @toggle-visibility="toggleVisibility"
            @update-filter="updateFilter"
          />
        </template>
      </draggable>

      <div class="section-header q-mb-sm q-mt-lg">
        <div class="row items-center justify-between">
          <div>
            <div class="text-subtitle1">{{ $t('filters.expanded') }}</div>
          </div>
          <q-btn
            flat
            dense
            :icon="selectAllExpandedColumns ? 'visibility' : 'visibility_off'"
            :color="selectAllExpandedColumns ? 'primary' : 'grey'"
            :label="selectAllExpandedColumns ? $t('app.hideAll') : $t('app.showAll')"
            @click="toggleAllExpandedColumns(!selectAllExpandedColumns)"
          />
        </div>
      </div>

      <draggable
        v-model="expandedColumns"
        group="columns"
        item-key="id"
        @end="onDragEnd"
        class="column-list"
        handle=".q-item"
        data-section="expanded"
        :delay="200"
        :delayOnTouchOnly="true"
      >
        <template #item="{element}">
          <column-filter-item
            :column="element"
            @toggle-visibility="toggleVisibility"
            @update-filter="updateFilter"
          />
        </template>
      </draggable>


      <!-- Template-specific sections (only shown for templates table) -->
      <template v-if="filterStore.currentTable === 'templates'">
        <!-- Prompt Elements Filter Section (Expandable) -->
        <div class="q-mt-lg">
          <q-expansion-item
            header-class="prompt-elements-header"
            expand-icon-class="text-primary"
            :default-opened="false"
          >
            <template v-slot:header>
              <q-item-section avatar>
                <!-- This is where the expand icon appears -->
              </q-item-section>

              <q-item-section>
                {{ $t('filters.promptElements') }}
              </q-item-section>

              <q-item-section side>
                <q-btn
                  flat
                  round
                  dense
                  icon="clear_all"
                  color="grey-7"
                  size="sm"
                  @click.stop="clearPromptElementFilters"
                  class="clear-all-btn"
                >
                  <q-tooltip>{{ $t('filters.clearAllPromptElementFilters') }}</q-tooltip>
                </q-btn>
              </q-item-section>
            </template>

            <prompt-elements-filter-section ref="promptElementsFilterSection" />
          </q-expansion-item>
        </div>

        <!-- Related Items Filter Section -->
        <div class="q-mt-lg">
          <related-items-filter-section />
        </div>
      </template>
    </div>

    <!-- Save Filter Dialog -->
    <q-dialog v-model="saveFilterDialog" persistent>
      <q-card style="min-width: 350px">
        <q-card-section>
          <div class="text-h6">{{ editingFilter ? $t('filters.editFilter') : $t('filters.saveFilter') }}</div>
        </q-card-section>

        <q-card-section>
          <q-input
            v-model="filterName"
            :label="$t('filters.filterName')"
            dense
            outlined
            :rules="[val => !!val || 'Name is required']"
          />

          <q-input
            v-model="filterDescription"
            label="Description (optional)"
            dense
            outlined
            class="q-mt-sm"
          />

          <q-checkbox
            v-model="makeDefault"
            label="Set as default filter for this view"
            class="q-mt-sm"
          />
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="Cancel" color="primary" v-close-popup />
          <q-btn
            flat
            label="Save"
            color="primary"
            @click="() => { saveFilterDialog = false; saveFilter(); }"
            :disable="!filterName"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- Delete Confirmation Dialog -->
    <q-dialog v-model="deleteFilterDialog" persistent>
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar icon="delete" color="negative" text-color="white" />
          <span class="q-ml-sm">Are you sure you want to delete this filter?</span>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="Cancel" color="primary" v-close-popup />
          <q-btn flat label="Delete" color="negative" @click="deleteFilter" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue';
import { useQuasar } from 'quasar';
import { useTableFilterStore } from 'src/stores/tableFilterStore';
import type { SavedFilter } from 'src/types/supabase-filters';
import { useContextStore } from 'src/stores/contextStore';
import draggable from 'vuedraggable';
import ColumnFilterItem from './ColumnFilterItem.vue';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import PromptElementsFilterSection from './PromptElementsFilterSection.vue';
import RelatedItemsFilterSection from './RelatedItemsFilterSection.vue';
import { notify } from 'src/boot/notifications';
import { useI18n } from 'vue-i18n';

const $q = useQuasar();
const filterStore = useTableFilterStore();
const contextStore = useContextStore();
const { t } = useI18n();

// Saved filter management
const selectedFilter = ref<number | null>(null);
const saveFilterDialog = ref(false);
const deleteFilterDialog = ref(false);
const filterToDeleteId = ref<number | null>(null);
const filterName = ref('');
const filterDescription = ref('');
const makeDefault = ref(false);
const editingFilter = ref<SavedFilter | null>(null);

// Select all checkboxes
const selectAllMainColumns = ref(false);
const selectAllExpandedColumns = ref(false);

// Reference to the prompt elements filter section component
const promptElementsFilterSection = ref<{ clearAllFilters: () => void } | null>(null);

// Update select all state when columns change
watch(() => filterStore.mainTableColumns, updateSelectAllMainState, { immediate: true, deep: true });
watch(() => filterStore.expandedViewColumns, updateSelectAllExpandedState, { immediate: true, deep: true });

// Computed properties for draggable lists
const mainColumns = computed({
  get: () => filterStore.mainTableColumns,
  set: (value) => {
    updateColumnOrder('main', value);
    filterStore.markUnsavedChanges();
  }
});

const expandedColumns = computed({
  get: () => filterStore.expandedViewColumns,
  set: (value) => {
    updateColumnOrder('expanded', value);
    filterStore.markUnsavedChanges();
  }
});

const filterOptions = computed(() => {
  return filterStore.filtersByTable.map((filter: SavedFilter) => ({
    id: filter.id,
    name: filter.name,
    description: filter.description,
    is_default: filter.is_default
  }));
});

// Initialize component
onMounted(async () => {
  try {
    // Load saved filters
    await filterStore.loadSavedFilters();

    // Set initial context
    if (contextStore.currentContext) {
      await setContext(contextStore.currentContext);
    }
  } catch (error) {
    console.error('Error initializing filter panel:', error);
    void notify({
      color: 'negative',
      message: t('filters.failedToInitialize'),
      icon: 'error'
    });
  }
});

// Watch for context changes
watch(() => contextStore.currentContext, async (newContext) => {
  if (newContext) {
    try {
      await setContext(newContext);
    } catch (error) {
      console.error('Error changing filter context:', error);
      void notify({
        color: 'negative',
        message: t('filters.failedToUpdate'),
        icon: 'error'
      });
    }
  }
});

// Set the current context and load appropriate filters
async function setContext(context: { id: string } | string) {
  // Map context to table name
  let tableName = '';

  // Get context ID from context object or use the string directly
  const contextId = typeof context === 'object' ? context.id : context;
  // console.log('Setting context in TableFilterPanel:', contextId);

  switch (contextId) {
    case 'templates':
    case 'templates_table':
    case 'templates.browse':
    case 'templates.builder':
      tableName = 'templates';
      break;
    case 'products':
      tableName = 'products';
      break;
    case 'collections':
      tableName = 'collections';
      break;
    default:
      tableName = contextId;
  }

  if (tableName) {
    await filterStore.setTable(tableName);

    // Try to load the default filter for this table
    const defaultFilter = filterStore.savedFilters.find(
      (f: SavedFilter) => f.table_name === tableName && f.is_default
    );

    if (defaultFilter) {
      console.log('Loading default filter:', defaultFilter);
      selectedFilter.value = defaultFilter.id;

      // Load the filter and ensure it's properly applied
      const loaded = filterStore.loadFilter(defaultFilter.id);
      if (loaded) {
        // Dispatch events to ensure the filter is properly applied
        dispatchFilterEvent();

        // Save to localStorage for templates table
        if (tableName === 'templates') {
          try {
            const activeFilters = filterStore.activeFilters;
            localStorage.setItem('lastTemplateFilter', JSON.stringify(activeFilters));
          } catch (err) {
            console.error('Error saving default filter to localStorage:', err);
          }
        }
      }
    } else {
      selectedFilter.value = null;
    }
  }
}

// Column management functions
function updateColumnOrder(section: 'main' | 'expanded', columns: { id: string }[]) {
  columns.forEach((col, index) => {
    filterStore.updateColumnOrder(col.id, section, index);
  });
}

function toggleVisibility(columnId: string, visible: boolean) {
  filterStore.toggleColumnVisibility(columnId, visible);
  filterStore.markUnsavedChanges();
}

function updateFilter(columnId: string, value: string | number | null) {
  console.log(`TableFilterPanel.updateFilter called for column ${columnId} with value:`, value);

  // Special handling for status filter
  if (columnId === 'status') {
    console.log('STATUS FILTER UPDATE in TableFilterPanel:', value);

    // If status filter is being cleared (value is null)
    if (value === null) {
      console.log('STATUS FILTER IS BEING CLEARED');

      // Clear from localStorage
      const lastFilterJson = localStorage.getItem('lastTemplateFilter');
      if (lastFilterJson) {
        try {
          const lastFilter = JSON.parse(lastFilterJson);
          if ('status' in lastFilter) {
            delete lastFilter.status;
            localStorage.setItem('lastTemplateFilter', JSON.stringify(lastFilter));
            console.log('STATUS FILTER removed from localStorage in updateFilter');
          }
        } catch (err) {
          console.error('Error updating localStorage:', err);
        }
      }

      // Dispatch a special event for status filter clearing
      document.dispatchEvent(new CustomEvent('status-filter-cleared', {
        detail: { columnId }
      }));
    }
  }

  filterStore.setColumnFilter(columnId, value);
  filterStore.markUnsavedChanges();

  // Dispatch global filter event
  dispatchFilterEvent();

  // Log the active filters after update
  console.log('Active filters after update:', filterStore.activeFilters);
}

// Dispatch a global filter event with the current filters
function dispatchFilterEvent() {
  // Get all active filters
  const filters = filterStore.activeFilters;
  console.log('Dispatching global filter event with filters:', filters);

  // Dispatch the event
  document.dispatchEvent(new CustomEvent('global-filter-changed', {
    detail: {
      filters
    }
  }));
}

// Clear all prompt element filters
function clearPromptElementFilters(event: Event) {
  // Stop the event from propagating to prevent the expansion panel from toggling
  event.stopPropagation();

  if (promptElementsFilterSection.value && typeof promptElementsFilterSection.value.clearAllFilters === 'function') {
    promptElementsFilterSection.value.clearAllFilters();

    // Show notification
    void notify({
      color: 'info',
      message: 'All prompt element filters cleared',
      icon: 'clear_all',
      position: 'top-right',
      timeout: 2000
    });
  }
}

// Note: We've removed the unused clearStatusFilter function
// The status filter is now cleared directly in the updateFilter function
// when the value is set to null

function onDragEnd(evt: { from: Element; to: Element; item: Element }) {
  // Handle drag between sections
  if (evt.from !== evt.to) {
    const columnId = evt.item.getAttribute('data-column-id');
    const newSection = evt.to.getAttribute('data-section') as 'main' | 'expanded';
    if (columnId && newSection) {
      filterStore.moveColumnToSection(columnId, newSection);
      filterStore.markUnsavedChanges();
    }
  }
}

// Filter management functions
function loadSelectedFilter(filterId: number | null) {
  console.log('loadSelectedFilter called with:', filterId);
  if (filterId) {
    // Load the filter
    filterStore.loadFilter(filterId);

    // Get the active filters after loading
    const activeFilters = filterStore.activeFilters;
    console.log('Active filters after loading:', activeFilters);

    // Special handling for templates table
    if (filterStore.currentTable === 'templates') {
      try {
        // Save to localStorage
        console.log('Saving active filters to localStorage:', activeFilters);
        localStorage.setItem('lastTemplateFilter', JSON.stringify(activeFilters));

        // Special handling for status filter
        if ('status' in activeFilters) {
          const statusValue = activeFilters.status;
          console.log('Status filter value:', statusValue);

          // Dispatch a special event for status filter
          document.dispatchEvent(new CustomEvent('status-filter-changed', {
            detail: {
              value: statusValue
            }
          }));
        } else {
          // Clear status filter if not present
          document.dispatchEvent(new CustomEvent('status-filter-cleared', {
            detail: { columnId: 'status' }
          }));
        }

        // Special handling for prompt elements filters
        // This will be picked up by the PromptElementsFilterSection component
        document.dispatchEvent(new CustomEvent('load-prompt-element-filters', {
          detail: {
            filters: activeFilters
          }
        }));
      } catch (err) {
        console.error('Error handling template filters:', err);
      }
    }

    // Dispatch general filter event
    dispatchFilterEvent();

    // Dispatch a global event that the table can listen for
    document.dispatchEvent(new CustomEvent('filter-selected', {
      detail: {
        filterId,
        filters: activeFilters,
        table: filterStore.currentTable
      }
    }));

    // Notify user
    void notify({
      color: 'info',
      message: 'Filter applied',
      icon: 'filter_list',
      position: 'top-right',
      timeout: 2000
    });
  } else {
    // If no filter selected, reset to base configuration
    if (filterStore.currentTable) {
      filterStore.loadBaseColumnsForTable(filterStore.currentTable);

      // Special handling for templates table
      if (filterStore.currentTable === 'templates') {
        // Clear localStorage
        localStorage.removeItem('lastTemplateFilter');

        // Clear status filter
        document.dispatchEvent(new CustomEvent('status-filter-cleared', {
          detail: { columnId: 'status' }
        }));

        // Clear prompt elements filters
        document.dispatchEvent(new CustomEvent('clear-prompt-element-filters', {
          detail: {}
        }));
      }

      // Dispatch general filter event
      dispatchFilterEvent();

      // Dispatch a global event that the table can listen for
      document.dispatchEvent(new CustomEvent('filter-cleared', {
        detail: {
          table: filterStore.currentTable
        }
      }));

      // Dispatch additional events to ensure all components get the message
      document.dispatchEvent(new CustomEvent('global-filter-reset', {
        detail: {
          table: filterStore.currentTable
        }
      }));

      // For templates table, dispatch a specific event
      if (filterStore.currentTable === 'templates') {
        document.dispatchEvent(new CustomEvent('templates-filter-reset', {
          detail: {}
        }));

        // Also try to directly update any filter-related UI elements
        // This is a more direct approach that might help if event listeners aren't working
        try {
          // Try to find and reset any filter UI elements
          const filterInputs = document.querySelectorAll('.filter-input, [data-filter-input]');
          console.log('Found filter inputs to reset:', filterInputs.length);
          filterInputs.forEach(input => {
            if (input instanceof HTMLInputElement) {
              input.value = '';
            } else if (input instanceof HTMLSelectElement) {
              input.selectedIndex = 0;
            }
          });
        } catch (err) {
          console.error('Error trying to reset filter UI elements:', err);
        }
      }
    }
  }
}

// This function is called when the menu button is clicked
// The menu is shown automatically by Quasar
function showFilterMenu(filter: { id: number; name: string; description: string | null; is_default: boolean }) {
  console.log('Menu opened for filter:', filter);
  // No implementation needed - Quasar handles the menu display
}

function showSaveFilterDialog(filter: SavedFilter | null = null) {
  if (filter) {
    // Editing existing filter
    editingFilter.value = filter;
    filterName.value = filter.name;
    filterDescription.value = filter.description || '';
    makeDefault.value = filter.is_default;

    // Set the current filter ID to the filter being edited
    filterStore.currentFilterId = filter.id;
  } else {
    // Creating new filter
    editingFilter.value = null;
    filterName.value = '';
    filterDescription.value = '';
    makeDefault.value = false;

    // Reset the current filter ID to null to ensure we create a new filter
    filterStore.currentFilterId = null;
  }

  saveFilterDialog.value = true;
}

async function saveFilter() {
  if (!filterName.value.trim()) {
    void notify({
      color: 'negative',
      message: 'Filter name is required',
      icon: 'error'
    });
    return;
  }

  // Get prompt element filters if we're in the templates table
  const combinedFilters: Record<string, unknown> = { ...filterStore.activeFilters };

  if (filterStore.currentTable === 'templates') {
    try {
      // Request prompt element filters via a custom event
      const promptElementFiltersEvent = new CustomEvent('get-prompt-element-filters', {
        detail: {
          callback: (elementFilters: Record<string, number[]>) => {
            console.log('Received prompt element filters for saving:', elementFilters);

            // Add prompt element filters to the combined filters
            Object.keys(elementFilters).forEach(key => {
              // Convert arrays to JSON strings to ensure type compatibility
              if (Array.isArray(elementFilters[key])) {
                combinedFilters[key] = JSON.stringify(elementFilters[key]);
              } else {
                combinedFilters[key] = elementFilters[key];
              }
            });
          }
        }
      });

      // Dispatch the event to get prompt element filters
      document.dispatchEvent(promptElementFiltersEvent);

      console.log('Combined filters for saving:', combinedFilters);
    } catch (err) {
      console.error('Error getting prompt element filters:', err);
    }
  }

  const success = await filterStore.saveCurrentFilter(
    filterName.value.trim(),
    filterDescription.value.trim(),
    makeDefault.value,
    combinedFilters // Pass the combined filters to the save function
  );

  if (success) {
    void notify({
      color: 'positive',
      message: `Filter ${editingFilter.value ? 'updated' : 'saved'} successfully`,
      icon: 'check_circle'
    });

    // Update selected filter and ensure the UI is updated
    console.log('Setting selectedFilter to:', filterStore.currentFilterId);
    selectedFilter.value = filterStore.currentFilterId;

    // Use nextTick to ensure the UI is updated after the filter is saved
    void nextTick(() => {
      // Force update the select component by setting it to null and then back to the filter ID
      selectedFilter.value = null;

      // Use another nextTick to ensure the null value is applied before setting the actual value
      void nextTick(() => {
        console.log('Setting selectedFilter back to:', filterStore.currentFilterId);
        selectedFilter.value = filterStore.currentFilterId;

        // Load the filter to ensure it's properly applied
        if (filterStore.currentFilterId) {
          console.log('Loading newly saved filter:', filterStore.currentFilterId);
          filterStore.loadFilter(filterStore.currentFilterId);
        }
      });
    });

    // Save to localStorage for templates table
    if (filterStore.currentTable === 'templates') {
      try {
        // Save combined filters to localStorage
        console.log('Saving combined filters to localStorage after save:', combinedFilters);
        localStorage.setItem('lastTemplateFilter', JSON.stringify(combinedFilters));
      } catch (err) {
        console.error('Error saving filter to localStorage:', err);
      }
    }

    // Dispatch filter event after saving
    dispatchFilterEvent();

    // Dispatch a global event that the table can listen for
    document.dispatchEvent(new CustomEvent('filter-selected', {
      detail: {
        filterId: filterStore.currentFilterId,
        filters: combinedFilters
      }
    }));
  }
}

async function setDefaultFilter(filterId: number) {
  const success = await filterStore.updateDefaultFilter(filterId);

  if (success) {
    void notify({
      color: 'positive',
      message: 'Default filter updated',
      icon: 'check_circle'
    });
  }
}

function confirmDeleteFilter(filterId: number) {
  filterToDeleteId.value = filterId;
  deleteFilterDialog.value = true;
}

async function deleteFilter() {
  if (filterToDeleteId.value) {
    const success = await filterStore.deleteFilter(filterToDeleteId.value);

    if (success) {
      void notify({
        color: 'positive',
        message: 'Filter deleted successfully',
        icon: 'check_circle'
      });

      // Update selected filter
      selectedFilter.value = filterStore.currentFilterId;
    }

    filterToDeleteId.value = null;
  }
}

// Save changes to the current filter without showing the dialog
async function saveCurrentFilterChanges() {
  // If there's a current filter selected, update it directly
  if (filterStore.currentFilter) {
    const currentFilter = filterStore.currentFilter;
    console.log('Saving changes to current filter:', currentFilter);

    const success = await filterStore.saveCurrentFilter(
      currentFilter.name,
      currentFilter.description || '', // Convert null to empty string
      currentFilter.is_default
    );

    if (success) {
      void notify({
        color: 'positive',
        message: 'Filter updated successfully',
        icon: 'check_circle'
      });
    }
  } else {
    // If no filter is selected, show the dialog to create a new one
    showSaveFilterDialog();
  }
}

// Update the select all checkbox state based on current column visibility
function updateSelectAllMainState() {
  const mainCols = filterStore.mainTableColumns;
  selectAllMainColumns.value = mainCols.length > 0 && mainCols.every(col => col.visible);
}

function updateSelectAllExpandedState() {
  const expandedCols = filterStore.expandedViewColumns;
  selectAllExpandedColumns.value = expandedCols.length > 0 && expandedCols.every(col => col.visible);
}

// Toggle all columns visibility
function toggleAllMainColumns(checked: boolean) {
  filterStore.mainTableColumns.forEach(col => {
    filterStore.toggleColumnVisibility(col.id, checked);
  });
  filterStore.markUnsavedChanges();
}

function toggleAllExpandedColumns(checked: boolean) {
  filterStore.expandedViewColumns.forEach(col => {
    filterStore.toggleColumnVisibility(col.id, checked);
  });
  filterStore.markUnsavedChanges();
}

// Dismiss unsaved changes notification without reverting changes
function dismissChanges() {
  // Just hide the notification without changing any filter settings
  filterStore.dismissUnsavedChanges();
}

// Clear the selected filter and reset to base configuration
function clearSelectedFilter() {
  selectedFilter.value = null;

  if (filterStore.currentTable) {
    // Reset to base configuration
    filterStore.loadBaseColumnsForTable(filterStore.currentTable);

    // Special handling for templates table
    if (filterStore.currentTable === 'templates') {
      // Clear localStorage - be more explicit about what we're clearing
      console.log('Clearing lastTemplateFilter from localStorage');
      localStorage.removeItem('lastTemplateFilter');

      // Also check if there are any other filter-related items in localStorage
      const filterKeys = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (key.includes('filter') || key.includes('Filter'))) {
          filterKeys.push(key);
        }
      }

      if (filterKeys.length > 0) {
        console.log('Found other filter-related localStorage keys:', filterKeys);
        // Remove any other filter-related items
        filterKeys.forEach(key => {
          console.log('Removing', key, 'from localStorage');
          localStorage.removeItem(key);
        });
      }

      // Clear status filter
      document.dispatchEvent(new CustomEvent('status-filter-cleared', {
        detail: { columnId: 'status' }
      }));

      // Clear prompt elements filters
      document.dispatchEvent(new CustomEvent('clear-prompt-element-filters', {
        detail: {}
      }));
    }

    // Dispatch general filter event
    dispatchFilterEvent();

    // Dispatch a global event that the table can listen for
    document.dispatchEvent(new CustomEvent('filter-cleared', {
      detail: {
        table: filterStore.currentTable
      }
    }));

    // Dispatch additional events to ensure all components get the message
    document.dispatchEvent(new CustomEvent('global-filter-reset', {
      detail: {
        table: filterStore.currentTable
      }
    }));

    // For templates table, dispatch a specific event
    if (filterStore.currentTable === 'templates') {
      document.dispatchEvent(new CustomEvent('templates-filter-reset', {
        detail: {}
      }));

      // Also try to directly update any filter-related UI elements
      // This is a more direct approach that might help if event listeners aren't working
      try {
        // Try to find and reset any filter UI elements
        const filterInputs = document.querySelectorAll('.filter-input, [data-filter-input]');
        console.log('Found filter inputs to reset:', filterInputs.length);
        filterInputs.forEach(input => {
          if (input instanceof HTMLInputElement) {
            input.value = '';
          } else if (input instanceof HTMLSelectElement) {
            input.selectedIndex = 0;
          }
        });
      } catch (err) {
        console.error('Error trying to reset filter UI elements:', err);
      }
    }

    // Show notification
    void notify({
      color: 'info',
      message: 'Filter cleared',
      icon: 'clear_all',
      position: 'top-right',
      timeout: 2000
    });
  }
}
</script>

<style lang="scss" scoped>
.table-filter-panel {
  // Style for the filter dropdown items
  :deep(.q-item) {
    cursor: pointer;

    &:hover {
      background-color: rgba(0, 0, 0, 0.03);
    }

    &.q-item--active {
      background-color: rgba(0, 0, 0, 0.05);
    }
  }
  padding: 16px;

  // Mobile padding
  @media (max-width: 599px) {
    padding: 8px;
  }

  .column-list {
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-radius: 4px;
    overflow: hidden;
    background-color: white;
    width: 100%;

    &:empty {
      padding: 16px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #999;
      font-style: italic;

      &::after {
        content: 'No columns available';
      }
    }

    // Make sure the items take full width
    :deep(.q-item:not(.column-filter-item)) {
      width: 100%;
      padding: 8px 0; /* Remove horizontal padding for non-filter items */
    }
  }

  .section-header {
    margin-top: 8px;
  }

  .prompt-elements-header {
    font-weight: 500;
    font-size: 1rem;
    padding: 8px 12px;
    background-color: rgba(0, 0, 0, 0.02);
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-radius: 4px;

    .clear-all-btn {
      opacity: 0.7;
      transition: opacity 0.3s;

      &:hover {
        opacity: 1;
      }
    }
  }

  /* Removed unsaved-changes-indicator class */
}

.q-dark {
  :deep(.q-item) {
    &:hover {
      background-color: rgba(255, 255, 255, 0.07);
    }

    &.q-item--active {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }

  .column-list {
    border-color: rgba(255, 255, 255, 0.12);
    background-color: #1d1d1d;
  }

  .prompt-elements-header {
    background-color: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.12);
  }
}
</style>
