import type { ContextConfig } from '../contextManager';
import { DeviceType } from 'src/types/deviceTypes';

export const dashboardContext: ContextConfig = {
  id: 'dashboard',
  name: 'Dashboard',

  // Base configuration (common for all devices)
  leftDrawer: {
    tabs: {
      filters: false,
      tools: false
    },
    components: {}
  },

  rightDrawer: {
    tabs: {
      preview: false,
      settings: false,
      chat: true
    },
    components: {}
  },

  search: {
    visible: false,
    placeholder: 'Search...',
    targetCollection: 'none'
  },

  bottomBarActions: [],

  // Device-specific overrides
  deviceOverrides: {
    // Mobile browser configuration
    [DeviceType.MOBILE_BROWSER]: {
      leftDrawer: {
        tabs: {
          filters: false,
          tools: false
        },
        components: {}
      },
      rightDrawer: {
        tabs: {
          preview: false,
          settings: false,
          chat: true
        },
        components: {}
      },
      search: {
        visible: false,
        placeholder: 'Search...',
        targetCollection: 'none'
      },
      bottomBarActions: []
    },

    // Desktop browser configuration
    [DeviceType.DESKTOP_BROWSER]: {
      leftDrawer: {
        tabs: {
          filters: false,
          tools: false
        },
        components: {}
      },
      rightDrawer: {
        tabs: {
          preview: false,
          settings: false,
          chat: true
        },
        components: {}
      },
      search: {
        visible: false,
        placeholder: 'Search...',
        targetCollection: 'none'
      },
      bottomBarActions: []
    },

    // Electron desktop configuration
    [DeviceType.DESKTOP_ELECTRON]: {
      leftDrawer: {
        tabs: {
          filters: false,
          tools: false
        },
        components: {}
      },
      rightDrawer: {
        tabs: {
          preview: false,
          settings: false,
          chat: true
        },
        components: {}
      },
      search: {
        visible: false,
        placeholder: 'Search...',
        targetCollection: 'none'
      },
      bottomBarActions: []
    }
  }
};
