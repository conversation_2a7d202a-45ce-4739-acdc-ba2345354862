{"name": "dreambox-studio", "version": "0.0.1", "description": "Dreambox Studio Application", "productName": "Dreambox Studio", "author": "Milan Košir <<EMAIL>>", "type": "module", "private": true, "scripts": {"lint": "eslint -c ./eslint.config.js \"./src*/**/*.{ts,js,cjs,mjs,vue}\"", "test": "bun test", "test:watch": "bun test --watch", "test:coverage": "bun test --coverage", "test:components": "bun test test/unit/components", "test:stores": "bun test test/unit/stores", "test:utils": "bun test test/unit/utils", "dev": "quasar dev", "dev:clean": "bash scripts/dev.sh", "build": "quasar build", "postinstall": "quasar prepare"}, "dependencies": {"@quasar/extras": "^1.16.4", "@supabase/supabase-js": "^2.49.4", "@vueuse/core": "^13.1.0", "axios": "^1.2.1", "pinia": "^3.0.1", "quasar": "^2.16.0", "uuid": "^11.1.0", "vue": "^3.4.18", "vue-draggable-next": "^2.2.1", "vue-i18n": "9", "vue-router": "^4.0.12", "vuedraggable": "^4.1.0"}, "devDependencies": {"@eslint/js": "^9.14.0", "@quasar/app-vite": "^2.1.0", "@types/node": "^20.5.9", "@types/uuid": "^10.0.0", "@vue/eslint-config-typescript": "^14.4.0", "@vue/test-utils": "^2.4.6", "autoprefixer": "^10.4.2", "electron": "^35.1.4", "eslint": "^9.14.0", "eslint-plugin-vue": "^9.30.0", "globals": "^15.12.0", "typescript": "~5.5.3", "vite-plugin-checker": "^0.9.0", "vue-tsc": "^2.0.29"}, "engines": {"node": "^28 || ^26 || ^24 || ^22 || ^20 || ^18", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}}