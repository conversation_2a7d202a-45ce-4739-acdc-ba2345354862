# Creating a RunPod Template for Swarm<PERSON> with Auto-Backend Configuration

This guide provides step-by-step instructions for creating a RunPod template with SwarmUI and ComfyUI pre-installed and automatically configured, allowing for direct API access without manual web interface setup.

## Prerequisites

- A RunPod account with billing set up
- Basic knowledge of Linux commands
- The `template-startup.sh` script from this repository

## Step 1: Create a RunPod Instance

1. Log in to RunPod (https://www.runpod.io/)
2. Click "Deploy" to create a new pod
3. Select a GPU (NVIDIA RTX A5000 or similar is recommended)
4. Under "Container", select "Docker Image" and enter:
   ```
   runpod/pytorch:2.0.1-py3.10-cuda11.8.0-devel-ubuntu22.04
   ```
5. Set the Container Disk to at least 50GB
6. Add a Volume with at least 100GB
7. Click "Deploy"

## Step 2: Upload and Test the Startup Script

1. Once the pod is running, click "Connect" and select "SSH"
2. Copy the SSH command and run it in your terminal

3. There are several ways to create the startup script on the pod:

   **Option A: Use the simplified script (recommended)**
   ```bash
   # Create the script file
   cat > /workspace/template-startup.sh << 'EOL'
   # Copy and paste the content from `.arch/foam/swarmUI-server/template-startup-simple.sh`
   EOL
   ```

   The simplified script is a more condensed version that's easier to copy-paste into the terminal.

   **Option B: Install nano first**
   ```bash
   apt-get update && apt-get install -y nano
   nano /workspace/template-startup.sh
   # Copy and paste the content from `.arch/foam/swarmUI-server/template-startup.sh`
   # Save the file (Ctrl+O, then Enter, then Ctrl+X)
   ```

   **Option C: Use vi (if you're familiar with it)**
   ```bash
   vi /workspace/template-startup.sh
   # Press i to enter insert mode
   # Copy and paste the content from `.arch/foam/swarmUI-server/template-startup.sh`
   # Press Esc, then type :wq and press Enter to save and exit
   ```

   **Option D: Create the file locally and upload via SCP**
   ```bash
   # On your local machine, save the script content to a file
   # Then upload it to the pod (replace <pod-ip> with your pod's IP)
   scp /path/to/local/template-startup.sh root@<pod-ip>:/workspace/
   ```

4. Make the script executable:
   ```bash
   chmod +x /workspace/template-startup.sh
   ```
7. Run the script to test it:
   ```bash
   /workspace/template-startup.sh
   ```
8. Monitor the log file to check progress:
   ```bash
   tail -f /workspace/template-startup.log
   ```

The script will:
- Install all necessary dependencies
- Download ComfyUI and SwarmUI
- Download the required models (Flux, CLIP, VAE)
- Configure SwarmUI with ComfyUI as a backend
- Start both ComfyUI and SwarmUI
- Generate an API key for SwarmUI

## Step 3: Verify the Installation

1. Check that both services are running:
   ```bash
   ps aux | grep ComfyUI
   ps aux | grep SwarmUI
   ```

2. Note the API key that is generated (shown in the log)
3. Access the SwarmUI API at:
   ```
   https://<pod-id>-7801.proxy.runpod.net/api
   ```

4. Test the API with curl:
   ```bash
   curl -H "Authorization: Bearer <your-api-key>" https://<pod-id>-7801.proxy.runpod.net/api/info
   ```

## Step 4: Create a Template

1. Stop the pod (but don't terminate it)
2. In the RunPod dashboard, select your pod
3. Click "Create Template"
4. Fill in the template details:
   - Name: "SwarmUI-Auto-Backend"
   - Description: "SwarmUI with auto-configured ComfyUI backend"
   - Container Image: `runpod/pytorch:2.0.1-py3.10-cuda11.8.0-devel-ubuntu22.04`
   - Container Disk: 50GB
   - Volume Size: 100GB
   - Expose HTTP Port: 7801 (the SwarmUI API port)
   - Start Script: `/workspace/template-startup.sh`

5. Click "Create Template"
6. Note the Template ID for use in your application

## Step 5: Update Supabase Edge Function

1. Go to your Supabase dashboard: https://app.supabase.com/
2. Select your project
3. Navigate to "Settings" > "API" in the left sidebar
4. Scroll down to "Edge Functions"
5. Click "Add a new secret"
6. Add the following secret:
   - `SWARMUI_TEMPLATE_ID`: Your new template ID

## Step 6: Test the Template

1. Deploy a new pod using your template
2. Wait for it to start (this may take 10-15 minutes as it downloads and installs everything)
3. Test the API with curl:
   ```bash
   curl -H "Authorization: Bearer <your-api-key>" https://<pod-id>-7801.proxy.runpod.net/api/info
   ```
4. You should be able to use the API directly without needing to access the web interface

## Step 7: Test from Your Application

1. Go to your application's admin dashboard
2. Click "Create RunPod" in the SwarmUI Server section
3. Select a GPU type
4. Click "Create"
5. The server should start automatically with the backend pre-configured
6. Once the server is running, you should be able to generate images directly via the API

## Troubleshooting

If you encounter issues:

1. Check the template startup log:
   ```bash
   cat /workspace/template-startup.log
   ```

2. Check the ComfyUI log:
   ```bash
   cat /workspace/comfyui.log
   ```

3. Check the SwarmUI log:
   ```bash
   cat /workspace/SwarmUI/logs/swarm.log
   ```

4. Verify that both services are running:
   ```bash
   ps aux | grep ComfyUI
   ps aux | grep SwarmUI
   ```

5. Check that the model paths are correct in the config.json file:
   ```bash
   cat ~/.swarmui/config.json
   ```

6. Test the SwarmUI API directly:
   ```bash
   curl -X GET "http://localhost:7801/api/models" \
     -H "Authorization: Bearer YOUR_API_KEY"
   ```
   Replace `YOUR_API_KEY` with the API key displayed in the startup log.

## Customizing the Template

If you need to customize the template:

1. Modify the `template-startup.sh` script
2. Create a new pod with the modified script
3. Test the changes
4. Create a new template from the pod
5. Update the `SWARMUI_TEMPLATE_ID` environment variable in Supabase

## Next Steps

After creating the template, you can:

1. Implement image generation directly from your application
2. Add more models to the template
3. Optimize the startup script for faster initialization
4. Implement auto-shutdown for idle servers to save costs
