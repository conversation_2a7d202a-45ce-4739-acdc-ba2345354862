<template>
  <div class="shortcut-recorder">
    <q-input
      v-model="displayValue"
      readonly
      outlined
      dense
      :placeholder="$t('settings.shortcuts.pressKeys')"
      @focus="startRecording"
      @blur="stopRecording"
      @keydown.stop.prevent="recordKeyCombo"
      :class="{ 'recording': isRecording }"
    >
      <template v-slot:append>
        <q-icon 
          v-if="modelValue" 
          name="close" 
          class="cursor-pointer" 
          @click.stop="clearShortcut" 
        />
      </template>
    </q-input>
    <div v-if="error" class="text-negative q-mt-xs text-caption">
      {{ error }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useKeyboardShortcutsStore } from 'src/stores/keyboardShortcutsStore';
import { useKeyboardShortcuts } from 'src/composables/useKeyboardShortcuts';
import { useI18n } from 'vue-i18n';

const props = defineProps<{
  modelValue: string;
  excludeId?: string;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void;
}>();

const { t } = useI18n();
const shortcutsStore = useKeyboardShortcutsStore();
const { formatKeyCombo } = useKeyboardShortcuts();

const isRecording = ref(false);
const displayValue = computed(() => props.modelValue || '');
const error = ref('');

// Start recording keyboard input
function startRecording() {
  isRecording.value = true;
  error.value = '';
}

// Stop recording keyboard input
function stopRecording() {
  isRecording.value = false;
}

// Clear the current shortcut
function clearShortcut() {
  emit('update:modelValue', '');
  error.value = '';
}

// Record key combinations
function recordKeyCombo(event: KeyboardEvent) {
  if (!isRecording.value) return;
  
  const keyCombo = formatKeyCombo(event);
  
  // Validate the key combo
  if (keyCombo.split('+').length < 2) {
    error.value = t('settings.shortcuts.needsModifier');
    return;
  }
  
  // Check if this shortcut is already used
  if (shortcutsStore.isShortcutUsed(keyCombo, props.excludeId)) {
    error.value = t('settings.shortcuts.alreadyUsed');
    return;
  }
  
  emit('update:modelValue', keyCombo);
  error.value = '';
}
</script>

<style lang="scss" scoped>
.shortcut-recorder {
  .recording {
    background-color: rgba(0, 0, 0, 0.05);
    
    .body--dark & {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
}
</style>
