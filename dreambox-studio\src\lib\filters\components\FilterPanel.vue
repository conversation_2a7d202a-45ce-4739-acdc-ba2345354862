<template>
  <div class="filter-panel">
    <!-- Saved Filter Selector -->
    <saved-filter-selector
      class="q-mb-xs"
      :filters="filterStore.filtersByTable"
      :current-filter-id="filterStore.currentFilterId"
      :has-unsaved-changes="filterStore.hasUnsavedChanges"
      :loading="filterStore.loading"
      @select="loadFilter"
      @save="saveFilter"
      @delete="deleteFilter"
      @clear="clearAllFilters"
    />

    <!-- Visible Columns Section (formerly Main Filters) -->
    <filter-section
      :title="t('filters.visibleColumns')"
      :active-filter-count="mainFilterCount"
      :has-filters="filterStore.mainTableColumns.length > 0"
    >
      <draggable
        v-model="mainColumns"
        group="filters"
        item-key="id"
        handle=".drag-handle"
        ghost-class="ghost"
        chosen-class="chosen"
        @end="handleDragEnd"
        @update="handleDragUpdate('main')"
        @add="handleDragAdd('main')"
        @remove="handleDragRemove('main')"
      >
        <template #item="{ element }">
          <filter-control
            :column="element"
            v-model="activeFilters[element.id]"
            @update:model-value="updateFilter(element.id, $event)"
            @clear="clearFilter(element.id)"
            @toggle-visibility="handleToggleVisibility"
          />
        </template>
      </draggable>
    </filter-section>

    <!-- Details Columns Section (formerly Expanded Filters) -->
    <filter-section
      :title="t('filters.detailsColumns')"
      :active-filter-count="expandedFilterCount"
      :has-filters="filterStore.expandedViewColumns.length > 0"
      :initial-expanded="false"
    >
      <draggable
        v-model="expandedColumns"
        group="filters"
        item-key="id"
        handle=".drag-handle"
        ghost-class="ghost"
        chosen-class="chosen"
        @end="handleDragEnd"
        @update="handleDragUpdate('expanded')"
        @add="handleDragAdd('expanded')"
        @remove="handleDragRemove('expanded')"
      >
        <template #item="{ element }">
          <filter-control
            :column="element"
            v-model="activeFilters[element.id]"
            @update:model-value="updateFilter(element.id, $event)"
            @clear="clearFilter(element.id)"
            @toggle-visibility="handleToggleVisibility"
          />
        </template>
      </draggable>
    </filter-section>

    <!-- Specialized Filters Sections -->
    <template v-for="filterType in filterStore.specializedFilterTypes" :key="filterType.id">
      <filter-section
        :title="t(`filters.${filterType.id}`)"
        :active-filter-count="specializedFilters[filterType.id] ? 1 : 0"
        :has-filters="true"
        :initial-expanded="false"
      >
        <component
          :is="filterType.component"
          v-model="specializedFilters[filterType.id]"
          @update:model-value="updateSpecializedFilter(filterType.id, $event)"
        />
      </filter-section>
    </template>

    <!-- Column Management Section removed as it's redundant with the drag and drop functionality in the filter sections -->

    <!-- Save Filter Dialog removed - using the one in SavedFilterSelector instead -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { useFilterStore } from '../../../stores/filterStore';
import SavedFilterSelector from './SavedFilterSelector.vue';
import FilterSection from './FilterSection.vue';
import FilterControl from './FilterControl.vue';
import draggable from 'vuedraggable';
import { useI18n } from 'vue-i18n';
import type { ColumnDefinition } from '../../../stores/filterStore';

// Props
const props = defineProps<{
  tableName: string;
  columns?: ColumnDefinition[];
}>();

// Emits
const emit = defineEmits<{
  (e: 'filter-changed'): void;
  (e: 'filter-loaded', filterId: number): void;
  (e: 'filter-saved', filterId: number): void;
  (e: 'filter-deleted', filterId: number): void;
}>();

// Stores
const filterStore = useFilterStore();

// Initialize i18n
const { t } = useI18n();

// State
const activeFilters = ref<Record<string, unknown>>({});
const specializedFilters = ref<Record<string, unknown>>({});

// Computed
const mainFilterCount = computed(() => {
  return Object.keys(activeFilters.value).filter(key => {
    const column = filterStore.columns.find(col => col.id === key);
    return column && column.section === 'main' && activeFilters.value[key] !== null;
  }).length;
});

const expandedFilterCount = computed(() => {
  return Object.keys(activeFilters.value).filter(key => {
    const column = filterStore.columns.find(col => col.id === key);
    return column && column.section === 'expanded' && activeFilters.value[key] !== null;
  }).length;
});

// Removed unused currentContext computed property

// State for draggable columns
const mainColumns = ref<ColumnDefinition[]>([]);
const expandedColumns = ref<ColumnDefinition[]>([]);

// Watch for changes in the filter store columns
watch(() => filterStore.mainTableColumns, (newColumns) => {
  mainColumns.value = [...newColumns];
}, { deep: true, immediate: true });

watch(() => filterStore.expandedViewColumns, (newColumns) => {
  expandedColumns.value = [...newColumns];
}, { deep: true, immediate: true });

// Watch for changes in store filters
watch(() => filterStore.activeFilters, (newFilters) => {
  activeFilters.value = { ...newFilters };
}, { deep: true });

watch(() => filterStore.specializedFilters, (newFilters) => {
  specializedFilters.value = { ...newFilters };
}, { deep: true });

// Methods
function updateFilter(columnId: string, value: unknown) {
  activeFilters.value[columnId] = value;
  filterStore.updateFilter(columnId, value);
  emit('filter-changed');

  // Dispatch global event
  document.dispatchEvent(new CustomEvent('filter-event', {
    detail: {
      type: 'filter-changed',
      columnId,
      value
    }
  }));
}

function clearFilter(columnId: string) {
  delete activeFilters.value[columnId];
  filterStore.clearFilter(columnId);
  emit('filter-changed');

  // Dispatch global event
  document.dispatchEvent(new CustomEvent('filter-event', {
    detail: {
      type: 'filter-changed',
      columnId
    }
  }));
}

// clearMainFilters and clearExpandedFilters functions removed as they're no longer used

function clearAllFilters() {
  console.log('Clearing all filters including specialized filters');

  // Clear all filters
  activeFilters.value = {};

  // Clear specialized filters
  specializedFilters.value = {};

  // Clear filters in the store
  filterStore.clearAllFilters();

  // Reset the current filter ID to allow creating a new filter
  filterStore.currentFilterId = null;

  // Emit filter changed event
  emit('filter-changed');

  // Dispatch global event
  document.dispatchEvent(new CustomEvent('filter-event', {
    detail: {
      type: 'filter-changed'
    }
  }));
}

function updateSpecializedFilter(filterId: string, value: unknown) {
  specializedFilters.value[filterId] = value;
  filterStore.updateSpecializedFilter(filterId, value);
  emit('filter-changed');
}

// clearSpecializedFilter function removed as it's no longer used

function loadFilter(filterId: number) {
  // This is not an async function, so don't await it
  filterStore.loadFilter(filterId);

  // Update local state
  activeFilters.value = { ...filterStore.activeFilters };
  specializedFilters.value = { ...filterStore.specializedFilters };

  // Emit event
  emit('filter-loaded', filterId);
  emit('filter-changed');

  // Dispatch global event
  document.dispatchEvent(new CustomEvent('filter-event', {
    detail: {
      type: 'filter-loaded',
      filterId
    }
  }));
}

async function saveFilter(filterData: { name: string, description: string, makeDefault: boolean }) {
  const filterId = await filterStore.saveCurrentFilter(
    filterData.name,
    filterData.description || null,
    filterData.makeDefault
  );

  if (filterId) {
    // Emit event
    emit('filter-saved', filterId);

    // Dispatch global event
    document.dispatchEvent(new CustomEvent('filter-event', {
      detail: {
        type: 'filter-saved',
        filterId
      }
    }));
  }
}

async function deleteFilter(filterId: number) {
  const success = await filterStore.deleteFilter(filterId);
  if (success) {
    emit('filter-deleted', filterId);

    // Dispatch global event
    document.dispatchEvent(new CustomEvent('filter-event', {
      detail: {
        type: 'filter-deleted',
        filterId
      }
    }));
  }
}

/**
 * Handle toggle visibility event from FilterControl
 */
function handleToggleVisibility(columnId: string, visible: boolean) {
  // The actual update is already done in the FilterControl component
  // This is just a hook for any additional logic we might want to add
  console.log(`Column ${columnId} visibility toggled to ${visible}`);

  // We could emit an event to parent components if needed
  emit('filter-changed');
}

// handleColumnMoved function removed as it's no longer needed

/**
 * Handle drag update event from draggable
 * This is called when items are reordered within the same list
 * section: 'main' = Visible Columns, 'expanded' = Details Columns
 */
function handleDragUpdate(section: 'main' | 'expanded') {
  console.log(`Drag update in ${section === 'main' ? 'Visible' : 'Details'} Columns section`);

  // Get the columns for this section
  const columns = section === 'main' ? mainColumns.value : expandedColumns.value;

  // Update the order of columns in the store
  columns.forEach((column, index) => {
    // Update the order in the column object
    column.order = index;

    // Update the order in the store
    filterStore.updateColumnOrder(column.id, index);
  });

  // Emit filter changed event to update the table
  emit('filter-changed');
}

/**
 * Handle drag add event from draggable
 * This is called when an item is added to a list from another list
 * section: 'main' = Visible Columns, 'expanded' = Details Columns
 */
function handleDragAdd(section: 'main' | 'expanded') {
  console.log(`Item added to ${section === 'main' ? 'Visible' : 'Details'} Columns section`);

  // Get the columns for this section
  const columns = section === 'main' ? mainColumns.value : expandedColumns.value;

  // Find the added column (the one with the wrong section)
  const addedColumn = columns.find(column =>
    column.section !== section
  );

  if (addedColumn) {
    console.log(`Column ${addedColumn.id} moved to ${section} section`);

    // Update the section in the column object
    addedColumn.section = section;

    // Update the section in the store
    filterStore.moveColumnToSection(addedColumn.id, section);
  }

  // Update the order of all columns in this section
  columns.forEach((column, index) => {
    column.order = index;
    filterStore.updateColumnOrder(column.id, index);
  });

  // Emit filter changed event to update the table
  emit('filter-changed');
}

/**
 * Handle drag remove event from draggable
 * This is called when an item is removed from a list to another list
 * section: 'main' = Visible Columns, 'expanded' = Details Columns
 */
function handleDragRemove(section: 'main' | 'expanded') {
  console.log(`Item removed from ${section === 'main' ? 'Visible' : 'Details'} Columns section`);

  // Update the order of remaining columns in this section
  const columns = section === 'main' ? mainColumns.value : expandedColumns.value;

  columns.forEach((column, index) => {
    column.order = index;
    filterStore.updateColumnOrder(column.id, index);
  });
}

/**
 * Handle drag end event from draggable
 * This is called when a drag operation is completed
 */
function handleDragEnd() {
  console.log('Drag ended');

  // Force a reactive update of both sections
  setTimeout(() => {
    // Update main columns order
    mainColumns.value.forEach((column, index) => {
      column.order = index;
      filterStore.updateColumnOrder(column.id, index);
    });

    // Update expanded columns order
    expandedColumns.value.forEach((column, index) => {
      column.order = index;
      filterStore.updateColumnOrder(column.id, index);
    });

    // Emit filter changed event to update the table
    emit('filter-changed');
  }, 0);
}

// Initialize
onMounted(async () => {
  // Initialize filter store with table name
  await filterStore.setTable(props.tableName);

  // Set columns if provided
  if (props.columns) {
    filterStore.setColumns(props.columns);
  }

  // Initialize local state
  activeFilters.value = { ...filterStore.activeFilters };
  specializedFilters.value = { ...filterStore.specializedFilters };

  // No need to set initial filter name anymore as it's handled in the SavedFilterSelector component
});
</script>

<style scoped>
.filter-panel {
  width: 100%;
  max-width: 100%;
}

/* Draggable styles */
.ghost {
  opacity: 0.5;
  background: #c8ebfb;
  border: 1px dashed #2196f3;
}

.chosen {
  background: #eaf5fe;
}
</style>
