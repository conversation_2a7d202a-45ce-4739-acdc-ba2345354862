/**
 * FilterRegistry.ts
 *
 * This module provides a central registry for all filter type definitions.
 * It allows registering, retrieving, and managing filter types across the application.
 */

import type { Component } from 'vue';

/**
 * Interface for filter type definitions
 */
export interface FilterTypeDefinition {
  id: string;                 // Unique identifier for the filter type
  name: string;               // Display name
  section: 'main' | 'expanded' | 'specialized'; // Section where the filter appears
  component: Component;       // Vue component for rendering the filter
  tableTypes: string[];       // Tables where this filter can be used
  stateExtractor?: (state: unknown) => unknown; // Function to extract filter state
  stateApplier?: (item: unknown, value: unknown) => boolean; // Function to apply filter to an item
  clearHandler?: (state: unknown) => unknown; // Function to clear filter state
}

/**
 * Filter Registry class
 */
class FilterRegistry {
  private filterTypes: FilterTypeDefinition[] = [];

  /**
   * Register a new filter type
   */
  registerFilterType(filterType: FilterTypeDefinition): void {
    // Check if filter type already exists
    const existingIndex = this.filterTypes.findIndex(ft => ft.id === filterType.id);

    if (existingIndex >= 0) {
      // Replace existing filter type
      this.filterTypes[existingIndex] = filterType;
      // console.log(`Filter type '${filterType.id}' updated`);
    } else {
      // Add new filter type
      this.filterTypes.push(filterType);
      // console.log(`Filter type '${filterType.id}' registered`);
    }
  }

  /**
   * Get all registered filter types
   */
  getAllFilterTypes(): FilterTypeDefinition[] {
    return [...this.filterTypes];
  }

  /**
   * Get filter types for a specific table
   */
  getFilterTypesForTable(tableName: string): FilterTypeDefinition[] {
    return this.filterTypes.filter(ft =>
      ft.tableTypes.includes(tableName) || ft.tableTypes.includes('*')
    );
  }

  /**
   * Get filter types for a specific section and table
   */
  getFilterTypesForSection(tableName: string, section: string): FilterTypeDefinition[] {
    return this.getFilterTypesForTable(tableName)
      .filter(ft => ft.section === section);
  }

  /**
   * Get a specific filter type by ID
   */
  getFilterTypeById(id: string): FilterTypeDefinition | undefined {
    return this.filterTypes.find(ft => ft.id === id);
  }

  /**
   * Clear all registered filter types
   */
  clearFilterTypes(): void {
    this.filterTypes = [];
    // console.log('All filter types cleared');
  }
}

// Create and export a singleton instance
export const filterRegistry = new FilterRegistry();

// Export default for module imports
export default filterRegistry;
