import type { DatabaseWithFilters } from './supabase-filters';

// Extend the Database interface to include filter_engine_filters table and filter_engine_templates function
export interface DatabaseWithFilterEngine extends DatabaseWithFilters {
  public: {
    Tables: {
      filter_engine_filters: {
        Row: {
          id: number;
          user_id: number;
          name: string;
          description: string | null;
          table_name: string;
          context: string;
          configuration: {
            columns: Array<{
              id: string;
              value?: unknown;
              visible?: boolean;
              section?: 'main' | 'expanded';
              order?: number;
              filterValue?: string | null;
            }>;
            specializedFilters: Record<string, unknown>;
          };
          is_default: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: number;
          user_id: number;
          name: string;
          description?: string | null;
          table_name: string;
          context: string;
          configuration: {
            columns: Array<{
              id: string;
              value?: unknown;
              visible?: boolean;
              section?: 'main' | 'expanded';
              order?: number;
              filterValue?: string | null;
            }>;
            specializedFilters: Record<string, unknown>;
          };
          is_default?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: number;
          user_id?: number;
          name?: string;
          description?: string | null;
          table_name?: string;
          context?: string;
          configuration?: {
            columns: Array<{
              id: string;
              value?: unknown;
              visible?: boolean;
              section?: 'main' | 'expanded';
              order?: number;
              filterValue?: string | null;
            }>;
            specializedFilters: Record<string, unknown>;
          };
          is_default?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "filter_engine_filters_user_id_fkey";
            columns: ["user_id"];
            referencedRelation: "app_users";
            referencedColumns: ["id"];
          }
        ];
      };
      collections: {
        Row: {
          id: number;
          name: string;
          description: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: number;
          name: string;
          description?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: number;
          name?: string;
          description?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [];
      };
      products: {
        Row: {
          id: number;
          name: string;
          description: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: number;
          name: string;
          description?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: number;
          name?: string;
          description?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [];
      };
    } & DatabaseWithFilters['public']['Tables'];
    Functions: {
      filter_engine_templates: {
        Args: {
          p_company_id?: number;
          p_designer_id?: number;
          p_status?: string;
          p_published?: boolean;
          p_search?: string;
          p_limit?: number;
          p_offset?: number;
          p_template_id?: number;
          p_sort_by?: string;
          p_sort_desc?: boolean;
          p_element_angles?: number[];
          p_element_artistic_reference?: number[];
          p_element_blend_concepts?: number[];
          p_element_camera_settings?: number[];
          p_element_color_scheme?: number[];
          p_element_composition?: number[];
          p_element_lighting?: number[];
          p_element_mood?: number[];
          p_element_style?: number[];
          p_element_subject?: number[];
        };
        Returns: {
          id: number;
          name: string;
          description: string | null;
          version: number;
          created_at: string;
          updated_at: string;
          tmpl_company_id: number;
          company_name: string;
          initiated_by: number;
          initiated_by_name: string;
          approved_by: number | null;
          approved_by_name: string | null;
          approval_date: string | null;
          agent_id: number | null;
          agent_name: string | null;
          status: string;
          published: boolean;
          image_server_data: unknown;
          elements: unknown;
          images: unknown;
          events: unknown;
          products: unknown;
          collections: unknown;
          total_count: number;
        }[];
      };
      filter_engine_templates_optimized: {
        Args: {
          p_company_id?: number;
          p_designer_id?: number;
          p_status?: string;
          p_published?: boolean;
          p_search?: string;
          p_limit?: number;
          p_offset?: number;
          p_template_id?: number;
          p_sort_by?: string;
          p_sort_desc?: boolean;
          p_element_types?: number[];
          p_include_elements?: boolean;
        };
        Returns: {
          id: number;
          name: string;
          description: string;
          status: string;
          version: number;
          created_at: string;
          updated_at: string;
          tmpl_company_id: number;
          designer_id: number;
          designer_name: string;
          approved_by: number | null;
          approval_date: string | null;
          agent_id: number | null;
          published: boolean;
          company_name: string;
          approver_name: string | null;
          agent_name: string | null;
          elements: unknown;
          collections: unknown;
          products: unknown;
          total_count: number;
        }[];
      };
      get_template_element_types: {
        Args: {
          p_template_id: number;
        };
        Returns: {
          id: number;
          name: string;
          description: string;
          is_array: boolean;
          is_required: boolean;
        }[];
      };
      get_elements_by_types: {
        Args: {
          p_type_ids: number[];
          p_user_id?: number;
        };
        Returns: {
          id: number;
          type_id: number;
          value: string;
          description: string | null;
          is_system: boolean;
          designer_id: number | null;
        }[];
      };
    } & DatabaseWithFilters['public']['Functions'];
    Views: DatabaseWithFilters['public']['Views'];
    Enums: DatabaseWithFilters['public']['Enums'];
    CompositeTypes: DatabaseWithFilters['public']['CompositeTypes'];
  };
}

// Export SavedFilterEngine type for use in components
export type SavedFilterEngine = DatabaseWithFilterEngine['public']['Tables']['filter_engine_filters']['Row'];
