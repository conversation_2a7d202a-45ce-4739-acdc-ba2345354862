// SwarmUI API Types

export interface SwarmUIModel {
  id: string;
  name: string;
  type: string;
  description?: string;
  size?: number;
}

export interface SwarmUIGenerationParams {
  prompt: string;
  negative_prompt?: string;
  model: string;
  width: number;
  height: number;
  steps: number;
  cfg_scale: number;
  sampler: string;
  batch_size?: number;
  seed?: number | undefined;
  scheduler?: string | undefined;
  clip_skip?: number | undefined;
  tiling?: boolean | undefined;
  hires_fix?: boolean | undefined;
  hires_scale?: number | undefined;
  hires_steps?: number | undefined;
  hires_upscaler?: string | undefined;
  hires_denoising_strength?: number | undefined;
}

export interface SwarmUIGenerationRequest {
  params: SwarmUIGenerationParams;
  webhook_url?: string;
  webhook_events?: string[];
}

export interface SwarmUIGenerationResponse {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  params: SwarmUIGenerationParams;
  images?: SwarmUIGeneratedImage[];
  error?: string;
  created_at: string;
  updated_at: string;
}

export interface SwarmUIGeneratedImage {
  id: string;
  url: string;
  width: number;
  height: number;
  seed: number;
  prompt: string;
  negative_prompt?: string;
}

export interface SwarmUIServerInfo {
  version: string;
  models: SwarmUIModel[];
  samplers: string[];
  schedulers: string[];
  upscalers: string[];
}

export interface ServerStatus {
  id?: number;
  status: 'starting' | 'running' | 'stopping' | 'stopped' | 'error' | 'no-server';
  message?: string;
  apiEndpoint?: string;
  apiKey?: string;
  podId?: string;
  startedAt?: string;
  lastActivity?: string;
}

export interface ServerStartOptions {
  gpuTypeId?: string;
  configurationId?: number;
}

export interface ServerStartResult {
  serverId?: number;
  status: 'starting' | 'running' | 'error';
  message: string;
}
