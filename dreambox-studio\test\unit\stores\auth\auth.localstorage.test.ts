import { describe, test, expect, beforeEach, afterEach, mock, spyOn } from 'bun:test';
import { setActive<PERSON><PERSON>, createP<PERSON> } from 'pinia';

// Create a mock for the auth store that only tests localStorage functionality
// This avoids the issues with Supabase client initialization

// Mock localStorage
const mockLocalStorage = {
  store: {} as Record<string, string>,
  getItem: function(key: string) {
    return this.store[key] || null;
  },
  setItem: function(key: string, value: string) {
    this.store[key] = value;
  },
  removeItem: function(key: string) {
    delete this.store[key];
  },
  clear: function() {
    this.store = {};
  }
};

// Mock console methods to avoid cluttering test output
const originalConsoleLog = console.log;
const originalConsoleWarn = console.warn;
const originalConsoleError = console.error;

// Create a simplified auth store for testing localStorage functionality
const createTestAuthStore = () => {
  // Auth user key
  const AUTH_USER_KEY = 'dreambox-auth-user';
  
  // State
  const user = {
    id: 'test-id',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    companies: [{ id: '1', name: 'Test Company' }],
    roles: ['admin'],
    currentCompany: { id: '1', name: 'Test Company' },
    currentRole: 'admin'
  };
  
  // Save user state to localStorage
  function saveUserToLocalStorage() {
    try {
      const userData = JSON.stringify(user);
      mockLocalStorage.setItem(AUTH_USER_KEY, userData);
      console.log('User data saved to localStorage');
    } catch (error) {
      console.error('Failed to save user data to localStorage:', error);
    }
  }

  // Load user state from localStorage
  function loadUserFromLocalStorage() {
    console.log('Attempting to load user from localStorage');
    const savedUser = mockLocalStorage.getItem(AUTH_USER_KEY);

    if (savedUser) {
      console.log('Found saved user data in localStorage');
      try {
        const parsedUser = JSON.parse(savedUser);
        Object.assign(user, parsedUser);
        console.log('Successfully loaded user from localStorage');
        return true;
      } catch (e) {
        console.error('Failed to parse saved user data:', e);
        mockLocalStorage.removeItem(AUTH_USER_KEY);
      }
    } else {
      console.log('No saved user found in localStorage');
    }
    return false;
  }

  // Clear user state from localStorage
  function clearUserFromLocalStorage() {
    console.log('Clearing user data from localStorage');
    mockLocalStorage.removeItem(AUTH_USER_KEY);
  }
  
  return {
    user,
    saveUserToLocalStorage,
    loadUserFromLocalStorage,
    clearUserFromLocalStorage
  };
};

describe('Auth Store - LocalStorage Functions', () => {
  beforeEach(() => {
    // Reset localStorage mock
    mockLocalStorage.clear();
    
    // Replace global localStorage with our mock
    Object.defineProperty(globalThis, 'localStorage', {
      value: mockLocalStorage,
      writable: true
    });
    
    // Silence console output during tests
    console.log = mock(() => {});
    console.warn = mock(() => {});
    console.error = mock(() => {});
  });
  
  afterEach(() => {
    // Restore console methods
    console.log = originalConsoleLog;
    console.warn = originalConsoleWarn;
    console.error = originalConsoleError;
  });
  
  test('saveUserToLocalStorage saves user data to localStorage', () => {
    const authStore = createTestAuthStore();
    
    // Save user to localStorage
    authStore.saveUserToLocalStorage();
    
    // Check that localStorage was updated
    const savedUser = mockLocalStorage.getItem('dreambox-auth-user');
    expect(savedUser).not.toBeNull();
    
    // Parse the saved data and verify it matches our user
    const parsedUser = JSON.parse(savedUser!);
    expect(parsedUser.id).toBe('test-id');
    expect(parsedUser.email).toBe('<EMAIL>');
    expect(parsedUser.firstName).toBe('Test');
    expect(parsedUser.lastName).toBe('User');
  });
  
  test('loadUserFromLocalStorage loads user data from localStorage', () => {
    // First save some user data to localStorage
    const userData = {
      id: 'test-id-2',
      email: '<EMAIL>',
      firstName: 'Test2',
      lastName: 'User2',
      companies: [{ id: '2', name: 'Test Company 2' }],
      roles: ['designer'],
      currentCompany: { id: '2', name: 'Test Company 2' },
      currentRole: 'designer'
    };
    mockLocalStorage.setItem('dreambox-auth-user', JSON.stringify(userData));
    
    // Create a new store instance
    const authStore = createTestAuthStore();
    
    // Load from localStorage
    const result = authStore.loadUserFromLocalStorage();
    
    // Verify the result
    expect(result).toBe(true);
    
    // Verify the user data was loaded
    expect(authStore.user.id).toBe('test-id-2');
    expect(authStore.user.email).toBe('<EMAIL>');
    expect(authStore.user.firstName).toBe('Test2');
    expect(authStore.user.lastName).toBe('User2');
  });
  
  test('loadUserFromLocalStorage handles invalid JSON', () => {
    // Save invalid JSON to localStorage
    mockLocalStorage.setItem('dreambox-auth-user', 'not valid json');
    
    // Create a new store instance
    const authStore = createTestAuthStore();
    
    // Load from localStorage
    const result = authStore.loadUserFromLocalStorage();
    
    // Verify the result
    expect(result).toBe(false);
    
    // Verify localStorage was cleared
    expect(mockLocalStorage.getItem('dreambox-auth-user')).toBeNull();
  });
  
  test('clearUserFromLocalStorage removes user data from localStorage', () => {
    // First save some user data to localStorage
    mockLocalStorage.setItem('dreambox-auth-user', JSON.stringify({
      id: 'test-id',
      email: '<EMAIL>'
    }));
    
    // Create a new store instance
    const authStore = createTestAuthStore();
    
    // Clear localStorage
    authStore.clearUserFromLocalStorage();
    
    // Verify localStorage was cleared
    expect(mockLocalStorage.getItem('dreambox-auth-user')).toBeNull();
  });
});
