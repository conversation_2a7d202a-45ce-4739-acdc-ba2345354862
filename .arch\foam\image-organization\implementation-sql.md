# SQL Implementation for Image Organization System

This document provides the SQL commands needed to implement the image organization system in your Supabase database.

## 1. Create the S3 Images Table

```sql
CREATE TABLE s3_images (
  id BIGSERIAL PRIMARY KEY,
  
  -- Basic image information
  filename TEXT NOT NULL,
  s3_key TEXT NOT NULL UNIQUE,
  s3_url TEXT NOT NULL,
  mime_type TEXT NOT NULL,
  file_size BIGINT,
  width INTEGER,
  height INTEGER,
  
  -- Image categorization
  title TEXT,
  description TEXT,
  tags TEXT[],
  resolution TEXT NOT NULL DEFAULT 'standard', -- 'low', 'standard', 'high'
  metadata JSONB DEFAULT '{}',
  is_public BOOLEAN DEFAULT false,
  
  -- Ownership and permissions
  user_id BIGINT NOT NULL REFERENCES app_users(id),
  company_id BIGINT NOT NULL REFERENCES companies(id),
  
  -- Relationships
  template_id BIGINT REFERENCES templates(id),
  collection_id BIGINT REFERENCES collections(id),
  parent_image_id BIGINT REFERENCES s3_images(id), -- For high-res versions of template previews
  
  -- AI generation metadata
  prompt TEXT,
  negative_prompt TEXT,
  seed BIGINT,
  model_used TEXT,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 2. Create the Product Images Junction Table

```sql
CREATE TABLE product_images (
  id BIGSERIAL PRIMARY KEY,
  product_id BIGINT NOT NULL REFERENCES products(id),
  image_id BIGINT NOT NULL REFERENCES s3_images(id),
  position TEXT, -- 'front', 'back', etc.
  is_primary BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure each image position appears only once per product
  UNIQUE(product_id, position)
);
```

## 3. Create Indexes for Performance

```sql
-- Indexes for s3_images table
CREATE INDEX s3_images_user_id_idx ON s3_images(user_id);
CREATE INDEX s3_images_company_id_idx ON s3_images(company_id);
CREATE INDEX s3_images_template_id_idx ON s3_images(template_id) WHERE template_id IS NOT NULL;
CREATE INDEX s3_images_parent_image_id_idx ON s3_images(parent_image_id) WHERE parent_image_id IS NOT NULL;
CREATE INDEX s3_images_tags_idx ON s3_images USING GIN(tags);
CREATE INDEX s3_images_metadata_idx ON s3_images USING GIN(metadata jsonb_path_ops);

-- Indexes for product_images table
CREATE INDEX product_images_product_id_idx ON product_images(product_id);
CREATE INDEX product_images_image_id_idx ON product_images(image_id);
```

## 4. Set Up Row Level Security

```sql
-- Enable RLS
ALTER TABLE s3_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_images ENABLE ROW LEVEL SECURITY;

-- Policies for s3_images
CREATE POLICY "Users can view their own images" 
ON s3_images FOR SELECT
USING (user_id = auth.uid());

CREATE POLICY "Users can view their company's images" 
ON s3_images FOR SELECT
USING (
  company_id IN (
    SELECT cu_company_id FROM companies_users 
    WHERE app_user_id = auth.uid()
  )
);

CREATE POLICY "Anyone can view public images" 
ON s3_images FOR SELECT
USING (is_public = true);

CREATE POLICY "Users can insert their own images" 
ON s3_images FOR INSERT
WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own images" 
ON s3_images FOR UPDATE
USING (user_id = auth.uid());

CREATE POLICY "Admins can do anything with images" 
ON s3_images
USING (
  EXISTS (
    SELECT 1 FROM companies_users_roles
    WHERE app_user_id = auth.uid() 
    AND role_id IN (SELECT id FROM roles WHERE name IN ('admin', 'super-admin'))
  )
);

-- Policies for product_images
CREATE POLICY "Users can view product images for their company" 
ON product_images FOR SELECT
USING (
  product_id IN (
    SELECT p.id FROM products p
    JOIN companies c ON p.company_id = c.id
    JOIN companies_users cu ON c.id = cu.cu_company_id
    WHERE cu.app_user_id = auth.uid()
  )
);

CREATE POLICY "Users can insert product images for their company" 
ON product_images FOR INSERT
WITH CHECK (
  product_id IN (
    SELECT p.id FROM products p
    JOIN companies c ON p.company_id = c.id
    JOIN companies_users cu ON c.id = cu.cu_company_id
    WHERE cu.app_user_id = auth.uid()
  )
);

CREATE POLICY "Admins can do anything with product images" 
ON product_images
USING (
  EXISTS (
    SELECT 1 FROM companies_users_roles
    WHERE app_user_id = auth.uid() 
    AND role_id IN (SELECT id FROM roles WHERE name IN ('admin', 'super-admin'))
  )
);
```

## 5. Create Database Functions

```sql
-- Function to create a new image record
CREATE OR REPLACE FUNCTION create_s3_image(
  p_filename TEXT,
  p_s3_key TEXT,
  p_s3_url TEXT,
  p_mime_type TEXT,
  p_file_size BIGINT DEFAULT NULL,
  p_width INTEGER DEFAULT NULL,
  p_height INTEGER DEFAULT NULL,
  p_title TEXT DEFAULT NULL,
  p_description TEXT DEFAULT NULL,
  p_tags TEXT[] DEFAULT NULL,
  p_template_id BIGINT DEFAULT NULL,
  p_resolution TEXT DEFAULT 'standard',
  p_is_public BOOLEAN DEFAULT false,
  p_metadata JSONB DEFAULT '{}',
  p_prompt TEXT DEFAULT NULL,
  p_negative_prompt TEXT DEFAULT NULL,
  p_seed BIGINT DEFAULT NULL,
  p_model_used TEXT DEFAULT NULL,
  p_parent_image_id BIGINT DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_user_id BIGINT;
  v_company_id BIGINT;
  v_image_id BIGINT;
  v_result JSONB;
BEGIN
  -- Get the current user ID
  SELECT id INTO v_user_id FROM app_users WHERE user_id = auth.uid();
  
  -- Get the user's company ID (assuming user belongs to one company)
  SELECT cu_company_id INTO v_company_id
  FROM companies_users
  WHERE app_user_id = v_user_id
  LIMIT 1;
  
  -- Insert the image record
  INSERT INTO s3_images (
    filename, s3_key, s3_url, mime_type, file_size, width, height,
    title, description, tags, resolution, template_id, parent_image_id,
    user_id, company_id, is_public, metadata,
    prompt, negative_prompt, seed, model_used
  ) VALUES (
    p_filename, p_s3_key, p_s3_url, p_mime_type, p_file_size, p_width, p_height,
    p_title, p_description, p_tags, p_resolution, p_template_id, p_parent_image_id,
    v_user_id, v_company_id, p_is_public, p_metadata,
    p_prompt, p_negative_prompt, p_seed, p_model_used
  )
  RETURNING id INTO v_image_id;
  
  -- Return the image data
  SELECT jsonb_build_object(
    'id', id,
    's3_url', s3_url,
    'filename', filename,
    'title', title,
    'description', description,
    'tags', tags,
    'template_id', template_id,
    'resolution', resolution,
    'user_id', user_id,
    'company_id', company_id,
    'created_at', created_at
  ) INTO v_result
  FROM s3_images
  WHERE id = v_image_id;
  
  RETURN v_result;
END;
$$;

-- Function to associate an image with a product
CREATE OR REPLACE FUNCTION associate_image_with_product(
  p_image_id BIGINT,
  p_product_id BIGINT,
  p_position TEXT DEFAULT NULL,
  p_is_primary BOOLEAN DEFAULT false
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_result JSONB;
BEGIN
  -- Insert the product_images record
  INSERT INTO product_images (
    product_id, image_id, position, is_primary
  ) VALUES (
    p_product_id, p_image_id, p_position, p_is_primary
  )
  RETURNING jsonb_build_object(
    'id', id,
    'product_id', product_id,
    'image_id', image_id,
    'position', position,
    'is_primary', is_primary
  ) INTO v_result;
  
  RETURN v_result;
END;
$$;
```

## Implementation Steps

1. Run the SQL commands in the order presented above
2. Deploy the Edge Functions from the documentation
3. Implement the frontend services
4. Test the system with basic image uploads
5. Integrate with SwarmUI

Remember to replace any references to existing tables (like `app_users`, `companies`, etc.) with the actual table names in your database if they differ.
