Great, almost working.
First, loading icon on CREATE COMFYUI SERVER button is diplayed only for about a second. It should be displayed until server is active.
Now when server is loaded, we have Connected badge, which is actually redundant (we already have Server Running badge). So Connected badge should be removed. This is not what I was wanting. When we run new pod, this pod needs to become available for image generation. In runpod we get connect button so we know that server is now available for image generation. I think there is no way, to get this information from runpod api. We can get this information only from comfyui server, when it is running inside runpod. So I propose that we send request to comfyui server and if we get response, we know that server is available for image generation.

As for now, when GENERATE IMAGE button is clicked, we get this in dev tools: 
Returning simulated image generation response in development mode

So next task is to figure out, how to connect to comfyui server (on newly generated pod), generate image and display image in our app.
What do we need to make this work?