import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import chalk from 'chalk';
import inquirer from 'inquirer';
import Table from 'cli-table3';
import { Parser } from 'json2csv';
import { Command } from 'commander';

// Get the directory name of the current module
const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Load environment variables from the parent directory's .env file
dotenv.config({ path: path.join(__dirname, '..', '.env') });

// Directory to save query results
const queriesDir = path.join(__dirname, 'queries');

// Create Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error(chalk.red('Supabase credentials not found in environment variables'));
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Function to sign in with email and password
async function signIn(email, password) {
  try {
    console.log(chalk.blue(`Signing in as ${email}...`));
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (error) {
      throw new Error(`Authentication failed: ${error.message}`);
    }

    console.log(chalk.green('Authentication successful!'));
    return data;
  } catch (error) {
    console.error(chalk.red(`Authentication error: ${error.message}`));
    return null;
  }
}

/**
 * Formats and displays query results in a table
 * @param {Array} data - The query results
 */
function displayResults(data) {
  if (!data || data.length === 0) {
    console.log(chalk.yellow('No results found'));
    return;
  }

  // Get column names from the first row
  const columns = Object.keys(data[0]);

  // Create a new table
  const table = new Table({
    head: columns.map(col => chalk.cyan(col)),
    wordWrap: true,
    wrapOnWordBoundary: true,
    truncate: '…'
  });

  // Add rows to the table
  data.forEach(row => {
    const rowData = columns.map(col => {
      const value = row[col];
      if (value === null) return chalk.gray('NULL');
      if (typeof value === 'object') return JSON.stringify(value);
      return String(value);
    });
    table.push(rowData);
  });

  console.log(table.toString());
  console.log(chalk.green(`${data.length} rows returned`));
}

/**
 * Saves query results to a file
 * @param {Array} data - The query results
 * @param {string} format - The output format (json or csv)
 */
async function saveResults(data, format) {
  if (!data || data.length === 0) {
    console.log(chalk.yellow('No results to save'));
    return;
  }

  // Ask for filename
  const { filename } = await inquirer.prompt([
    {
      type: 'input',
      name: 'filename',
      message: 'Enter filename to save results (without extension):',
      default: `query_result_${Date.now()}`
    }
  ]);

  // Create the queries directory if it doesn't exist
  if (!fs.existsSync(queriesDir)) {
    fs.mkdirSync(queriesDir, { recursive: true });
  }

  let filePath;

  if (format === 'csv') {
    // Convert to CSV
    const parser = new Parser();
    const csv = parser.parse(data);

    filePath = path.join(queriesDir, `${filename}.csv`);
    fs.writeFileSync(filePath, csv);
  } else {
    // Save as JSON
    filePath = path.join(queriesDir, `${filename}.json`);
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
  }

  console.log(chalk.green(`Results saved to ${filePath}`));
}

/**
 * Executes a SQL query
 * @param {string} query - The SQL query to execute
 * @returns {Promise<Array>} The query results
 */
async function executeQuery(query) {
  try {
    console.log(chalk.blue('Executing query...'));

    // Try using the admin_query function first (which bypasses RLS)
    const { data: adminData, error: adminError } = await supabase.rpc('admin_query', { query_text: query });

    if (!adminError) {
      return adminData;
    }

    // If admin_query fails, try execute_sql
    console.log(chalk.yellow('Falling back to execute_sql...'));
    const { data, error } = await supabase.rpc('execute_sql', { sql_query: query });

    if (error) {
      throw new Error(`Query execution failed: ${error.message}`);
    }

    return data;
  } catch (error) {
    // If both RPC functions don't exist, try a direct query
    console.log(chalk.yellow('Falling back to direct query...'));

    try {
      // For simple SELECT queries, we can try to use the from() method directly
      if (query.trim().toLowerCase().startsWith('select')) {
        // Extract table name, removing any trailing semicolons or whitespace
        const parts = query.trim().split(' ');
        let tableName = '';

        // Find the FROM clause
        for (let i = 0; i < parts.length; i++) {
          if (parts[i].toLowerCase() === 'from' && i + 1 < parts.length) {
            tableName = parts[i + 1].replace(/;$/, ''); // Remove trailing semicolon
            break;
          }
        }

        if (tableName) {
          console.log(`Attempting to query table: ${tableName}`);
          const { data, error } = await supabase.from(tableName).select('*');

          if (error) {
            throw new Error(`Direct query failed: ${error.message}`);
          }

          return data;
        }
      }
      throw new Error('Could not execute query directly');
    } catch (directError) {
      console.error(chalk.red(`Error executing query: ${directError.message}`));
      console.log(chalk.yellow('To enable arbitrary SQL queries, create the following functions in your Supabase project:'));
      console.log(`
-- Function that bypasses RLS (preferred method)
CREATE OR REPLACE FUNCTION admin_query(query_text text)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  result JSONB;
BEGIN
  -- Execute the query with elevated privileges (no email check)
  EXECUTE 'SELECT to_jsonb(array_agg(row_to_json(t))) FROM (' || query_text || ') t' INTO result;
  RETURN COALESCE(result, '[]'::jsonb);
END;
$$;

-- Alternative function if admin_query doesn't work
CREATE OR REPLACE FUNCTION execute_sql(sql_query text)
RETURNS JSONB LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  result JSONB;
BEGIN
  EXECUTE 'SELECT to_jsonb(array_agg(row_to_json(t))) FROM (' || sql_query || ') t' INTO result;
  RETURN COALESCE(result, '[]'::jsonb);
END;
$$;
      `);
      return [];
    }
  }
}

/**
 * Interactive query prompt
 * @param {boolean} skipAuth - Whether to skip authentication prompt
 */
async function interactiveQuery(skipAuth = false) {
  let running = true;

  console.log(chalk.green('Interactive SQL Query Tool'));
  console.log(chalk.gray('Type SQL queries and press Enter to execute'));
  console.log(chalk.gray('Type .exit to quit, .help for help'));

  // Sign in if not already authenticated
  if (!skipAuth) {
    const { email, password } = await inquirer.prompt([
      {
        type: 'input',
        name: 'email',
        message: 'Email:',
        default: '<EMAIL>'
      },
      {
        type: 'password',
        name: 'password',
        message: 'Password:',
        mask: '*'
      }
    ]);

    const session = await signIn(email, password);
    if (!session) {
      console.error(chalk.red('Authentication failed. Exiting...'));
      process.exit(1);
    }
  }

  while (running) {
    const { query } = await inquirer.prompt([
      {
        type: 'input',
        name: 'query',
        message: 'SQL>',
        prefix: ''
      }
    ]);

    // Handle special commands
    if (query.trim().toLowerCase() === '.exit') {
      running = false;
      continue;
    }

    if (query.trim().toLowerCase() === '.help') {
      console.log(chalk.cyan('\nAvailable commands:'));
      console.log(chalk.white('  .exit          Exit the program'));
      console.log(chalk.white('  .help          Show this help message'));
      console.log(chalk.white('  .tables        List all tables'));
      console.log(chalk.white('  .save json     Save last results as JSON'));
      console.log(chalk.white('  .save csv      Save last results as CSV'));
      console.log('\n');
      continue;
    }

    if (query.trim().toLowerCase() === '.tables') {
      const tables = await executeQuery(`
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'public'
        ORDER BY table_name
      `);

      console.log(chalk.cyan('\nTables:'));
      tables.forEach(table => {
        console.log(chalk.white(`  ${table.table_name}`));
      });
      console.log('\n');
      continue;
    }

    if (query.trim().toLowerCase() === '.save json') {
      if (lastResults) {
        await saveResults(lastResults, 'json');
      } else {
        console.log(chalk.yellow('No results to save'));
      }
      continue;
    }

    if (query.trim().toLowerCase() === '.save csv') {
      if (lastResults) {
        await saveResults(lastResults, 'csv');
      } else {
        console.log(chalk.yellow('No results to save'));
      }
      continue;
    }

    // Execute the query
    if (query.trim()) {
      try {
        const startTime = Date.now();
        const results = await executeQuery(query);
        const endTime = Date.now();

        lastResults = results;

        // Display results
        displayResults(results);
        console.log(chalk.gray(`Query executed in ${endTime - startTime}ms`));

        // Don't ask about saving results - just continue
        // Uncomment the following lines if you want to enable the save prompt
        /*
        if (results && results.length > 0) {
          const { saveOption } = await inquirer.prompt([
            {
              type: 'list',
              name: 'saveOption',
              message: 'Save results?',
              choices: [
                { name: 'No', value: 'no' },
                { name: 'Save as JSON', value: 'json' },
                { name: 'Save as CSV', value: 'csv' }
              ]
            }
          ]);

          if (saveOption !== 'no') {
            await saveResults(results, saveOption);
          }
        }
        */
      } catch (error) {
        console.error(chalk.red(`Error: ${error.message}`));
      }
    }
  }

  console.log(chalk.green('Goodbye!'));
}

// Store the last query results
let lastResults = null;

// Parse command line arguments
const program = new Command();
program
  .option('-e, --email <email>', 'Email for authentication')
  .option('-p, --password <password>', 'Password for authentication')
  .parse(process.argv);

const options = program.opts();

// Run the interactive query prompt with command line arguments if provided
if (options.email && options.password) {
  // Authenticate and run queries with provided credentials
  (async () => {
    const session = await signIn(options.email, options.password);
    if (!session) {
      console.error(chalk.red('Authentication failed. Exiting...'));
      process.exit(1);
    }
    interactiveQuery(true); // Skip authentication prompt
  })();
} else {
  // Run with interactive authentication
  interactiveQuery(false);
}
