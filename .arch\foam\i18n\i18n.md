# Internationalization (i18n) Implementation Guide

This document outlines how internationalization (i18n) is implemented in the Dreambox Studio application and provides guidelines for using and extending it.

## Overview

The application supports multiple languages using Vue I18n. Currently, the following languages are implemented:

- English (en-US) - Default language
- Croatian (hr-HR)

## Architecture

### Core Components

1. **Vue I18n Integration**
   - Located in `src/i18n/index.ts`
   - Creates and configures the i18n instance
   - Provides helper functions for language detection and switching

2. **Language Files**
   - English: `src/i18n/en-US.ts`
   - Croatian: `src/i18n/hr-HR.ts`
   - Organized by feature area (general, auth, navigation, etc.)

3. **Boot File**
   - Located in `src/boot/i18n.ts`
   - Initializes i18n during application startup
   - Sets the initial language based on browser settings or stored preference

4. **Language Switcher Component**
   - Located in `src/components/common/LanguageSwitcher.vue`
   - Provides UI for changing the application language
   - Persists language preference in localStorage

## Language Structure

Language files are organized into sections for different parts of the application:

```typescript
export default {
  // General
  app: {
    name: 'Dreambox Studio',
    loading: 'Loading...',
    // ...
  },

  // Auth
  auth: {
    login: 'Login',
    logout: 'Logout',
    // ...
  },

  // Navigation
  nav: {
    dashboard: 'Dashboard',
    settings: 'Settings',
    // ...
  },

  // Voice Commands
  voiceCommands: {
    title: 'Voice Commands',
    listening: 'Listening...',
    // ...
  },

  // ...other sections
}
```

## How to Use i18n in Components

### In Templates

Use the `$t` function to access translations in templates:

```html
<template>
  <div>
    <h1>{{ $t('app.name') }}</h1>
    <p>{{ $t('app.loading') }}</p>
  </div>
</template>
```

### In Script

Import and use the `useI18n` composable in the script section:

```typescript
<script setup lang="ts">
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

// Use t function to access translations
const pageTitle = t('nav.dashboard');
</script>
```

### Dynamic Content

For dynamic content with variables, use the following syntax:

```typescript
// In language file
{
  "welcome": "Welcome, {name}!"
}

// In component
const message = t('welcome', { name: user.name });
```

## Adding New Translations

### Adding New Text

1. Add the new text to both language files (`en-US.ts` and `hr-HR.ts`)
2. Use a consistent key structure (e.g., `feature.action.element`)
3. Ensure all translations are present in all language files

Example:

```typescript
// In en-US.ts
export default {
  // ...
  features: {
    newFeature: {
      title: 'New Feature',
      description: 'This is a new feature'
    }
  }
}

// In hr-HR.ts
export default {
  // ...
  features: {
    newFeature: {
      title: 'Nova značajka',
      description: 'Ovo je nova značajka'
    }
  }
}
```

### Adding a New Language

To add a new language:

1. Create a new language file in `src/i18n/` (e.g., `de-DE.ts` for German)
2. Copy the structure from an existing language file
3. Translate all strings to the new language
4. Import and register the new language in `src/i18n/index.ts`:

```typescript
import deDE from './de-DE';

const i18n = createI18n({
  // ...
  messages: {
    'en-US': enUS,
    'hr-HR': hrHR,
    'de-DE': deDE
  }
});
```

5. Add the new language to the language switcher in `src/components/common/LanguageSwitcher.vue`:

```typescript
const languages = [
  { label: 'English', value: 'en-US' },
  { label: 'Hrvatski', value: 'hr-HR' },
  { label: 'Deutsch', value: 'de-DE' }
];
```

## Voice Command Integration

The i18n system is integrated with the voice command system to support multilingual commands:

1. Command descriptions and examples are translated in the language files
2. The current language is sent to the Command Agent for processing
3. This allows users to issue voice commands in their preferred language

When implementing the Command Agent in n8n, the current language should be passed as a parameter to help it understand commands in different languages.

## Best Practices

1. **Use Namespaced Keys**
   - Group related translations under common prefixes
   - Example: `auth.login`, `auth.logout`, etc.

2. **Keep Translations Simple**
   - Avoid complex HTML in translation strings
   - Use parameters for dynamic content

3. **Maintain Consistency**
   - Use consistent terminology across the application
   - Keep similar structures for similar UI elements

4. **Test All Languages**
   - Regularly switch between languages to ensure all content is properly translated
   - Check for layout issues with longer text in different languages

5. **Document Special Cases**
   - Note any language-specific formatting or behavior
   - Document any translations that require special handling

## Troubleshooting

### Common Issues

1. **Missing Translations**
   - Check if the key exists in all language files
   - Verify the key path is correct (case-sensitive)

2. **Language Not Changing**
   - Check localStorage for saved preferences
   - Verify the language code is correctly formatted (e.g., 'en-US', not 'en')

3. **Layout Issues**
   - Some languages may have longer text that breaks layouts
   - Use flexible containers and ellipsis for overflow

### Debugging

To debug i18n issues:

1. Check the browser console for warnings or errors
2. Inspect the i18n instance in the Vue devtools
3. Temporarily add debug logging:

```typescript
console.log('Translation key:', key);
console.log('Current locale:', i18n.global.locale.value);
```

## Future Enhancements

Potential future enhancements for the i18n system:

1. **Lazy Loading Translations**
   - Load language files on demand to reduce initial bundle size

2. **Pluralization Support**
   - Implement proper pluralization rules for different languages

3. **Date and Number Formatting**
   - Add locale-specific formatting for dates, times, and numbers

4. **RTL Support**
   - Add support for right-to-left languages like Arabic or Hebrew

5. **Translation Management Tool**
   - Implement a tool to help manage and update translations