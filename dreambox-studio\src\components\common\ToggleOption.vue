<template>
  <div class="toggle-option">
    <div v-if="label" class="toggle-option-label q-mb-sm">{{ label }}</div>
    <div class="toggle-option-container">
      <q-btn
        :color="modelValue === option1.value ? 'primary' : 'grey-4'"
        :text-color="modelValue === option1.value ? 'white' : 'grey-8'"
        :label="option1.label"
        :icon="option1.icon"
        @click="updateValue(option1.value)"
        dense
        no-caps
        unelevated
        class="toggle-btn"
      />
      <q-btn
        :color="modelValue === option2.value ? 'primary' : 'grey-4'"
        :text-color="modelValue === option2.value ? 'white' : 'grey-8'"
        :label="option2.label"
        :icon="option2.icon"
        @click="updateValue(option2.value)"
        dense
        no-caps
        unelevated
        class="toggle-btn"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
interface ToggleOption {
  label: string;
  value: string | boolean | number;
  icon?: string;
}

defineProps<{
  modelValue: string | boolean | number;
  option1: ToggleOption;
  option2: ToggleOption;
  label?: string;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: string | boolean | number): void;
}>();

function updateValue(value: string | boolean | number) {
  emit('update:modelValue', value);
}
</script>

<style lang="scss" scoped>
.toggle-option {
  margin-bottom: 16px;

  .toggle-option-label {
    font-weight: 500;
    color: rgba(0, 0, 0, 0.7);

    .body--dark & {
      color: rgba(255, 255, 255, 0.7);
    }
  }

  .toggle-option-container {
    display: flex;
    border-radius: 4px;
    overflow: hidden;

    .toggle-btn {
      flex: 1;
      min-width: 80px;
      border-radius: 0;

      &:first-child {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
      }

      &:last-child {
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
      }
    }
  }
}
</style>
