<template>
  <q-btn
    :color="color || 'primary'"
    :icon="icon"
    :label="label"
    :disable="disabled"
    no-caps
    class="q-mx-sm"
    @click="executeAction"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRouter } from 'vue-router';
// import { useQuasar } from 'quasar';
import { useContextStore } from 'src/stores/contextStore';

const props = defineProps<{
  id: string;
  icon: string;
  label: string;
  color?: string;
  action: string;
  condition?: string;
}>();

const router = useRouter();
// const $q = useQuasar(); // Not used yet
const contextStore = useContextStore();

// Determine if button should be disabled
const disabled = computed(() => {
  if (!props.condition) return false;

  // Get state from context data
  const state = contextStore.contextData;

  // Evaluate condition
  try {
    // Simple conditions
    if (props.condition === 'hasSelection' && Array.isArray(state.selectedItems)) {
      return state.selectedItems.length === 0;
    }
    if (props.condition === 'singleSelection' && Array.isArray(state.selectedItems)) {
      return state.selectedItems.length !== 1;
    }
    if (props.condition === 'multiSelection' && Array.isArray(state.selectedItems)) {
      return state.selectedItems.length <= 1;
    }

    // More complex conditions would need a proper evaluator
    return false;
  } catch (error) {
    console.error('Error evaluating condition:', error);
    return false;
  }
});

// Execute the action
function executeAction() {
  console.log(`Executing action: ${props.action}`);

  // Get template from context data if available
  const template = contextStore.contextData.template as Record<string, string> | undefined;

  // Map action to function
  switch (props.action) {
    case 'createTemplate':
      void router.push('/templates/new');
      break;
    case 'editTemplate':
      if (template && 'id' in template) {
        void router.push(`/templates/edit/${template.id}`);
      }
      break;
    case 'saveTemplate':
      // Emit save event
      document.dispatchEvent(new CustomEvent('save-template', {
        detail: { templateId: template && 'id' in template ? template.id : undefined }
      }));
      break;
    case 'previewTemplate':
      // Open preview
      if (contextStore.rightDrawerTabs.preview) {
        // Set active tab to preview
        document.dispatchEvent(new CustomEvent('set-right-drawer-tab', {
          detail: 'preview'
        }));
      }
      break;
    default:
      console.warn(`Unknown action: ${props.action}`);
  }
}
</script>
