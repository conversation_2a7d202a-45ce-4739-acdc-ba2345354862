# Context Manager Migration Guide

This document provides guidance on migrating from the old Context Manager to the new system.

## Current Context Manager

The current Context Manager uses a simple approach with:
- Context IDs and sub-context IDs
- Device-specific overrides
- Direct component references

## New Context Manager

The new Context Manager introduces:
- Pattern matching for contexts
- Role-based component visibility
- Dynamic property resolution
- Component registry for lazy loading

## Migration Steps

### 1. Update Context Definitions

**Old Format:**
```typescript
const templatesContext: ContextConfig = {
  id: 'templates',
  name: 'Templates',
  leftDrawer: {
    tabs: {
      filters: true,
      tools: false
    },
    components: {
      filters: 'TableFilterPanel'
    }
  },
  // ...
};
```

**New Format:**
```typescript
const componentMappings = [
  {
    component: 'FilterPanel',
    area: 'left_drawer',
    context: 'templates_table.table_view',
    role: 'ALL',
    device: 'ALL',
    properties: {
      targetCollection: 'templates_table',
      initiallySelected: false
    }
  },
  // ...
];
```

### 2. Update Component Props

**Old Approach:**
Components received minimal props and had to determine their behavior based on the current context.

**New Approach:**
Components receive detailed props that specify exactly how they should behave in the current context.

Example:
```vue
<!-- Old -->
<template-filter />

<!-- New -->
<filter-panel
  target-collection="templates_table"
  :initially-selected="false"
/>
```

### 3. Update Layout Components

**Old Approach:**
The layout directly referenced specific components based on the current context.

**New Approach:**
The layout uses dynamic components provided by the Context Manager.

Example:
```vue
<!-- Old -->
<component :is="contextStore.leftDrawer.components.filters" />

<!-- New -->
<dynamic-component
  v-for="comp in contextStore.leftDrawerComponents"
  :key="comp.id"
  :name="comp.component"
  v-bind="comp.props"
/>
```

### 4. Update Context Setting

**Old Approach:**
```typescript
contextStore.setContext('templates', 'builder');
```

**New Approach:**
```typescript
contextStore.setContext('templates_table', 'table_view', {
  templateId: 123,
  view: 'table_view'
});
```

### 5. Register Components

**New Requirement:**
All components need to be registered with the Component Registry.

```typescript
// In boot/componentRegistry.ts
registerAsyncComponent('FilterPanel', () => import('src/components/filters/FilterPanel.vue'));
```

## Migration Checklist

1. [ ] Create the new Context Manager files
2. [ ] Register all components in the Component Registry
3. [ ] Convert all context definitions to the new format
4. [ ] Update the MainLayout to use dynamic components
5. [ ] Update route components to set context correctly
6. [ ] Update UI components to be context-aware
7. [ ] Test all contexts, roles, and devices

## Backward Compatibility

During the migration, you can maintain backward compatibility by:

1. Keeping the old Context Manager functions but having them delegate to the new system
2. Creating adapter components that bridge between old and new approaches
3. Gradually migrating one context at a time

## Testing the Migration

1. Create a test branch for the migration
2. Implement the new Context Manager alongside the old one
3. Migrate one context at a time, testing thoroughly
4. Compare the behavior before and after migration
5. Get feedback from users before finalizing the migration
