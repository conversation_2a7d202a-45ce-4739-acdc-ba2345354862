# Step 4: Database Schema for Server Management

This document outlines the database schema required to track and manage SwarmUI server instances in our Supabase database.

## 4.1 Create GPU Servers Table

The `gpu_servers` table will track all RunPod instances created by users:

```sql
CREATE TABLE gpu_servers (
  id BIGSERIAL PRIMARY KEY,
  user_id BIGINT NOT NULL REFERENCES app_users(id),
  pod_id TEXT NOT NULL,
  api_endpoint TEXT NOT NULL,
  api_key TEXT NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('starting', 'running', 'stopping', 'stopped', 'error')),
  gpu_type TEXT,
  started_at TIMESTAMP WITH TIME ZONE NOT NULL,
  stopped_at TIMESTAMP WITH TIME ZONE,
  last_activity TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes
CREATE INDEX gpu_servers_user_id_idx ON gpu_servers(user_id);
CREATE INDEX gpu_servers_status_idx ON gpu_servers(status);
CREATE INDEX gpu_servers_pod_id_idx ON gpu_servers(pod_id);
```

## 4.2 Create GPU Server Usage Table

The `gpu_server_usage` table will track usage metrics for billing and analytics:

```sql
CREATE TABLE gpu_server_usage (
  id BIGSERIAL PRIMARY KEY,
  server_id BIGINT NOT NULL REFERENCES gpu_servers(id),
  user_id BIGINT NOT NULL REFERENCES app_users(id),
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE,
  duration_seconds INTEGER,
  cost_estimate DECIMAL(10, 4),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes
CREATE INDEX gpu_server_usage_server_id_idx ON gpu_server_usage(server_id);
CREATE INDEX gpu_server_usage_user_id_idx ON gpu_server_usage(user_id);
```

## 4.3 Create Image Generations Table

The `image_generations` table will track all images generated by SwarmUI:

```sql
CREATE TABLE image_generations (
  id BIGSERIAL PRIMARY KEY,
  user_id BIGINT NOT NULL REFERENCES app_users(id),
  server_id BIGINT NOT NULL REFERENCES gpu_servers(id),
  template_id BIGINT REFERENCES templates(id),
  prompt TEXT NOT NULL,
  negative_prompt TEXT,
  width INTEGER NOT NULL,
  height INTEGER NOT NULL,
  num_images INTEGER NOT NULL DEFAULT 1,
  seed BIGINT,
  model TEXT NOT NULL,
  sampler TEXT NOT NULL,
  steps INTEGER NOT NULL,
  cfg_scale DECIMAL(5, 2) NOT NULL,
  storage_path TEXT,
  status TEXT NOT NULL CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes
CREATE INDEX image_generations_user_id_idx ON image_generations(user_id);
CREATE INDEX image_generations_server_id_idx ON image_generations(server_id);
CREATE INDEX image_generations_template_id_idx ON image_generations(template_id);
CREATE INDEX image_generations_status_idx ON image_generations(status);
```

## 4.4 Create Generated Images Table

The `generated_images` table will store information about individual generated images:

```sql
CREATE TABLE generated_images (
  id BIGSERIAL PRIMARY KEY,
  generation_id BIGINT NOT NULL REFERENCES image_generations(id),
  user_id BIGINT NOT NULL REFERENCES app_users(id),
  storage_path TEXT NOT NULL,
  width INTEGER NOT NULL,
  height INTEGER NOT NULL,
  seed BIGINT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes
CREATE INDEX generated_images_generation_id_idx ON generated_images(generation_id);
CREATE INDEX generated_images_user_id_idx ON generated_images(user_id);
```

## 4.5 Create Server Configuration Table

The `server_configurations` table will store configuration options for SwarmUI servers:

```sql
CREATE TABLE server_configurations (
  id BIGSERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  template_id TEXT NOT NULL,
  gpu_type TEXT NOT NULL,
  gpu_count INTEGER NOT NULL DEFAULT 1,
  memory_gb INTEGER NOT NULL,
  disk_gb INTEGER NOT NULL,
  hourly_cost DECIMAL(10, 4) NOT NULL,
  is_default BOOLEAN NOT NULL DEFAULT FALSE,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(name)
);

-- Insert default configuration
INSERT INTO server_configurations (
  name, description, template_id, gpu_type, gpu_count, 
  memory_gb, disk_gb, hourly_cost, is_default
) VALUES (
  'Standard', 
  'Standard configuration with RTX A4000 GPU', 
  'your-template-id-here', 
  'NVIDIA RTX A4000', 
  1, 
  16, 
  100, 
  0.50, 
  TRUE
);
```

## 4.6 Create User Server Preferences Table

The `user_server_preferences` table will store user-specific preferences for SwarmUI servers:

```sql
CREATE TABLE user_server_preferences (
  id BIGSERIAL PRIMARY KEY,
  user_id BIGINT NOT NULL REFERENCES app_users(id),
  auto_shutdown_minutes INTEGER NOT NULL DEFAULT 30,
  default_configuration_id BIGINT REFERENCES server_configurations(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id)
);
```

## 4.7 Add Row Level Security Policies

Add RLS policies to secure the tables:

```sql
-- Enable RLS on all tables
ALTER TABLE gpu_servers ENABLE ROW LEVEL SECURITY;
ALTER TABLE gpu_server_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE image_generations ENABLE ROW LEVEL SECURITY;
ALTER TABLE generated_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE server_configurations ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_server_preferences ENABLE ROW LEVEL SECURITY;

-- GPU Servers policies
CREATE POLICY "Users can view their own GPU servers" ON gpu_servers
  FOR SELECT USING (user_id = auth.uid());
  
CREATE POLICY "Users can insert their own GPU servers" ON gpu_servers
  FOR INSERT WITH CHECK (user_id = auth.uid());
  
CREATE POLICY "Users can update their own GPU servers" ON gpu_servers
  FOR UPDATE USING (user_id = auth.uid());

-- Image Generations policies
CREATE POLICY "Users can view their own image generations" ON image_generations
  FOR SELECT USING (user_id = auth.uid());
  
CREATE POLICY "Users can insert their own image generations" ON image_generations
  FOR INSERT WITH CHECK (user_id = auth.uid());
  
CREATE POLICY "Users can update their own image generations" ON image_generations
  FOR UPDATE USING (user_id = auth.uid());

-- Generated Images policies
CREATE POLICY "Users can view their own generated images" ON generated_images
  FOR SELECT USING (user_id = auth.uid());
  
CREATE POLICY "Users can insert their own generated images" ON generated_images
  FOR INSERT WITH CHECK (user_id = auth.uid());

-- Server Configurations policies
CREATE POLICY "Anyone can view active server configurations" ON server_configurations
  FOR SELECT USING (is_active = TRUE);
  
CREATE POLICY "Only super-admins can modify server configurations" ON server_configurations
  USING (EXISTS (
    SELECT 1 FROM user_roles_view
    WHERE user_id = auth.uid() AND role = 'super-admin'
  ));

-- User Server Preferences policies
CREATE POLICY "Users can view their own server preferences" ON user_server_preferences
  FOR SELECT USING (user_id = auth.uid());
  
CREATE POLICY "Users can insert their own server preferences" ON user_server_preferences
  FOR INSERT WITH CHECK (user_id = auth.uid());
  
CREATE POLICY "Users can update their own server preferences" ON user_server_preferences
  FOR UPDATE USING (user_id = auth.uid());
```

## 4.8 Create Database Functions

Create functions to manage server instances:

```sql
-- Function to start a new server
CREATE OR REPLACE FUNCTION start_server(
  p_user_id BIGINT,
  p_configuration_id BIGINT DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_config_id BIGINT;
  v_config RECORD;
  v_server_id BIGINT;
  v_result JSONB;
BEGIN
  -- Get configuration ID (use default if not specified)
  IF p_configuration_id IS NULL THEN
    SELECT id INTO v_config_id FROM server_configurations 
    WHERE is_default = TRUE AND is_active = TRUE
    LIMIT 1;
  ELSE
    v_config_id := p_configuration_id;
  END IF;
  
  -- Get configuration details
  SELECT * INTO v_config FROM server_configurations
  WHERE id = v_config_id AND is_active = TRUE;
  
  IF v_config IS NULL THEN
    RAISE EXCEPTION 'Invalid server configuration';
  END IF;
  
  -- Create server record
  INSERT INTO gpu_servers (
    user_id, pod_id, api_endpoint, api_key, status, 
    gpu_type, started_at, last_activity
  ) VALUES (
    p_user_id, 'pending', 'pending', 'pending', 'starting',
    v_config.gpu_type, NOW(), NOW()
  ) RETURNING id INTO v_server_id;
  
  -- Create usage record
  INSERT INTO gpu_server_usage (
    server_id, user_id, start_time
  ) VALUES (
    v_server_id, p_user_id, NOW()
  );
  
  -- Return server ID and configuration
  v_result := jsonb_build_object(
    'server_id', v_server_id,
    'template_id', v_config.template_id,
    'gpu_type', v_config.gpu_type,
    'gpu_count', v_config.gpu_count,
    'memory_gb', v_config.memory_gb,
    'disk_gb', v_config.disk_gb
  );
  
  RETURN v_result;
END;
$$;

-- Function to update server status
CREATE OR REPLACE FUNCTION update_server_status(
  p_server_id BIGINT,
  p_pod_id TEXT,
  p_api_endpoint TEXT,
  p_api_key TEXT,
  p_status TEXT
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE gpu_servers
  SET 
    pod_id = p_pod_id,
    api_endpoint = p_api_endpoint,
    api_key = p_api_key,
    status = p_status,
    updated_at = NOW(),
    last_activity = NOW()
  WHERE id = p_server_id;
END;
$$;

-- Function to stop a server
CREATE OR REPLACE FUNCTION stop_server(
  p_server_id BIGINT
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Update server status
  UPDATE gpu_servers
  SET 
    status = 'stopping',
    updated_at = NOW()
  WHERE id = p_server_id;
  
  -- Update usage record
  UPDATE gpu_server_usage
  SET 
    end_time = NOW(),
    duration_seconds = EXTRACT(EPOCH FROM (NOW() - start_time))::INTEGER
  WHERE server_id = p_server_id AND end_time IS NULL;
END;
$$;

-- Function to update server activity
CREATE OR REPLACE FUNCTION update_server_activity(
  p_server_id BIGINT
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE gpu_servers
  SET last_activity = NOW()
  WHERE id = p_server_id;
END;
$$;
```

## Next Steps

Now that you have set up the database schema for server management, proceed to [Step 5: Server Management API Implementation](./05-server-management-api.md) to implement the API for starting and stopping servers.
