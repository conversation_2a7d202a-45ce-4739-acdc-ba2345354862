# Frontend Services for Image Management

This document outlines the frontend services needed to manage images in the Dreambox Studio application.

## Image Service

The Image Service provides a centralized interface for all image-related operations in the frontend.

```typescript
// src/services/imageService.ts
import { supabase } from '../boot/supabase'

export const imageService = {
  /**
   * Get a pre-signed URL for uploading an image to S3
   */
  async getUploadUrl(params) {
    const { data, error } = await supabase.functions.invoke('get-s3-upload-url', {
      body: params
    })

    if (error) throw error
    return data
  },

  /**
   * Upload an image to S3 using a pre-signed URL
   */
  async uploadImage(file, params = {}) {
    // Get image dimensions if it's an image
    let width, height
    if (file.type.startsWith('image/')) {
      const dimensions = await this.getImageDimensions(file)
      width = dimensions.width
      height = dimensions.height
    }

    // Get a pre-signed URL
    const { uploadUrl, image } = await this.getUploadUrl({
      filename: file.name,
      contentType: file.type,
      fileSize: file.size,
      width,
      height,
      ...params
    })

    // Upload the file directly to S3
    const uploadResult = await fetch(uploadUrl, {
      method: 'PUT',
      body: file,
      headers: {
        'Content-Type': file.type
      }
    })

    if (!uploadResult.ok) {
      throw new Error('Failed to upload image to S3')
    }

    // If a product ID was provided, associate the image with the product
    if (params.productId) {
      await this.associateImageWithProduct(image.id, params.productId, params.position, params.isPrimary)
    }

    return image
  },

  /**
   * Associate an image with a product
   */
  async associateImageWithProduct(imageId, productId, position = null, isPrimary = false) {
    const { data, error } = await supabase.rpc('associate_image_with_product', {
      p_image_id: imageId,
      p_product_id: productId,
      p_position: position,
      p_is_primary: isPrimary
    })

    if (error) throw error
    return data
  },

  /**
   * Get image dimensions
   */
  getImageDimensions(file) {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => {
        resolve({
          width: img.width,
          height: img.height
        })
      }
      img.onerror = () => {
        reject(new Error('Failed to load image'))
      }
      img.src = URL.createObjectURL(file)
    })
  },

  /**
   * Get images with filtering options
   */
  async getImages(options = {}) {
    let query

    // If filtering by product, use the product_images junction table
    if (options.productId) {
      query = supabase
        .from('product_images')
        .select(`
          id,
          position,
          is_primary,
          s3_images:image_id (*)
        `)
        .eq('product_id', options.productId)
    } else {
      query = supabase
        .from('s3_images')
        .select('*')
    }

    // Apply filters to s3_images table
    if (!options.productId) {
      if (options.templateId) {
        query.eq('template_id', options.templateId)
      }

      if (options.tags && options.tags.length) {
        query.contains('tags', options.tags)
      }

      if (options.userId) {
        query.eq('user_id', options.userId)
      }

      if (options.companyId) {
        query.eq('company_id', options.companyId)
      }

      if (options.resolution) {
        query.eq('resolution', options.resolution)
      }

      if (options.parentImageId) {
        query.eq('parent_image_id', options.parentImageId)
      }
    }

    // Pagination
    if (options.limit) {
      query.limit(options.limit)
    }

    if (options.offset) {
      query.range(options.offset, options.offset + (options.limit || 10) - 1)
    }

    // Sorting
    if (options.productId) {
      // Sort by position or primary status for product images
      if (options.orderBy === 'position') {
        query.order('position', { ascending: options.ascending !== false })
      } else if (options.orderBy === 'is_primary') {
        query.order('is_primary', { ascending: false }) // Primary images first
      } else {
        query.order('is_primary', { ascending: false }).order('position')
      }
    } else {
      // Sort s3_images directly
      if (options.orderBy) {
        query.order(options.orderBy, { ascending: options.ascending !== false })
      } else {
        query.order('created_at', { ascending: false })
      }
    }

    const { data, error } = await query

    if (error) throw error

    // Transform the result if it came from product_images
    if (options.productId && data) {
      return data.map(item => ({
        ...item.s3_images,
        position: item.position,
        is_primary: item.is_primary,
        product_image_id: item.id
      }))
    }

    return data
  },

  /**
   * Get a single image by ID
   */
  async getImage(id) {
    const { data, error } = await supabase
      .from('s3_images')
      .select('*')
      .eq('id', id)
      .single()

    if (error) throw error
    return data
  },

  /**
   * Update image metadata
   */
  async updateImage(id, updates) {
    const { data, error } = await supabase
      .from('s3_images')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  /**
   * Delete an image
   */
  async deleteImage(id) {
    // First get the image to get the S3 key
    const image = await this.getImage(id)

    // Delete from S3 using an Edge Function
    const { error: s3Error } = await supabase.functions.invoke('delete-s3-image', {
      body: { imageId: id }
    })

    if (s3Error) throw s3Error

    // Delete from the database
    const { error: dbError } = await supabase
      .from('s3_images')
      .delete()
      .eq('id', id)

    if (dbError) throw dbError

    return true
  },

  /**
   * Get image tags for autocomplete
   */
  async getImageTags() {
    const { data, error } = await supabase.rpc('get_all_image_tags')

    if (error) throw error
    return data
  }
}
```

## Image Upload Composable

A Vue composable for handling image uploads:

```typescript
// src/composables/useImageUpload.js
import { ref, computed } from 'vue'
import { imageService } from '../services/imageService'

export function useImageUpload(options = {}) {
  const isUploading = ref(false)
  const uploadProgress = ref(0)
  const uploadedImage = ref(null)
  const error = ref(null)

  const isComplete = computed(() => !!uploadedImage.value)
  const hasError = computed(() => !!error.value)

  async function uploadImage(file, params = {}) {
    if (!file) return

    try {
      isUploading.value = true
      error.value = null
      uploadProgress.value = 0

      // Create a combined params object
      const uploadParams = {
        ...options,
        ...params
      }

      // Upload the image
      uploadedImage.value = await imageService.uploadImage(file, uploadParams)
      uploadProgress.value = 100

      return uploadedImage.value
    } catch (err) {
      error.value = err.message || 'Failed to upload image'
      throw err
    } finally {
      isUploading.value = false
    }
  }

  function reset() {
    isUploading.value = false
    uploadProgress.value = 0
    uploadedImage.value = null
    error.value = null
  }

  return {
    isUploading,
    uploadProgress,
    uploadedImage,
    error,
    isComplete,
    hasError,
    uploadImage,
    reset
  }
}
```

## Image Gallery Composable

A Vue composable for managing image galleries:

```typescript
// src/composables/useImageGallery.js
import { ref, computed, watch } from 'vue'
import { imageService } from '../services/imageService'

export function useImageGallery(options = {}) {
  const images = ref([])
  const isLoading = ref(false)
  const error = ref(null)
  const totalCount = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(options.pageSize || 20)
  const filters = ref(options.filters || {})

  const pageCount = computed(() => Math.ceil(totalCount.value / pageSize.value))

  async function loadImages(page = 1, newFilters = null) {
    try {
      isLoading.value = true
      error.value = null

      if (newFilters !== null) {
        filters.value = newFilters
      }

      currentPage.value = page

      const offset = (page - 1) * pageSize.value

      // Get images with pagination
      const result = await imageService.getImages({
        ...filters.value,
        limit: pageSize.value,
        offset
      })

      images.value = result

      // Get total count
      const { count, error: countError } = await supabase
        .from('s3_images')
        .select('id', { count: 'exact', head: true })
        .match(filters.value)

      if (!countError) {
        totalCount.value = count
      }

      return result
    } catch (err) {
      error.value = err.message || 'Failed to load images'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Watch for filter changes
  watch(filters, () => {
    loadImages(1)
  }, { deep: true })

  // Initial load
  if (options.autoLoad !== false) {
    loadImages(1)
  }

  return {
    images,
    isLoading,
    error,
    totalCount,
    currentPage,
    pageSize,
    pageCount,
    filters,
    loadImages
  }
}
```

## Next Steps

Once the frontend services are implemented, proceed to [SwarmUI Integration](./06-swarmui-integration.md) to integrate with the AI image generation system.
