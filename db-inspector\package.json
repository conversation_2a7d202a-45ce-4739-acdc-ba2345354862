{"name": "db-inspector-new", "version": "1.0.0", "description": "Enhanced database inspector for Dreambox Studio", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "generate": "node generate-docs.js", "query": "node query.js", "browse": "node browse.js", "export": "node export.js", "setup": "node setup.js"}, "dependencies": {"@supabase/supabase-js": "^2.39.7", "dotenv": "^16.4.7", "inquirer": "^9.2.15", "chalk": "^5.3.0", "cli-table3": "^0.6.3", "commander": "^12.0.0", "json2csv": "^6.0.0-alpha.2"}}