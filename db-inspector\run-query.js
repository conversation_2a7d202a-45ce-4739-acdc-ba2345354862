import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import chalk from 'chalk';
import { Command } from 'commander';
import Table from 'cli-table3';

// Get the directory name of the current module
const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Load environment variables from the parent directory's .env file
dotenv.config({ path: path.join(__dirname, '..', '.env') });

// Parse command line arguments
const program = new Command();
program
  .requiredOption('-e, --email <email>', 'Email for authentication')
  .requiredOption('-p, --password <password>', 'Password for authentication')
  .requiredOption('-q, --query <query>', 'SQL query to execute')
  .parse(process.argv);

const options = program.opts();

// Create Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error(chalk.red('Supabase credentials not found in environment variables'));
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Sign in with email and password
 * @param {string} email - The email address
 * @param {string} password - The password
 * @returns {Promise<Object>} The session data
 */
async function signIn(email, password) {
  try {
    console.log(chalk.blue(`Signing in as ${email}...`));
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    
    if (error) {
      throw new Error(`Authentication failed: ${error.message}`);
    }
    
    console.log(chalk.green('Authentication successful!'));
    return data;
  } catch (error) {
    console.error(chalk.red(`Authentication error: ${error.message}`));
    return null;
  }
}

/**
 * Executes a SQL query
 * @param {string} query - The SQL query to execute
 * @returns {Promise<Array>} The query results
 */
async function executeQuery(query) {
  try {
    console.log(chalk.blue('Executing query...'));
    
    // Try using the admin_query function first (which bypasses RLS)
    const { data: adminData, error: adminError } = await supabase.rpc('admin_query', { query_text: query });
    
    if (!adminError) {
      return adminData;
    }
    
    // If admin_query fails, try execute_sql
    console.log(chalk.yellow('Falling back to execute_sql...'));
    const { data, error } = await supabase.rpc('execute_sql', { sql_query: query });
    
    if (error) {
      throw new Error(`Query execution failed: ${error.message}`);
    }
    
    return data;
  } catch (error) {
    console.error(chalk.red(`Error executing query: ${error.message}`));
    return [];
  }
}

/**
 * Formats and displays query results in a table
 * @param {Array} data - The query results
 */
function displayResults(data) {
  if (!data || data.length === 0) {
    console.log(chalk.yellow('No results found'));
    return;
  }

  // Get column names from the first row
  const columns = Object.keys(data[0]);
  
  // Create a new table
  const table = new Table({
    head: columns.map(col => chalk.cyan(col)),
    wordWrap: true,
    wrapOnWordBoundary: true,
    truncate: '…'
  });
  
  // Add rows to the table
  data.forEach(row => {
    const rowData = columns.map(col => {
      const value = row[col];
      if (value === null) return chalk.gray('NULL');
      if (typeof value === 'object') return JSON.stringify(value);
      return String(value);
    });
    table.push(rowData);
  });
  
  console.log(table.toString());
  console.log(chalk.green(`${data.length} rows returned`));
}

/**
 * Main function
 */
async function main() {
  try {
    // Sign in
    const session = await signIn(options.email, options.password);
    if (!session) {
      console.error(chalk.red('Authentication failed. Exiting...'));
      process.exit(1);
    }
    
    // Execute the query
    const startTime = Date.now();
    const results = await executeQuery(options.query);
    const endTime = Date.now();
    
    // Display results
    displayResults(results);
    console.log(chalk.gray(`Query executed in ${endTime - startTime}ms`));
    
    process.exit(0);
  } catch (error) {
    console.error(chalk.red(`Error: ${error.message}`));
    process.exit(1);
  }
}

// Run the main function
main();
