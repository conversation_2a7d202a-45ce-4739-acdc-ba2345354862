/**
 * Image interface
 */
export interface Image {
  id: number;
  title: string;
  description: string | null;
  image_url: string;
  template_id: number | null;
  img_company_id: number; // Changed from company_id
  status: 'pending' | 'approved' | 'rejected';
  metadata: Record<string, unknown> | null;
  created_at: string;
  updated_at: string;
}

/**
 * Image filter interface
 */
export interface ImageFilter {
  img_company_id?: number; // Changed from company_id
  template_id?: number;
  status?: string;
  search?: string;
}

/**
 * Image pagination interface
 */
export interface ImagePagination {
  page: number;
  rowsPerPage: number;
  rowsNumber: number;
  sortBy?: string;
  descending?: boolean;
}
