# Step 10: Integration with Template Creation

This document outlines the integration of SwarmUI image generation with the template creation process, allowing designers to generate images based on their templates.

## 10.1 Update Template Schema

First, ensure the template schema includes fields needed for image generation:

```sql
-- Add fields to templates table if they don't already exist
ALTER TABLE templates ADD COLUMN IF NOT EXISTS flux_params JSONB;
ALTER TABLE templates ADD COLUMN IF NOT EXISTS generated_images JSONB[];
```

## 10.2 Create Template Image Generation Component

Create a component for generating images from templates:

```
src/
└── components/
    └── templates/
        └── TemplateImageGenerator.vue
```

Implement the component:

```vue
<template>
  <div class="template-image-generator">
    <q-card class="generator-card">
      <q-card-section>
        <div class="text-h6">Generate Images</div>
        <div class="text-subtitle2">
          Generate images based on this template using AI
        </div>
      </q-card-section>

      <q-card-section v-if="!serverRunning">
        <q-banner class="bg-warning text-white">
          <template v-slot:avatar>
            <q-icon name="mdi-server-off" />
          </template>
          You need to start an image generation server first.
          <template v-slot:action>
            <q-btn flat color="white" label="Start Server" @click="startServer" />
          </template>
        </q-banner>
      </q-card-section>

      <q-card-section v-else-if="loading">
        <div class="text-center">
          <q-spinner color="primary" size="3rem" />
          <div class="q-mt-sm">Loading models...</div>
        </div>
      </q-card-section>

      <q-card-section v-else>
        <div class="row q-col-gutter-md">
          <div class="col-12 col-md-6">
            <q-input
              v-model="generationParams.prompt"
              type="textarea"
              label="Prompt"
              rows="4"
              filled
              :disable="generating"
            />
          </div>
          
          <div class="col-12 col-md-6">
            <q-input
              v-model="generationParams.negative_prompt"
              type="textarea"
              label="Negative Prompt"
              rows="4"
              filled
              :disable="generating"
            />
          </div>
          
          <div class="col-12 col-md-4">
            <q-select
              v-model="generationParams.model"
              :options="availableModels"
              label="Model"
              filled
              :disable="generating"
            />
          </div>
          
          <div class="col-12 col-md-4">
            <q-select
              v-model="selectedAspectRatio"
              :options="aspectRatios"
              label="Aspect Ratio"
              filled
              :disable="generating"
              @update:model-value="updateDimensions"
            />
          </div>
          
          <div class="col-12 col-md-4">
            <q-select
              v-model="generationParams.sampler"
              :options="availableSamplers"
              label="Sampler"
              filled
              :disable="generating"
            />
          </div>
          
          <div class="col-12 col-md-4">
            <q-input
              v-model.number="generationParams.steps"
              type="number"
              label="Steps"
              filled
              :disable="generating"
              :min="10"
              :max="150"
            />
          </div>
          
          <div class="col-12 col-md-4">
            <q-input
              v-model.number="generationParams.cfg_scale"
              type="number"
              label="CFG Scale"
              filled
              :disable="generating"
              :min="1"
              :max="30"
              :step="0.5"
            />
          </div>
          
          <div class="col-12 col-md-4">
            <q-input
              v-model.number="generationParams.batch_size"
              type="number"
              label="Batch Size"
              filled
              :disable="generating"
              :min="1"
              :max="4"
            />
          </div>
          
          <div class="col-12 col-md-6">
            <div class="row items-center">
              <div class="col">
                <q-input
                  v-model.number="generationParams.seed"
                  type="number"
                  label="Seed"
                  filled
                  :disable="generating"
                />
              </div>
              <div class="col-auto q-ml-sm">
                <q-btn
                  icon="mdi-dice-multiple"
                  flat
                  round
                  :disable="generating"
                  @click="generateRandomSeed"
                >
                  <q-tooltip>Random Seed</q-tooltip>
                </q-btn>
              </div>
            </div>
          </div>
          
          <div class="col-12 col-md-6">
            <q-select
              v-model="generationParams.scheduler"
              :options="availableSchedulers"
              label="Scheduler"
              filled
              :disable="generating"
            />
          </div>
        </div>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn
          flat
          color="primary"
          label="Reset to Defaults"
          :disable="generating"
          @click="resetToDefaults"
        />
        
        <q-btn
          color="primary"
          :loading="generating"
          :disable="!serverRunning || generating"
          @click="generateImages"
          icon="mdi-image-plus"
          label="Generate Images"
        />
      </q-card-actions>
      
      <q-inner-loading :showing="generating">
        <div class="text-center">
          <q-spinner-dots color="primary" size="3rem" />
          <div class="q-mt-sm">Generating images...</div>
          <div class="text-caption">This may take a minute or two</div>
        </div>
      </q-inner-loading>
    </q-card>
    
    <div v-if="generatedImages.length > 0" class="q-mt-md">
      <div class="text-h6">Generated Images</div>
      
      <div class="row q-col-gutter-md q-mt-sm">
        <div
          v-for="image in generatedImages"
          :key="image.id"
          class="col-12 col-sm-6 col-md-4 col-lg-3"
        >
          <q-card class="image-card">
            <q-img
              :src="image.url"
              :ratio="image.width / image.height"
              spinner-color="primary"
              spinner-size="3rem"
            />
            
            <q-card-actions align="right">
              <q-btn
                flat
                round
                color="primary"
                icon="mdi-content-copy"
                @click="copyImageToTemplate(image)"
              >
                <q-tooltip>Use for Template</q-tooltip>
              </q-btn>
              
              <q-btn
                flat
                round
                color="primary"
                icon="mdi-download"
                @click="downloadImage(image)"
              >
                <q-tooltip>Download</q-tooltip>
              </q-btn>
            </q-card-actions>
          </q-card>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue';
import { useQuasar } from 'quasar';
import { serverManagementService } from '../../services/serverManagement';
import { swarmUIService } from '../../services/swarmUI';
import { imageStorageService } from '../../services/imageStorage';
import {
  createDefaultGenerationParams,
  createFastGenerationParams,
  generateRandomSeed,
  getCommonAspectRatios
} from '../../services/swarmUI/utils';
import { useAuthStore } from '../../stores/auth';

export default {
  name: 'TemplateImageGenerator',
  
  props: {
    template: {
      type: Object,
      required: true
    }
  },
  
  emits: ['image-selected', 'server-start-requested'],
  
  setup(props, { emit }) {
    const $q = useQuasar();
    const authStore = useAuthStore();
    const loading = ref(true);
    const serverRunning = ref(false);
    const generating = ref(false);
    const generationParams = ref(createDefaultGenerationParams());
    const availableModels = ref([]);
    const availableSamplers = ref([]);
    const availableSchedulers = ref([]);
    const generatedImages = ref([]);
    const aspectRatios = ref(getCommonAspectRatios());
    const selectedAspectRatio = ref(aspectRatios.value[0]);
    
    // Check if server is running
    async function checkServerStatus() {
      try {
        const server = await serverManagementService.getActiveServer();
        serverRunning.value = server && server.status === 'running';
        
        if (serverRunning.value) {
          await loadServerInfo();
        }
      } catch (error) {
        console.error('Error checking server status:', error);
      } finally {
        loading.value = false;
      }
    }
    
    // Load available models, samplers, etc.
    async function loadServerInfo() {
      try {
        loading.value = true;
        
        // Initialize SwarmUI service
        const initialized = await swarmUIService.initialize();
        if (!initialized) {
          serverRunning.value = false;
          return;
        }
        
        // Get server info
        const serverInfo = await swarmUIService.getServerInfo();
        
        // Update available options
        availableModels.value = serverInfo.models.map(model => model.name);
        availableSamplers.value = serverInfo.samplers;
        availableSchedulers.value = serverInfo.schedulers;
        
        // Check if Flux is available
        if (!availableModels.value.includes('Flux')) {
          $q.notify({
            color: 'warning',
            message: 'Flux model not found on server. Using available models instead.',
            icon: 'mdi-alert'
          });
          
          // If Flux is not available, use the first available model
          if (availableModels.value.length > 0) {
            generationParams.value.model = availableModels.value[0];
          }
        }
      } catch (error) {
        console.error('Error loading server info:', error);
        $q.notify({
          color: 'negative',
          message: 'Failed to load server information',
          icon: 'mdi-alert'
        });
      } finally {
        loading.value = false;
      }
    }
    
    // Update dimensions based on selected aspect ratio
    function updateDimensions() {
      if (selectedAspectRatio.value) {
        generationParams.value.width = selectedAspectRatio.value.value.width;
        generationParams.value.height = selectedAspectRatio.value.value.height;
      }
    }
    
    // Generate random seed
    function generateRandomSeed() {
      generationParams.value.seed = Math.floor(Math.random() * 2147483647);
    }
    
    // Reset to default parameters
    function resetToDefaults() {
      generationParams.value = createDefaultGenerationParams();
      selectedAspectRatio.value = aspectRatios.value[0];
    }
    
    // Start server
    function startServer() {
      emit('server-start-requested');
    }
    
    // Generate images
    async function generateImages() {
      if (!serverRunning.value) return;
      
      try {
        generating.value = true;
        
        // Build prompt from template
        const basePrompt = buildPromptFromTemplate(props.template);
        
        // If user hasn't modified the prompt, use the template-generated one
        if (!generationParams.value.prompt || generationParams.value.prompt === '') {
          generationParams.value.prompt = basePrompt;
        }
        
        // Generate images
        const response = await swarmUIService.generateImage(generationParams.value);
        
        // Poll for completion
        await pollGenerationStatus(response.id);
        
        // Load generated images
        await loadGeneratedImages();
        
        $q.notify({
          color: 'positive',
          message: 'Images generated successfully',
          icon: 'mdi-check'
        });
      } catch (error) {
        console.error('Error generating images:', error);
        $q.notify({
          color: 'negative',
          message: 'Failed to generate images: ' + error.message,
          icon: 'mdi-alert'
        });
      } finally {
        generating.value = false;
      }
    }
    
    // Build prompt from template
    function buildPromptFromTemplate(template) {
      // This is a simplified example - you would need to customize this
      // based on your template structure
      const promptParts = [];
      
      if (template.style && template.style.length > 0) {
        promptParts.push(`Style: ${template.style.join(', ')}`);
      }
      
      if (template.topics && template.topics.length > 0) {
        promptParts.push(`Subject: ${template.topics.join(', ')}`);
      }
      
      if (template.mood_atmosphere && template.mood_atmosphere.length > 0) {
        promptParts.push(`Mood: ${template.mood_atmosphere.join(', ')}`);
      }
      
      if (template.colors && template.colors.length > 0) {
        promptParts.push(`Colors: ${template.colors.join(', ')}`);
      }
      
      if (template.light && template.light.length > 0) {
        promptParts.push(`Lighting: ${template.light.join(', ')}`);
      }
      
      // Add more template fields as needed
      
      return promptParts.join('. ');
    }
    
    // Poll for generation status
    async function pollGenerationStatus(generationId) {
      return new Promise((resolve, reject) => {
        const checkStatus = async () => {
          try {
            const status = await swarmUIService.getGenerationStatus(generationId);
            
            if (status.status === 'completed') {
              resolve(status);
            } else if (status.status === 'failed') {
              reject(new Error(status.error || 'Generation failed'));
            } else {
              // Still processing, check again in 2 seconds
              setTimeout(checkStatus, 2000);
            }
          } catch (error) {
            reject(error);
          }
        };
        
        checkStatus();
      });
    }
    
    // Load generated images
    async function loadGeneratedImages() {
      try {
        const user = authStore.getUser();
        if (!user) return;
        
        const images = await imageStorageService.getImages({
          userId: user.id,
          limit: 12,
          orderBy: 'created_at',
          orderDirection: 'desc'
        });
        
        generatedImages.value = images;
      } catch (error) {
        console.error('Error loading generated images:', error);
      }
    }
    
    // Copy image to template
    function copyImageToTemplate(image) {
      emit('image-selected', image);
      
      $q.notify({
        color: 'positive',
        message: 'Image selected for template',
        icon: 'mdi-check'
      });
    }
    
    // Download image
    async function downloadImage(image) {
      if (!image) return;
      
      try {
        // Create a temporary anchor element
        const a = document.createElement('a');
        a.href = image.url;
        a.download = `template_${props.template.id}_image_${image.id}.png`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      } catch (error) {
        console.error('Error downloading image:', error);
        $q.notify({
          color: 'negative',
          message: 'Failed to download image',
          icon: 'mdi-alert'
        });
      }
    }
    
    // Watch for template changes
    watch(() => props.template, (newTemplate) => {
      // Update prompt based on new template
      if (newTemplate && !generating.value) {
        const basePrompt = buildPromptFromTemplate(newTemplate);
        generationParams.value.prompt = basePrompt;
      }
    }, { deep: true });
    
    // Initialize
    onMounted(() => {
      checkServerStatus();
      
      // If template has saved Flux parameters, use them
      if (props.template.flux_params) {
        try {
          const savedParams = JSON.parse(props.template.flux_params);
          generationParams.value = { ...generationParams.value, ...savedParams };
          
          // Find matching aspect ratio
          const ratio = aspectRatios.value.find(
            r => r.value.width === savedParams.width && r.value.height === savedParams.height
          );
          
          if (ratio) {
            selectedAspectRatio.value = ratio;
          }
        } catch (error) {
          console.error('Error parsing saved Flux parameters:', error);
        }
      } else {
        // Initialize with template-based prompt
        const basePrompt = buildPromptFromTemplate(props.template);
        generationParams.value.prompt = basePrompt;
      }
    });
    
    return {
      loading,
      serverRunning,
      generating,
      generationParams,
      availableModels,
      availableSamplers,
      availableSchedulers,
      generatedImages,
      aspectRatios,
      selectedAspectRatio,
      updateDimensions,
      generateRandomSeed,
      resetToDefaults,
      startServer,
      generateImages,
      copyImageToTemplate,
      downloadImage
    };
  }
};
</script>

<style scoped>
.generator-card {
  max-width: 1200px;
}

.image-card {
  transition: transform 0.2s;
}

.image-card:hover {
  transform: scale(1.02);
}
</style>
```

## 10.3 Integrate with Template Editor

Update the template editor to include the image generator component:

```vue
<template>
  <div class="template-editor">
    <!-- Existing template editor content -->
    
    <div class="q-mt-lg">
      <q-expansion-item
        icon="mdi-server"
        label="Image Generation Server"
        caption="Start a GPU server to generate images"
        header-class="text-primary"
      >
        <server-controls />
      </q-expansion-item>
    </div>
    
    <div class="q-mt-md">
      <q-expansion-item
        icon="mdi-image-plus"
        label="Generate Images"
        caption="Generate images based on this template"
        header-class="text-primary"
      >
        <template-image-generator
          :template="template"
          @image-selected="selectImageForTemplate"
          @server-start-requested="openServerControls"
        />
      </q-expansion-item>
    </div>
    
    <!-- Rest of template editor -->
  </div>
</template>

<script>
import { ref } from 'vue';
import ServerControls from '../components/serverManagement/ServerControls.vue';
import TemplateImageGenerator from '../components/templates/TemplateImageGenerator.vue';

export default {
  name: 'TemplateEditor',
  
  components: {
    ServerControls,
    TemplateImageGenerator
  },
  
  setup() {
    const template = ref({
      // Template data
    });
    
    function selectImageForTemplate(image) {
      // Update template with selected image
      // This would depend on your template structure
    }
    
    function openServerControls() {
      // Open the server controls expansion item
      // This would depend on your implementation
    }
    
    return {
      template,
      selectImageForTemplate,
      openServerControls
    };
  }
};
</script>
```

## 10.4 Save Generated Images with Template

Update the template save functionality to store generated images:

```typescript
// In your template save function
async function saveTemplate(template) {
  try {
    // If template has generated images, save them
    if (template.generatedImages && template.generatedImages.length > 0) {
      // Convert to JSON format for storage
      const imagesJson = template.generatedImages.map(img => ({
        id: img.id,
        url: img.url,
        width: img.width,
        height: img.height,
        seed: img.seed
      }));
      
      // Save Flux parameters for future use
      const fluxParams = JSON.stringify(template.fluxParams || {});
      
      // Update template with images and parameters
      const { error } = await supabase
        .from('templates')
        .update({
          generated_images: imagesJson,
          flux_params: fluxParams
        })
        .eq('id', template.id);
        
      if (error) {
        throw new Error('Failed to save template images');
      }
    }
    
    // Rest of template save logic
  } catch (error) {
    console.error('Error saving template:', error);
    throw error;
  }
}
```

## 10.5 Display Generated Images in Template View

Update the template view to display generated images:

```vue
<template>
  <div class="template-view">
    <!-- Existing template view content -->
    
    <div v-if="template.generated_images && template.generated_images.length > 0" class="q-mt-lg">
      <div class="text-h6">Generated Images</div>
      
      <div class="row q-col-gutter-md q-mt-sm">
        <div
          v-for="(image, index) in template.generated_images"
          :key="index"
          class="col-12 col-sm-6 col-md-4 col-lg-3"
        >
          <q-card class="image-card">
            <q-img
              :src="image.url"
              :ratio="image.width / image.height"
              spinner-color="primary"
              spinner-size="3rem"
            />
          </q-card>
        </div>
      </div>
    </div>
    
    <!-- Rest of template view -->
  </div>
</template>
```

## Next Steps

Now that you have integrated SwarmUI image generation with the template creation process, proceed to [Step 11: Testing and Troubleshooting](./11-testing.md) to test the implementation and troubleshoot any issues.
