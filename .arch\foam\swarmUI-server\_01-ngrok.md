if ! command -v ngrok &> /dev/null; then
    echo "Installing ngrok..."
    # Download and install ngrok directly (without package manager)
    cd /tmp
    wget https://bin.equinox.io/c/bNyj1mQVY4c/ngrok-v3-stable-linux-amd64.tgz
    tar xvzf ngrok-v3-stable-linux-amd64.tgz
    mv ngrok /usr/local/bin/
    chmod +x /usr/local/bin/ngrok
    echo "Ngrok installed successfully"
fi

# Configure ngrok with your auth token
ngrok config add-authtoken *************************************************

# Start the tunnel to port 7801 (SwarmUI)
echo "Starting ngrok tunnel to SwarmUI (port 7801)..."
ngrok http 7801