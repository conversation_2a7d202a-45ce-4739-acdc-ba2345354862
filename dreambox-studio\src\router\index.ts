import { route } from 'quasar/wrappers'
import {
  create<PERSON><PERSON>oryH<PERSON><PERSON>,
  createRouter,
  createWebHashHistory,
  createWebHistory,
} from 'vue-router'
import routes from './routes'
import { useAuthStore } from 'src/stores/auth'

export default route(function () {
  const createHistory = process.env.SERVER
    ? createMemoryHistory
    : (process.env.VUE_ROUTER_MODE === 'history' ? createWebHistory : createWebHashHistory)

  const Router = createRouter({
    scrollBehavior: () => ({ left: 0, top: 0 }),
    routes,
    history: createHistory(process.env.VUE_ROUTER_BASE)
  })

  Router.beforeEach(async (to, from, next) => {
    const authStore = useAuthStore()
    const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
    const requiredRole = to.matched.find(record => record.meta.role)?.meta.role

    // Check if we need to refresh auth state
    if (requiresAuth && !authStore.isAuthenticated) {
      console.log('Route requires auth but user is not authenticated, checking session...')
      // Check if we have a valid session
      const session = await authStore.checkSession()
      if (session && session.user) {
        console.log('Valid session found, loading user data...')
        // We have a valid session, try to load user data
        await authStore.fetchUserData(session.user.id)
      }
    }

    // Allow access to login page without authentication
    if (to.path === '/auth/login') {
      if (authStore.isAuthenticated) {
        // If already logged in, redirect to appropriate dashboard
        const role = authStore.state.user?.currentRole
        console.log('User already authenticated with role:', role)
        if (role === 'super-admin') {
          next('/super-admin')
        } else if (role === 'admin') {
          next('/admin')
        } else if (role === 'designer') {
          next('/designer')
        } else {
          next('/dashboard')
        }
      } else {
        next()
      }
    } else if (requiresAuth && !authStore.isAuthenticated) {
      // Redirect to login if authentication is required but user is not authenticated
      console.log('Route requires auth but user is not authenticated, redirecting to login')
      next('/auth/login')
    } else if (requiredRole && authStore.state.user?.currentRole !== requiredRole) {
      // Redirect to login if the user doesn't have the required role
      console.log('User does not have required role:', requiredRole)
      next('/auth/login')
    } else {
      next()
    }
  })

  return Router
})

