import { defaultContext } from './default';
import { dashboardContext } from './dashboard';
import { templatesBrowseContext, templatesBuilderContext } from './templates';
import { settingsContext } from './settings';

// Context registry with sub-contexts
export const contexts = {
  default: defaultContext,
  dashboard: dashboardContext,
  templates: {
    default: templatesBrowseContext,
    browse: templatesBrowseContext,
    builder: templatesBuilderContext
  },
  settings: settingsContext
};

// Flatten contexts for direct access
export const flattenedContexts = {
  default: defaultContext,
  dashboard: dashboardContext,
  templates: templatesBrowseContext,
  'templates.browse': templatesBrowseContext,
  'templates.builder': templatesBuilderContext,
  settings: settingsContext
};
