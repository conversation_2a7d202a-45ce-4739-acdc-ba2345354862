import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { AuthState, Company, UserRole, UserRoleWithCompany, UserData } from '../types/auth'

// Local storage keys
const AUTH_USER_KEY = 'dreambox-auth-user';

// This is a test-specific version of the auth store that exposes private methods for testing
export const useTestAuthStore = defineStore('auth-test', () => {
  const state = ref<AuthState>({
    user: null,
    loading: false,
    error: null
  })

  // Save user state to localStorage
  function saveUserToLocalStorage() {
    if (state.value.user) {
      try {
        const userData = JSON.stringify(state.value.user);
        localStorage.setItem(AUTH_USER_KEY, userData);
        console.log('User data saved to localStorage:', { key: AUTH_USER_KEY, size: userData.length });
      } catch (error) {
        console.error('Failed to save user data to localStorage:', error);
      }
    } else {
      console.warn('Attempted to save null user to localStorage');
    }
  }

  // Load user state from localStorage
  function loadUserFromLocalStorage() {
    console.log('Attempting to load user from localStorage');
    const savedUser = localStorage.getItem(AUTH_USER_KEY);

    if (savedUser) {
      console.log('Found saved user data in localStorage');
      try {
        state.value.user = JSON.parse(savedUser);
        console.log('Successfully loaded user from localStorage:', {
          email: state.value.user?.email,
          role: state.value.user?.currentRole,
          company: state.value.user?.currentCompany?.name
        });
        return true;
      } catch (e) {
        console.error('Failed to parse saved user data:', e);
        localStorage.removeItem(AUTH_USER_KEY);
      }
    } else {
      console.log('No saved user found in localStorage');
    }
    return false;
  }

  // Clear user state from localStorage
  function clearUserFromLocalStorage() {
    console.log('Clearing user data from localStorage');
    localStorage.removeItem(AUTH_USER_KEY);
  }

  const isAuthenticated = computed(() => !!state.value.user)
  const hasMultipleCompanies = computed(() =>
    state.value.user?.companies && state.value.user.companies.length > 1 || false
  )
  const hasMultipleRoles = computed(() =>
    state.value.user?.roles && state.value.user.roles.length > 1 || false
  )

  // Computed properties for role checks
  const isSuperAdmin = computed(() => 
    state.value.user?.currentRole === 'super-admin'
  )
  
  const isAdmin = computed(() => 
    state.value.user?.currentRole === 'admin'
  )
  
  const isDesigner = computed(() => 
    state.value.user?.currentRole === 'designer'
  )

  // Process user roles from the database
  function processUserRoles(userPermissions: Array<{
    app_user_id: number;
    user_id: string;
    first_name: string;
    last_name: string;
    company_id: number;
    company_name: string;
    role_id: number;
    role_name: string;
  }>) {
    const companies: Company[] = [];
    const roles: UserRole[] = [];
    const userRoles: UserRoleWithCompany[] = [];
    
    // Maps to track unique companies and roles
    const companyMap = new Map<string, boolean>();
    const roleMap = new Map<string, boolean>();
    
    userPermissions.forEach(permission => {
      // Add company if not already added
      if (!companyMap.has(String(permission.company_id))) {
        companyMap.set(String(permission.company_id), true);
        companies.push({
          id: String(permission.company_id),
          name: permission.company_name
        });
      }
      
      // Add role if not already added
      if (!roleMap.has(permission.role_name)) {
        roleMap.set(permission.role_name, true);
        if (
          permission.role_name === 'designer' ||
          permission.role_name === 'admin' ||
          permission.role_name === 'super-admin'
        ) {
          roles.push(permission.role_name as UserRole);
        }
      }
      
      // Add user role
      userRoles.push({
        role: {
          id: String(permission.role_id),
          name: permission.role_name
        },
        company: {
          id: String(permission.company_id),
          name: permission.company_name
        }
      });
    });
    
    // Determine current company and role
    let currentCompany: Company | null = null;
    let currentRole: UserRole | null = null;
    
    if (companies.length > 0) {
      currentCompany = companies[0] || null;
      
      // Find roles for the current company
      const rolesForCompany = userRoles
        .filter((ur: UserRoleWithCompany) => ur.company.id === currentCompany?.id)
        .map((ur: UserRoleWithCompany) => ur.role.name as UserRole);
      
      if (rolesForCompany.length > 0) {
        currentRole = rolesForCompany[0] || null;
      }
    }
    
    return {
      companies,
      roles,
      userRoles,
      currentCompany,
      currentRole
    };
  }

  // Check if user has a specific role
  function hasRole(role: UserRole, companyId?: string): boolean {
    if (!state.value.user) return false;
    
    // Make sure userRoles exists in the user object
    if (!('userRoles' in state.value.user)) {
      return false;
    }
    
    const userWithRoles = state.value.user as UserData & { userRoles: UserRoleWithCompany[] };
    
    // If company ID is provided, check if user has the role for that company
    if (companyId) {
      return !!userWithRoles.userRoles?.some(
        (ur: UserRoleWithCompany) => ur.role.name === role && ur.company.id === companyId
      );
    }
    
    // Otherwise, check if user has the role for the current company
    return !!userWithRoles.userRoles?.some(
      (ur: UserRoleWithCompany) => ur.role.name === role && 
           ur.company.id === state.value.user?.currentCompany?.id
    );
  }

  // Get roles for the current company
  function getRolesForCurrentCompany(): UserRole[] {
    if (!state.value.user || !state.value.user.currentCompany) return [];
    
    // Make sure userRoles exists in the user object
    if (!('userRoles' in state.value.user)) {
      return [];
    }
    
    const userWithRoles = state.value.user as UserData & { userRoles: UserRoleWithCompany[] };
    const currentCompanyId = state.value.user.currentCompany.id;
    
    return userWithRoles.userRoles
      ?.filter((ur: UserRoleWithCompany) => ur.company.id === currentCompanyId)
      .map((ur: UserRoleWithCompany) => ur.role.name as UserRole) || [];
  }

  // Get user's full name
  function getUserFullName(): string {
    if (!state.value.user) return '';
    
    const firstName = state.value.user.firstName || '';
    const lastName = state.value.user.lastName || '';
    
    if (firstName && lastName) {
      return `${firstName} ${lastName}`;
    } else if (firstName) {
      return firstName;
    } else if (lastName) {
      return lastName;
    } else {
      return '';
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async function login(_email: string, _password: string) {
    // Implementation not needed for testing
    return Promise.resolve();
  }

  async function logout() {
    // Implementation not needed for testing
    return Promise.resolve();
  }

  function setCurrentCompany(company: Company) {
    if (state.value.user) {
      state.value.user.currentCompany = company;
    }
  }

  function setCurrentRole(role: UserRole) {
    if (state.value.user) {
      state.value.user.currentRole = role;
    }
  }

  async function checkSession() {
    // Implementation not needed for testing
    return Promise.resolve(null);
  }

  async function fetchUserData() {
    // Implementation not needed for testing
    return Promise.resolve();
  }

  async function initAuth() {
    // Implementation not needed for testing
    return Promise.resolve();
  }

  return {
    state,
    isAuthenticated,
    hasMultipleCompanies,
    hasMultipleRoles,
    isSuperAdmin,
    isAdmin,
    isDesigner,
    login,
    logout,
    setCurrentCompany,
    setCurrentRole,
    initAuth,
    checkSession,
    fetchUserData,
    // Expose private methods for testing
    processUserRoles,
    hasRole,
    getRolesForCurrentCompany,
    getUserFullName,
    saveUserToLocalStorage,
    loadUserFromLocalStorage,
    clearUserFromLocalStorage
  }
})
