/**
 * Boot file for initializing the command system
 */
import { boot } from 'quasar/wrappers';
import { createCommandSetup } from 'src/services/commands/setup';

export default boot(({ app }) => {
  // Register a global property for command setup
  // This will be called in MainLayout after authentication
  app.config.globalProperties.$setupCommands = createCommandSetup();
  
  // Make it available in the Vue instance
  app.provide('setupCommands', createCommandSetup());
});
