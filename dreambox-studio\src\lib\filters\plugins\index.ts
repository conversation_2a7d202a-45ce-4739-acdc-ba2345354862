/**
 * Filter Plugins Index
 * 
 * This module exports all filter plugins and provides a function to register them.
 */

import type { FilterEngine } from '../FilterEngine';
import { TemplatesPlugin } from './TemplatesPlugin';

/**
 * Register all plugins with the filter engine
 * 
 * @param engine Filter engine instance
 */
export function registerAllPlugins(engine: FilterEngine): void {
  // Create and register the Templates Plugin
  const templatesPlugin = new TemplatesPlugin();
  engine.registerPlugin(templatesPlugin);
  
  // Add more plugins here as needed
  
  console.log('All filter plugins registered');
}

// Export plugins for direct import
export { TemplatesPlugin };
