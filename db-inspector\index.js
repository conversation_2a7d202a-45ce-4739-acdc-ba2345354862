import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import chalk from 'chalk';

// Get the directory name of the current module
const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Load environment variables from the parent directory's .env file
dotenv.config({ path: path.join(__dirname, '..', '.env') });

// Path to save the database structure
const outputPath = path.join(__dirname, 'db-structure.json');

/**
 * Fetches the database structure from Supabase
 * @returns {Promise<Object>} The database structure
 */
async function fetchDatabaseStructure() {
  // Get Supabase credentials from environment variables
  const supabaseUrl = process.env.VITE_SUPABASE_URL;
  const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseKey) {
    throw new Error('Supabase credentials not found in environment variables');
  }

  console.log(chalk.blue('Connecting to Supabase...'));
  console.log(chalk.gray(`URL: ${supabaseUrl}`));

  // Create Supabase client
  const supabase = createClient(supabaseUrl, supabaseKey);

  // Fetch the entire database structure using the get_db_structure function
  console.log(chalk.blue('Fetching database structure...'));
  const { data, error } = await supabase.rpc('get_db_structure');

  if (error) {
    throw new Error(`Error fetching database structure: ${error.message}`);
  }

  // Log the results
  console.log(chalk.green(`Found ${data.tables.length} tables`));
  console.log(chalk.green(`Found ${data.views.length} views`));
  console.log(chalk.green(`Found ${data.columns.length} columns`));
  console.log(chalk.green(`Found ${data.constraints.length} constraints`));
  console.log(chalk.green(`Found ${data.policies ? data.policies.length : 0} RLS policies`));
  console.log(chalk.green(`Found ${data.functions.length} functions`));

  // Add metadata
  data.metadata = {
    ...data.metadata,
    supabase_url: supabaseUrl
  };

  return data;
}

/**
 * Main function
 */
async function main() {
  try {
    console.log(chalk.yellow('Fetching database structure...'));

    // Fetch the database structure
    const dbStructure = await fetchDatabaseStructure();

    // Save to file
    fs.writeFileSync(outputPath, JSON.stringify(dbStructure, null, 2));

    console.log(chalk.green(`\nDatabase structure saved to ${outputPath}`));
    console.log(chalk.blue('\nTo generate documentation:'));
    console.log(chalk.white('  bun generate'));
    console.log(chalk.blue('\nTo browse table data:'));
    console.log(chalk.white('  bun browse'));
    console.log(chalk.blue('\nTo run queries:'));
    console.log(chalk.white('  bun query'));
  } catch (error) {
    console.error(chalk.red(`\nError: ${error.message}`));
    process.exit(1);
  }
}

// Run the main function
main();
