# Testing in Dreambox Studio

This document outlines the testing approach for the Dreambox Studio application.

## Testing Stack

- **Unit and Integration Tests**: B<PERSON>'s built-in test runner
- **E2E Tests**: (Future implementation with Cypress)

## Running Tests

```bash
# Run all tests
bun test

# Run tests in watch mode (automatically re-run on file changes)
bun test --watch

# Run tests with coverage report
bun test --coverage
```

## Test Structure

Tests are organized following the same structure as the source code:

- Unit tests are placed next to the files they test with a `.test.ts` or `.spec.ts` suffix
- Helper utilities for testing are in the `test/helpers` directory
- Test setup and configuration is in the `test/setup.ts` file

## Mocking

We use several approaches for mocking:

1. **Jest-compatible mocks**: B<PERSON>'s test runner supports Jest-style mocking
2. **Manual mocks**: For more complex scenarios
3. **Spy functions**: For verifying function calls

## Testing Vue Components

Testing Vue components requires special handling:

1. Mock the Quasar framework and Vue ecosystem
2. Use helper functions in `test/helpers/vue-test-utils.ts`
3. Test component behavior rather than implementation details

## Testing Stores (Pinia)

For testing Pinia stores:

1. Create a fresh Pinia instance for each test using `setActivePinia(createPinia())`
2. Mock external dependencies (e.g., Supabase)
3. Test store actions, state changes, and computed properties

## Testing Utilities

Utility functions are tested directly:

1. Mock any dependencies
2. Test each function with various inputs
3. Verify expected outputs

## Code Coverage

We aim for high code coverage, focusing on:

1. Core business logic
2. Critical user flows
3. Edge cases and error handling

## Best Practices

1. **Isolation**: Each test should be independent
2. **Readability**: Tests should be clear about what they're testing
3. **Maintainability**: Avoid brittle tests that break with minor changes
4. **Speed**: Tests should run quickly to provide fast feedback

## Future Improvements

1. Add E2E testing with Cypress
2. Implement visual regression testing
3. Set up CI/CD integration for automated testing
