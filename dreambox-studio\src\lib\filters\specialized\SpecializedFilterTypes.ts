/**
 * SpecializedFilterTypes.ts
 *
 * This module registers specialized filter types.
 * It includes PromptElementsFilter and RelatedItemsFilter.
 */

import { filterRegistry } from '../FilterRegistry';
import PromptElementsFilter from './PromptElementsFilter.vue';
import RelatedItemsFilter from './RelatedItemsFilter.vue';

/**
 * Register specialized filter types
 */
export function registerSpecializedFilterTypes() {
  // Prompt Elements Filter
  filterRegistry.registerFilterType({
    id: 'promptElements',
    name: 'Prompt Elements',
    section: 'specialized',
    component: PromptElementsFilter,
    tableTypes: ['templates'],
    stateExtractor: (state: unknown) => {
      if (state && typeof state === 'object' && state !== null && 'promptElements' in state) {
        return (state as Record<string, unknown>).promptElements;
      }
      return null;
    },
    stateApplier: (item: unknown, value: unknown) => {
      if (!value || typeof value !== 'object' || value === null) return true;

      const filterValue = value as { elements?: number[], matchType?: 'all' | 'any' };
      if (!filterValue.elements || !Array.isArray(filterValue.elements) || filterValue.elements.length === 0) return true;

      // Check if the template has prompt elements
      if (!item || typeof item !== 'object' || item === null) return false;

      const template = item as Record<string, unknown>;
      if (!template.prompt_elements || !Array.isArray(template.prompt_elements)) {
        return false;
      }

      // Get element IDs from the template
      const templateElementIds = template.prompt_elements.map((el: { id: number }) => el.id);

      // Check if the template contains the required elements
      if (filterValue.matchType === 'all') {
        // Must contain all selected elements
        return filterValue.elements.every((id: number) => templateElementIds.includes(id));
      } else {
        // Must contain at least one selected element
        return filterValue.elements.some((id: number) => templateElementIds.includes(id));
      }
    },
    clearHandler: (state: unknown) => {
      if (state && typeof state === 'object' && state !== null) {
        const newState = { ...(state as Record<string, unknown>) };
        delete newState.promptElements;
        return newState;
      }
      return state;
    }
  });

  // Related Items Filter
  filterRegistry.registerFilterType({
    id: 'relatedItems',
    name: 'Related Items',
    section: 'specialized',
    component: RelatedItemsFilter,
    tableTypes: ['templates'],
    stateExtractor: (state: unknown) => {
      if (state && typeof state === 'object' && state !== null && 'relatedItems' in state) {
        return (state as Record<string, unknown>).relatedItems;
      }
      return null;
    },
    stateApplier: (item: unknown, value: unknown) => {
      if (!value || typeof value !== 'object' || value === null) return true;

      const filterValue = value as {
        items?: number[],
        matchType?: 'all' | 'any',
        type?: 'collections' | 'products'
      };

      if (!filterValue.items || !Array.isArray(filterValue.items) || filterValue.items.length === 0) return true;

      // Check if item is a valid object
      if (!item || typeof item !== 'object' || item === null) return false;
      const template = item as Record<string, unknown>;

      // Check based on relation type
      if (filterValue.type === 'collections') {
        // Check if the template is in the selected collections
        if (!template.collections || !Array.isArray(template.collections)) {
          return false;
        }

        const templateCollectionIds = (template.collections as Array<{ id: number }>).map(col => col.id);

        if (filterValue.matchType === 'all') {
          // Must be in all selected collections
          return filterValue.items.every(id => templateCollectionIds.includes(id));
        } else {
          // Must be in at least one selected collection
          return filterValue.items.some(id => templateCollectionIds.includes(id));
        }
      } else if (filterValue.type === 'products') {
        // Check if the template is associated with the selected products
        if (!template.products || !Array.isArray(template.products)) {
          return false;
        }

        const templateProductIds = (template.products as Array<{ id: number }>).map(prod => prod.id);

        if (filterValue.matchType === 'all') {
          // Must be associated with all selected products
          return filterValue.items.every(id => templateProductIds.includes(id));
        } else {
          // Must be associated with at least one selected product
          return filterValue.items.some(id => templateProductIds.includes(id));
        }
      }

      return true;
    },
    clearHandler: (state: unknown) => {
      if (state && typeof state === 'object' && state !== null) {
        const newState = { ...(state as Record<string, unknown>) };
        delete newState.relatedItems;
        return newState;
      }
      return state;
    }
  });

  // console.log('Specialized filter types registered');
}

// Export default for module imports
export default { registerSpecializedFilterTypes };
