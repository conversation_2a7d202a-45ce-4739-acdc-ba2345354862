import { defineStore } from 'pinia';
import { ref, reactive } from 'vue';

export type SearchContext = 'templates' | 'users' | 'products' | 'none';

export interface FilterOption {
  label: string;
  value: string | number | boolean;
}

export interface Filter {
  name: string;
  label: string;
  value: string | number | boolean | null;
  options?: FilterOption[];
}

export interface ActionButton {
  label: string;
  icon: string;
  color: string;
  action: () => void;
}

export const useSearchStore = defineStore('search', () => {
  // Current search context
  const context = ref<SearchContext>('none');

  // Search text
  const searchText = ref('');

  // Available filters for the current context
  const filters = reactive<Record<string, Filter>>({});

  // Action button configuration
  const actionButton = reactive<ActionButton>({
    label: '',
    icon: '',
    color: 'primary',
    action: () => {}
  });

  // Is the search expanded (showing filters)
  const expanded = ref(false);

  // Set the current search context
  function setContext(newContext: SearchContext) {
    context.value = newContext;
    // Reset search when changing context
    searchText.value = '';
    // Clear filters
    Object.keys(filters).forEach(key => {
      delete filters[key];
    });
    // Reset action button
    actionButton.label = '';
    actionButton.icon = '';
    actionButton.color = 'primary';
    actionButton.action = () => {};
    // Collapse search
    expanded.value = false;
  }

  // Add a filter
  function addFilter(filter: Filter) {
    filters[filter.name] = filter;
  }

  // Set multiple filters at once
  function setFilters(newFilters: Filter[]) {
    // Clear existing filters
    Object.keys(filters).forEach(key => {
      delete filters[key];
    });

    // Add new filters
    newFilters.forEach(filter => {
      filters[filter.name] = filter;
    });
  }

  // Update a filter value
  function updateFilter(name: string, value: string | number | boolean | null) {
    if (filters[name]) {
      filters[name].value = value;
    }
  }

  // Set the action button
  function setActionButton(button: Partial<ActionButton>) {
    Object.assign(actionButton, button);
  }

  // Toggle expanded state
  function toggleExpanded() {
    expanded.value = !expanded.value;
  }

  // Set expanded state
  function setExpanded(value: boolean) {
    expanded.value = value;
  }

  // Reset all search parameters
  function reset() {
    searchText.value = '';
    Object.keys(filters).forEach(key => {
      if (filters[key]) {
        filters[key].value = null;
      }
    });
  }

  return {
    context,
    searchText,
    filters,
    actionButton,
    expanded,
    setContext,
    addFilter,
    setFilters,
    updateFilter,
    setActionButton,
    toggleExpanded,
    setExpanded,
    reset
  };
});
