// SwarmUI Generation Edge Function with permissive CORS
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// Extend Request type to include parsedBody
declare global {
  interface Request {
    parsedBody?: any;
  }
}

// CORS headers for all responses
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': '*',
  'Access-Control-Allow-Headers': '*',
  'Access-Control-Max-Age': '86400',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  // Create Supabase client
  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
  )

  // Get user from request
  const { data: { user } } = await supabaseClient.auth.getUser()
  if (!user) {
    return new Response(JSON.stringify({ error: 'Unauthorized' }), {
      status: 401,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }

  // Get active server for the user
  const { data: servers, error: serverError } = await supabaseClient
    .from('swarmui_servers')
    .select('*')
    .eq('user_id', user.id)
    .eq('status', 'running')
    .order('created_at', { ascending: false })
    .limit(1)

  if (serverError) {
    return new Response(JSON.stringify({ error: serverError.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }

  if (servers.length === 0) {
    return new Response(JSON.stringify({ error: 'No active server found' }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }

  const server = servers[0]
  const apiEndpoint = server.api_endpoint
  const apiKey = server.api_key

  // Update server activity timestamp
  await supabaseClient
    .from('swarmui_servers')
    .update({ last_activity: new Date().toISOString() })
    .eq('id', server.id)

  // Get path from URL or body
  let path = '';
  let body: any = {};
  
  try {
    if (req.headers.get('content-type')?.includes('application/json')) {
      body = await req.json();
      path = body.path || '';
      req.parsedBody = body;
    }
  } catch (e) {
    // If JSON parsing fails, try URL params
    console.error('Error parsing JSON body:', e);
  }
  
  // If path not in body, try URL params
  if (!path) {
    const url = new URL(req.url);
    path = url.searchParams.get('path') || '';
  }

  // Handle different actions based on HTTP method
  if (req.method === 'POST') {
    // Generate image
    try {
      const generationParams = req.parsedBody || body;

      // Forward request to SwarmUI API
      const response = await fetch(`${apiEndpoint}/generations`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(generationParams)
      })

      if (!response.ok) {
        const errorText = await response.text()
        return new Response(JSON.stringify({ 
          error: `SwarmUI API error: ${errorText || response.statusText}` 
        }), {
          status: response.status,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        })
      }

      const data = await response.json()

      return new Response(JSON.stringify(data), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    } catch (error) {
      return new Response(JSON.stringify({ 
        error: `Failed to generate image: ${error.message}` 
      }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
  } else if (req.method === 'GET') {
    // Get generation status or other API endpoints
    try {
      // Forward request to SwarmUI API
      const response = await fetch(`${apiEndpoint}/${path}`, {
        headers: {
          'Authorization': `Bearer ${apiKey}`
        }
      })

      if (!response.ok) {
        const errorText = await response.text()
        return new Response(JSON.stringify({ 
          error: `SwarmUI API error: ${errorText || response.statusText}` 
        }), {
          status: response.status,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        })
      }

      const data = await response.json()

      return new Response(JSON.stringify(data), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    } catch (error) {
      return new Response(JSON.stringify({ 
        error: `Failed to get data from SwarmUI API: ${error.message}` 
      }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
  }

  return new Response(JSON.stringify({ error: 'Invalid method' }), {
    status: 400,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  })
})
