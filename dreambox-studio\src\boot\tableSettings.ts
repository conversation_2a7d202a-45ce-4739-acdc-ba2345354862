import { boot } from 'quasar/wrappers';
import { TableTextWrapMode, TABLE_SETTINGS_KEY } from 'src/types/TableSettings';
import { userSettingsService } from 'src/services/userSettingsService';

// Initialize table settings
export default boot(async () => {
  try {
    // Load table text wrap mode setting
    const savedWrapMode = await userSettingsService.getSetting(TABLE_SETTINGS_KEY);
    if (savedWrapMode) {
      const settings = JSON.parse(savedWrapMode);
      const textWrapMode = settings.textWrapMode || TableTextWrapMode.WRAP;

      // Apply the setting to the CSS
      if (textWrapMode === TableTextWrapMode.WRAP) {
        document.documentElement.style.setProperty('--table-text-wrap-mode', 'normal');
        document.documentElement.style.setProperty('--table-text-overflow', 'visible');
      } else {
        document.documentElement.style.setProperty('--table-text-wrap-mode', 'nowrap');
        document.documentElement.style.setProperty('--table-text-overflow', 'ellipsis');
      }

      // Add a class to the body to help with CSS specificity
      if (textWrapMode === TableTextWrapMode.ELLIPSIS) {
        document.body.classList.add('table-text-ellipsis');
      } else {
        document.body.classList.remove('table-text-ellipsis');
      }
    }
  } catch (error) {
    console.error('Error loading table settings:', error);
  }

  // Listen for table text wrap mode changes
  document.addEventListener('table-text-wrap-mode-changed', (event: Event) => {
    const customEvent = event as CustomEvent;
    const { mode } = customEvent.detail;

    // Apply the setting to the CSS
    if (mode === TableTextWrapMode.WRAP) {
      document.documentElement.style.setProperty('--table-text-wrap-mode', 'normal');
      document.documentElement.style.setProperty('--table-text-overflow', 'visible');
    } else {
      document.documentElement.style.setProperty('--table-text-wrap-mode', 'nowrap');
      document.documentElement.style.setProperty('--table-text-overflow', 'ellipsis');
    }

    // Add a class to the body to help with CSS specificity
    if (mode === TableTextWrapMode.ELLIPSIS) {
      document.body.classList.add('table-text-ellipsis');
    } else {
      document.body.classList.remove('table-text-ellipsis');
    }
  });
});
