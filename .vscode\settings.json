{"sqltools.connections": [{"previewLimit": 50, "server": "db.ppzaqhuaqzjofsorlmbn.supabase.co", "port": 5432, "driver": "PostgreSQL", "name": "Supabase", "database": "postgres", "username": "postgres", "password": "opfb7oFzck6hbG07"}], "files.exclude": {"**/node_modules": true}, "search.exclude": {"**/node_modules": true}, "foam.files.ignore": ["**/node_modules/**/*"], "foam.files.newNotePath": "currentDir", "foam.files.attachmentExtensions": "md codediagram", "foam.links.sync.enable": true, "foam.openDailyNote.directory": ".arch/foam/daily", "foam.graph.style": {"background": "#202020", "fontSize": 12, "lineColor": "#277da1", "lineWidth": 0.5, "particleWidth": 2.0, "highlightedForeground": "#f9c74f", "node": {"note": "#ffffff"}, "size-increment": 2}}