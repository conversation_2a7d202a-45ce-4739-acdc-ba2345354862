const { execSync } = require('child_process');
const cleanDist = require('./clean-dist');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Define the environment variables we want to bake in
const ENV_VARS_TO_BAKE = {
    SUPABASE_URL: process.env.SUPABASE_URL,
    SUPABASE_KEY: process.env.SUPABASE_KEY,
    GH_TOKEN: process.env.GH_TOKEN
};

function createBakedConfig() {
    return `// This file is auto-generated during build. Do not edit.
module.exports = {
    SUPABASE_URL: '${ENV_VARS_TO_BAKE.SUPABASE_URL}',
    SUPABASE_KEY: '${ENV_VARS_TO_BAKE.SUPABASE_KEY}',
    GH_TOKEN: '${ENV_VARS_TO_BAKE.GH_TOKEN}'
};`;
}

async function main() {
    try {
        // Clean dist folder first
        const cleaned = cleanDist();
        if (!cleaned) {
            process.exit(1);
        }

        // Validate environment variables
        const missingVars = Object.entries(ENV_VARS_TO_BAKE)
            .filter(([_, value]) => !value)
            .map(([key]) => key);

        if (missingVars.length > 0) {
            console.error('Missing required environment variables:', missingVars.join(', '));
            process.exit(1);
        }

        // Ensure src directory exists
        const srcDir = path.join(__dirname, '../src');
        if (!fs.existsSync(srcDir)) {
            fs.mkdirSync(srcDir, { recursive: true });
        }

        // Create baked config file
        const configPath = path.join(srcDir, 'baked-config.js');
        fs.writeFileSync(configPath, createBakedConfig());

        console.log('Created baked configuration file');

        // Run electron-builder with additional flags to skip problematic steps
        execSync('npx electron-builder --dir --config.win.signAndEditExecutable=false -c.compression=store -p never', {
            stdio: 'inherit',
            env: {
                ...process.env,
                FORCE_COLOR: true
            }
        });

        // Clean up baked config
        fs.unlinkSync(configPath);
        console.log('Cleaned up baked configuration file');
        
        // Log the location of the unpacked app
        console.log('\nBuild completed! You can find the unpacked app in:');
        console.log('dist/win-unpacked/Dreambox Studio.exe');

    } catch (err) {
        console.error('Build failed:', err);
        process.exit(1);
    }
}

main();


