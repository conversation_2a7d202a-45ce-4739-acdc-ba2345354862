<template>
  <q-input
    v-model="searchText"
    dense
    borderless
    dark
    color="white"
    class="search-input q-mr-sm"
    :placeholder="placeholder"
    clearable
    debounce="300"
    @update:model-value="onSearch"
  >
    <template v-slot:append>
      <q-icon name="search" />
    </template>
  </q-input>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useSearchStore } from 'src/stores/searchStore';

const props = defineProps<{
  targetCollection: string;
  placeholder?: string;
}>();

const searchStore = useSearchStore();
const searchText = ref('');

// Initialize search text from store
searchText.value = searchStore.searchText;

// Watch for changes from store
watch(() => searchStore.searchText, (newText) => {
  searchText.value = newText;
});

// Handle search
function onSearch(text: string | number | null) {
  if (text !== null) {
    const searchValue = String(text);
    searchStore.searchText = searchValue;
    // Use the context property directly instead of setContext
    searchStore.context = props.targetCollection as 'templates' | 'users' | 'products' | 'none';

    // Emit a custom event that components can listen for
    document.dispatchEvent(new CustomEvent('global-search-changed', {
      detail: { value: searchValue, filters: searchStore.filters }
    }));
  }
}
</script>

<style lang="scss" scoped>
.search-input {
  min-width: 200px;
  max-width: 300px;
}

@media (max-width: 600px) {
  .search-input {
    min-width: 120px;
  }
}
</style>
