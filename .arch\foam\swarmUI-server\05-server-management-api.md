# Step 5: Server Management API Implementation

This document outlines the implementation of the Server Management API, which will handle starting and stopping RunPod instances with SwarmUI.

## 5.1 Create API Service Directory Structure

First, create the directory structure for the server management API:

```
src/
└── services/
    └── serverManagement/
        ├── index.ts
        ├── ServerManagementService.ts
        ├── RunPodService.ts
        ├── types.ts
        └── utils.ts
```

## 5.2 Define Types

Create the types for the server management API in `src/services/serverManagement/types.ts`:

```typescript
// types.ts
export interface ServerConfig {
  id: number;
  name: string;
  description: string;
  templateId: string;
  gpuType: string;
  gpuCount: number;
  memoryGB: number;
  diskGB: number;
  hourlyCost: number;
  isDefault: boolean;
  isActive: boolean;
}

export interface ServerInstance {
  id: number;
  userId: number;
  podId: string;
  apiEndpoint: string;
  apiKey: string;
  status: ServerStatus;
  gpuType: string;
  startedAt: string;
  stoppedAt: string | null;
  lastActivity: string;
  createdAt: string;
  updatedAt: string;
}

export type ServerStatus = 'starting' | 'running' | 'stopping' | 'stopped' | 'error';

export interface ServerStartOptions {
  configurationId?: number;
}

export interface ServerStartResult {
  serverId: number;
  status: ServerStatus;
  message: string;
}

export interface ServerStopResult {
  success: boolean;
  message: string;
}

export interface RunPodStartOptions {
  templateId: string;
  gpuCount: number;
  volumeInGb: number;
  containerDiskInGb: number;
  name: string;
  env: Record<string, string>;
}

export interface RunPodInstance {
  id: string;
  name: string;
  runtime: {
    ports: {
      [key: string]: {
        ip: string;
        isExposed: boolean;
        privatePort: number;
        publicPort: number;
        publicDns: string;
      }
    }
  };
  env: Record<string, string>;
}
```

## 5.3 Implement RunPod Service

Create the RunPod service in `src/services/serverManagement/RunPodService.ts`:

```typescript
// RunPodService.ts
import axios from 'axios';
import { supabase } from '../../boot/supabase';
import { RunPodInstance, RunPodStartOptions } from './types';

export class RunPodService {
  private apiKey: string | null = null;
  private baseUrl = 'https://api.runpod.io/v2';

  constructor() {
    this.initApiKey();
  }

  private async initApiKey() {
    try {
      const { data, error } = await supabase
        .rpc('get_api_key', { service_name: 'runpod' });

      if (error) {
        console.error('Error getting RunPod API key:', error);
        return;
      }

      this.apiKey = data;
    } catch (error) {
      console.error('Error initializing RunPod API key:', error);
    }
  }

  private async ensureApiKey(): Promise<string> {
    if (!this.apiKey) {
      await this.initApiKey();
    }

    if (!this.apiKey) {
      throw new Error('RunPod API key not available');
    }

    return this.apiKey;
  }

  private getHeaders() {
    return {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json'
    };
  }

  async startPod(options: RunPodStartOptions): Promise<RunPodInstance> {
    await this.ensureApiKey();

    try {
      const response = await axios.post(
        `${this.baseUrl}/pods`,
        options,
        { headers: this.getHeaders() }
      );

      return response.data;
    } catch (error) {
      console.error('Error starting RunPod instance:', error);
      throw new Error('Failed to start RunPod instance');
    }
  }

  async stopPod(podId: string): Promise<boolean> {
    await this.ensureApiKey();

    try {
      await axios.post(
        `${this.baseUrl}/pods/${podId}/stop`,
        {},
        { headers: this.getHeaders() }
      );

      return true;
    } catch (error) {
      console.error('Error stopping RunPod instance:', error);
      throw new Error('Failed to stop RunPod instance');
    }
  }

  async getPodStatus(podId: string): Promise<any> {
    await this.ensureApiKey();

    try {
      const response = await axios.get(
        `${this.baseUrl}/pods/${podId}`,
        { headers: this.getHeaders() }
      );

      return response.data;
    } catch (error) {
      console.error('Error getting RunPod status:', error);
      throw new Error('Failed to get RunPod status');
    }
  }

  async waitForPodRunning(podId: string, maxWaitSeconds = 300): Promise<RunPodInstance> {
    const startTime = Date.now();
    const maxWaitMs = maxWaitSeconds * 1000;

    while (Date.now() - startTime < maxWaitMs) {
      const status = await this.getPodStatus(podId);

      if (status.status === 'RUNNING') {
        return status.pod;
      }

      // Wait 5 seconds before checking again
      await new Promise(resolve => setTimeout(resolve, 5000));
    }

    throw new Error('Timeout waiting for pod to start');
  }
}

export const runPodService = new RunPodService();
```

## 5.4 Implement Server Management Service

Create the Server Management service in `src/services/serverManagement/ServerManagementService.ts`:

```typescript
// ServerManagementService.ts
import { supabase } from '../../boot/supabase';
import { useAuthStore } from '../../stores/auth';
import { runPodService } from './RunPodService';
import {
  ServerConfig,
  ServerInstance,
  ServerStartOptions,
  ServerStartResult,
  ServerStopResult
} from './types';

export class ServerManagementService {
  async getServerConfigurations(): Promise<ServerConfig[]> {
    const { data, error } = await supabase
      .from('server_configurations')
      .select('*')
      .eq('is_active', true)
      .order('hourly_cost', { ascending: true });

    if (error) {
      console.error('Error fetching server configurations:', error);
      throw new Error('Failed to fetch server configurations');
    }

    return data;
  }

  async getUserServers(): Promise<ServerInstance[]> {
    const authStore = useAuthStore();
    const user = authStore.getUser();

    if (!user) {
      throw new Error('User not authenticated');
    }

    const { data, error } = await supabase
      .from('gpu_servers')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching user servers:', error);
      throw new Error('Failed to fetch user servers');
    }

    return data;
  }

  async getActiveServer(): Promise<ServerInstance | null> {
    const authStore = useAuthStore();
    const user = authStore.getUser();

    if (!user) {
      throw new Error('User not authenticated');
    }

    const { data, error } = await supabase
      .from('gpu_servers')
      .select('*')
      .eq('user_id', user.id)
      .eq('status', 'running')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned"
      console.error('Error fetching active server:', error);
      throw new Error('Failed to fetch active server');
    }

    return data || null;
  }

  async startServer(options: ServerStartOptions = {}): Promise<ServerStartResult> {
    const authStore = useAuthStore();
    const user = authStore.getUser();

    if (!user) {
      throw new Error('User not authenticated');
    }

    try {
      // Check if user already has a running server
      const activeServer = await this.getActiveServer();
      if (activeServer) {
        return {
          serverId: activeServer.id,
          status: activeServer.status,
          message: 'Server is already running'
        };
      }

      // Start a new server using the database function
      const { data, error } = await supabase
        .rpc('start_server', {
          p_user_id: user.id,
          p_configuration_id: options.configurationId || null
        });

      if (error) {
        console.error('Error starting server:', error);
        throw new Error('Failed to start server');
      }

      const serverId = data.server_id;
      const templateId = data.template_id;

      // Start the RunPod instance
      const podInstance = await runPodService.startPod({
        templateId,
        gpuCount: data.gpu_count,
        volumeInGb: data.disk_gb,
        containerDiskInGb: 10,
        name: `SwarmUI-${user.id}`,
        env: {
          SWARMUI_API_KEY: `swarmui-${Math.random().toString(36).substring(2, 15)}`
        }
      });

      // Wait for the pod to be running
      const runningPod = await runPodService.waitForPodRunning(podInstance.id);

      // Get the API endpoint and key
      const apiEndpoint = `https://${runningPod.runtime.ports['8000'].publicDns}`;
      const apiKey = runningPod.env.SWARMUI_API_KEY;

      // Update the server record
      await supabase.rpc('update_server_status', {
        p_server_id: serverId,
        p_pod_id: runningPod.id,
        p_api_endpoint: apiEndpoint,
        p_api_key: apiKey,
        p_status: 'running'
      });

      return {
        serverId,
        status: 'running',
        message: 'Server started successfully'
      };
    } catch (error) {
      console.error('Error in startServer:', error);
      throw new Error('Failed to start server');
    }
  }

  async stopServer(serverId: number): Promise<ServerStopResult> {
    const authStore = useAuthStore();
    const user = authStore.getUser();

    if (!user) {
      throw new Error('User not authenticated');
    }

    try {
      // Get the server details
      const { data: server, error } = await supabase
        .from('gpu_servers')
        .select('*')
        .eq('id', serverId)
        .eq('user_id', user.id)
        .single();

      if (error) {
        console.error('Error fetching server:', error);
        throw new Error('Failed to fetch server');
      }

      if (!server) {
        return {
          success: false,
          message: 'Server not found'
        };
      }

      if (server.status !== 'running') {
        return {
          success: false,
          message: `Server is not running (status: ${server.status})`
        };
      }

      // Update server status to stopping
      await supabase.rpc('stop_server', {
        p_server_id: serverId
      });

      // Stop the RunPod instance
      await runPodService.stopPod(server.pod_id);

      // Update the server record
      await supabase
        .from('gpu_servers')
        .update({
          status: 'stopped',
          stopped_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', serverId);

      return {
        success: true,
        message: 'Server stopped successfully'
      };
    } catch (error) {
      console.error('Error in stopServer:', error);
      throw new Error('Failed to stop server');
    }
  }

  async updateServerActivity(serverId: number): Promise<void> {
    try {
      await supabase.rpc('update_server_activity', {
        p_server_id: serverId
      });
    } catch (error) {
      console.error('Error updating server activity:', error);
    }
  }
}

export const serverManagementService = new ServerManagementService();
```

## 5.5 Create Service Index File

Create the index file to export the services in `src/services/serverManagement/index.ts`:

```typescript
// index.ts
export * from './ServerManagementService';
export * from './RunPodService';
export * from './types';
```

## 5.6 Create Utility Functions

Create utility functions in `src/services/serverManagement/utils.ts`:

```typescript
// utils.ts
import { ServerInstance } from './types';

export function getServerCost(server: ServerInstance): number {
  if (!server.startedAt || !server.stoppedAt) {
    return 0;
  }

  const startTime = new Date(server.startedAt).getTime();
  const endTime = new Date(server.stoppedAt).getTime();
  const durationHours = (endTime - startTime) / (1000 * 60 * 60);

  // This is a placeholder - you would need to look up the actual cost
  // based on the GPU type from your server_configurations table
  const hourlyRate = 0.5; // $0.50 per hour

  return durationHours * hourlyRate;
}

export function formatDuration(durationSeconds: number): string {
  if (durationSeconds < 60) {
    return `${durationSeconds} seconds`;
  }

  const minutes = Math.floor(durationSeconds / 60);
  if (minutes < 60) {
    return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
  }

  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;

  if (remainingMinutes === 0) {
    return `${hours} hour${hours !== 1 ? 's' : ''}`;
  }

  return `${hours} hour${hours !== 1 ? 's' : ''} ${remainingMinutes} minute${remainingMinutes !== 1 ? 's' : ''}`;
}
```

## 5.7 Implementing Server Management with Supabase Edge Functions

For PWA applications without a Node.js backend (like our TypeScript/HTML application hosted on Netlify), we can use Supabase Edge Functions to securely interact with the RunPod API. This approach keeps API keys secure while allowing the frontend to manage RunPod instances.

### 5.7.1 Edge Functions Overview

Supabase Edge Functions are serverless functions that run on Deno, allowing us to create secure API endpoints that can be called from our frontend application. For our SwarmUI integration, we'll create the following Edge Functions:

1. `start-pod`: Start a RunPod instance from our SwarmUI template
2. `stop-pod`: Stop a running RunPod instance
3. `get-pod-status`: Check the status of a RunPod instance
4. `generate-image`: Send image generation requests to SwarmUI

### 5.7.2 Setting Up Edge Functions

First, install the Supabase CLI and initialize the Edge Functions:

```bash
# Install Supabase CLI
npm install -g supabase

# Login to Supabase
supabase login

# Initialize Supabase in your project (if not already done)
supabase init

# Create the Edge Functions directory structure
mkdir -p supabase/functions
```

### 5.7.3 Implementing the Start Pod Edge Function

Create a new Edge Function for starting a RunPod instance:

```bash
# Create the start-pod function
mkdir -p supabase/functions/start-pod
```

Create the function implementation in `supabase/functions/start-pod/index.ts`:

```typescript
// supabase/functions/start-pod/index.ts
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

serve(async (req) => {
  // CORS headers
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Authorization, Content-Type',
      }
    })
  }

  // Create Supabase client
  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
  )

  // Get user from request
  const { data: { user } } = await supabaseClient.auth.getUser()
  if (!user) {
    return new Response(JSON.stringify({ error: 'Unauthorized' }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    })
  }

  // Check if user has permission (e.g., is admin or designer)
  const { data: roles } = await supabaseClient
    .from('user_roles_view')
    .select('role')
    .eq('user_id', user.id)

  const isAuthorized = roles.some(r => ['admin', 'super-admin', 'designer'].includes(r.role))
  if (!isAuthorized) {
    return new Response(JSON.stringify({ error: 'Forbidden' }), {
      status: 403,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    })
  }

  try {
    // Get RunPod API key securely from database
    const { data: apiKey, error: keyError } = await supabaseClient.rpc('get_api_key', {
      service_name: 'runpod'
    })

    if (keyError) {
      throw new Error(`Failed to retrieve API key: ${keyError.message}`)
    }

    // Start a new server using the database function
    const { data: serverData, error: serverError } = await supabaseClient.rpc('start_server', {
      p_user_id: user.id,
      p_configuration_id: null // Use default configuration
    })

    if (serverError) {
      throw new Error(`Failed to start server: ${serverError.message}`)
    }

    const serverId = serverData.server_id
    const templateId = serverData.template_id

    // Call RunPod API to start the pod
    const response = await fetch('https://api.runpod.io/v2/pods', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        templateId: templateId,
        gpuCount: serverData.gpu_count,
        volumeInGb: serverData.disk_gb,
        containerDiskInGb: 10,
        name: `SwarmUI-${user.id}`,
        env: {
          SWARMUI_API_KEY: `swarmui-${crypto.randomUUID()}`
        }
      })
    })

    if (!response.ok) {
      throw new Error(`RunPod API error: ${response.status} ${response.statusText}`)
    }

    const podData = await response.json()

    // Update the server record with the pod ID
    await supabaseClient.rpc('update_server_status', {
      p_server_id: serverId,
      p_pod_id: podData.id,
      p_api_endpoint: 'pending', // Will be updated when pod is running
      p_api_key: podData.env.SWARMUI_API_KEY,
      p_status: 'starting'
    })

    return new Response(JSON.stringify({
      serverId: serverId,
      podId: podData.id,
      status: 'starting',
      message: 'Server is starting'
    }), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    })
  } catch (error) {
    console.error('Error starting pod:', error)
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    })
  }
})
```

### 5.7.4 Implementing the Stop Pod Edge Function

Create a new Edge Function for stopping a RunPod instance:

```bash
# Create the stop-pod function
mkdir -p supabase/functions/stop-pod
```

Create the function implementation in `supabase/functions/stop-pod/index.ts`:

```typescript
// supabase/functions/stop-pod/index.ts
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

serve(async (req) => {
  // CORS headers
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Authorization, Content-Type',
      }
    })
  }

  // Create Supabase client
  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
  )

  // Get user from request
  const { data: { user } } = await supabaseClient.auth.getUser()
  if (!user) {
    return new Response(JSON.stringify({ error: 'Unauthorized' }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    })
  }

  try {
    // Parse request body
    const { serverId } = await req.json()

    if (!serverId) {
      throw new Error('Server ID is required')
    }

    // Get the server details
    const { data: server, error: serverError } = await supabaseClient
      .from('gpu_servers')
      .select('*')
      .eq('id', serverId)
      .eq('user_id', user.id)
      .single()

    if (serverError) {
      throw new Error(`Failed to fetch server: ${serverError.message}`)
    }

    if (!server) {
      return new Response(JSON.stringify({ error: 'Server not found' }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      })
    }

    if (server.status !== 'running') {
      return new Response(JSON.stringify({
        success: false,
        message: `Server is not running (status: ${server.status})`
      }), {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      })
    }

    // Update server status to stopping
    await supabaseClient.rpc('stop_server', {
      p_server_id: serverId
    })

    // Get RunPod API key securely from database
    const { data: apiKey, error: keyError } = await supabaseClient.rpc('get_api_key', {
      service_name: 'runpod'
    })

    if (keyError) {
      throw new Error(`Failed to retrieve API key: ${keyError.message}`)
    }

    // Call RunPod API to stop the pod
    const response = await fetch(`https://api.runpod.io/v2/pods/${server.pod_id}/stop`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`RunPod API error: ${response.status} ${response.statusText}`)
    }

    // Update the server record
    await supabaseClient
      .from('gpu_servers')
      .update({
        status: 'stopped',
        stopped_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', serverId)

    return new Response(JSON.stringify({
      success: true,
      message: 'Server stopped successfully'
    }), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    })
  } catch (error) {
    console.error('Error stopping pod:', error)
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    })
  }
})
```

### 5.7.5 Implementing the Get Pod Status Edge Function

Create a new Edge Function for checking the status of a RunPod instance:

```bash
# Create the get-pod-status function
mkdir -p supabase/functions/get-pod-status
```

Create the function implementation in `supabase/functions/get-pod-status/index.ts`:

```typescript
// supabase/functions/get-pod-status/index.ts
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

serve(async (req) => {
  // CORS headers
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Allow-Headers': 'Authorization, Content-Type',
      }
    })
  }

  // Create Supabase client
  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
  )

  // Get user from request
  const { data: { user } } = await supabaseClient.auth.getUser()
  if (!user) {
    return new Response(JSON.stringify({ error: 'Unauthorized' }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    })
  }

  try {
    // Get URL parameters
    const url = new URL(req.url)
    const serverId = url.searchParams.get('serverId')

    if (!serverId) {
      throw new Error('Server ID is required')
    }

    // Get the server details
    const { data: server, error: serverError } = await supabaseClient
      .from('gpu_servers')
      .select('*')
      .eq('id', serverId)
      .eq('user_id', user.id)
      .single()

    if (serverError) {
      throw new Error(`Failed to fetch server: ${serverError.message}`)
    }

    if (!server) {
      return new Response(JSON.stringify({ error: 'Server not found' }), {
        status: 404,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      })
    }

    // If the server is in 'starting' state, check the RunPod status
    if (server.status === 'starting' && server.pod_id !== 'pending') {
      // Get RunPod API key securely from database
      const { data: apiKey, error: keyError } = await supabaseClient.rpc('get_api_key', {
        service_name: 'runpod'
      })

      if (keyError) {
        throw new Error(`Failed to retrieve API key: ${keyError.message}`)
      }

      // Call RunPod API to get pod status
      const response = await fetch(`https://api.runpod.io/v2/pods/${server.pod_id}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`RunPod API error: ${response.status} ${response.statusText}`)
      }

      const podData = await response.json()

      // If the pod is running, update the server record
      if (podData.status === 'RUNNING') {
        const apiEndpoint = `https://${podData.pod.runtime.ports['8000'].publicDns}`

        await supabaseClient.rpc('update_server_status', {
          p_server_id: serverId,
          p_pod_id: server.pod_id,
          p_api_endpoint: apiEndpoint,
          p_api_key: server.api_key,
          p_status: 'running'
        })

        // Update the server object with the new status
        server.status = 'running'
        server.api_endpoint = apiEndpoint
      }
    }

    // Update the last activity timestamp
    await supabaseClient.rpc('update_server_activity', {
      p_server_id: serverId
    })

    return new Response(JSON.stringify({
      server: {
        id: server.id,
        status: server.status,
        apiEndpoint: server.api_endpoint,
        apiKey: server.api_key,
        startedAt: server.started_at,
        lastActivity: server.last_activity
      }
    }), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    })
  } catch (error) {
    console.error('Error getting pod status:', error)
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    })
  }
})
```

### 5.7.6 Deploying Edge Functions

Deploy the Edge Functions to your Supabase project:

```bash
# Deploy all functions
supabase functions deploy

# Or deploy individual functions
supabase functions deploy start-pod
supabase functions deploy stop-pod
supabase functions deploy get-pod-status
```

### 5.7.7 Calling Edge Functions from the Frontend

Now you can call these Edge Functions from your PWA frontend:

```typescript
// Example: Starting a pod
async function startServer() {
  try {
    const { data, error } = await supabase.functions.invoke('start-pod', {
      method: 'POST'
    })

    if (error) throw error

    console.log('Server starting:', data)
    return data
  } catch (error) {
    console.error('Error starting server:', error)
    throw error
  }
}

// Example: Stopping a pod
async function stopServer(serverId) {
  try {
    const { data, error } = await supabase.functions.invoke('stop-pod', {
      method: 'POST',
      body: { serverId }
    })

    if (error) throw error

    console.log('Server stopped:', data)
    return data
  } catch (error) {
    console.error('Error stopping server:', error)
    throw error
  }
}

// Example: Getting pod status
async function getServerStatus(serverId) {
  try {
    const { data, error } = await supabase.functions.invoke('get-pod-status', {
      method: 'GET',
      query: { serverId }
    })

    if (error) throw error

    console.log('Server status:', data)
    return data
  } catch (error) {
    console.error('Error getting server status:', error)
    throw error
  }
}
```

### 5.7.8 Benefits of Using Edge Functions

Using Supabase Edge Functions for RunPod management provides several benefits:

1. **Security**: API keys are never exposed to the client
2. **Authentication**: Leverages Supabase's built-in authentication
3. **Authorization**: Can enforce role-based access control
4. **Simplicity**: No need for a separate backend server
5. **Cost-Efficiency**: Serverless functions only run when needed
6. **Scalability**: Can handle multiple concurrent requests

This approach is ideal for PWA applications without a Node.js backend, as it provides a secure way to interact with external APIs while keeping sensitive information protected.

## Next Steps

Now that you have implemented the Server Management API, proceed to [Step 6: SwarmUI API Client Implementation](./06-swarmui-api-client.md) to implement the client for interacting with the SwarmUI API.
