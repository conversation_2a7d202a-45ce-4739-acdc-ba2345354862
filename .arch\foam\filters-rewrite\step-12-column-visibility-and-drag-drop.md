# Step 12: Column Visibility and Drag/Drop Functionality

## Overview

This document outlines how the new filter engine will handle column visibility management and drag/drop functionality between sections. These features are essential for providing a flexible and responsive table experience, especially for complex tables like Templates.

## Column Visibility Management

### Goals

1. **Flexible Column Display**: Allow users to show/hide columns based on their preferences
2. **Responsive Design Support**: Support responsive tables with main (always visible) and expanded (hidden by default) sections
3. **Persistent Preferences**: Save column visibility preferences for each table and context
4. **Table-Specific Configurations**: Support different column configurations for different tables

### Column Definition Interface

```typescript
interface ColumnDefinition {
  id: string;                      // Unique identifier
  name: string;                    // Display name
  field: string;                   // Data field name
  filterType: string;              // Type of filter to use
  section: 'main' | 'expanded';    // Which section the column belongs to
  order: number;                   // Display order within section
  visible: boolean;                // Whether the column is visible
  filterValue: any;                // Current filter value
  filterOptions?: Array<{          // Options for select/multiselect filters
    label: string;
    value: any;
  }>;
  width?: string;                  // Optional column width
  sortable?: boolean;              // Whether the column is sortable
  align?: 'left' | 'center' | 'right'; // Column alignment
}
```

### Column Visibility Store Extensions

The filter store will be extended to handle column visibility:

```typescript
// Additional state properties
state: {
  // Existing properties...
  
  // Column visibility state
  columnVisibility: Record<string, boolean>;
  columnSections: Record<string, 'main' | 'expanded'>;
  columnOrder: Record<string, number>;
}

// Additional getters
getters: {
  // Existing getters...
  
  // Get visible columns for the main section
  visibleMainColumns: (state) => {
    return state.columns
      .filter(col => col.section === 'main' && col.visible)
      .sort((a, b) => a.order - b.order);
  },
  
  // Get visible columns for the expanded section
  visibleExpandedColumns: (state) => {
    return state.columns
      .filter(col => col.section === 'expanded' && col.visible)
      .sort((a, b) => a.order - b.order);
  },
  
  // Get hidden columns
  hiddenColumns: (state) => {
    return state.columns
      .filter(col => !col.visible)
      .sort((a, b) => a.order - b.order);
  }
}

// Additional actions
actions: {
  // Existing actions...
  
  // Toggle column visibility
  toggleColumnVisibility(columnId: string, visible: boolean) {
    const column = this.columns.find(col => col.id === columnId);
    if (column) {
      column.visible = visible;
      this.columnVisibility[columnId] = visible;
      this.hasUnsavedChanges = true;
    }
  },
  
  // Update column section
  updateColumnSection(columnId: string, section: 'main' | 'expanded') {
    const column = this.columns.find(col => col.id === columnId);
    if (column) {
      column.section = section;
      this.columnSections[columnId] = section;
      
      // Update order to be at the end of the section
      const sectionColumns = this.columns.filter(col => col.section === section);
      const maxOrder = sectionColumns.reduce((max, col) => Math.max(max, col.order), -1);
      column.order = maxOrder + 1;
      this.columnOrder[columnId] = column.order;
      
      this.hasUnsavedChanges = true;
    }
  },
  
  // Update column order
  updateColumnOrder(columnId: string, order: number) {
    const column = this.columns.find(col => col.id === columnId);
    if (column) {
      column.order = order;
      this.columnOrder[columnId] = order;
      this.hasUnsavedChanges = true;
    }
  },
  
  // Save column configuration
  async saveColumnConfiguration() {
    if (!this.currentFilterId) {
      // Create a new filter for the column configuration
      return this.saveCurrentFilter('Column Configuration', 'Saved column layout', false);
    } else {
      // Update the existing filter
      const filter = this.savedFilters.find(f => f.id === this.currentFilterId);
      if (filter) {
        // Update the configuration
        filter.configuration.columns = this.columns.map(col => ({
          id: col.id,
          visible: col.visible,
          section: col.section,
          order: col.order,
          filterValue: col.filterValue || null
        }));
        
        // Save to database
        const { error } = await supabase
          .from('user_filters')
          .update({
            configuration: filter.configuration,
            updated_at: new Date().toISOString()
          })
          .eq('id', this.currentFilterId);
        
        if (error) {
          console.error('Error saving column configuration:', error);
          return false;
        }
        
        this.hasUnsavedChanges = false;
        return true;
      }
      return false;
    }
  }
}
```

### Column Visibility Component

A dedicated component for managing column visibility:

```vue
<!-- src/components/filters/ColumnVisibilityManager.vue -->
<template>
  <div class="column-visibility-manager">
    <div class="text-subtitle1 q-mb-sm">Column Visibility</div>
    
    <div class="section-container">
      <div class="section-header">
        <div class="text-subtitle2">Main Columns</div>
        <q-btn
          flat
          round
          dense
          icon="visibility"
          color="primary"
          @click="showAllMainColumns"
        >
          <q-tooltip>Show all main columns</q-tooltip>
        </q-btn>
      </div>
      
      <draggable
        v-model="mainColumns"
        group="columns"
        item-key="id"
        @end="onDragEnd"
        class="column-list q-mb-md"
        handle=".drag-handle"
      >
        <template #item="{element}">
          <div class="column-item q-py-xs">
            <div class="row items-center no-wrap">
              <q-icon name="drag_indicator" class="drag-handle cursor-move q-mr-xs" />
              
              <q-checkbox
                v-model="element.visible"
                :label="element.name"
                @update:model-value="(val) => toggleVisibility(element.id, val)"
              />
            </div>
          </div>
        </template>
      </draggable>
    </div>
    
    <div class="section-container">
      <div class="section-header">
        <div class="text-subtitle2">Expanded Columns</div>
        <q-btn
          flat
          round
          dense
          icon="visibility"
          color="primary"
          @click="showAllExpandedColumns"
        >
          <q-tooltip>Show all expanded columns</q-tooltip>
        </q-btn>
      </div>
      
      <draggable
        v-model="expandedColumns"
        group="columns"
        item-key="id"
        @end="onDragEnd"
        class="column-list"
        handle=".drag-handle"
      >
        <template #item="{element}">
          <div class="column-item q-py-xs">
            <div class="row items-center no-wrap">
              <q-icon name="drag_indicator" class="drag-handle cursor-move q-mr-xs" />
              
              <q-checkbox
                v-model="element.visible"
                :label="element.name"
                @update:model-value="(val) => toggleVisibility(element.id, val)"
              />
            </div>
          </div>
        </template>
      </draggable>
    </div>
    
    <div class="actions q-mt-md">
      <q-btn
        outline
        color="primary"
        label="Save Layout"
        :disable="!filterStore.hasUnsavedChanges"
        @click="saveLayout"
      />
      
      <q-btn
        outline
        color="grey-7"
        label="Reset to Default"
        class="q-ml-sm"
        @click="resetToDefault"
      />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useFilterStore } from '@/stores/filterStore';
import draggable from 'vuedraggable';

// Store
const filterStore = useFilterStore();

// Computed
const mainColumns = computed({
  get: () => {
    return filterStore.columns
      .filter(col => col.section === 'main')
      .sort((a, b) => a.order - b.order);
  },
  set: (value) => {
    // Update order based on new array order
    value.forEach((col, index) => {
      filterStore.updateColumnOrder(col.id, index);
    });
  }
});

const expandedColumns = computed({
  get: () => {
    return filterStore.columns
      .filter(col => col.section === 'expanded')
      .sort((a, b) => a.order - b.order);
  },
  set: (value) => {
    // Update order based on new array order
    value.forEach((col, index) => {
      filterStore.updateColumnOrder(col.id, index);
    });
  }
});

// Methods
function toggleVisibility(columnId, visible) {
  filterStore.toggleColumnVisibility(columnId, visible);
}

function onDragEnd(evt) {
  const { from, to, oldIndex, newIndex, item } = evt;
  
  // Get the column ID from the dragged item
  const columnId = item.getAttribute('data-column-id');
  
  // Get the source and target sections
  const fromSection = from.getAttribute('data-section');
  const toSection = to.getAttribute('data-section');
  
  // If moving between sections
  if (fromSection !== toSection) {
    filterStore.updateColumnSection(columnId, toSection);
  }
  
  // The order is already updated by the computed setter
}

function showAllMainColumns() {
  mainColumns.value.forEach(col => {
    filterStore.toggleColumnVisibility(col.id, true);
  });
}

function showAllExpandedColumns() {
  expandedColumns.value.forEach(col => {
    filterStore.toggleColumnVisibility(col.id, true);
  });
}

function saveLayout() {
  filterStore.saveColumnConfiguration();
}

function resetToDefault() {
  // Reset to the default column configuration for this table
  filterStore.loadBaseColumnsForTable(filterStore.currentTable);
}
</script>

<style lang="scss" scoped>
.column-visibility-manager {
  padding: 16px;
  
  .section-container {
    margin-bottom: 16px;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }
    
    .column-list {
      border: 1px solid rgba(0, 0, 0, 0.12);
      border-radius: 4px;
      padding: 8px;
      min-height: 40px;
      
      .column-item {
        cursor: pointer;
        
        &:hover {
          background-color: rgba(0, 0, 0, 0.03);
        }
      }
    }
  }
  
  .actions {
    display: flex;
    justify-content: flex-end;
  }
}
</style>
```

## Drag/Drop Functionality

### Goals

1. **Intuitive Column Organization**: Allow users to organize columns via drag and drop
2. **Section Management**: Support moving columns between main and expanded sections
3. **Order Customization**: Allow reordering columns within sections
4. **Table-Specific Behavior**: Support different drag/drop behavior for different tables

### Drag/Drop Implementation

The drag/drop functionality will be implemented using the `vuedraggable` library, which is a Vue wrapper for SortableJS.

#### Base Draggable Section Component

```vue
<!-- src/components/filters/DraggableSection.vue -->
<template>
  <div class="draggable-section">
    <div class="section-header">
      <div class="text-subtitle2">{{ title }}</div>
      <slot name="header-actions"></slot>
    </div>
    
    <draggable
      v-model="modelValue"
      v-bind="dragOptions"
      :group="group"
      item-key="id"
      @end="onDragEnd"
      class="draggable-container"
      :data-section="sectionId"
    >
      <template #item="{element}">
        <slot name="item" :element="element"></slot>
      </template>
      
      <template #header>
        <slot name="header"></slot>
      </template>
      
      <template #footer>
        <slot name="footer"></slot>
      </template>
    </draggable>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import draggable from 'vuedraggable';

// Props
const props = defineProps({
  modelValue: {
    type: Array,
    required: true
  },
  title: {
    type: String,
    required: true
  },
  sectionId: {
    type: String,
    required: true
  },
  group: {
    type: [String, Object],
    default: 'columns'
  },
  dragOptions: {
    type: Object,
    default: () => ({
      animation: 150,
      handle: '.drag-handle'
    })
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'drag-end']);

// Methods
function onDragEnd(evt) {
  emit('drag-end', {
    from: evt.from.getAttribute('data-section'),
    to: evt.to.getAttribute('data-section'),
    oldIndex: evt.oldIndex,
    newIndex: evt.newIndex,
    item: evt.item
  });
}
</script>

<style lang="scss" scoped>
.draggable-section {
  margin-bottom: 16px;
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }
  
  .draggable-container {
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-radius: 4px;
    padding: 8px;
    min-height: 40px;
    background-color: white;
  }
}
</style>
```

#### Integration with FilterPanel

The FilterPanel component will be updated to use the draggable sections:

```vue
<template>
  <div class="filter-panel">
    <!-- Saved Filter Selector -->
    <saved-filter-selector
      :filters="filterStore.filtersByTable"
      :current-filter-id="filterStore.currentFilterId"
      :has-unsaved-changes="filterStore.hasUnsavedChanges"
      @select="loadFilter"
      @save="showSaveFilterDialog"
      @delete="deleteFilter"
      @clear="clearAllFilters"
    />
    
    <!-- Main Filters Section -->
    <draggable-section
      v-model="mainFilters"
      title="Main Filters"
      section-id="main"
      @drag-end="onDragEnd"
    >
      <template #item="{element}">
        <filter-control
          :column="element"
          @update="(value) => updateColumnFilter(element.id, value)"
          @clear="() => clearColumnFilter(element.id)"
          @toggle-visibility="(visible) => toggleColumnVisibility(element.id, visible)"
        />
      </template>
      
      <template #header-actions>
        <q-btn
          flat
          round
          dense
          icon="clear_all"
          color="grey-7"
          @click="clearMainFilters"
        >
          <q-tooltip>Clear all main filters</q-tooltip>
        </q-btn>
      </template>
    </draggable-section>
    
    <!-- Expanded Filters Section -->
    <draggable-section
      v-model="expandedFilters"
      title="Expanded Filters"
      section-id="expanded"
      :group="{ name: 'columns', pull: true, put: true }"
      @drag-end="onDragEnd"
    >
      <template #item="{element}">
        <filter-control
          :column="element"
          @update="(value) => updateColumnFilter(element.id, value)"
          @clear="() => clearColumnFilter(element.id)"
          @toggle-visibility="(visible) => toggleColumnVisibility(element.id, visible)"
        />
      </template>
      
      <template #header-actions>
        <q-btn
          flat
          round
          dense
          icon="clear_all"
          color="grey-7"
          @click="clearExpandedFilters"
        >
          <q-tooltip>Clear all expanded filters</q-tooltip>
        </q-btn>
      </template>
    </draggable-section>
    
    <!-- Specialized Filters Section -->
    <div v-for="filterType in filterStore.specializedFilterTypes" :key="filterType.id">
      <component
        :is="filterType.component"
        :filter-type="filterType"
        :filter-value="filterStore.specializedFilters[filterType.id]"
        @update="updateSpecializedFilter(filterType.id, $event)"
        @clear="clearSpecializedFilter(filterType.id)"
      />
    </div>
    
    <!-- Filter Actions -->
    <filter-action-bar
      :has-unsaved-changes="filterStore.hasUnsavedChanges"
      :has-active-filters="hasActiveFilters"
      @save="showSaveFilterDialog"
      @clear="clearAllFilters"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useFilterStore } from '@/stores/filterStore';
import SavedFilterSelector from './SavedFilterSelector.vue';
import FilterControl from './FilterControl.vue';
import FilterActionBar from './FilterActionBar.vue';
import DraggableSection from './DraggableSection.vue';
import { filterEventBus } from '@/lib/filters/FilterEventBus';

// Store
const filterStore = useFilterStore();

// Computed
const mainFilters = computed({
  get: () => {
    return filterStore.columns
      .filter(col => col.section === 'main')
      .sort((a, b) => a.order - b.order);
  },
  set: (value) => {
    // Update order based on new array order
    value.forEach((col, index) => {
      filterStore.updateColumnOrder(col.id, index);
    });
  }
});

const expandedFilters = computed({
  get: () => {
    return filterStore.columns
      .filter(col => col.section === 'expanded')
      .sort((a, b) => a.order - b.order);
  },
  set: (value) => {
    // Update order based on new array order
    value.forEach((col, index) => {
      filterStore.updateColumnOrder(col.id, index);
    });
  }
});

const hasActiveFilters = computed(() => {
  return Object.keys(filterStore.activeFilters).length > 0 || 
         Object.keys(filterStore.specializedFilters).length > 0;
});

// Methods
function loadFilter(filterId) {
  filterStore.loadFilter(filterId);
  filterEventBus.emit(FilterEventBus.EVENTS.FILTER_LOADED, {
    filterId,
    tableName: filterStore.currentTable
  });
}

function showSaveFilterDialog() {
  // Show save filter dialog
}

function deleteFilter(filterId) {
  filterStore.deleteFilter(filterId);
  filterEventBus.emit(FilterEventBus.EVENTS.FILTER_DELETED, {
    filterId,
    tableName: filterStore.currentTable
  });
}

function updateColumnFilter(columnId, value) {
  filterStore.setColumnFilter(columnId, value);
  filterEventBus.emit(FilterEventBus.EVENTS.FILTER_CHANGED, {
    filterId: columnId,
    value,
    tableName: filterStore.currentTable
  });
}

function clearColumnFilter(columnId) {
  filterStore.clearFilter(columnId);
  filterEventBus.emit(FilterEventBus.EVENTS.FILTER_CLEARED, {
    filterId: columnId,
    tableName: filterStore.currentTable
  });
}

function updateSpecializedFilter(filterId, value) {
  filterStore.setSpecializedFilter(filterId, value);
  filterEventBus.emit(FilterEventBus.EVENTS.SPECIALIZED_FILTER_CHANGED, {
    filterId,
    value,
    tableName: filterStore.currentTable
  });
}

function clearSpecializedFilter(filterId) {
  filterStore.clearSpecializedFilter(filterId);
  filterEventBus.emit(FilterEventBus.EVENTS.SPECIALIZED_FILTER_CLEARED, {
    filterId,
    tableName: filterStore.currentTable
  });
}

function clearAllFilters() {
  filterStore.clearAllFilters();
  filterEventBus.emit(FilterEventBus.EVENTS.FILTER_CLEARED, {
    tableName: filterStore.currentTable
  });
}

function clearMainFilters() {
  mainFilters.value.forEach(col => {
    if (col.filterValue) {
      filterStore.clearFilter(col.id);
    }
  });
}

function clearExpandedFilters() {
  expandedFilters.value.forEach(col => {
    if (col.filterValue) {
      filterStore.clearFilter(col.id);
    }
  });
}

function toggleColumnVisibility(columnId, visible) {
  filterStore.toggleColumnVisibility(columnId, visible);
}

function onDragEnd(evt) {
  const { from, to, item } = evt;
  
  // Get the column ID from the dragged item
  const columnId = item.getAttribute('data-column-id');
  
  // If moving between sections
  if (from !== to) {
    filterStore.updateColumnSection(columnId, to);
  }
}
</script>
```

### Table-Specific Drag/Drop Behavior

For tables with special requirements like Templates, we can create table-specific adapters:

```typescript
// src/lib/filters/adapters/TemplatesFilterAdapter.ts
import { FilterAdapter } from '@/lib/filters/FilterAdapter';

export class TemplatesFilterAdapter extends FilterAdapter {
  // Override the getDragDropConfig method to provide Templates-specific behavior
  getDragDropConfig() {
    return {
      // Main section can receive items from expanded.details but not from specialized sections
      main: {
        group: {
          name: 'columns',
          pull: true,
          put: (to, from) => {
            return from.el.getAttribute('data-section') === 'expanded.details';
          }
        }
      },
      
      // Expanded details section can receive items from main but not from specialized sections
      'expanded.details': {
        group: {
          name: 'columns',
          pull: true,
          put: (to, from) => {
            return from.el.getAttribute('data-section') === 'main';
          }
        }
      },
      
      // Specialized sections cannot participate in drag/drop
      'expanded.promptElements': {
        group: {
          name: 'promptElements',
          pull: false,
          put: false
        }
      },
      
      'expanded.relatedItems': {
        group: {
          name: 'relatedItems',
          pull: false,
          put: false
        }
      }
    };
  }
  
  // Override the getSectionLayout method to provide Templates-specific layout
  getSectionLayout() {
    return {
      main: {
        title: 'Main Columns',
        expanded: true
      },
      expanded: {
        title: 'Expanded Columns',
        expanded: false,
        tabs: [
          {
            id: 'details',
            label: 'Details',
            icon: 'info'
          },
          {
            id: 'promptElements',
            label: 'Prompt Elements',
            icon: 'category'
          },
          {
            id: 'relatedItems',
            label: 'Related Items',
            icon: 'link'
          }
        ]
      }
    };
  }
}
```

### Adapter Factory

To support different tables with different requirements, we'll create an adapter factory:

```typescript
// src/lib/filters/FilterAdapterFactory.ts
import { FilterAdapter } from '@/lib/filters/FilterAdapter';
import { TemplatesFilterAdapter } from '@/lib/filters/adapters/TemplatesFilterAdapter';
import { ProductsFilterAdapter } from '@/lib/filters/adapters/ProductsFilterAdapter';
import { DefaultFilterAdapter } from '@/lib/filters/adapters/DefaultFilterAdapter';

export class FilterAdapterFactory {
  static createAdapter(tableName: string): FilterAdapter {
    switch (tableName) {
      case 'templates':
        return new TemplatesFilterAdapter();
      case 'products':
        return new ProductsFilterAdapter();
      default:
        return new DefaultFilterAdapter();
    }
  }
}
```

## Implementation Plan

1. **Column Visibility Management**:
   - Extend FilterStore with column visibility state and actions
   - Create ColumnVisibilityManager component
   - Integrate with FilterPanel

2. **Drag/Drop Functionality**:
   - Create DraggableSection component
   - Update FilterPanel to use draggable sections
   - Implement drag/drop event handling

3. **Table-Specific Adapters**:
   - Create base FilterAdapter class
   - Implement table-specific adapters
   - Create adapter factory

4. **Integration Testing**:
   - Test column visibility management
   - Test drag/drop between sections
   - Test table-specific behavior

## Expected Outcome

After implementing column visibility and drag/drop functionality, the filter engine will:

1. Allow users to show/hide columns based on their preferences
2. Support responsive tables with main and expanded sections
3. Allow intuitive reorganization of columns via drag and drop
4. Support different behavior for different tables
5. Provide a flexible and customizable table experience

These features will make the filter engine more versatile and user-friendly, especially for complex tables like Templates.
