/**
 * Helper functions for testing Vue components
 */

import { createApp, defineComponent, h } from 'vue';
import { createPinia } from 'pinia';
import { Quasar } from 'quasar';

/**
 * Creates a mock Quasar instance for testing
 */
export function createMockQuasar() {
  return {
    dark: {
      isActive: false,
      set: (val: boolean) => {
        mockQuasar.dark.isActive = val;
      }
    },
    screen: {
      lt: {
        sm: false,
        md: false,
        lg: false,
        xl: false
      },
      gt: {
        xs: true,
        sm: true,
        md: true,
        lg: false,
        xl: false
      },
      setSizes: () => {}
    },
    notify: () => {}
  };
}

// Create a mock Quasar instance
const mockQuasar = createMockQuasar();

/**
 * Creates a test wrapper for Vue components
 */
export function createTestWrapper(component: any, options: any = {}) {
  // Create a test app
  const app = createApp({
    render() {
      return h(component, options.props || {});
    }
  });

  // Add Pinia
  const pinia = createPinia();
  app.use(pinia);

  // Add Quasar
  app.use(Quasar, {
    plugins: {}
  });

  // Add global properties
  app.config.globalProperties.$q = mockQuasar as any;

  // Add any provided plugins
  if (options.plugins) {
    options.plugins.forEach((plugin: any) => {
      app.use(plugin);
    });
  }

  // Add any provided components
  if (options.components) {
    Object.entries(options.components).forEach(([name, comp]) => {
      app.component(name, comp as any);
    });
  }

  // Create a div to mount the app
  const el = document.createElement('div');
  document.body.appendChild(el);

  // Mount the app
  const instance = app.mount(el);

  // Return the wrapper
  return {
    app,
    instance,
    el,
    unmount: () => {
      app.unmount();
      document.body.removeChild(el);
    }
  };
}
