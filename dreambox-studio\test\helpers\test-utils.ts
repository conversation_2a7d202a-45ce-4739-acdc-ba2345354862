/**
 * Common test utilities for Dreambox Studio tests
 */

import { setActive<PERSON><PERSON>, create<PERSON><PERSON> } from 'pinia';

/**
 * Creates a fresh Pinia instance for testing
 * This helps ensure tests are isolated from each other
 */
export function createFreshPinia() {
  const pinia = createPinia();
  setActivePinia(pinia);
  return pinia;
}

/**
 * Mock localStorage for testing
 */
export const mockLocalStorage = {
  store: {} as Record<string, string>,
  getItem: function(key: string) {
    return this.store[key] || null;
  },
  setItem: function(key: string, value: string) {
    this.store[key] = value;
  },
  removeItem: function(key: string) {
    delete this.store[key];
  },
  clear: function() {
    this.store = {};
  }
};

/**
 * Setup mock localStorage for testing
 */
export function setupMockLocalStorage() {
  // Save original localStorage
  const originalLocalStorage = globalThis.localStorage;
  
  // Replace with mock
  globalThis.localStorage = mockLocalStorage as any;
  
  // Return function to restore original
  return () => {
    globalThis.localStorage = originalLocalStorage;
  };
}

/**
 * Mock console methods to avoid cluttering test output
 */
export function mockConsole() {
  const originalConsoleLog = console.log;
  const originalConsoleWarn = console.warn;
  const originalConsoleError = console.error;
  
  console.log = () => {};
  console.warn = () => {};
  console.error = () => {};
  
  return () => {
    console.log = originalConsoleLog;
    console.warn = originalConsoleWarn;
    console.error = originalConsoleError;
  };
}
