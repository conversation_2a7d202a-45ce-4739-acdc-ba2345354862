import { Platform } from 'quasar';

/**
 * Platform detection utility
 * Provides methods to detect the current platform and adjust UI accordingly
 */
export const PlatformUtil = {
  /**
   * Check if running in Electron
   */
  isElectron(): boolean {
    return Platform.is.electron;
  },

  /**
   * Check if running in a mobile browser or as a PWA on mobile
   */
  isMobile(): boolean {
    return Platform.is.mobile || Platform.is.cordova || Platform.is.capacitor;
  },

  /**
   * Check if running as a PWA
   */
  isPwa(): boolean {
    // Check if running in standalone mode (PWA indicator)
    return window.matchMedia('(display-mode: standalone)').matches;
  },

  /**
   * Check if running in a desktop browser (not Electron)
   */
  isDesktopBrowser(): boolean {
    return Platform.is.desktop && !Platform.is.electron;
  },

  /**
   * Check if running on iOS
   */
  isIos(): boolean {
    return Platform.is.ios;
  },

  /**
   * Check if running on Android
   */
  isAndroid(): boolean {
    return Platform.is.android;
  },

  /**
   * Get the current platform name
   */
  getPlatformName(): string {
    if (this.isElectron()) return 'electron';
    if (this.isMobile()) {
      if (this.isIos()) return 'ios';
      if (this.isAndroid()) return 'android';
      return 'mobile';
    }
    if (this.isPwa()) return 'pwa';
    return 'browser';
  },

  /**
   * Get platform-specific CSS class
   */
  getPlatformClass(): string {
    return `platform-${this.getPlatformName()}`;
  },

  /**
   * Get appropriate padding based on platform
   */
  getBasePadding(): string {
    return this.isMobile() ? 'q-pa-sm' : 'q-pa-md';
  },

  /**
   * Get appropriate margin based on platform
   */
  getBaseMargin(): string {
    return this.isMobile() ? 'q-ma-sm' : 'q-ma-md';
  },

  /**
   * Get appropriate gutter for grid based on platform
   */
  getGridGutter(): string {
    return this.isMobile() ? 'q-gutter-sm' : 'q-gutter-md';
  },

  /**
   * Get appropriate column width for responsive grid
   */
  getColumnWidth(): { xs: number; sm: number; md: number; lg: number; xl: number } {
    if (this.isMobile()) {
      return { xs: 12, sm: 6, md: 6, lg: 4, xl: 3 };
    }
    return { xs: 12, sm: 6, md: 4, lg: 3, xl: 2 };
  }
};

export default PlatformUtil;
