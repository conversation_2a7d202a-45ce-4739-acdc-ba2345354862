Here are the changes needed to implement a searchable dropdown for voice commands:

1. Add these variables and functions to the script section:

```javascript
// Filtered commands for the dropdown
const filteredCommands = ref<string[]>([]);

// Filter commands based on user input
function filterCommands(val, update) {
  if (val === '') {
    update(() => {
      filteredCommands.value = availableCommands.value.map(cmd => cmd.name);
    });
    return;
  }

  update(() => {
    const needle = val.toLowerCase();
    filteredCommands.value = availableCommands.value
      .filter(cmd => {
        if (cmd.name.toLowerCase().includes(needle)) return true;
        if (normalizeCommandName(cmd.name).includes(needle)) return true;
        if (cmd.examples && cmd.examples.some(ex => ex.toLowerCase().includes(needle))) return true;
        return false;
      })
      .map(cmd => cmd.name);
  });
}

// Helper functions to get command details
function getCommandDescription(commandName) {
  const cmd = availableCommands.value.find(c => c.name === commandName);
  return cmd?.description || '';
}

function getCommandExample(commandName) {
  const cmd = availableCommands.value.find(c => c.name === commandName);
  return cmd?.examples?.[0] || '';
}
```

2. Update the onMounted function to initialize filteredCommands:

```javascript
onMounted(() => {
  setTimeout(() => {
    refreshAvailableCommands();
    filteredCommands.value = availableCommands.value.map(cmd => cmd.name);
  }, 500);
  
  // Rest of your existing code...
});
```

3. Replace the q-input with q-select in the template:

```html
<q-select
  v-model="commandText"
  :options="filteredCommands"
  use-input
  hide-selected
  fill-input
  input-debounce="0"
  label="Enter or select a command"
  outlined
  @filter="filterCommands"
  @keyup.enter="executeCommand"
  class="q-mb-sm"
>
  <template v-slot:no-option>
    <q-item>
      <q-item-section class="text-grey">
        No results
      </q-item-section>
    </q-item>
  </template>
  
  <template v-slot:option="scope">
    <q-item v-bind="scope.itemProps">
      <q-item-section>
        <q-item-label>{{ scope.opt }}</q-item-label>
        <q-item-label caption v-if="getCommandDescription(scope.opt)">
          {{ getCommandDescription(scope.opt) }}
        </q-item-label>
        <q-item-label caption v-if="getCommandExample(scope.opt)">
          Example: "{{ getCommandExample(scope.opt) }}"
        </q-item-label>
      </q-item-section>
    </q-item>
  </template>
  
  <template v-slot:append>
    <q-btn
      round
      dense
      flat
      :icon="isListening ? 'mic_off' : 'mic'"
      :color="isListening ? 'negative' : 'primary'"
      @click="simulateVoiceInput"
    />
  </template>
</q-select>
```

4. Remove the expansion panel for available commands since we don't need it anymore.
