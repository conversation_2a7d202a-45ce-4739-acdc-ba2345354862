-- Create a function to test element value filtering
CREATE OR REPLACE FUNCTION test_element_value_filtering(
    p_element_id BIGINT
)
RETURNS TABLE (
    template_id BIGINT,
    template_name VARCHAR,
    element_count BIGINT,
    elements JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.template_id,
        t.template_name::VARCHAR,
        (
            SELECT COUNT(*)
            FROM jsonb_array_elements(t.elements) AS elem
            WHERE (elem->>'id')::BIGINT = p_element_id
        ) AS element_count,
        t.elements
    FROM 
        template_elements_optimized_view t
    WHERE EXISTS (
        SELECT 1
        FROM jsonb_array_elements(t.elements) AS elem
        WHERE (elem->>'id')::BIGINT = p_element_id
    )
    ORDER BY t.template_id;
END;
$$ LANGUAGE plpgsql;

-- Test the function with element ID 110 (High Angle)
SELECT * FROM test_element_value_filtering(110);
