<template>
  <div class="inline-dropdown-edit-container">
    <!-- Display mode -->
    <div
      v-if="!isEditing"
      class="inline-display q-pa-xs"
      :class="{ 'cursor-pointer': !disabled, 'inline-display-disabled': disabled }"
      @click="startEditing"
    >
      <slot name="display" :value="modelValue" :options="options" :getOptionLabel="getOptionLabel">
        <div class="inline-display-content">
          <span v-if="hasSelectedValue">{{ displayValue }}</span>
          <span v-else class="text-grey">{{ placeholder }}</span>
          <q-icon v-if="!disabled" name="edit" size="xs" class="edit-icon q-ml-xs" />
        </div>
      </slot>
    </div>

    <!-- Edit mode -->
    <div v-else class="inline-edit">
      <slot name="edit" :value="editValue" :options="options" :update="updateEditValue" :save="save" :cancel="cancel">
        <q-select
          ref="selectRef"
          v-model="editValue"
          :options="options"
          :option-label="optionLabel"
          :option-value="optionValue"
          :multiple="multiple"
          :use-chips="multiple"
          :emit-value="emitValue"
          :map-options="mapOptions"
          :placeholder="placeholder"
          :use-input="useInput"
          :input-debounce="inputDebounce"
          :filter="filter"
          dense
          outlined
          popup-content-class="custom-scrollbar"
          @keyup.esc="cancel"
          @blur="onBlur"
        />
      </slot>

      <!-- Action buttons -->
      <div class="inline-edit-actions q-mt-xs" v-if="showButtons">
        <q-btn
          flat
          dense
          color="primary"
          icon="check"
          size="sm"
          @click="save"
        />
        <q-btn
          flat
          dense
          color="negative"
          icon="close"
          size="sm"
          @click="cancel"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import type { PropType } from 'vue';

const props = defineProps({
  modelValue: {
    type: [String, Number, Boolean, Array, Object],
    required: true
  },
  options: {
    type: Array,
    required: true
  },
  optionLabel: {
    type: [String, Function] as PropType<string | ((option: Record<string, unknown>) => string)>,
    default: 'label'
  },
  optionValue: {
    type: [String, Function] as PropType<string | ((option: Record<string, unknown>) => unknown)>,
    default: 'value'
  },
  multiple: {
    type: Boolean,
    default: false
  },
  emitValue: {
    type: Boolean,
    default: false
  },
  mapOptions: {
    type: Boolean,
    default: false
  },
  placeholder: {
    type: String,
    default: 'Select an option'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  showButtons: {
    type: Boolean,
    default: true
  },
  blurOnSave: {
    type: Boolean,
    default: true
  },
  saveOnBlur: {
    type: Boolean,
    default: false
  },
  useInput: {
    type: Boolean,
    default: false
  },
  inputDebounce: {
    type: Number,
    default: 300
  },
  filter: {
    type: Function,
    default: undefined
  }
});

const emit = defineEmits<{
  (e: 'update:modelValue', value: unknown): void;
  (e: 'save', value: unknown): void;
  (e: 'cancel'): void;
  (e: 'edit-start'): void;
}>();

const isEditing = ref(false);
const editValue = ref<unknown>(null);
const selectRef = ref<HTMLElement | null>(null);

// Determine if there's a selected value
const hasSelectedValue = computed(() => {
  if (props.multiple && Array.isArray(props.modelValue)) {
    return props.modelValue.length > 0;
  }
  return props.modelValue !== null && props.modelValue !== undefined && props.modelValue !== '';
});

// Get the display value for the current selection
const displayValue = computed(() => {
  if (!hasSelectedValue.value) return '';

  if (props.multiple && Array.isArray(props.modelValue)) {
    return props.modelValue.map(val => getOptionLabel(val)).join(', ');
  }

  return getOptionLabel(props.modelValue);
});

// Helper function to get the label for an option
function getOptionLabel(value: unknown): string {
  if (value === null || value === undefined) return '';

  // If emitValue is true, find the option with matching value
  if (props.emitValue) {
    // Use the options array directly
    const option = props.options.find(opt => {
      // Safe type check before accessing properties
      if (typeof opt !== 'object' || opt === null) return false;

      const typedOpt = opt as Record<string, unknown>;
      const optionValueKey = typeof props.optionValue === 'string' ? props.optionValue : 'value';
      const optValue = typeof props.optionValue === 'function'
        ? props.optionValue(typedOpt)
        : typedOpt[optionValueKey];
      return optValue === value;
    });

    if (!option) {
      // Safely convert value to string
      return typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean'
        ? String(value)
        : '';
    }

    // Type assertion for the found option
    const typedOption = option as Record<string, unknown>;

    const optionLabelKey = typeof props.optionLabel === 'string' ? props.optionLabel : 'label';
    return typeof props.optionLabel === 'function'
      ? props.optionLabel(typedOption)
      : typeof typedOption[optionLabelKey] === 'string' ||
        typeof typedOption[optionLabelKey] === 'number' ||
        typeof typedOption[optionLabelKey] === 'boolean'
        ? String(typedOption[optionLabelKey])
        : '';
  }

  // If the value is the full option object
  if (typeof value !== 'object' || value === null) {
    // Safely convert primitive values to string
    return typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean'
      ? String(value)
      : '';
  }

  // Type assertion for the value as an object
  const typedValue = value as Record<string, unknown>;

  const optionLabelKey = typeof props.optionLabel === 'string' ? props.optionLabel : 'label';
  return typeof props.optionLabel === 'function'
    ? props.optionLabel(typedValue)
    : typeof typedValue[optionLabelKey] === 'string' ||
      typeof typedValue[optionLabelKey] === 'number' ||
      typeof typedValue[optionLabelKey] === 'boolean'
      ? String(typedValue[optionLabelKey])
      : '';
}

// Start editing mode
function startEditing() {
  if (props.disabled) return;

  isEditing.value = true;
  editValue.value = props.modelValue;
  emit('edit-start');

  // Focus the select after the DOM updates
  void nextTick(() => {
    if (selectRef.value) {
      selectRef.value.focus();
    }
  });
}

// Update the edit value (used in slot)
function updateEditValue(value: unknown) {
  editValue.value = value;
}

// Save the changes
function save() {
  emit('update:modelValue', editValue.value);
  emit('save', editValue.value);
  isEditing.value = false;

  if (props.blurOnSave && selectRef.value) {
    selectRef.value.blur();
  }
}

// Cancel editing
function cancel() {
  isEditing.value = false;
  emit('cancel');
}

// Handle blur event
function onBlur() {
  if (props.saveOnBlur) {
    save();
  }
}

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  if (!isEditing.value) {
    editValue.value = newValue;
  }
});
</script>

<style lang="scss" scoped>
.inline-dropdown-edit-container {
  position: relative;
}

.inline-display {
  min-height: 24px;
  border-radius: 4px;
  transition: background-color 0.3s;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);

    .edit-icon {
      opacity: 1;
    }
  }
}

.inline-display-content {
  display: flex;
  align-items: center;
}

.inline-display-disabled {
  cursor: default !important;

  &:hover {
    background-color: transparent;
  }
}

.edit-icon {
  opacity: 0;
  transition: opacity 0.3s;
}

.inline-edit {
  position: relative;
}

.inline-edit-actions {
  display: flex;
  justify-content: flex-end;
  gap: 4px;
}
</style>
