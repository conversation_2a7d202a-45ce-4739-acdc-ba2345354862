# Container Component Implementations

This document provides implementation details for the container components that will host dynamically loaded components in the top bar and bottom bar.

## File: `components/layout/TopBarContainer.vue`

```vue
<template>
  <div class="top-bar-container row items-center no-wrap">
    <!-- Slot for components to be placed on the left side -->
    <div class="top-bar-left row items-center no-wrap">
      <slot name="left"></slot>
    </div>
    
    <!-- Spacer -->
    <q-space />
    
    <!-- Slot for components to be placed on the right side -->
    <div class="top-bar-right row items-center no-wrap">
      <slot name="right"></slot>
      
      <!-- Default slot for components with no specific placement -->
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, provide, onMounted } from 'vue';

// Props for configuration
const props = defineProps<{
  // Configuration options can be added here
  spacing?: number;
  alignRight?: boolean;
}>();

// Component state
const containerWidth = ref(0);

// Provide context to child components
provide('topBarContext', {
  containerWidth,
  spacing: props.spacing || 8,
  alignRight: props.alignRight || true
});

// Measure container width for responsive adjustments
onMounted(() => {
  const updateWidth = () => {
    const element = document.querySelector('.top-bar-container');
    if (element) {
      containerWidth.value = element.clientWidth;
    }
  };
  
  updateWidth();
  window.addEventListener('resize', updateWidth);
  
  // Clean up
  onUnmounted(() => {
    window.removeEventListener('resize', updateWidth);
  });
});
</script>

<style lang="scss" scoped>
.top-bar-container {
  width: 100%;
  height: 100%;
  padding: 0 8px;
  
  .top-bar-left, .top-bar-right {
    height: 100%;
  }
  
  // Ensure proper spacing between components
  :deep(.q-btn) {
    margin: 0 4px;
  }
  
  // Responsive adjustments
  @media (max-width: 599px) {
    .top-bar-left {
      max-width: 60%;
    }
  }
}
</style>
```

## File: `components/layout/BottomBarContainer.vue`

```vue
<template>
  <div class="bottom-bar-container row items-center justify-between">
    <!-- Left section -->
    <div class="bottom-bar-left row items-center">
      <slot name="left"></slot>
    </div>
    
    <!-- Center section -->
    <div class="bottom-bar-center row items-center justify-center">
      <slot name="center"></slot>
    </div>
    
    <!-- Right section -->
    <div class="bottom-bar-right row items-center justify-end">
      <slot name="right"></slot>
    </div>
    
    <!-- Default slot for evenly distributed items -->
    <template v-if="$slots.default">
      <div class="bottom-bar-default row items-center justify-around full-width">
        <slot></slot>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, provide, computed } from 'vue';
import { useQuasar } from 'quasar';

// Props for configuration
const props = defineProps<{
  // Configuration options
  expanded?: boolean;
  distribution?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';
  buttonSize?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
}>();

// Emit events
const emit = defineEmits<{
  (e: 'expand'): void;
  (e: 'collapse'): void;
}>();

// Component state
const $q = useQuasar();
const isMobile = computed(() => $q.screen.lt.sm);

// Provide context to child components
provide('bottomBarContext', {
  isMobile,
  expanded: props.expanded || false,
  distribution: props.distribution || 'around',
  buttonSize: props.buttonSize || (isMobile.value ? 'sm' : 'md')
});

// Methods for expansion control
function expand() {
  emit('expand');
}

function collapse() {
  emit('collapse');
}

// Expose methods to parent
defineExpose({
  expand,
  collapse
});
</script>

<style lang="scss" scoped>
.bottom-bar-container {
  width: 100%;
  height: 100%;
  padding: 0 12px;
  
  .bottom-bar-left, .bottom-bar-center, .bottom-bar-right {
    flex: 1 1 0;
  }
  
  .bottom-bar-default {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    padding: 0 12px;
  }
  
  // Ensure proper spacing between buttons
  :deep(.q-btn) {
    margin: 0 4px;
  }
  
  // Responsive adjustments
  @media (max-width: 599px) {
    padding: 0 8px;
    
    :deep(.q-btn) {
      margin: 0 2px;
    }
  }
}
</style>
```

## Usage in MainLayout.vue

```vue
<template>
  <q-layout view="hHh lpR fFf">
    <!-- Top Bar -->
    <q-header elevated class="bg-primary text-white">
      <q-toolbar>
        <!-- Standard UI elements -->
        <q-btn
          flat
          round
          dense
          icon="menu"
          aria-label="Menu"
          @click="leftDrawerOpen = !leftDrawerOpen"
        />
        
        <q-toolbar-title>
          {{ $t('app.name') }}
        </q-toolbar-title>
        
        <!-- Container for dynamic components -->
        <top-bar-container>
          <template v-for="(comp, index) in contextStore.topBarComponents" :key="index">
            <dynamic-component
              :name="comp.component"
              v-bind="comp.props"
            />
          </template>
        </top-bar-container>
      </q-toolbar>
    </q-header>
    
    <!-- Bottom Bar -->
    <q-footer
      v-if="contextStore.bottomBarComponents.length > 0"
      elevated
      class="bg-primary text-white"
      :class="{ 'footer-expanded': footerExpanded }"
    >
      <q-toolbar>
        <!-- Container for dynamic components -->
        <bottom-bar-container
          :expanded="footerExpanded"
          @expand="footerExpanded = true"
          @collapse="footerExpanded = false"
        >
          <template v-for="(comp, index) in contextStore.bottomBarComponents" :key="index">
            <dynamic-component
              :name="comp.component"
              v-bind="comp.props"
            />
          </template>
        </bottom-bar-container>
        
        <!-- Footer expand toggle -->
        <div class="absolute-center footer-icon-wrapper" @click="toggleFooterExpand">
          <q-icon
            :name="footerExpanded ? 'expand_more' : 'expand_less'"
            color="white"
            size="24px"
          >
            <q-tooltip>{{ footerExpanded ? 'Collapse' : 'Expand' }}</q-tooltip>
          </q-icon>
        </div>
      </q-toolbar>
      
      <!-- Expanded footer content -->
      <div v-if="footerExpanded" class="footer-expanded-content q-px-md q-pt-md q-pb-md">
        <!-- Additional footer content -->
      </div>
    </q-footer>
    
    <!-- Rest of the layout... -->
  </q-layout>
</template>
```

## Component Registration

```typescript
// In boot/componentRegistry.ts
import TopBarContainer from 'src/components/layout/TopBarContainer.vue';
import BottomBarContainer from 'src/components/layout/BottomBarContainer.vue';

// Register container components
registerComponent('TopBarContainer', TopBarContainer);
registerComponent('BottomBarContainer', BottomBarContainer);
```

## Key Features

### TopBarContainer
1. **Flexible Layout**: Supports left and right alignment of components
2. **Responsive Design**: Adjusts based on screen size
3. **Context Provision**: Provides layout context to child components
4. **Slot-Based**: Uses named slots for precise positioning

### BottomBarContainer
1. **Multiple Sections**: Supports left, center, and right sections
2. **Expansion Control**: Handles expanded/collapsed states
3. **Responsive Sizing**: Adjusts button sizes based on device
4. **Even Distribution**: Can evenly distribute components across the bar

These container components ensure that dynamically loaded components are properly positioned and styled in the top and bottom bars, maintaining a consistent UI across different contexts and devices.
