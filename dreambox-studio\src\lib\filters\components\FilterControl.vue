<template>
  <div class="filter-control q-mb-sm">
    <div class="filter-control-header q-mb-xs">
      <div class="row items-center no-wrap full-width">
        <q-icon name="drag_indicator" class="drag-handle q-mr-sm cursor-move" />
        <div class="filter-control-label">{{ translateColumnName(column.name) }}</div>
        <q-space />
        <q-btn
          flat
          dense
          round
          size="md"
          :icon="column.visible ? 'visibility' : 'visibility_off'"
          :color="column.visible ? 'primary' : 'grey'"
          @click="toggleColumnVisibility"
          class="visibility-toggle"
        />
      </div>
    </div>

    <!-- Text filter -->
    <q-input
      v-if="column.filterType === 'text'"
      v-model="filterValue"
      dense
      outlined
      :placeholder="t('filters.search')"
      clearable
      @update:model-value="updateFilter"
    >
      <template v-slot:prepend>
        <q-icon name="search" />
      </template>
    </q-input>

    <!-- Number filter -->
    <div v-else-if="column.filterType === 'number'" class="row q-col-gutter-sm">
      <q-input
        v-model.number="numberRange.min"
        type="number"
        dense
        outlined
        :placeholder="t('filters.min')"
        class="col-6"
        clearable
        @update:model-value="updateNumberFilter"
      />
      <q-input
        v-model.number="numberRange.max"
        type="number"
        dense
        outlined
        :placeholder="t('filters.max')"
        class="col-6"
        clearable
        @update:model-value="updateNumberFilter"
      />
    </div>

    <!-- Date filter - supports both single date and date range with OK button -->
    <div v-else-if="column.filterType === 'date'">
      <q-input
        v-model="dateRangeDisplay"
        dense
        outlined
        :placeholder="t('filters.selectDateOrRange')"
        clearable
        @update:model-value="(value: string | number | null) => value === null ? clearDateRange() : null"
        readonly
      >
        <template v-slot:append>
          <q-icon name="event" class="cursor-pointer">
            <q-popup-proxy cover transition-show="scale" transition-hide="scale" ref="datePopupRef">
              <q-date
                v-model="dateRangeModel"
                range
                mask="YYYY-MM-DD"
                today-btn
              >
                <div class="row items-center justify-end q-gutter-sm q-pa-sm">
                  <q-btn :label="t('common.cancel')" color="primary" flat v-close-popup />
                  <q-btn :label="t('common.ok')" color="primary" @click="confirmDateSelection" v-close-popup />
                </div>
              </q-date>
            </q-popup-proxy>
          </q-icon>
        </template>
      </q-input>
    </div>

    <!-- Select filter -->
    <q-select
      v-else-if="column.filterType === 'select'"
      v-model="filterValue"
      :options="column.filterOptions || []"
      dense
      outlined
      emit-value
      map-options
      clearable
      @update:model-value="updateFilter"
    />

    <!-- Multi-select filter -->
    <q-select
      v-else-if="column.filterType === 'multi-select'"
      v-model="filterValue"
      :options="column.filterOptions || []"
      dense
      outlined
      multiple
      emit-value
      map-options
      clearable
      @update:model-value="updateFilter"
    >
      <template v-slot:selected-item="scope">
        <q-chip
          removable
          dense
          @remove="removeOption(scope.index)"
          :label="getOptionLabel(scope.opt)"
        />
      </template>
    </q-select>

    <!-- Boolean filter -->
    <q-option-group
      v-else-if="column.filterType === 'boolean'"
      v-model="filterValue"
      :options="booleanOptions"
      type="radio"
      inline
      @update:model-value="updateFilter"
    />

    <!-- Default: text filter -->
    <q-input
      v-else
      v-model="filterValue"
      dense
      outlined
      :placeholder="t('filters.search')"
      clearable
      @update:model-value="updateFilter"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, computed } from 'vue';
import { useFilterStore } from '../../../stores/filterStore';
import type { ColumnDefinition } from '../../../stores/filterStore';
import { useI18n } from 'vue-i18n';

// Props
const props = defineProps<{
  column: ColumnDefinition;
  modelValue?: unknown;
}>();

// Emits
const emit = defineEmits<{
  (e: 'update:modelValue', value: unknown): void;
  (e: 'clear'): void;
  (e: 'toggle-visibility', columnId: string, visible: boolean): void;
}>();

// Store
const filterStore = useFilterStore();

// Initialize i18n
const { t } = useI18n();

// State
// Use a more compatible type for Quasar's v-model
const filterValue = ref<string | number | null>(null);
const numberRange = ref({ min: null as number | null, max: null as number | null });
const dateRange = ref({ from: null as string | null, to: null as string | null });
const dateRangeModel = ref(null as string | { from: string, to: string } | null);
const dateRangeDisplay = ref('');
const datePopupRef = ref(null);

// Boolean options
const booleanOptions = computed(() => [
  { label: t('common.yes'), value: true },
  { label: t('common.no'), value: false },
  { label: t('common.all'), value: null }
]);

// Removed hasValue computed property as it's no longer used

// Watch for external changes
watch(() => props.modelValue, (newValue) => {
  if (props.column.filterType === 'number') {
    if (newValue === null || newValue === undefined) {
      numberRange.value = { min: null, max: null };
    } else {
      const rangeValue = newValue as { min?: number | null, max?: number | null };
      numberRange.value = {
        min: rangeValue.min !== undefined ? rangeValue.min : null,
        max: rangeValue.max !== undefined ? rangeValue.max : null
      };
    }
  } else if (props.column.filterType === 'date') {
    if (newValue === null || newValue === undefined) {
      dateRange.value = { from: null, to: null };
      dateRangeModel.value = null;
      dateRangeDisplay.value = '';
    } else {
      const dateValue = newValue as { from?: string | null, to?: string | null };
      dateRange.value = {
        from: dateValue.from !== undefined ? dateValue.from : null,
        to: dateValue.to !== undefined ? dateValue.to : null
      };

      // Update the date range model and display
      if (dateRange.value.from && dateRange.value.to) {
        if (dateRange.value.from === dateRange.value.to) {
          // Single date
          dateRangeModel.value = dateRange.value.from;
          dateRangeDisplay.value = dateRange.value.from;
        } else {
          // Date range
          dateRangeModel.value = {
            from: dateRange.value.from,
            to: dateRange.value.to
          };
          dateRangeDisplay.value = `${dateRange.value.from} to ${dateRange.value.to}`;
        }
      } else {
        dateRangeModel.value = null;
        dateRangeDisplay.value = '';
      }
    }
  } else {
    // Cast the value to a compatible type for v-model
    filterValue.value = newValue as string | number | null;
  }
}, { immediate: true });

// Methods
function updateFilter(value: unknown) {
  emit('update:modelValue', value);
}

function updateNumberFilter() {
  // Only emit if at least one value is set
  if (numberRange.value.min !== null || numberRange.value.max !== null) {
    emit('update:modelValue', {
      min: numberRange.value.min,
      max: numberRange.value.max
    });
  } else {
    emit('update:modelValue', null);
  }
}

// These functions have been replaced by confirmDateSelection

function confirmDateSelection() {
  if (dateRangeModel.value) {
    if (typeof dateRangeModel.value === 'string') {
      // Single date selection
      const selectedDate = dateRangeModel.value;
      dateRange.value = {
        from: selectedDate,
        to: selectedDate // Same date for both from and to
      };

      // Update the display value for a single date
      dateRangeDisplay.value = selectedDate;

      // Emit the update
      emit('update:modelValue', {
        from: selectedDate,
        to: selectedDate
      });
    } else {
      // Date range selection
      dateRange.value = {
        from: dateRangeModel.value.from,
        to: dateRangeModel.value.to
      };

      // Update the display value for a range
      dateRangeDisplay.value = `${dateRangeModel.value.from} to ${dateRangeModel.value.to}`;

      // Emit the update
      emit('update:modelValue', {
        from: dateRangeModel.value.from,
        to: dateRangeModel.value.to
      });
    }
  } else {
    clearDateRange();
  }
}

// This function has been replaced by an inline handler in the template

function clearDateRange() {
  dateRange.value = { from: null, to: null };
  dateRangeModel.value = null;
  dateRangeDisplay.value = '';
  emit('update:modelValue', null);
}

// Removed clearFilter function as it's no longer used

function removeOption(index: number) {
  // Handle array values separately from the v-model
  // This is needed because Quasar's v-model doesn't support arrays directly
  const currentValue = props.modelValue as unknown[];
  if (Array.isArray(currentValue)) {
    const newValue = [...currentValue];
    newValue.splice(index, 1);
    const result = newValue.length > 0 ? newValue : null;
    // Update the model value through emit, not directly
    emit('update:modelValue', result);
  }
}

function getOptionLabel(option: unknown): string {
  if (typeof option === 'object' && option !== null) {
    const opt = option as { label?: string, value?: string | number };
    return opt.label || String(opt.value || '');
  }
  return String(option);
}

/**
 * Translate column name using the column name mapping
 */
function translateColumnName(name: string): string {
  // Map common column names to translation keys
  const columnMap: Record<string, string> = {
    'Name': 'columns.name',
    'Description': 'columns.description',
    'Status': 'columns.status',
    'Created Date': 'columns.createdDate',
    'Updated Date': 'columns.updatedDate',
    'Designer': 'columns.designer',
    'Type': 'columns.type',
    'Category': 'columns.category',
    'Value': 'columns.value',
    'Actions': 'columns.actions'
  };

  // If the column name is in our map, translate it, otherwise return the original name
  return columnMap[name] ? t(columnMap[name]) : name;
}

/**
 * Toggle column visibility
 */
function toggleColumnVisibility() {
  // Toggle the visibility
  const newVisibility = !props.column.visible;

  // Update the column visibility in the store
  filterStore.updateColumnVisibility(
    props.column.id,
    newVisibility,
    props.column.section
  );

  // Emit the event for parent components
  emit('toggle-visibility', props.column.id, newVisibility);
}

// Initialize
onMounted(() => {
  // Initialize from store if available
  const storeValue = filterStore.activeFilters[props.column.id];

  if (storeValue !== undefined) {
    if (props.column.filterType === 'number') {
      if (storeValue && typeof storeValue === 'object') {
        const rangeValue = storeValue as { min?: number | null, max?: number | null };
        numberRange.value = {
          min: rangeValue.min !== undefined ? rangeValue.min : null,
          max: rangeValue.max !== undefined ? rangeValue.max : null
        };
      }
    } else if (props.column.filterType === 'date') {
      if (storeValue && typeof storeValue === 'object') {
        const dateValue = storeValue as { from?: string | null, to?: string | null };
        dateRange.value = {
          from: dateValue.from !== undefined ? dateValue.from : null,
          to: dateValue.to !== undefined ? dateValue.to : null
        };

        // Update the date range model and display
        if (dateRange.value.from && dateRange.value.to) {
          if (dateRange.value.from === dateRange.value.to) {
            // Single date
            dateRangeModel.value = dateRange.value.from;
            dateRangeDisplay.value = dateRange.value.from;
          } else {
            // Date range
            dateRangeModel.value = {
              from: dateRange.value.from,
              to: dateRange.value.to
            };
            dateRangeDisplay.value = `${dateRange.value.from} to ${dateRange.value.to}`;
          }
        }
      }
    } else {
      // Convert boolean values to strings for compatibility with v-model
      if (typeof storeValue === 'boolean') {
        filterValue.value = storeValue ? 'true' : 'false';
      } else {
        filterValue.value = storeValue as string | number | null;
      }
    }
  }
});
</script>

<style scoped>
.filter-control {
  width: 100%;
}

.filter-control-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  min-height: 36px;
}

.filter-control-label {
  font-weight: 500;
  font-size: 0.9rem;
  display: flex;
  align-items: flex-end;
  height: 100%;
}

.drag-handle {
  cursor: move;
  opacity: 0.6;
  font-size: 1.2rem;
}

.drag-handle:hover {
  opacity: 1;
}

.visibility-toggle {
  opacity: 0.8;
  transition: all 0.2s;
  min-width: 36px;
  min-height: 36px;
  font-size: 1.2rem;
}

.visibility-toggle :deep(.q-icon) {
  font-size: 1.5rem; /* Increase icon size */
  width: 100%;
  height: 100%;
}

.visibility-toggle:hover {
  opacity: 1;
  transform: scale(1.05);
}

/* Make the icon more touch-friendly on mobile */
@media (max-width: 600px) {
  .visibility-toggle {
    min-width: 44px;
    min-height: 44px;
  }

  .visibility-toggle :deep(.q-icon) {
    font-size: 1.8rem; /* Even larger on mobile */
  }
}
</style>
