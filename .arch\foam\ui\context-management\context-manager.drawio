<mxfile host="65bd71144e">
    <diagram id="pbjdBqEG8q_JUIxUeovn" name="Page-1">
        <mxGraphModel dx="859" dy="779" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="3300" pageHeight="2339" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="2" value="" style="shape=table;startSize=0;container=1;collapsible=0;childLayout=tableLayout;" vertex="1" parent="1">
                    <mxGeometry x="300" y="130" width="760" height="523" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;strokeColor=inherit;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="2">
                    <mxGeometry width="760" height="75" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="Component" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="3">
                    <mxGeometry width="109" height="75" as="geometry">
                        <mxRectangle width="109" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="5" value="Area" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="3">
                    <mxGeometry x="109" width="108" height="75" as="geometry">
                        <mxRectangle width="108" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="6" value="Context" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="3">
                    <mxGeometry x="217" width="109" height="75" as="geometry">
                        <mxRectangle width="109" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="7" value="Role" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="3">
                    <mxGeometry x="326" width="108" height="75" as="geometry">
                        <mxRectangle width="108" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="8" value="Device" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="3">
                    <mxGeometry x="434" width="109" height="75" as="geometry">
                        <mxRectangle width="109" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="9" value="Properties" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="3">
                    <mxGeometry x="543" width="108" height="75" as="geometry">
                        <mxRectangle width="108" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="10" value="Default Active" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="3">
                    <mxGeometry x="651" width="109" height="75" as="geometry">
                        <mxRectangle width="109" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="43" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;strokeColor=inherit;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="2">
                    <mxGeometry y="75" width="760" height="74" as="geometry"/>
                </mxCell>
                <mxCell id="44" value="Navigation" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="43">
                    <mxGeometry width="109" height="74" as="geometry">
                        <mxRectangle width="109" height="74" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="45" value="Left Drawer" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="43">
                    <mxGeometry x="109" width="108" height="74" as="geometry">
                        <mxRectangle width="108" height="74" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="46" value="ALL" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="43">
                    <mxGeometry x="217" width="109" height="74" as="geometry">
                        <mxRectangle width="109" height="74" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="47" value="ALL" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="43">
                    <mxGeometry x="326" width="108" height="74" as="geometry">
                        <mxRectangle width="108" height="74" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="48" value="ALL" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="43">
                    <mxGeometry x="434" width="109" height="74" as="geometry">
                        <mxRectangle width="109" height="74" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="49" value="items (label, icon, route),&lt;div&gt;expanded&lt;/div&gt;" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="43">
                    <mxGeometry x="543" width="108" height="74" as="geometry">
                        <mxRectangle width="108" height="74" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="50" value="True" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="43">
                    <mxGeometry x="651" width="109" height="74" as="geometry">
                        <mxRectangle width="109" height="74" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="11" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;strokeColor=inherit;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="2">
                    <mxGeometry y="149" width="760" height="75" as="geometry"/>
                </mxCell>
                <mxCell id="12" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="11">
                    <mxGeometry width="109" height="75" as="geometry">
                        <mxRectangle width="109" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="13" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="11">
                    <mxGeometry x="109" width="108" height="75" as="geometry">
                        <mxRectangle width="108" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="14" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="11">
                    <mxGeometry x="217" width="109" height="75" as="geometry">
                        <mxRectangle width="109" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="15" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="11">
                    <mxGeometry x="326" width="108" height="75" as="geometry">
                        <mxRectangle width="108" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="16" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="11">
                    <mxGeometry x="434" width="109" height="75" as="geometry">
                        <mxRectangle width="109" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="17" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="11">
                    <mxGeometry x="543" width="108" height="75" as="geometry">
                        <mxRectangle width="108" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="18" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="11">
                    <mxGeometry x="651" width="109" height="75" as="geometry">
                        <mxRectangle width="109" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="19" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;strokeColor=inherit;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="2">
                    <mxGeometry y="224" width="760" height="75" as="geometry"/>
                </mxCell>
                <mxCell id="20" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="19">
                    <mxGeometry width="109" height="75" as="geometry">
                        <mxRectangle width="109" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="21" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="19">
                    <mxGeometry x="109" width="108" height="75" as="geometry">
                        <mxRectangle width="108" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="22" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="19">
                    <mxGeometry x="217" width="109" height="75" as="geometry">
                        <mxRectangle width="109" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="23" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="19">
                    <mxGeometry x="326" width="108" height="75" as="geometry">
                        <mxRectangle width="108" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="24" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="19">
                    <mxGeometry x="434" width="109" height="75" as="geometry">
                        <mxRectangle width="109" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="25" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="19">
                    <mxGeometry x="543" width="108" height="75" as="geometry">
                        <mxRectangle width="108" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="26" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="19">
                    <mxGeometry x="651" width="109" height="75" as="geometry">
                        <mxRectangle width="109" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="35" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;strokeColor=inherit;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="2">
                    <mxGeometry y="299" width="760" height="74" as="geometry"/>
                </mxCell>
                <mxCell id="36" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="35">
                    <mxGeometry width="109" height="74" as="geometry">
                        <mxRectangle width="109" height="74" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="37" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="35">
                    <mxGeometry x="109" width="108" height="74" as="geometry">
                        <mxRectangle width="108" height="74" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="38" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="35">
                    <mxGeometry x="217" width="109" height="74" as="geometry">
                        <mxRectangle width="109" height="74" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="39" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="35">
                    <mxGeometry x="326" width="108" height="74" as="geometry">
                        <mxRectangle width="108" height="74" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="40" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="35">
                    <mxGeometry x="434" width="109" height="74" as="geometry">
                        <mxRectangle width="109" height="74" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="41" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="35">
                    <mxGeometry x="543" width="108" height="74" as="geometry">
                        <mxRectangle width="108" height="74" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="42" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="35">
                    <mxGeometry x="651" width="109" height="74" as="geometry">
                        <mxRectangle width="109" height="74" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="58" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;strokeColor=inherit;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="2">
                    <mxGeometry y="373" width="760" height="75" as="geometry"/>
                </mxCell>
                <mxCell id="59" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="58">
                    <mxGeometry width="109" height="75" as="geometry">
                        <mxRectangle width="109" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="60" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="58">
                    <mxGeometry x="109" width="108" height="75" as="geometry">
                        <mxRectangle width="108" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="61" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="58">
                    <mxGeometry x="217" width="109" height="75" as="geometry">
                        <mxRectangle width="109" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="62" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="58">
                    <mxGeometry x="326" width="108" height="75" as="geometry">
                        <mxRectangle width="108" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="63" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="58">
                    <mxGeometry x="434" width="109" height="75" as="geometry">
                        <mxRectangle width="109" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="64" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="58">
                    <mxGeometry x="543" width="108" height="75" as="geometry">
                        <mxRectangle width="108" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="65" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="58">
                    <mxGeometry x="651" width="109" height="75" as="geometry">
                        <mxRectangle width="109" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="66" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;strokeColor=inherit;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="2">
                    <mxGeometry y="448" width="760" height="75" as="geometry"/>
                </mxCell>
                <mxCell id="67" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="66">
                    <mxGeometry width="109" height="75" as="geometry">
                        <mxRectangle width="109" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="68" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="66">
                    <mxGeometry x="109" width="108" height="75" as="geometry">
                        <mxRectangle width="108" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="69" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="66">
                    <mxGeometry x="217" width="109" height="75" as="geometry">
                        <mxRectangle width="109" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="70" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="66">
                    <mxGeometry x="326" width="108" height="75" as="geometry">
                        <mxRectangle width="108" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="71" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="66">
                    <mxGeometry x="434" width="109" height="75" as="geometry">
                        <mxRectangle width="109" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="72" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="66">
                    <mxGeometry x="543" width="108" height="75" as="geometry">
                        <mxRectangle width="108" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="73" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;" vertex="1" parent="66">
                    <mxGeometry x="651" width="109" height="75" as="geometry">
                        <mxRectangle width="109" height="75" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>