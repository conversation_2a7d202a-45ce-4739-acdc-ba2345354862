<template>
  <q-page class="template-browser-page">
    <div class="row">
      <div class="col-12">
        <div class="row">
          <div class="col-12">
            <q-card class="no-transition">
              <q-card-section>
                <div class="row items-center justify-between">
                  <div>
                    <div class="text-h6">{{ $t('templates.title') }}</div>
                    <div class="text-subtitle2">
                      {{ $t('templates.templatesFound', { count: pagination.total }) }}
                    </div>
                  </div>
                  <div>
                    <q-btn
                      color="primary"
                      :icon="$q.screen.lt.sm ? 'add' : undefined"
                      :label="$q.screen.gt.xs ? $t('templates.newTemplate') : ''"
                      @click="viewTemplate({ id: 'new' })"
                      class="new-template-btn"
                    />
                  </div>
                </div>
              </q-card-section>

              <q-card-section>
                <!-- Template Table -->
                <template-table
                  :templates="templates"
                  :loading="loading"
                  :pagination="pagination"
                  :sorting="sorting"
                  @request="onRequest"
                  @view="viewTemplate"
                  @selection-change="handleSelectionChange"
                />


              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { useQuasar } from 'quasar';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { SupabaseAdapter } from 'src/lib/filters/adapters/SupabaseAdapter';
import { useAuthStore } from 'src/stores/auth';
import { useContextStore } from 'src/stores/contextStore';
import { useFilterStore } from 'src/stores/filterStore';
import { useElementCacheStore } from 'src/stores/elementCache';
import type { ColumnDefinition } from 'src/stores/filterStore';
import TemplateTable from 'src/components/templates/TemplateTable.vue';
import { supabase } from 'src/boot/supabase';
import { notify } from 'src/boot/notifications';

// Stores
const authStore = useAuthStore();
const contextStore = useContextStore();
const filterStore = useFilterStore();
const elementCacheStore = useElementCacheStore();

// Router and Quasar
const router = useRouter();
const $q = useQuasar();

// Initialize i18n
useI18n();

// State
interface Template {
  id: number;
  name: string;
  description: string | null;
  status: string;
  created_at: string;
  updated_at: string;
  prompt_elements: Array<{
    id: number;
    name?: string;
    value?: string;
    type_name?: string;
    category?: string;
    [key: string]: unknown;
  }>;
  collections: Array<{
    id: number;
    name: string;
    [key: string]: unknown;
  }>;
  products: Array<{
    id: number;
    name: string;
    [key: string]: unknown;
  }>;
  elements?: Array<{
    id: number;
    name?: string;
    value?: string;
    type_name?: string;
    category?: string;
    [key: string]: unknown;
  }>;
  [key: string]: unknown;
}

const templates = ref<Template[]>([]);
const loading = ref(false);
const standardFilters = ref<Record<string, unknown>>({});
const specializedFilters = ref<Record<string, unknown>>({});
const selectedTemplates = ref<Template[]>([]);
const pagination = ref({
  limit: 10,
  offset: 0,
  total: 0
});
const sorting = ref({
  sort_by: 'updated_at',
  sort_desc: true
});

// Note: tablePagination is now handled by the EnhancedTemplateTable component

// Note: Table columns are now handled by the EnhancedTemplateTable component

// Methods
async function loadTemplates(forceRefresh = false) {
  console.log('TemplateBrowser: loadTemplates called with pagination:', {
    limit: pagination.value.limit,
    offset: pagination.value.offset,
    forceRefresh
  });

  // Check if user is authenticated
  if (!authStore.isAuthenticated) return;

  loading.value = true;

  try {
    // If force refresh is requested, clear the element cache
    if (forceRefresh) {
      // console.log('Force refresh requested, clearing element cache');
      elementCacheStore.clearCache();
    }

    // Prepare specialized filters for the API
    const preparedSpecializedFilters = { ...specializedFilters.value };

    // Handle prompt elements filter
    if (preparedSpecializedFilters.promptElements) {
      // Define a type for element objects
      interface ElementObject {
        id: number;
        [key: string]: unknown;
      }

      const promptElementsFilter = preparedSpecializedFilters.promptElements as {
        elements?: Array<number | ElementObject>,
        matchType?: 'all' | 'any'
      };

      if (promptElementsFilter.elements && promptElementsFilter.elements.length > 0) {
        // console.log('DEBUG - promptElements filter has elements:', promptElementsFilter.elements);

        // Make sure we're passing an array of numbers, not objects
        const elementIds = promptElementsFilter.elements.map(el => {
          if (typeof el === 'number') {
            return el;
          } else if (typeof el === 'object' && el !== null && 'id' in el) {
            return el.id;
          } else {
            return Number(el);
          }
        });

        // For backward compatibility with the old function
        preparedSpecializedFilters.prompt_elements_filter = JSON.stringify(elementIds);

        // For the new function with element value filtering
        preparedSpecializedFilters.prompt_element_values = elementIds;
        preparedSpecializedFilters.prompt_element_match_type = promptElementsFilter.matchType || 'any';

        // console.log('DEBUG - Prepared prompt element values:', {
        //   values: preparedSpecializedFilters.prompt_element_values,
        //   matchType: preparedSpecializedFilters.prompt_element_match_type
        // });
      }
    }

    // Handle related items filter
    if (preparedSpecializedFilters.relatedItems) {
      const relatedItemsFilter = preparedSpecializedFilters.relatedItems as {
        type?: 'collections' | 'products',
        items?: number[],
        matchType?: 'all' | 'any'
      };

      if (relatedItemsFilter.items && relatedItemsFilter.items.length > 0 && relatedItemsFilter.type) {
        // Convert to the format expected by the SQL function
        preparedSpecializedFilters.related_items_filter = {
          [relatedItemsFilter.type]: relatedItemsFilter.items
        };
      }
    }

    // Extract element type IDs for optimized loading
    let elementTypeIds: number[] | null = null;
    if (preparedSpecializedFilters.prompt_elements_filter) {
      try {
        elementTypeIds = JSON.parse(preparedSpecializedFilters.prompt_elements_filter as string);
        console.log('DEBUG - Extracted element type IDs for optimized loading:', elementTypeIds);
      } catch (err) {
        console.error('Error parsing element type IDs:', err);
      }
    }

    const result = await SupabaseAdapter.filterTemplates(
      standardFilters.value,
      preparedSpecializedFilters,
      pagination.value,
      sorting.value
    );

    // Log the raw data from the API
    // if (result.items.length > 0) {
    //   console.log('DEBUG - Raw template data from API:', result.items[0]);
    // }

    // Process the templates to ensure proper structure
    templates.value = result.items.map(item => {
      // Cast item to a record with string keys and unknown values
      const typedItem = item as Record<string, unknown>;

      // Ensure prompt_elements is an array
      const promptElements = (typedItem.elements as Array<unknown>) ||
                            (typedItem.prompt_elements as Array<unknown>) ||
                            [];

      // Ensure collections is an array
      const collections = (typedItem.collections as Array<unknown>) || [];

      // Ensure products is an array
      const products = (typedItem.products as Array<unknown>) || [];

      return {
        ...typedItem,
        prompt_elements: promptElements,
        collections: collections,
        products: products
      } as Template;
    });

    // Update pagination with total count
    pagination.value = {
      ...pagination.value,
      ...result.pagination,
      total: result.pagination.total || 0
    };
  } catch (err) {
    console.error('Error loading templates:', err);
    $q.notify({
      type: 'negative',
      message: 'Error loading templates'
    });
  } finally {
    loading.value = false;
  }
}

function onRequest(props: { pagination: { page: number, rowsPerPage: number, sortBy: string, descending: boolean } }) {
  const { page, rowsPerPage, sortBy, descending } = props.pagination;

  console.log('TemplateBrowser: onRequest called with pagination:', { page, rowsPerPage, sortBy, descending });
  console.log('TemplateBrowser: Current pagination before update:', { ...pagination.value });

  // Update pagination
  pagination.value.limit = rowsPerPage;
  pagination.value.offset = (page - 1) * rowsPerPage;

  console.log('TemplateBrowser: Updated pagination:', { ...pagination.value });

  // Update sorting
  sorting.value.sort_by = sortBy || 'updated_at';
  sorting.value.sort_desc = descending;

  // Reload templates
  void loadTemplates();
}



function viewTemplate(template: { id: number | string }) {
  // Get the current role path prefix
  const rolePrefix = authStore.state.user?.currentRole === 'admin' ? '/admin' : '/designer';

  if (template.id === 'new') {
    // Navigate to template builder with 'new' mode
    void router.push(`${rolePrefix}/templates/builder/new`);
  } else {
    // Navigate to existing template details
    void router.push(`${rolePrefix}/templates/builder/${template.id}`);
  }
}

// Status color is now handled by the EnhancedTemplateTable component

// Handle selection change
function handleSelectionChange(selected: Template[]) {
  selectedTemplates.value = selected;
}

// Event handlers for filter events
function handleFilterEvent(event: CustomEvent) {
  if (event.detail && event.detail.type) {
    switch (event.detail.type) {
      case 'filter-changed':
        // Update standard and specialized filters from the filter store
        standardFilters.value = { ...filterStore.activeFilters };
        specializedFilters.value = { ...filterStore.specializedFilters };

        // Reset pagination to first page
        pagination.value.offset = 0;

        // Reload templates
        void loadTemplates();
        break;

      case 'filter-loaded':
        // Update standard and specialized filters from the filter store
        standardFilters.value = { ...filterStore.activeFilters };
        specializedFilters.value = { ...filterStore.specializedFilters };

        // Reset pagination to first page
        pagination.value.offset = 0;

        // Reload templates
        void loadTemplates();

        void notify({
          type: 'positive',
          message: 'Filter loaded'
        });
        break;

      case 'filter-saved':
        void notify({
          type: 'positive',
          message: 'Filter saved'
        });
        break;

      case 'filter-deleted':
        void notify({
          type: 'positive',
          message: 'Filter deleted'
        });
        break;

      case 'specialized-filter-changed':
      case 'specialized-changed':
        // Update specialized filters from the filter store
        specializedFilters.value = { ...filterStore.specializedFilters };

        // Reset pagination to first page
        pagination.value.offset = 0;

        // Reload templates
        void loadTemplates();
        break;
    }
  }
}

// Function to fetch designers
async function fetchDesigners() {
  try {
    // Fetch designers from the app_users table
    const { data, error } = await supabase
      .from('app_users')
      .select('id, first_name, last_name')
      .order('first_name');

    if (error) {
      console.error('Error fetching designers:', error);
      return [];
    }

    // Transform the data into the format needed for filterOptions
    return data.map(user => ({
      label: `${user.first_name} ${user.last_name}`,
      value: user.id
    }));
  } catch (error) {
    console.error('Error in fetchDesigners:', error);
    return [];
  }
}

// Initialize
onMounted(async () => {
  // Set the context for the templates page
  contextStore.setContext('templates_table');

  // Set the table for the filter store
  await filterStore.setTable('templates');

  // Fetch designers for the dropdown
  const designers = await fetchDesigners();

  // If no designers were found, add a default one
  if (designers.length === 0) {
    designers.push({ label: 'Milan Košir', value: 2 });
  }

  // Define column definitions for templates with proper types
  const templateColumns: ColumnDefinition[] = [
    {
      id: 'name',
      name: 'Name',
      field: 'name',
      section: 'main',
      visible: true,
      order: 0,
      filterType: 'text'
    },
    {
      id: 'description',
      name: 'Description',
      field: 'description',
      section: 'main',
      visible: true,
      order: 1,
      filterType: 'text'
    },
    {
      id: 'status',
      name: 'Status',
      field: 'status',
      section: 'main',
      visible: true,
      order: 2,
      filterType: 'select',
      filterOptions: [
        { label: 'Draft', value: 'draft' },
        { label: 'Active', value: 'active' },
        { label: 'Archived', value: 'archived' }
      ]
    },
    {
      id: 'created_at',
      name: 'Created Date',
      field: 'created_at',
      section: 'expanded',
      visible: true,
      order: 0,
      filterType: 'date'
    },
    {
      id: 'updated_at',
      name: 'Updated Date',
      field: 'updated_at',
      section: 'expanded',
      visible: true,
      order: 1,
      filterType: 'date'
    },
    {
      id: 'designer_id',
      name: 'Designer',
      field: 'designer_id',
      section: 'expanded',
      visible: true,
      order: 2,
      filterType: 'select',
      filterOptions: designers
    }
  ];

  // Set columns in the filter store
  filterStore.setColumns(templateColumns);

  // Add event listener for filter events
  document.addEventListener('filter-event', handleFilterEvent as EventListener);

  // Load templates
  await loadTemplates();
});

// Cleanup
onUnmounted(() => {
  // Remove event listener
  document.removeEventListener('filter-event', handleFilterEvent as EventListener);
});
</script>

<style scoped>
.new-template-btn {
  font-weight: 500;
}

/* Remove card transition and hover effects */
.no-transition {
  transition: none !important;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1) !important;
  border-radius: 0 !important;
  background-color: #fff !important; /* Force white background */
}

.no-transition:hover {
  transform: none !important;
  background-color: #fff !important; /* Ensure consistent background on hover */
}

/* Dark mode support */
.body--dark .no-transition,
.body--dark .no-transition:hover {
  background-color: #1d1d1d !important; /* Dark theme background */
}

/* Match page background to card background */
.template-browser-page {
  background-color: #fff !important;
}

/* Dark mode page background */
.body--dark .template-browser-page {
  background-color: #1d1d1d !important;
}

/* Remove card border radius and adjust spacing */
:deep(.q-card) {
  border-radius: 0;
}

/* Adjust card section padding */
:deep(.q-card__section) {
  padding: 12px 16px;
}

/* Remove bottom margin from the header section */
:deep(.q-card__section:first-child) {
  padding-bottom: 8px;
}

/* Mobile styling */
@media (max-width: 599px) {
  .new-template-btn {
    min-width: 36px;
    min-height: 36px;
  }
}
</style>
