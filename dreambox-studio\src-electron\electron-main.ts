import { app, BrowserWindow, ipc<PERSON>ain, nativeTheme, Menu } from 'electron';
import path from 'path';
import os from 'os';
import { fileURLToPath } from 'url'

// needed in case process is undefined under Linux
const platform = process.platform || os.platform();

const currentDir = fileURLToPath(new URL('.', import.meta.url));

// Track all open windows
let mainWindow: BrowserWindow | undefined;
const allWindows: BrowserWindow[] = [];

/**
 * Create a new application window
 * @returns The newly created BrowserWindow
 */
async function createWindow(): Promise<BrowserWindow> {
  /**
   * Initial window options
   */
  const newWindow = new BrowserWindow({
    icon: path.resolve(currentDir, 'icons/icon.png'), // tray icon
    width: 1000,
    height: 600,
    useContentSize: true,
    webPreferences: {
      contextIsolation: true,
      // More info: https://v2.quasar.dev/quasar-cli-vite/developing-electron-apps/electron-preload-script
      preload: path.resolve(
        currentDir,
        path.join(process.env.QUASAR_ELECTRON_PRELOAD_FOLDER, 'electron-preload' + process.env.QUASAR_ELECTRON_PRELOAD_EXTENSION)
      ),
    },
  });

  // Add to our windows array
  allWindows.push(newWindow);

  // If this is the first window, set it as the main window
  if (!mainWindow) {
    mainWindow = newWindow;
  }

  if (process.env.DEV) {
    await newWindow.loadURL(process.env.APP_URL);
  } else {
    await newWindow.loadFile('index.html');
  }

  // Don't automatically open DevTools, even in development mode
  // Users can still open DevTools manually with Ctrl+Shift+I or from the menu
  if (process.env.DEBUGGING === 'force') {
    // Only open DevTools if explicitly forced
    newWindow.webContents.openDevTools();
  }

  newWindow.on('closed', () => {
    // Remove from our windows array
    const index = allWindows.indexOf(newWindow);
    if (index > -1) {
      allWindows.splice(index, 1);
    }

    // If this was the main window, clear the reference
    if (newWindow === mainWindow) {
      mainWindow = undefined;
    }
  });

  return newWindow;
}

/**
 * Create and set the application menu
 */
function createAppMenu() {
  const template: (Electron.MenuItemConstructorOptions | Electron.MenuItem)[] = [
    {
      label: 'File',
      submenu: [
        { role: 'quit' }
      ]
    },
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' },
        { role: 'delete' },
        { type: 'separator' },
        { role: 'selectAll' }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Window',
      submenu: [
        {
          label: 'New Window',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            void createWindow(); // Use void to explicitly ignore the Promise
          }
        },
        { type: 'separator' },
        { role: 'minimize' },
        { role: 'close' }
      ]
    }
  ];

  // Add macOS specific menu items
  if (platform === 'darwin') {
    template.unshift({
      label: app.name,
      submenu: [
        { role: 'about' },
        { type: 'separator' },
        { role: 'services' },
        { type: 'separator' },
        { role: 'hide' },
        { role: 'hideOthers' },
        { role: 'unhide' },
        { type: 'separator' },
        { role: 'quit' }
      ]
    });
  }

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

void app.whenReady().then(() => {
  // Set up IPC handlers for theme management
  setupThemeHandlers();

  // Create the application menu
  createAppMenu();

  // Create the main window
  void createWindow();
});

/**
 * Set up IPC handlers for theme management
 */
function setupThemeHandlers() {
  // Handle theme:set messages from renderer
  ipcMain.handle('theme:set', (_event, theme: 'light' | 'dark' | 'system') => {
    if (theme === 'system') {
      nativeTheme.themeSource = 'system';
    } else {
      nativeTheme.themeSource = theme;
    }

    // Notify all windows of the current effective theme
    const currentTheme = nativeTheme.shouldUseDarkColors ? 'dark' : 'light';
    notifyAllWindowsOfThemeChange(currentTheme);
  });

  // Handle theme:get messages from renderer
  ipcMain.handle('theme:get', () => {
    return nativeTheme.shouldUseDarkColors ? 'dark' : 'light';
  });

  // Listen for system theme changes
  nativeTheme.on('updated', () => {
    const currentTheme = nativeTheme.shouldUseDarkColors ? 'dark' : 'light';
    notifyAllWindowsOfThemeChange(currentTheme);
  });
}

/**
 * Notify all open windows of a theme change
 * @param theme The current theme ('light' or 'dark')
 */
function notifyAllWindowsOfThemeChange(theme: 'light' | 'dark') {
  allWindows.forEach(window => {
    if (!window.isDestroyed()) {
      window.webContents.send('theme:changed', theme);
    }
  });
}

app.on('window-all-closed', () => {
  if (platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (mainWindow === undefined) {
    void createWindow();
  }
});
