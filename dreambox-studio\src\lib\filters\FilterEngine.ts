/**
 * FilterEngine.ts
 *
 * This module provides the core filter engine functionality.
 * It applies filters to data based on filter definitions and handles complex filter combinations.
 */

import { filterRegistry } from '../../lib/filters/FilterRegistry';

/**
 * Filter Plugin interface
 */
export interface FilterPlugin {
  id: string;
  name: string;

  /**
   * Initialize the plugin
   *
   * @param engine Filter engine instance
   */
  initialize(engine: FilterEngine): void;

  /**
   * Apply plugin-specific filters
   *
   * @param data Data to filter
   * @param tableName Table name
   * @param filters Filter values
   * @returns Filtered data
   */
  applyFilters<T>(data: T[], tableName: string, filters: Record<string, unknown>): T[];

  /**
   * Get plugin-specific components
   *
   * @returns Component map
   */
  getComponents(): Record<string, unknown>;

  /**
   * Get plugin-specific filter types
   *
   * @returns Filter type definitions
   */
  getFilterTypes(): unknown[];
}

/**
 * Filter Engine class
 */
export class FilterEngine {
  private plugins: FilterPlugin[] = [];

  /**
   * Register a plugin
   *
   * @param plugin Plugin to register
   */
  registerPlugin(plugin: FilterPlugin): void {
    this.plugins.push(plugin);
    plugin.initialize(this);
    console.log(`Plugin registered: ${plugin.name}`);
  }

  /**
   * Get a plugin by ID
   *
   * @param id Plugin ID
   * @returns Plugin instance or undefined
   */
  getPlugin(id: string): FilterPlugin | undefined {
    return this.plugins.find(p => p.id === id);
  }

  /**
   * Get all registered plugins
   *
   * @returns Array of plugins
   */
  getPlugins(): FilterPlugin[] {
    return [...this.plugins];
  }

  /**
   * Get the filter registry
   *
   * @returns Filter registry
   */
  getRegistry() {
    return filterRegistry;
  }

  /**
   * Apply filters to data
   *
   * @param data Array of items to filter
   * @param tableName Table name for context
   * @param standardFilters Standard column filters
   * @param specializedFilters Specialized filters (prompt elements, related items, etc.)
   * @returns Filtered data
   */
  applyFilters<T>(
    data: T[],
    tableName: string,
    standardFilters: Record<string, unknown> = {},
    specializedFilters: Record<string, unknown> = {}
  ): T[] {
    if (!data || data.length === 0) return [];

    // Apply standard filters
    let filteredData = this.applyStandardFilters(data, standardFilters);

    // Apply specialized filters
    filteredData = this.applySpecializedFilters(
      filteredData,
      tableName,
      specializedFilters
    );

    // Apply plugin filters
    for (const plugin of this.plugins) {
      // Combine standard and specialized filters for plugins
      const combinedFilters = { ...standardFilters, ...specializedFilters };
      filteredData = plugin.applyFilters(filteredData, tableName, combinedFilters);
    }

    return filteredData;
  }

  /**
   * Apply standard column filters
   *
   * @param data Array of items to filter
   * @param filters Standard column filters
   * @returns Filtered data
   */
  private applyStandardFilters<T>(
    data: T[],
    filters: Record<string, unknown>
  ): T[] {
    if (!filters || Object.keys(filters).length === 0) return data;

    return data.filter(item => {
      // Check each filter
      for (const [key, value] of Object.entries(filters)) {
        if (value === null || value === undefined) continue;

        // Get item value (support nested properties with dot notation)
        let itemValue: unknown = item;
        for (const prop of key.split('.')) {
          if (itemValue === null || typeof itemValue !== 'object') {
            itemValue = null;
            break;
          }
          itemValue = (itemValue as Record<string, unknown>)[prop];
          if (itemValue === undefined) {
            itemValue = null;
            break;
          }
        }

        // Skip if item doesn't have this property
        if (itemValue === null) continue;

        // Apply filter based on value type
        if (!this.matchesFilter(itemValue, value)) {
          return false;
        }
      }

      return true;
    });
  }

  /**
   * Apply specialized filters
   *
   * @param data Array of items to filter
   * @param tableName Table name for context
   * @param filters Specialized filters
   * @returns Filtered data
   */
  private applySpecializedFilters<T>(
    data: T[],
    tableName: string,
    filters: Record<string, unknown>
  ): T[] {
    if (!filters || Object.keys(filters).length === 0) return data;

    // Get specialized filter types for this table
    const filterTypes = filterRegistry.getFilterTypesForTable(tableName)
      .filter(ft => ft.section === 'specialized');

    // Apply each specialized filter
    return data.filter(item => {
      for (const [filterId, value] of Object.entries(filters)) {
        if (value === null || value === undefined) continue;

        // Find filter type definition
        const filterType = filterTypes.find(ft => ft.id === filterId);
        if (!filterType || !filterType.stateApplier) continue;

        // Apply filter using its stateApplier function
        if (!filterType.stateApplier(item, value)) {
          return false;
        }
      }

      return true;
    });
  }

  /**
   * Check if an item value matches a filter value
   *
   * @param itemValue Value from the item
   * @param filterValue Value from the filter
   * @returns True if the item matches the filter
   */
  private matchesFilter(itemValue: unknown, filterValue: unknown): boolean {
    // Handle different filter value types
    if (filterValue === null || filterValue === undefined) {
      return true; // Null filter means no filtering
    }

    // Array filter (multi-select)
    if (Array.isArray(filterValue)) {
      if (filterValue.length === 0) return true;

      // If item value is also an array, check for any intersection
      if (Array.isArray(itemValue)) {
        return filterValue.some(fv => itemValue.includes(fv));
      }

      // Otherwise check if item value is in the filter array
      return filterValue.includes(itemValue);
    }

    // String filter (text)
    if (typeof filterValue === 'string') {
      if (filterValue === '') return true;

      // Convert item value to string for comparison
      const strValue = String(itemValue).toLowerCase();
      return strValue.includes(filterValue.toLowerCase());
    }

    // Number filter
    if (typeof filterValue === 'number') {
      return itemValue === filterValue;
    }

    // Boolean filter
    if (typeof filterValue === 'boolean') {
      return itemValue === filterValue;
    }

    // Date filter
    if (filterValue instanceof Date) {
      const itemDate = itemValue instanceof Date
        ? itemValue
        : new Date(String(itemValue));

      return itemDate.getTime() === filterValue.getTime();
    }

    // Object filter (range, etc.)
    if (typeof filterValue === 'object' && filterValue !== null) {
      // Range filter
      const rangeFilter = filterValue as { min?: number, max?: number };
      if ('min' in rangeFilter || 'max' in rangeFilter) {
        const numValue = Number(itemValue);

        if (isNaN(numValue)) return false;

        if (rangeFilter.min !== undefined && numValue < rangeFilter.min) {
          return false;
        }

        if (rangeFilter.max !== undefined && numValue > rangeFilter.max) {
          return false;
        }

        return true;
      }

      // Date range filter
      const dateFilter = filterValue as { from?: string, to?: string };
      if ('from' in dateFilter || 'to' in dateFilter) {
        const itemDate = itemValue instanceof Date
          ? itemValue
          : new Date(String(itemValue));

        if (isNaN(itemDate.getTime())) return false;

        if (dateFilter.from !== undefined) {
          const fromDate = new Date(dateFilter.from);
          if (itemDate < fromDate) return false;
        }

        if (dateFilter.to !== undefined) {
          const toDate = new Date(dateFilter.to);
          if (itemDate > toDate) return false;
        }

        return true;
      }
    }

    // Default: strict equality
    return itemValue === filterValue;
  }
}

// Create and export a singleton instance
export const filterEngine = new FilterEngine();

// Export default for module imports
export default filterEngine;
