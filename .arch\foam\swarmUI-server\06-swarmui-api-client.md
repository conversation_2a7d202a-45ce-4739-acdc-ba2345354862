# Step 6: SwarmUI API Client Implementation

This document outlines the implementation of the SwarmUI API client, which will handle communication with the SwarmUI API for image generation.

## 6.1 Create API Client Directory Structure

First, create the directory structure for the SwarmUI API client:

```
src/
└── services/
    └── swarmUI/
        ├── index.ts
        ├── SwarmUIService.ts
        ├── types.ts
        └── utils.ts
```

## 6.2 Define Types

Create the types for the SwarmUI API client in `src/services/swarmUI/types.ts`:

```typescript
// types.ts
export interface SwarmUIModel {
  id: string;
  name: string;
  type: string;
  description?: string;
  size?: number;
}

export interface SwarmUIGenerationParams {
  prompt: string;
  negative_prompt?: string;
  model: string;
  width: number;
  height: number;
  steps: number;
  cfg_scale: number;
  sampler: string;
  batch_size?: number;
  seed?: number;
  scheduler?: string;
  clip_skip?: number;
  tiling?: boolean;
  hires_fix?: boolean;
  hires_scale?: number;
  hires_steps?: number;
  hires_upscaler?: string;
  hires_denoising_strength?: number;
  controlnet?: SwarmUIControlNet[];
  loras?: SwarmUILora[];
  embeddings?: SwarmUIEmbedding[];
}

export interface SwarmUIControlNet {
  model: string;
  image: string;
  weight: number;
  guidance_start?: number;
  guidance_end?: number;
  control_mode?: string;
  resize_mode?: string;
}

export interface SwarmUILora {
  model: string;
  weight: number;
}

export interface SwarmUIEmbedding {
  name: string;
  weight: number;
}

export interface SwarmUIGenerationRequest {
  params: SwarmUIGenerationParams;
  webhook_url?: string;
  webhook_auth_header?: string;
}

export interface SwarmUIGenerationResponse {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  created_at: string;
  updated_at: string;
  params: SwarmUIGenerationParams;
  images?: SwarmUIGeneratedImage[];
  error?: string;
}

export interface SwarmUIGeneratedImage {
  id: string;
  url: string;
  width: number;
  height: number;
  seed: number;
  metadata: Record<string, any>;
}

export interface SwarmUIServerInfo {
  version: string;
  models: SwarmUIModel[];
  samplers: string[];
  schedulers: string[];
  upscalers: string[];
  controlnet_models: string[];
  lora_models: string[];
  embedding_models: string[];
}
```

## 6.3 Implement SwarmUI Service

Create the SwarmUI service in `src/services/swarmUI/SwarmUIService.ts`:

```typescript
// SwarmUIService.ts
import axios from 'axios';
import { serverManagementService } from '../serverManagement';
import {
  SwarmUIGenerationParams,
  SwarmUIGenerationRequest,
  SwarmUIGenerationResponse,
  SwarmUIModel,
  SwarmUIServerInfo
} from './types';
import { supabase } from '../../boot/supabase';
import { useAuthStore } from '../../stores/auth';

export class SwarmUIService {
  private apiEndpoint: string | null = null;
  private apiKey: string | null = null;
  private serverId: number | null = null;

  constructor() {}

  async initialize(): Promise<boolean> {
    try {
      const activeServer = await serverManagementService.getActiveServer();

      if (!activeServer) {
        console.log('No active server found');
        return false;
      }

      this.apiEndpoint = activeServer.apiEndpoint;
      this.apiKey = activeServer.apiKey;
      this.serverId = activeServer.id;

      return true;
    } catch (error) {
      console.error('Error initializing SwarmUI service:', error);
      return false;
    }
  }

  private getHeaders() {
    return {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json'
    };
  }

  private async ensureInitialized() {
    if (!this.apiEndpoint || !this.apiKey) {
      const success = await this.initialize();
      if (!success) {
        throw new Error('SwarmUI service not initialized');
      }
    }

    // Update server activity
    if (this.serverId) {
      await serverManagementService.updateServerActivity(this.serverId);
    }
  }

  async getServerInfo(): Promise<SwarmUIServerInfo> {
    await this.ensureInitialized();

    try {
      const response = await axios.get(
        `${this.apiEndpoint}/api/info`,
        { headers: this.getHeaders() }
      );

      return response.data;
    } catch (error) {
      console.error('Error getting SwarmUI server info:', error);
      throw new Error('Failed to get SwarmUI server info');
    }
  }

  async getModels(): Promise<SwarmUIModel[]> {
    await this.ensureInitialized();

    try {
      const response = await axios.get(
        `${this.apiEndpoint}/api/models`,
        { headers: this.getHeaders() }
      );

      return response.data.models;
    } catch (error) {
      console.error('Error getting SwarmUI models:', error);
      throw new Error('Failed to get SwarmUI models');
    }
  }

  async generateImage(params: SwarmUIGenerationParams): Promise<SwarmUIGenerationResponse> {
    await this.ensureInitialized();

    const authStore = useAuthStore();
    const user = authStore.getUser();

    if (!user) {
      throw new Error('User not authenticated');
    }

    try {
      // Create a record in the database
      const { data: generationRecord, error: dbError } = await supabase
        .from('image_generations')
        .insert({
          user_id: user.id,
          server_id: this.serverId,
          prompt: params.prompt,
          negative_prompt: params.negative_prompt || '',
          width: params.width,
          height: params.height,
          num_images: params.batch_size || 1,
          seed: params.seed,
          model: params.model,
          sampler: params.sampler,
          steps: params.steps,
          cfg_scale: params.cfg_scale,
          status: 'pending'
        })
        .select()
        .single();

      if (dbError) {
        console.error('Error creating generation record:', dbError);
        throw new Error('Failed to create generation record');
      }

      // Prepare the request
      const request: SwarmUIGenerationRequest = {
        params
      };

      // Send the request to SwarmUI
      const response = await axios.post(
        `${this.apiEndpoint}/api/generate`,
        request,
        { headers: this.getHeaders() }
      );

      const generationResponse = response.data;

      // Update the record with the generation ID
      await supabase
        .from('image_generations')
        .update({
          status: 'processing',
          updated_at: new Date().toISOString()
        })
        .eq('id', generationRecord.id);

      return generationResponse;
    } catch (error) {
      console.error('Error generating image:', error);
      throw new Error('Failed to generate image');
    }
  }

  async getGenerationStatus(generationId: string): Promise<SwarmUIGenerationResponse> {
    await this.ensureInitialized();

    try {
      const response = await axios.get(
        `${this.apiEndpoint}/api/generations/${generationId}`,
        { headers: this.getHeaders() }
      );

      return response.data;
    } catch (error) {
      console.error('Error getting generation status:', error);
      throw new Error('Failed to get generation status');
    }
  }

  async downloadImage(imageUrl: string): Promise<Blob> {
    await this.ensureInitialized();

    try {
      const response = await axios.get(
        imageUrl,
        {
          headers: this.getHeaders(),
          responseType: 'blob'
        }
      );

      return response.data;
    } catch (error) {
      console.error('Error downloading image:', error);
      throw new Error('Failed to download image');
    }
  }
}

export const swarmUIService = new SwarmUIService();
```

## 6.4 Create Service Index File

Create the index file to export the service in `src/services/swarmUI/index.ts`:

```typescript
// index.ts
export * from './SwarmUIService';
export * from './types';
export * from './utils';
```

## 6.5 Create Utility Functions

Create utility functions in `src/services/swarmUI/utils.ts`:

```typescript
// utils.ts
import { SwarmUIGenerationParams } from './types';
import { supabase } from '../../boot/supabase';

export function createDefaultGenerationParams(): SwarmUIGenerationParams {
  return {
    prompt: '',
    negative_prompt: 'ugly, deformed, noisy, blurry, distorted, out of focus, bad anatomy, extra limbs, poorly drawn face, poorly drawn hands, missing fingers',
    model: 'Flux',
    width: 1024,
    height: 1024,
    steps: 30,
    cfg_scale: 7.0,
    sampler: 'dpmpp_2m',
    batch_size: 1,
    scheduler: 'karras',
    clip_skip: 1
  };
}

export function createFastGenerationParams(): SwarmUIGenerationParams {
  return {
    prompt: '',
    negative_prompt: 'ugly, deformed, noisy, blurry, distorted',
    model: 'Flux',
    width: 768,
    height: 768,
    steps: 20,
    cfg_scale: 7.0,
    sampler: 'dpmpp_2m',
    batch_size: 1,
    scheduler: 'karras',
    clip_skip: 1
  };
}

export function generateRandomSeed(): number {
  return Math.floor(Math.random() * 2147483647);
}

export async function saveImageToStorage(
  userId: number,
  generationId: number,
  imageBlob: Blob,
  seed: number,
  width: number,
  height: number
): Promise<string> {
  try {
    // Generate a unique filename
    const timestamp = Date.now();
    const filename = `${userId}/${generationId}/${timestamp}_${seed}.png`;
    const storagePath = `generated_images/${filename}`;

    // Upload the image to Supabase Storage
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('images')
      .upload(storagePath, imageBlob, {
        contentType: 'image/png',
        cacheControl: '3600'
      });

    if (uploadError) {
      console.error('Error uploading image:', uploadError);
      throw new Error('Failed to upload image');
    }

    // Get the public URL
    const { data: urlData } = supabase.storage
      .from('images')
      .getPublicUrl(storagePath);

    const publicUrl = urlData.publicUrl;

    // Create a record in the generated_images table
    const { error: dbError } = await supabase
      .from('generated_images')
      .insert({
        generation_id: generationId,
        user_id: userId,
        storage_path: storagePath,
        width,
        height,
        seed
      });

    if (dbError) {
      console.error('Error creating image record:', dbError);
      throw new Error('Failed to create image record');
    }

    return publicUrl;
  } catch (error) {
    console.error('Error saving image to storage:', error);
    throw new Error('Failed to save image to storage');
  }
}

export function getAspectRatio(width: number, height: number): string {
  const gcd = (a: number, b: number): number => {
    return b === 0 ? a : gcd(b, a % b);
  };

  const divisor = gcd(width, height);
  return `${width / divisor}:${height / divisor}`;
}

export function getCommonAspectRatios(): { label: string, value: { width: number, height: number } }[] {
  return [
    { label: 'Square (1:1)', value: { width: 512, height: 512 } },
    { label: 'Portrait (2:3)', value: { width: 512, height: 768 } },
    { label: 'Landscape (3:2)', value: { width: 768, height: 512 } },
    { label: 'Widescreen (16:9)', value: { width: 768, height: 432 } },
    { label: 'Ultrawide (21:9)', value: { width: 896, height: 384 } }
  ];
}
```

## 6.6 Create a Webhook Handler

To handle callbacks from SwarmUI when generations are complete, create a webhook handler in your backend:

```typescript
// This would typically be implemented in a serverless function or API endpoint
export async function handleSwarmUIWebhook(req, res) {
  const { generationId, status, images, error } = req.body;

  // Verify the request is from SwarmUI
  // This would require additional security measures in production

  try {
    // Get the generation record
    const { data: generation, error: dbError } = await supabase
      .from('image_generations')
      .select('*')
      .eq('id', generationId)
      .single();

    if (dbError) {
      console.error('Error fetching generation:', dbError);
      return res.status(404).json({ error: 'Generation not found' });
    }

    // Update the generation status
    await supabase
      .from('image_generations')
      .update({
        status: status,
        error_message: error || null,
        updated_at: new Date().toISOString()
      })
      .eq('id', generationId);

    // If the generation is complete, save the images
    if (status === 'completed' && images && images.length > 0) {
      for (const image of images) {
        // Download the image
        const response = await fetch(image.url);
        const imageBlob = await response.blob();

        // Save to storage
        await saveImageToStorage(
          generation.user_id,
          generationId,
          imageBlob,
          image.seed,
          image.width,
          image.height
        );
      }
    }

    return res.status(200).json({ success: true });
  } catch (error) {
    console.error('Error handling webhook:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
```

## Next Steps

Now that you have implemented the SwarmUI API client, proceed to [Step 7: UI Components for Server Management](./07-ui-components.md) to implement the user interface for managing SwarmUI servers.
