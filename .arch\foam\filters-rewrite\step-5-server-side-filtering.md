# Step 5: Server-Side Filtering

## Overview

This document outlines the approach for implementing server-side filtering with the new filter engine. Server-side filtering is essential for performance when dealing with large datasets, as it reduces the amount of data transferred to the client and improves rendering performance.

## Current State Analysis

Currently, the Templates table uses a mix of client-side and server-side filtering:

1. **Server-side filtering** is used for pagination and basic filtering (search, status)
2. **Client-side filtering** is applied for more complex filters (prompt elements, related items)

This hybrid approach has several limitations:
- Pagination becomes unreliable when client-side filters are applied
- Performance issues with large datasets
- Inconsistent user experience

## Server-Side Filtering Architecture

### Goals
- Move all filtering logic to the server
- Create a consistent API for all filter types
- Support complex filter combinations
- Maintain pagination reliability

### Key Components

#### 1. Filter Query Builder

The Filter Query Builder will convert filter definitions from the client into SQL queries:

```typescript
// Conceptual structure
interface FilterQueryBuilder {
  // Convert filter state to SQL conditions
  buildFilterQuery(
    tableName: string,
    filters: Record<string, any>,
    specializedFilters: Record<string, any>
  ): {
    sql: string;
    params: any[];
  };
  
  // Build specialized filter queries
  buildPromptElementsQuery(filters: Record<string, number[]>): {
    sql: string;
    params: any[];
  };
  
  buildRelatedItemsQuery(filters: Record<string, number[]>): {
    sql: string;
    params: any[];
  };
}
```

#### 2. Supabase SQL Functions

We'll create or update SQL functions in Supabase to handle the filtering:

```sql
-- Example structure for templates filter function
CREATE OR REPLACE FUNCTION filter_templates(
  search TEXT DEFAULT NULL,
  status TEXT[] DEFAULT NULL,
  created_from TIMESTAMP DEFAULT NULL,
  created_to TIMESTAMP DEFAULT NULL,
  -- Other standard filters
  prompt_elements JSONB DEFAULT NULL, -- For specialized filters
  related_items JSONB DEFAULT NULL,   -- For specialized filters
  sort_by TEXT DEFAULT 'updated_at',
  sort_desc BOOLEAN DEFAULT TRUE,
  limit_val INTEGER DEFAULT 10,
  offset_val INTEGER DEFAULT 0
) RETURNS TABLE (
  -- Template fields
  id BIGINT,
  name TEXT,
  -- Other fields
  total_count BIGINT
) AS $$
DECLARE
  query TEXT;
  count_query TEXT;
  total BIGINT;
BEGIN
  -- Build the base query
  query := 'SELECT t.* FROM templates t WHERE 1=1';
  
  -- Add standard filters
  IF search IS NOT NULL THEN
    query := query || ' AND (t.name ILIKE ''%' || search || '%'' OR t.description ILIKE ''%' || search || '%'')';
  END IF;
  
  IF status IS NOT NULL AND array_length(status, 1) > 0 THEN
    query := query || ' AND t.status = ANY($1)';
  END IF;
  
  -- Add date range filters
  IF created_from IS NOT NULL THEN
    query := query || ' AND t.created_at >= $2';
  END IF;
  
  IF created_to IS NOT NULL THEN
    query := query || ' AND t.created_at <= $3';
  END IF;
  
  -- Add prompt elements filter
  IF prompt_elements IS NOT NULL AND jsonb_typeof(prompt_elements) = 'object' THEN
    -- For each element type in the prompt_elements JSON
    -- Add a subquery to filter templates that have matching elements
    -- This is a simplified example - actual implementation would be more complex
    query := query || ' AND t.id IN (
      SELECT template_id FROM template_elements te
      WHERE (te.element_type, te.element_value_id) IN (
        -- Extract key-value pairs from the JSON
        -- Each key is an element_type, and the value is an array of element_value_ids
        -- This would be implemented as a complex SQL function
      )
    )';
  END IF;
  
  -- Add related items filter
  IF related_items IS NOT NULL AND jsonb_typeof(related_items) = 'object' THEN
    -- Similar approach for collections, products, etc.
    -- This would be implemented as a complex SQL function
  END IF;
  
  -- Get total count before applying limit/offset
  count_query := 'SELECT COUNT(*) FROM (' || query || ') AS count_query';
  EXECUTE count_query INTO total;
  
  -- Add sorting
  query := query || ' ORDER BY t.' || sort_by;
  IF sort_desc THEN
    query := query || ' DESC';
  ELSE
    query := query || ' ASC';
  END IF;
  
  -- Add pagination
  query := query || ' LIMIT ' || limit_val || ' OFFSET ' || offset_val;
  
  -- Return the results with the total count
  RETURN QUERY EXECUTE query;
END;
$$ LANGUAGE plpgsql;
```

#### 3. Client-Side Filter Adapter

The client-side adapter will convert between the filter engine's format and the API format:

```typescript
// Conceptual structure
class ServerFilterAdapter {
  // Convert filter store state to API parameters
  convertToApiParams(
    tableName: string,
    activeFilters: Record<string, any>,
    specializedFilters: Record<string, any>,
    pagination: { page: number; rowsPerPage: number },
    sorting: { sortBy: string; descending: boolean }
  ): Record<string, any> {
    const params: Record<string, any> = {
      limit: pagination.rowsPerPage,
      offset: (pagination.page - 1) * pagination.rowsPerPage,
      sort_by: sorting.sortBy || 'updated_at',
      sort_desc: sorting.descending
    };
    
    // Convert standard filters
    for (const [key, value] of Object.entries(activeFilters)) {
      // Special handling for certain fields
      if (key === 'name') {
        params.search = value;
      } else {
        params[key] = value;
      }
    }
    
    // Convert specialized filters
    if (Object.keys(specializedFilters).length > 0) {
      // Handle prompt elements
      if (specializedFilters.promptElements) {
        params.prompt_elements = JSON.stringify(specializedFilters.promptElements);
      }
      
      // Handle related items
      if (specializedFilters.relatedItems) {
        params.related_items = JSON.stringify(specializedFilters.relatedItems);
      }
    }
    
    return params;
  }
}
```

## Implementation Plan

### Phase 1: SQL Function Development

1. **Create or Update SQL Functions**
   - Develop the `filter_templates` function in Supabase
   - Test with various filter combinations
   - Optimize for performance

2. **Create Test Harness**
   - Build a simple test harness to verify SQL function behavior
   - Test with real data from the production database
   - Document performance characteristics

### Phase 2: Client-Side Integration

1. **Implement ServerFilterAdapter**
   - Create the adapter class to convert between filter formats
   - Test with various filter combinations
   - Ensure backward compatibility with existing code

2. **Update TemplateBrowser Component**
   - Modify the component to use the new server-side filtering approach
   - Update pagination handling to work with server-side filtering
   - Test with real data

### Phase 3: Specialized Filter Integration

1. **Implement Prompt Elements Server-Side Filtering**
   - Update the SQL function to handle prompt elements filtering
   - Create client-side adapter for prompt elements filters
   - Test with various filter combinations

2. **Implement Related Items Server-Side Filtering**
   - Update the SQL function to handle related items filtering
   - Create client-side adapter for related items filters
   - Test with various filter combinations

### Phase 4: Performance Optimization

1. **Analyze Query Performance**
   - Use Supabase query analysis tools to identify bottlenecks
   - Optimize SQL queries for performance
   - Add appropriate indexes

2. **Implement Caching**
   - Add caching for frequently used filters
   - Implement cache invalidation strategy
   - Measure performance improvements

## SQL Function Implementation Details

### Handling Prompt Elements Filters

The prompt elements filter requires special handling in SQL:

```sql
-- Helper function to process prompt elements filter
CREATE OR REPLACE FUNCTION process_prompt_elements_filter(
  template_id BIGINT,
  filter_json JSONB
) RETURNS BOOLEAN AS $$
DECLARE
  element_type TEXT;
  element_values BIGINT[];
  matches BOOLEAN := TRUE;
BEGIN
  -- For each element type in the filter
  FOR element_type, element_values IN SELECT * FROM jsonb_each(filter_json)
  LOOP
    -- Check if the template has any of the specified element values for this type
    -- The element_type will be in format 'element_TYPE_NAME'
    -- We need to extract the actual type name
    DECLARE
      actual_type TEXT := substring(element_type FROM 9); -- Remove 'element_' prefix
      has_match BOOLEAN;
    BEGIN
      SELECT EXISTS (
        SELECT 1 FROM template_elements te
        WHERE te.template_id = process_prompt_elements_filter.template_id
          AND te.element_type = actual_type
          AND te.element_value_id = ANY(element_values::BIGINT[])
      ) INTO has_match;
      
      -- If no match for this element type, the template doesn't match the filter
      IF NOT has_match THEN
        matches := FALSE;
        EXIT; -- No need to check other element types
      END IF;
    END;
  END LOOP;
  
  RETURN matches;
END;
$$ LANGUAGE plpgsql;
```

### Handling Related Items Filters

Similarly, related items require special handling:

```sql
-- Helper function to process related items filter
CREATE OR REPLACE FUNCTION process_related_items_filter(
  template_id BIGINT,
  filter_json JSONB
) RETURNS BOOLEAN AS $$
DECLARE
  matches BOOLEAN := TRUE;
  collection_ids BIGINT[];
  product_ids BIGINT[];
BEGIN
  -- Extract collection IDs if present
  IF filter_json ? 'collections' THEN
    collection_ids := (SELECT array_agg(value::BIGINT) FROM jsonb_array_elements_text(filter_json->'collections'));
    
    -- Check if the template is in any of the specified collections
    IF NOT EXISTS (
      SELECT 1 FROM template_collections tc
      WHERE tc.template_id = process_related_items_filter.template_id
        AND tc.collection_id = ANY(collection_ids)
    ) THEN
      matches := FALSE;
    END IF;
  END IF;
  
  -- Extract product IDs if present and we still have a match
  IF matches AND filter_json ? 'products' THEN
    product_ids := (SELECT array_agg(value::BIGINT) FROM jsonb_array_elements_text(filter_json->'products'));
    
    -- Check if the template is associated with any of the specified products
    IF NOT EXISTS (
      SELECT 1 FROM template_products tp
      WHERE tp.template_id = process_related_items_filter.template_id
        AND tp.product_id = ANY(product_ids)
    ) THEN
      matches := FALSE;
    END IF;
  END IF;
  
  RETURN matches;
END;
$$ LANGUAGE plpgsql;
```

## Benefits of Server-Side Filtering

1. **Improved Performance**
   - Reduced data transfer between server and client
   - More efficient filtering for large datasets
   - Better use of database indexes

2. **Reliable Pagination**
   - Consistent page sizes regardless of filter complexity
   - Accurate total count for pagination controls
   - Better user experience

3. **Scalability**
   - Can handle larger datasets as the application grows
   - Reduced client-side processing
   - More efficient use of resources

4. **Consistency**
   - Same filtering logic applied regardless of client
   - Centralized filter implementation
   - Easier to maintain and update

## Challenges and Mitigations

1. **Complex SQL Development**
   - **Challenge**: Developing complex SQL functions for specialized filters
   - **Mitigation**: Start with simpler filters and incrementally add complexity

2. **Performance Tuning**
   - **Challenge**: Ensuring good performance with complex filter combinations
   - **Mitigation**: Use query analysis tools, add appropriate indexes, implement caching

3. **Backward Compatibility**
   - **Challenge**: Maintaining compatibility with existing code during transition
   - **Mitigation**: Create adapter layer, use feature flags, thorough testing

4. **Testing Complexity**
   - **Challenge**: Testing all possible filter combinations
   - **Mitigation**: Create automated test suite, use real data for testing

## Conclusion

Implementing server-side filtering is a critical component of the new filter engine architecture. By moving filtering logic to the server, we can improve performance, ensure reliable pagination, and create a more consistent user experience.

The implementation will require careful development of SQL functions, client-side adapters, and thorough testing. However, the benefits in terms of performance, scalability, and maintainability make this effort worthwhile.

This approach also sets the foundation for future tables (Products, Users, etc.) to use the same server-side filtering pattern, ensuring consistency across the application.
