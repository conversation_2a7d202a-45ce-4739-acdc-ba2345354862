import { Notify } from 'quasar';
import type { QNotifyCreateOptions } from 'quasar';
import { userSettingsService } from './userSettingsService';

// Setting key for toast notifications
export const TOAST_NOTIFICATIONS_ENABLED = 'toast_notifications_enabled';

/**
 * Service for managing notifications
 */
export const notificationService = {
  /**
   * Show a notification if enabled in user settings
   * @param options Quasar notification options
   */
  async notify(options: QNotifyCreateOptions): Promise<void> {
    // Check if toast notifications are enabled
    const enabled = await this.areToastNotificationsEnabled();

    // If notifications are disabled, don't show the toast
    if (!enabled) {
      console.log('Toast notification suppressed:', options.message);
      return;
    }

    // Show the notification
    Notify.create(options);
  },

  /**
   * Check if toast notifications are enabled
   * @returns True if toast notifications are enabled, false otherwise
   */
  async areToastNotificationsEnabled(): Promise<boolean> {
    try {
      const setting = await userSettingsService.getSetting(TOAST_NOTIFICATIONS_ENABLED);
      // If the setting doesn't exist, default to enabled
      return setting === null || setting === 'true';
    } catch (error) {
      console.error('Error checking toast notification setting:', error);
      // Default to enabled if there's an error
      return true;
    }
  },

  /**
   * Set whether toast notifications are enabled
   * @param enabled True to enable toast notifications, false to disable
   */
  async setToastNotificationsEnabled(enabled: boolean): Promise<boolean> {
    try {
      return await userSettingsService.saveSetting(TOAST_NOTIFICATIONS_ENABLED, enabled.toString());
    } catch (error) {
      console.error('Error saving toast notification setting:', error);
      return false;
    }
  }
};
