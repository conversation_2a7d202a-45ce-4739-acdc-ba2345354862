// This file provides proper TypeScript types for the Supabase client
// to fix TypeScript errors in the codebase

// Import SupabaseClient as a default import
import type SupabaseClient from '@supabase/supabase-js/dist/module/SupabaseClient';
// Import the DatabaseWithFilterEngine type
import type { DatabaseWithFilterEngine } from './supabase-filter-engine';

// Export a type that extends the SupabaseClient with our database types
export type TypedSupabaseClient = SupabaseClient<DatabaseWithFilterEngine>;
export type TypedSupabaseClientWithFilters = SupabaseClient<DatabaseWithFilterEngine>;

// Helper types for common Supabase query patterns
export type SupabaseQueryResult<T> = {
  data: T | null;
  error: Error | null;
};

export type SupabaseQueryArrayResult<T> = {
  data: T[] | null;
  error: Error | null;
};

// PostgrestError type for error handling
export interface PostgrestError {
  message: string;
  details: string;
  hint: string;
  code: string;
}
