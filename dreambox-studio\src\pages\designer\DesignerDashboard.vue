<template>
  <q-page class="flex flex-center">
    <div class="column items-center q-gutter-y-md">
      <h1 class="text-h4">Designer Dashboard</h1>
      <p class="text-subtitle1">
        Welcome to the Designer interface
      </p>
      <p v-if="authStore.state.user?.currentCompany">
        Company: {{ authStore.state.user.currentCompany.name }}
      </p>
      <div class="row q-gutter-md">
        <q-btn color="primary" label="Admin Dashboard" @click="switchToAdmin" v-if="hasAdminRole" />
        <q-btn color="negative" label="Logout" @click="handleLogout" />
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { useAuthStore } from 'src/stores/auth'
import { useRouter } from 'vue-router'
import { computed } from 'vue'

const authStore = useAuthStore()
const router = useRouter()

const hasAdminRole = computed(() =>
  authStore.state.user?.roles.includes('admin')
)

async function switchToAdmin() {
  if (authStore.state.user) {
    authStore.setCurrentRole('admin')
    try {
      await router.push('/admin')
    } catch (error) {
      console.error('Navigation error:', error)
    }
  }
}

async function handleLogout() {
  await authStore.logout()
  try {
    await router.push('/auth/login')
  } catch (error) {
    console.error('Navigation error:', error)
  }
}
</script>
