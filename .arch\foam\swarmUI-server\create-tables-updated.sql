-- SwarmUI Server Management Tables

-- Server instances table
CREATE TABLE IF NOT EXISTS swarmui_servers (
  id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
  user_id BIGINT NOT NULL REFERENCES app_users(id) ON DELETE CASCADE,
  pod_id TEXT NOT NULL,
  api_endpoint TEXT NOT NULL,
  api_key TEXT NOT NULL,
  status TEXT NOT NULL,
  gpu_type TEXT NOT NULL,
  started_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  stopped_at TIMESTAMP WITH TIME ZONE,
  last_activity TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Add RLS policies for swarmui_servers
ALTER TABLE swarmui_servers ENABLE ROW LEVEL SECURITY;

-- Policy for users to view only their own servers
CREATE POLICY "Users can view their own servers"
  ON swarmui_servers
  FOR SELECT
  USING (user_id = get_current_app_user_id() OR is_super_admin());

-- Policy for users to insert their own servers
CREATE POLICY "Users can insert their own servers"
  ON swarmui_servers
  FOR INSERT
  WITH CHECK (user_id = get_current_app_user_id() OR is_super_admin());

-- Policy for users to update their own servers
CREATE POLICY "Users can update their own servers"
  ON swarmui_servers
  FOR UPDATE
  USING (user_id = get_current_app_user_id() OR is_super_admin());

-- Policy for users to delete their own servers
CREATE POLICY "Users can delete their own servers"
  ON swarmui_servers
  FOR DELETE
  USING (user_id = get_current_app_user_id() OR is_super_admin());

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update the updated_at timestamp
CREATE TRIGGER update_swarmui_servers_updated_at
BEFORE UPDATE ON swarmui_servers
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Note: Storage bucket and policies should be managed separately
-- This script only creates the swarmui_servers table
