<template>
  <q-btn-dropdown
    flat
    :label="currentLanguageLabel"
    icon="language"
    class="language-switcher"
  >
    <q-list>
      <q-item
        v-for="lang in languages"
        :key="lang.value"
        clickable
        v-close-popup
        @click="changeLanguage(lang.value)"
        :active="currentLocale === lang.value"
      >
        <q-item-section>
          <q-item-label>{{ lang.label }}</q-item-label>
        </q-item-section>
      </q-item>
    </q-list>
  </q-btn-dropdown>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { setLocale } from 'src/i18n';

// Get the current locale from localStorage or default to English
const storedLocale = localStorage.getItem('dreambox-locale') || 'en-US';
const currentLocale = ref(storedLocale);

const languages = [
  { label: 'English', value: 'en-US' },
  { label: 'Hrvatski', value: 'hr-HR' }
];

const currentLanguageLabel = computed(() => {
  const lang = languages.find(lang => lang.value === currentLocale.value);
  return lang ? lang.label : 'English';
});

function changeLanguage(locale: string) {
  console.log('Changing language to:', locale);
  currentLocale.value = locale as 'en-US' | 'hr-HR';
  setLocale(locale as 'en-US' | 'hr-HR');

  // Update the UI without reloading the page
  // This avoids logging the user out
  document.dispatchEvent(new CustomEvent('language-changed', { detail: locale }));
}
</script>

<style scoped>
.language-switcher {
  min-width: 100px;
}
</style>
