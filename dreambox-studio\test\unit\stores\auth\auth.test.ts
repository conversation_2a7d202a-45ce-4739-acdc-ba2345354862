import { describe, test, expect, beforeEach, mock, spyOn } from 'bun:test';
import { setActivePinia, createPinia } from 'pinia';
import type { Company, UserRole } from '../../../../src/types/auth';

// Import the store first
import { useAuthStore } from '../../../../src/stores/auth';

// Create a simplified mock of the auth store for testing
const createMockAuthStore = () => {
  // Create a fresh Pinia instance
  setActivePinia(createPinia());

  // Create a mock auth store with simplified implementation
  const authStore = {
    state: {
      user: null as any,
      loading: false,
      error: null as string | null
    },

    isAuthenticated: false,
    hasMultipleCompanies: false,
    hasMultipleRoles: false,

    // Mock supabase client
    supabase: {
      auth: {
        signInWithPassword: mock((credentials: { email: string, password: string }) => {
          // Default success response
          return Promise.resolve({
            data: { user: { id: 'test-user-id' } },
            error: null
          });
        }),
        signOut: mock(() => {
          // Reset state on signOut
          authStore.state.user = null;
          authStore.isAuthenticated = false;
          return Promise.resolve({ error: null });
        })
      },
      from: mock((table: string) => {
        if (table === 'allowed_emails') {
          return {
            select: () => ({
              eq: () => ({
                limit: () => Promise.resolve({
                  data: [{ email: '<EMAIL>' }],
                  error: null
                })
              })
            })
          };
        }

        if (table === 'users') {
          return {
            select: () => ({
              eq: () => ({
                single: () => Promise.resolve({
                  data: {
                    id: 'test-user-id',
                    email: '<EMAIL>',
                    first_name: 'Test',
                    last_name: 'User'
                  },
                  error: null
                })
              })
            })
          };
        }

        if (table === 'user_roles') {
          return {
            select: () => ({
              eq: () => Promise.resolve({
                data: [
                  {
                    role: { id: 'role-1', name: 'admin' },
                    company: { id: 'company-1', name: 'Test Company' }
                  }
                ],
                error: null
              })
            })
          };
        }

        return authStore.supabase;
      })
    },

    // Mock login method
    login: async (email: string, password: string) => {
      authStore.state.loading = true;
      authStore.state.error = null;

      try {
        const { data, error } = await authStore.supabase.auth.signInWithPassword({ email, password });

        if (error) {
          authStore.state.error = error.message;
          return;
        }

        if (!data.user) {
          authStore.state.error = 'Invalid login credentials';
          return;
        }

        // Check if email is allowed
        const allowedEmailsResponse = await authStore.supabase.from('allowed_emails')
          .select()
          .eq('email', email)
          .limit(1);

        if (!allowedEmailsResponse.data || allowedEmailsResponse.data.length === 0) {
          authStore.state.error = 'Access denied. Your email is not authorized to use this application.';
          await authStore.supabase.auth.signOut();
          return;
        }

        // Get user details
        const userResponse = await authStore.supabase.from('users')
          .select()
          .eq('email', email)
          .single();

        if (userResponse.error || !userResponse.data) {
          authStore.state.error = 'Failed to fetch user details';
          return;
        }

        // Get user roles and companies
        const userRolesResponse = await authStore.supabase.from('user_roles')
          .select()
          .eq('user_id', data.user.id);

        if (userRolesResponse.error) {
          authStore.state.error = 'Failed to fetch user roles';
          return;
        }

        // Create user object
        const userData = userResponse.data;
        const userRoles = userRolesResponse.data || [];

        const companies = userRoles.map((ur: any) => ur.company);
        const roles = userRoles.map((ur: any) => ur.role.name);

        authStore.state.user = {
          id: data.user.id,
          email: userData.email,
          firstName: userData.first_name,
          lastName: userData.last_name,
          companies,
          roles,
          currentCompany: companies.length === 1 ? companies[0] : undefined,
          currentRole: roles.length === 1 ? roles[0] : undefined
        };

        authStore.isAuthenticated = true;
        authStore.hasMultipleCompanies = companies.length > 1;
        authStore.hasMultipleRoles = roles.length > 1;
      } catch (err) {
        authStore.state.error = 'An unexpected error occurred';
      } finally {
        authStore.state.loading = false;
      }
    },

    // Mock logout method
    logout: async () => {
      await authStore.supabase.auth.signOut();
      authStore.state.user = null;
      authStore.isAuthenticated = false;
    },

    // Mock setCurrentCompany method
    setCurrentCompany: (company: Company) => {
      if (authStore.state.user) {
        authStore.state.user.currentCompany = company;
      }
    },

    // Mock setCurrentRole method
    setCurrentRole: (role: UserRole) => {
      if (authStore.state.user) {
        authStore.state.user.currentRole = role;
      }
    }
  };

  return { authStore, mockSupabase: authStore.supabase };
};

describe('Auth Store', () => {
  test('initial state', async () => {
    // Create a fresh pinia instance
    setActivePinia(createPinia());

    const authStore = useAuthStore();

    // Wait for the initialization to complete
    await new Promise(resolve => setTimeout(resolve, 50));

    expect(authStore.state.user).toBeNull();
    expect(authStore.state.loading).toBe(false);
    expect(authStore.state.error).toBeNull();
    expect(authStore.isAuthenticated).toBe(false);
  });

  test('login success', async () => {
    // Create a mock auth store
    const { authStore, mockSupabase } = createMockAuthStore();

    await authStore.login('<EMAIL>', 'password');

    // Check that Supabase was called correctly
    expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'password'
    });

    // Check that the store was updated correctly
    expect(authStore.state.loading).toBe(false);
    expect(authStore.state.error).toBeNull();
    expect(authStore.state.user).not.toBeNull();
    expect(authStore.state.user?.email).toBe('<EMAIL>');
    expect(authStore.state.user?.firstName).toBe('Test');
    expect(authStore.state.user?.lastName).toBe('User');
    expect(authStore.isAuthenticated).toBe(true);

    // Check that companies and roles were processed correctly
    expect(authStore.state.user?.companies).toHaveLength(1);
    expect(authStore.state.user?.companies[0]?.name).toBe('Test Company');
    expect(authStore.state.user?.roles).toHaveLength(1);
    expect(authStore.state.user?.roles[0]).toBe('admin');

    // Check that current company and role were set automatically
    expect(authStore.state.user?.currentCompany?.name).toBe('Test Company');
    expect(authStore.state.user?.currentRole).toBe('admin');
  });

  test('login failure - authentication error', async () => {
    // Create a mock auth store with authentication failure
    const { authStore, mockSupabase } = createMockAuthStore();

    // Mock authentication failure
    mockSupabase.auth.signInWithPassword.mockImplementation(() => Promise.resolve({
      data: { user: null },
      error: new Error('Invalid login credentials')
    }));

    await authStore.login('<EMAIL>', 'wrong-password');

    // Check that the store was updated correctly
    expect(authStore.state.loading).toBe(false);
    expect(authStore.state.error).toBe('Invalid login credentials');
    expect(authStore.state.user).toBeNull();
    expect(authStore.isAuthenticated).toBe(false);
  });

  test('login failure - unauthorized email', async () => {
    // Create a mock auth store
    const { authStore, mockSupabase } = createMockAuthStore();

    // Mock successful authentication but unauthorized email
    mockSupabase.auth.signInWithPassword.mockImplementation(() => Promise.resolve({
      data: { user: { id: 'test-user-id' } },
      error: null
    }));

    // Mock empty allowed_emails result
    mockSupabase.from.mockImplementation((table: string) => {
      if (table === 'allowed_emails') {
        return {
          select: () => ({
            eq: () => ({
              limit: () => Promise.resolve({
                data: [],
                error: null
              })
            })
          })
        };
      }

      return mockSupabase;
    });

    await authStore.login('<EMAIL>', 'password');

    // Check that the store was updated correctly
    expect(authStore.state.loading).toBe(false);
    expect(authStore.state.error).toBe('Access denied. Your email is not authorized to use this application.');
    expect(authStore.state.user).toBeNull();
    expect(authStore.isAuthenticated).toBe(false);

    // Check that signOut was called
    expect(mockSupabase.auth.signOut).toHaveBeenCalled();
  });

  test('logout', async () => {
    // Create a mock auth store
    const { authStore, mockSupabase } = createMockAuthStore();

    // First login
    await authStore.login('<EMAIL>', 'password');
    expect(authStore.isAuthenticated).toBe(true);

    // Then logout
    await authStore.logout();

    // Check that Supabase was called correctly
    expect(mockSupabase.auth.signOut).toHaveBeenCalled();

    // Check that the store was updated correctly
    expect(authStore.state.user).toBeNull();
    expect(authStore.isAuthenticated).toBe(false);
  });

  test('setCurrentCompany', async () => {
    // Create a mock auth store
    const { authStore } = createMockAuthStore();

    // First login
    await authStore.login('<EMAIL>', 'password');

    // Set a new current company
    const newCompany: Company = { id: 'company-2', name: 'New Company' };
    authStore.setCurrentCompany(newCompany);

    // Check that the current company was updated
    expect(authStore.state.user?.currentCompany).toEqual(newCompany);
  });

  test('setCurrentRole', async () => {
    // Create a mock auth store
    const { authStore } = createMockAuthStore();

    // First login
    await authStore.login('<EMAIL>', 'password');

    // Set a new current role
    const newRole: UserRole = 'designer';
    authStore.setCurrentRole(newRole);

    // Check that the current role was updated
    expect(authStore.state.user?.currentRole).toBe(newRole);
  });

  test('hasMultipleCompanies and hasMultipleRoles', async () => {
    // Create a mock auth store
    const { authStore, mockSupabase } = createMockAuthStore();

    // Mock multiple companies and roles
    mockSupabase.from.mockImplementation((table: string) => {
      if (table === 'user_roles') {
        return {
          select: () => ({
            eq: () => Promise.resolve({
              data: [
                {
                  role: { id: 'role-1', name: 'admin' },
                  company: { id: 'company-1', name: 'Test Company 1' }
                },
                {
                  role: { id: 'role-2', name: 'designer' },
                  company: { id: 'company-2', name: 'Test Company 2' }
                }
              ],
              error: null
            })
          })
        };
      }

      // Return default implementation for other tables
      if (table === 'allowed_emails') {
        return {
          select: () => ({
            eq: () => ({
              limit: () => Promise.resolve({
                data: [{ email: '<EMAIL>' }],
                error: null
              })
            })
          })
        };
      }

      if (table === 'users') {
        return {
          select: () => ({
            eq: () => ({
              single: () => Promise.resolve({
                data: {
                  id: 'test-user-id',
                  email: '<EMAIL>',
                  first_name: 'Test',
                  last_name: 'User'
                },
                error: null
              })
            })
          })
        };
      }

      return mockSupabase;
    });

    // Login
    await authStore.login('<EMAIL>', 'password');

    // Check computed properties
    expect(authStore.hasMultipleCompanies).toBe(true);
    expect(authStore.hasMultipleRoles).toBe(true);

    // Check that current company and role were not set automatically
    expect(authStore.state.user?.currentCompany).toBeUndefined();
    expect(authStore.state.user?.currentRole).toBeUndefined();
  });
});
