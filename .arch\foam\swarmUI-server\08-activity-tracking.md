# Step 8: Activity Tracking and Auto-Shutdown

This document outlines the implementation of activity tracking and auto-shutdown functionality to ensure that SwarmUI servers are automatically shut down when not in use, minimizing costs.

## 8.1 Create Activity Tracking Composable

First, create a composable function for tracking user activity:

```
src/
└── composables/
    └── useActivityTracker.ts
```

Implement the activity tracker in `src/composables/useActivityTracker.ts`:

```typescript
// useActivityTracker.ts
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { useQuasar } from 'quasar';
import { serverManagementService } from '../services/serverManagement';

export interface ActivityTrackerOptions {
  serverId: number;
  inactivityTimeout?: number; // in milliseconds
  warningTimeout?: number; // in milliseconds
  onInactivityWarning?: () => void;
  onInactivityTimeout?: () => void;
}

export function useActivityTracker(options: ActivityTrackerOptions) {
  const $q = useQuasar();
  const lastActivity = ref(Date.now());
  const inactivityTimer = ref<number | null>(null);
  const warningTimer = ref<number | null>(null);
  const warningShown = ref(false);
  
  const DEFAULT_INACTIVITY_TIMEOUT = 30 * 60 * 1000; // 30 minutes
  const DEFAULT_WARNING_TIMEOUT = 25 * 60 * 1000; // 25 minutes (5 minutes before shutdown)
  
  const inactivityTimeout = options.inactivityTimeout || DEFAULT_INACTIVITY_TIMEOUT;
  const warningTimeout = options.warningTimeout || DEFAULT_WARNING_TIMEOUT;
  
  function updateActivity() {
    lastActivity.value = Date.now();
    
    // If a warning was shown, hide it
    if (warningShown.value) {
      warningShown.value = false;
    }
    
    // Update server activity in the database
    if (options.serverId) {
      serverManagementService.updateServerActivity(options.serverId)
        .catch(error => console.error('Error updating server activity:', error));
    }
  }
  
  function checkInactivity() {
    const now = Date.now();
    const inactiveTime = now - lastActivity.value;
    
    // If warning threshold reached and warning not shown yet
    if (inactiveTime >= warningTimeout && !warningShown.value) {
      warningShown.value = true;
      
      if (options.onInactivityWarning) {
        options.onInactivityWarning();
      } else {
        // Default warning
        $q.notify({
          message: 'Your GPU server will be shut down soon due to inactivity',
          color: 'warning',
          icon: 'mdi-server-off',
          timeout: 0,
          actions: [
            { label: 'Keep Running', color: 'white', handler: updateActivity },
            { label: 'Shut Down Now', color: 'white', handler: () => {
              if (options.onInactivityTimeout) {
                options.onInactivityTimeout();
              }
            }}
          ]
        });
      }
    }
    
    // If inactivity threshold reached
    if (inactiveTime >= inactivityTimeout) {
      if (options.onInactivityTimeout) {
        options.onInactivityTimeout();
      } else {
        // Default action: stop the server
        serverManagementService.stopServer(options.serverId)
          .catch(error => console.error('Error stopping server:', error));
        
        $q.notify({
          message: 'GPU server has been shut down due to inactivity',
          color: 'info',
          icon: 'mdi-server-off'
        });
      }
      
      // Reset timers
      resetTimers();
    }
  }
  
  function resetTimers() {
    if (inactivityTimer.value) {
      clearInterval(inactivityTimer.value);
      inactivityTimer.value = null;
    }
    
    if (warningTimer.value) {
      clearInterval(warningTimer.value);
      warningTimer.value = null;
    }
  }
  
  function startTracking() {
    // Track user activity
    window.addEventListener('mousemove', updateActivity);
    window.addEventListener('keydown', updateActivity);
    window.addEventListener('click', updateActivity);
    window.addEventListener('touchstart', updateActivity);
    window.addEventListener('scroll', updateActivity);
    
    // Set up inactivity checker
    inactivityTimer.value = window.setInterval(checkInactivity, 60000); // Check every minute
    
    // Initial activity update
    updateActivity();
  }
  
  function stopTracking() {
    // Remove event listeners
    window.removeEventListener('mousemove', updateActivity);
    window.removeEventListener('keydown', updateActivity);
    window.removeEventListener('click', updateActivity);
    window.removeEventListener('touchstart', updateActivity);
    window.removeEventListener('scroll', updateActivity);
    
    // Clear timers
    resetTimers();
  }
  
  // Watch for changes to serverId
  watch(() => options.serverId, (newServerId, oldServerId) => {
    if (newServerId !== oldServerId) {
      // Reset activity tracking for the new server
      lastActivity.value = Date.now();
      warningShown.value = false;
    }
  });
  
  onMounted(() => {
    startTracking();
  });
  
  onUnmounted(() => {
    stopTracking();
  });
  
  return {
    lastActivity,
    updateActivity,
    startTracking,
    stopTracking
  };
}
```

## 8.2 Implement Server Auto-Shutdown Service

Create a service to handle server auto-shutdown:

```
src/
└── services/
    └── serverManagement/
        └── ServerAutoShutdownService.ts
```

Implement the auto-shutdown service in `src/services/serverManagement/ServerAutoShutdownService.ts`:

```typescript
// ServerAutoShutdownService.ts
import { supabase } from '../../boot/supabase';
import { runPodService } from './RunPodService';

export class ServerAutoShutdownService {
  private shutdownCheckInterval: number | null = null;
  private readonly INACTIVITY_THRESHOLD_MINUTES = 30;
  
  constructor() {}
  
  /**
   * Start the auto-shutdown service
   */
  start() {
    // Check for inactive servers every 5 minutes
    this.shutdownCheckInterval = window.setInterval(
      this.checkForInactiveServers.bind(this),
      5 * 60 * 1000
    );
    
    // Run an initial check
    this.checkForInactiveServers();
  }
  
  /**
   * Stop the auto-shutdown service
   */
  stop() {
    if (this.shutdownCheckInterval) {
      clearInterval(this.shutdownCheckInterval);
      this.shutdownCheckInterval = null;
    }
  }
  
  /**
   * Check for inactive servers and shut them down
   */
  private async checkForInactiveServers() {
    try {
      // Get all running servers that have been inactive for more than the threshold
      const thresholdTime = new Date();
      thresholdTime.setMinutes(thresholdTime.getMinutes() - this.INACTIVITY_THRESHOLD_MINUTES);
      
      const { data: inactiveServers, error } = await supabase
        .from('gpu_servers')
        .select('*')
        .eq('status', 'running')
        .lt('last_activity', thresholdTime.toISOString());
        
      if (error) {
        console.error('Error checking for inactive servers:', error);
        return;
      }
      
      // Shut down each inactive server
      for (const server of inactiveServers) {
        console.log(`Auto-shutting down inactive server: ${server.id}`);
        
        try {
          // Update server status to stopping
          await supabase.rpc('stop_server', {
            p_server_id: server.id
          });
          
          // Stop the RunPod instance
          await runPodService.stopPod(server.pod_id);
          
          // Update the server record
          await supabase
            .from('gpu_servers')
            .update({
              status: 'stopped',
              stopped_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
            .eq('id', server.id);
            
          console.log(`Successfully shut down server: ${server.id}`);
        } catch (shutdownError) {
          console.error(`Error shutting down server ${server.id}:`, shutdownError);
        }
      }
    } catch (error) {
      console.error('Error in checkForInactiveServers:', error);
    }
  }
}

export const serverAutoShutdownService = new ServerAutoShutdownService();
```

## 8.3 Implement Server Auto-Shutdown in Backend

For more reliable auto-shutdown, implement a server-side function that runs periodically:

```sql
-- Create a function to check for and shut down inactive servers
CREATE OR REPLACE FUNCTION auto_shutdown_inactive_servers()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_inactive_threshold TIMESTAMP WITH TIME ZONE;
  v_server RECORD;
  v_shutdown_count INTEGER := 0;
BEGIN
  -- Set the inactivity threshold (30 minutes)
  v_inactive_threshold := NOW() - INTERVAL '30 minutes';
  
  -- Find all running servers that have been inactive for more than the threshold
  FOR v_server IN
    SELECT * FROM gpu_servers
    WHERE status = 'running'
    AND last_activity < v_inactive_threshold
  LOOP
    -- Update server status to stopping
    PERFORM stop_server(v_server.id);
    
    -- Increment the shutdown count
    v_shutdown_count := v_shutdown_count + 1;
  END LOOP;
  
  RETURN v_shutdown_count;
END;
$$;
```

## 8.4 Set Up a Cron Job for Auto-Shutdown

Set up a cron job to run the auto-shutdown function periodically:

```sql
-- This would typically be set up in your database management system
-- or using a scheduled function in your backend
-- Example for PostgreSQL:

SELECT cron.schedule(
  'auto-shutdown-inactive-servers',
  '*/5 * * * *',  -- Run every 5 minutes
  $$SELECT auto_shutdown_inactive_servers()$$
);
```

## 8.5 Integrate Activity Tracking with Server Controls

Update the Server Controls component to use the activity tracker:

```vue
<template>
  <!-- Existing template -->
</template>

<script>
import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue';
import { useQuasar } from 'quasar';
import { serverManagementService } from '../../services/serverManagement';
import { useActivityTracker } from '../../composables/useActivityTracker';
import ServerConfigSelector from './ServerConfigSelector.vue';
import ServerStatus from './ServerStatus.vue';
import ServerUsageStats from './ServerUsageStats.vue';

export default {
  name: 'ServerControls',
  
  components: {
    ServerConfigSelector,
    ServerStatus,
    ServerUsageStats
  },
  
  setup() {
    const $q = useQuasar();
    const activeServer = ref(null);
    const serverStatus = ref('unknown');
    const starting = ref(false);
    const stopping = ref(false);
    const showServerStarting = ref(false);
    const showServerStopping = ref(false);
    const selectedConfig = ref(null);
    const statusCheckInterval = ref(null);
    
    const serverRunning = computed(() => {
      return activeServer.value && 
             (serverStatus.value === 'running' || 
              serverStatus.value === 'starting');
    });
    
    const showUsageStats = computed(() => {
      return activeServer.value !== null;
    });
    
    // Activity tracking
    const activityTracker = useActivityTracker({
      serverId: computed(() => activeServer.value?.id || 0).value,
      onInactivityWarning: () => {
        $q.notify({
          message: 'Your GPU server will be shut down in 5 minutes due to inactivity',
          color: 'warning',
          icon: 'mdi-server-off',
          timeout: 0,
          actions: [
            { label: 'Keep Running', color: 'white', handler: () => activityTracker.updateActivity() },
            { label: 'Shut Down Now', color: 'white', handler: () => stopServer() }
          ]
        });
      },
      onInactivityTimeout: () => {
        stopServer();
      }
    });
    
    // Watch for changes to the active server
    watch(() => activeServer.value, (newServer) => {
      if (newServer && newServer.id) {
        // Update the activity tracker with the new server ID
        activityTracker.updateActivity();
      }
    });
    
    async function checkServerStatus() {
      try {
        const server = await serverManagementService.getActiveServer();
        activeServer.value = server;
        serverStatus.value = server ? server.status : 'stopped';
        
        // If server was starting and is now running, close the dialog
        if (showServerStarting.value && serverStatus.value === 'running') {
          showServerStarting.value = false;
          starting.value = false;
          $q.notify({
            color: 'positive',
            message: 'Server is now running',
            icon: 'mdi-server-network'
          });
        }
        
        // If server was stopping and is now stopped, close the dialog
        if (showServerStopping.value && serverStatus.value === 'stopped') {
          showServerStopping.value = false;
          stopping.value = false;
          $q.notify({
            color: 'info',
            message: 'Server has been stopped',
            icon: 'mdi-server-off'
          });
        }
      } catch (error) {
        console.error('Error checking server status:', error);
      }
    }
    
    // Rest of the component implementation...
    
    return {
      // Existing return values...
      activityTracker
    };
  }
};
</script>
```

## 8.6 Implement User Preferences for Auto-Shutdown

Allow users to customize their auto-shutdown preferences:

```vue
<template>
  <q-card class="server-preferences">
    <q-card-section>
      <div class="text-h6">Server Preferences</div>
    </q-card-section>

    <q-card-section>
      <q-form @submit="savePreferences">
        <div class="row q-col-gutter-md">
          <div class="col-12 col-md-6">
            <q-input
              v-model.number="autoShutdownMinutes"
              type="number"
              label="Auto-shutdown after inactivity (minutes)"
              :rules="[
                val => val >= 5 || 'Minimum is 5 minutes',
                val => val <= 120 || 'Maximum is 120 minutes'
              ]"
              min="5"
              max="120"
              filled
            />
          </div>
          
          <div class="col-12 col-md-6">
            <q-select
              v-model="defaultConfiguration"
              :options="configOptions"
              label="Default server configuration"
              filled
            />
          </div>
        </div>
        
        <div class="row q-mt-md">
          <q-btn
            type="submit"
            color="primary"
            label="Save Preferences"
            :loading="saving"
          />
        </div>
      </q-form>
    </q-card-section>
  </q-card>
</template>

<script>
import { ref, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import { supabase } from '../../boot/supabase';
import { useAuthStore } from '../../stores/auth';
import { serverManagementService } from '../../services/serverManagement';

export default {
  name: 'ServerPreferences',
  
  setup() {
    const $q = useQuasar();
    const authStore = useAuthStore();
    const autoShutdownMinutes = ref(30);
    const defaultConfiguration = ref(null);
    const configOptions = ref([]);
    const saving = ref(false);
    
    async function loadPreferences() {
      try {
        const user = authStore.getUser();
        if (!user) return;
        
        // Load user preferences
        const { data: preferences, error } = await supabase
          .from('user_server_preferences')
          .select('*')
          .eq('user_id', user.id)
          .single();
          
        if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned"
          console.error('Error loading preferences:', error);
          return;
        }
        
        if (preferences) {
          autoShutdownMinutes.value = preferences.auto_shutdown_minutes;
          
          // Load server configurations
          const configs = await serverManagementService.getServerConfigurations();
          configOptions.value = configs.map(config => ({
            label: config.name,
            value: config.id
          }));
          
          // Set default configuration
          if (preferences.default_configuration_id) {
            defaultConfiguration.value = configOptions.value.find(
              opt => opt.value === preferences.default_configuration_id
            );
          } else if (configOptions.value.length > 0) {
            defaultConfiguration.value = configOptions.value[0];
          }
        }
      } catch (error) {
        console.error('Error in loadPreferences:', error);
      }
    }
    
    async function savePreferences() {
      try {
        saving.value = true;
        
        const user = authStore.getUser();
        if (!user) return;
        
        const { data: existing, error: checkError } = await supabase
          .from('user_server_preferences')
          .select('id')
          .eq('user_id', user.id)
          .single();
          
        const preferences = {
          user_id: user.id,
          auto_shutdown_minutes: autoShutdownMinutes.value,
          default_configuration_id: defaultConfiguration.value?.value || null,
          updated_at: new Date().toISOString()
        };
        
        if (existing) {
          // Update existing preferences
          const { error } = await supabase
            .from('user_server_preferences')
            .update(preferences)
            .eq('id', existing.id);
            
          if (error) {
            throw new Error('Failed to update preferences');
          }
        } else {
          // Insert new preferences
          const { error } = await supabase
            .from('user_server_preferences')
            .insert(preferences);
            
          if (error) {
            throw new Error('Failed to save preferences');
          }
        }
        
        $q.notify({
          color: 'positive',
          message: 'Preferences saved successfully',
          icon: 'mdi-check'
        });
      } catch (error) {
        console.error('Error saving preferences:', error);
        $q.notify({
          color: 'negative',
          message: 'Failed to save preferences: ' + error.message,
          icon: 'mdi-alert'
        });
      } finally {
        saving.value = false;
      }
    }
    
    onMounted(() => {
      loadPreferences();
    });
    
    return {
      autoShutdownMinutes,
      defaultConfiguration,
      configOptions,
      saving,
      savePreferences
    };
  }
};
</script>
```

## 8.7 Initialize Auto-Shutdown Service in App Startup

Initialize the auto-shutdown service when the app starts:

```typescript
// src/boot/server-management.ts
import { boot } from 'quasar/wrappers';
import { serverAutoShutdownService } from '../services/serverManagement/ServerAutoShutdownService';

export default boot(({ app }) => {
  // Start the auto-shutdown service
  serverAutoShutdownService.start();
  
  // Clean up on app unmount
  app.unmount(() => {
    serverAutoShutdownService.stop();
  });
});
```

Register the boot file in `quasar.conf.js`:

```javascript
boot: [
  // ... other boot files
  'server-management'
],
```

## Next Steps

Now that you have implemented activity tracking and auto-shutdown functionality, proceed to [Step 9: Image Storage in Supabase](./09-image-storage.md) to implement the storage of generated images in Supabase.
