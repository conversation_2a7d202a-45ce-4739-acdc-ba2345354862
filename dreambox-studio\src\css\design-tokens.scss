// Design Tokens - Single source of truth for design variables

// COLORS
// Primary palette
$primary: #1976D2;
$primary-light: #63a4ff;
$primary-dark: #004ba0;

// Secondary palette
$secondary: #26A69A;
$secondary-light: #64d8cb;
$secondary-dark: #00766c;

// Accent colors
$accent: #9C27B0;
$accent-light: #d05ce3;
$accent-dark: #6a0080;

// Semantic colors
$positive: #21BA45;
$negative: #C10015;
$info: #31CCEC;
$warning: #F2C037;

// Neutral palette
$dark: #1D1D1D;
$dark-page: #121212;
$light: #F5F5F5;
$light-page: #FFFFFF;

// TYPOGRAPHY
// Font families
$typography-font-family: 'Roboto', '-apple-system', 'Helvetica Neue', Helvetica, Arial, sans-serif;
$typography-heading-font-family: $typography-font-family;

// Font sizes
$font-size-xs: 0.75rem;    // 12px
$font-size-sm: 0.875rem;   // 14px
$font-size-md: 1rem;       // 16px
$font-size-lg: 1.125rem;   // 18px
$font-size-xl: 1.25rem;    // 20px
$font-size-xxl: 1.5rem;    // 24px

// Font weights
$font-weight-light: 300;
$font-weight-regular: 400;
$font-weight-medium: 500;
$font-weight-bold: 700;

// Line heights
$line-height-xs: 1;
$line-height-sm: 1.25;
$line-height-md: 1.5;
$line-height-lg: 1.75;
$line-height-xl: 2;

// SPACING
// Base spacing unit: 4px
$space-unit: 4px;
$space-xs: $space-unit;             // 4px
$space-sm: $space-unit * 2;         // 8px
$space-md: $space-unit * 4;         // 16px
$space-lg: $space-unit * 6;         // 24px
$space-xl: $space-unit * 10;        // 40px
$space-xxl: $space-unit * 16;       // 64px

// BREAKPOINTS
// Mobile-first breakpoints
$breakpoint-xs: 0;          // Extra small devices (portrait phones)
$breakpoint-sm: 600px;      // Small devices (landscape phones)
$breakpoint-md: 960px;      // Medium devices (tablets)
$breakpoint-lg: 1264px;     // Large devices (desktops)
$breakpoint-xl: 1904px;     // Extra large devices (large desktops)

// ELEVATION
// Shadow levels for elevation
$shadow-1: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
$shadow-2: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
$shadow-3: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
$shadow-4: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);
$shadow-5: 0 19px 38px rgba(0,0,0,0.30), 0 15px 12px rgba(0,0,0,0.22);

// BORDER RADIUS
$border-radius-sm: 2px;
$border-radius-md: 4px;
$border-radius-lg: 8px;
$border-radius-xl: 16px;
$border-radius-circle: 50%;

// TRANSITIONS
$transition-quick: 0.15s ease;
$transition-medium: 0.3s ease;
$transition-slow: 0.5s ease;

// Z-INDEX SCALE
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// PLATFORM-SPECIFIC ADJUSTMENTS
// These can be used with platform detection to adjust styles
$desktop-padding-base: $space-md;
$mobile-padding-base: $space-sm;
$electron-window-frame-height: 32px; // For Electron title bar
