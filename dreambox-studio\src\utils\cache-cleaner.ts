/**
 * Utility to clear browser cache and local storage
 */

/**
 * Clear all application cache and storage
 * This will force a reload of all components and assets
 */
export function clearAppCache(): void {
  try {
    // Clear localStorage
    localStorage.clear();
    
    // Set a flag to indicate cache was cleared
    localStorage.setItem('cache-cleared', Date.now().toString());
    
    console.log('Application cache cleared');
    
    // Reload the page to ensure all components are reloaded
    window.location.reload();
  } catch (error) {
    console.error('Failed to clear cache:', error);
  }
}

/**
 * Add a cache buster parameter to a URL
 * This forces the browser to reload the resource
 */
export function addCacheBuster(url: string): string {
  const separator = url.includes('?') ? '&' : '?';
  return `${url}${separator}cacheBuster=${Date.now()}`;
}
