# Keyboard Shortcuts Manager Proposal

## Overview

The Keyboard Shortcuts Manager will allow users to assign custom keyboard shortcuts to commands in our application. This feature will significantly enhance the user experience for desktop users by providing quick access to frequently used commands.

## Benefits

1. **Increased Productivity**: Users can execute commands without typing or clicking through menus
2. **Customization**: Different users have different workflows and preferences
3. **Accessibility**: Provides alternative ways to interact with the application
4. **Power User Appeal**: Advanced features like this attract and retain power users

## Implementation Approach

### 1. Shortcuts Registry

We'll create a Pinia store to manage keyboard shortcuts:

```typescript
// src/stores/keyboardShortcutsStore.ts
import { defineStore } from 'pinia';
import { useLocalStorage } from '@vueuse/core';

export interface ShortcutDefinition {
  id: string;
  command: string;
  keys: string;
  description: string;
  context?: string; // Optional context where the shortcut is applicable
}

export const useKeyboardShortcutsStore = defineStore('keyboardShortcuts', () => {
  // Store shortcuts in local storage for persistence
  const shortcuts = useLocalStorage<ShortcutDefinition[]>('dreambox-keyboard-shortcuts', []);
  
  // Default shortcuts that can be restored
  const defaultShortcuts: ShortcutDefinition[] = [
    { id: '1', command: 'toggleDarkMode', keys: 'Ctrl+Shift+D', description: 'Toggle dark mode' },
    { id: '2', command: 'toggleLeftDrawer', keys: 'Ctrl+B', description: 'Toggle left drawer' },
    { id: '3', command: 'toggleRightDrawer', keys: 'Ctrl+Shift+B', description: 'Toggle right drawer' },
    { id: '4', command: 'openDashboard', keys: 'Ctrl+Home', description: 'Go to dashboard' },
    { id: '5', command: 'logout', keys: 'Ctrl+Shift+L', description: 'Log out' },
  ];
  
  // Initialize with default shortcuts if empty
  if (shortcuts.value.length === 0) {
    shortcuts.value = [...defaultShortcuts];
  }
  
  // Add a new shortcut
  function addShortcut(shortcut: Omit<ShortcutDefinition, 'id'>) {
    const id = Date.now().toString();
    shortcuts.value.push({ ...shortcut, id });
  }
  
  // Update an existing shortcut
  function updateShortcut(id: string, shortcut: Partial<ShortcutDefinition>) {
    const index = shortcuts.value.findIndex(s => s.id === id);
    if (index !== -1) {
      shortcuts.value[index] = { ...shortcuts.value[index], ...shortcut };
    }
  }
  
  // Remove a shortcut
  function removeShortcut(id: string) {
    shortcuts.value = shortcuts.value.filter(s => s.id !== id);
  }
  
  // Reset to default shortcuts
  function resetToDefaults() {
    shortcuts.value = [...defaultShortcuts];
  }
  
  // Check if a key combination is already used
  function isShortcutUsed(keys: string, excludeId?: string): boolean {
    return shortcuts.value.some(s => s.keys === keys && s.id !== excludeId);
  }
  
  // Get shortcut by command
  function getShortcutByCommand(command: string): ShortcutDefinition | undefined {
    return shortcuts.value.find(s => s.command === command);
  }
  
  // Get shortcut by keys
  function getShortcutByKeys(keys: string): ShortcutDefinition | undefined {
    return shortcuts.value.find(s => s.keys === keys);
  }
  
  return {
    shortcuts,
    addShortcut,
    updateShortcut,
    removeShortcut,
    resetToDefaults,
    isShortcutUsed,
    getShortcutByCommand,
    getShortcutByKeys
  };
});
```

### 2. Global Shortcut Listener

We'll create a composable to listen for keyboard shortcuts globally:

```typescript
// src/composables/useKeyboardShortcuts.ts
import { onMounted, onUnmounted } from 'vue';
import { useKeyboardShortcutsStore } from 'src/stores/keyboardShortcutsStore';
import { mockWebSocket } from 'src/services/commands/mock-websocket';

export function useKeyboardShortcuts() {
  const shortcutsStore = useKeyboardShortcutsStore();
  
  // Convert key event to standardized format
  function formatKeyCombo(event: KeyboardEvent): string {
    const keys: string[] = [];
    
    if (event.ctrlKey) keys.push('Ctrl');
    if (event.shiftKey) keys.push('Shift');
    if (event.altKey) keys.push('Alt');
    if (event.metaKey) keys.push('Meta');
    
    // Add the key if it's not a modifier
    if (!['Control', 'Shift', 'Alt', 'Meta'].includes(event.key)) {
      keys.push(event.key);
    }
    
    return keys.join('+');
  }
  
  // Handle keyboard events
  function handleKeyDown(event: KeyboardEvent) {
    // Ignore if in an input field
    if (event.target instanceof HTMLInputElement || 
        event.target instanceof HTMLTextAreaElement) {
      return;
    }
    
    const keyCombo = formatKeyCombo(event);
    const shortcut = shortcutsStore.getShortcutByKeys(keyCombo);
    
    if (shortcut) {
      event.preventDefault();
      console.log(`Executing command: ${shortcut.command}`);
      mockWebSocket.simulateCommand(shortcut.command);
    }
  }
  
  // Set up and tear down event listeners
  onMounted(() => {
    window.addEventListener('keydown', handleKeyDown);
  });
  
  onUnmounted(() => {
    window.removeEventListener('keydown', handleKeyDown);
  });
  
  return {
    formatKeyCombo
  };
}
```

### 3. Shortcut Recorder Component

We'll create a component to record keyboard shortcuts:

```vue
<!-- src/components/settings/ShortcutRecorder.vue -->
<template>
  <div class="shortcut-recorder">
    <q-input
      v-model="displayValue"
      readonly
      outlined
      dense
      :placeholder="$t('settings.shortcuts.pressKeys')"
      @focus="startRecording"
      @blur="stopRecording"
      @keydown.stop.prevent="recordKeyCombo"
      :class="{ 'recording': isRecording }"
    >
      <template v-slot:append>
        <q-icon 
          v-if="modelValue" 
          name="close" 
          class="cursor-pointer" 
          @click.stop="clearShortcut" 
        />
      </template>
    </q-input>
    <div v-if="error" class="text-negative q-mt-xs text-caption">
      {{ error }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useKeyboardShortcutsStore } from 'src/stores/keyboardShortcutsStore';
import { useKeyboardShortcuts } from 'src/composables/useKeyboardShortcuts';
import { useI18n } from 'vue-i18n';

const props = defineProps<{
  modelValue: string;
  excludeId?: string;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void;
}>();

const { t } = useI18n();
const shortcutsStore = useKeyboardShortcutsStore();
const { formatKeyCombo } = useKeyboardShortcuts();

const isRecording = ref(false);
const displayValue = computed(() => props.modelValue || '');
const error = ref('');

// Start recording keyboard input
function startRecording() {
  isRecording.value = true;
  error.value = '';
}

// Stop recording keyboard input
function stopRecording() {
  isRecording.value = false;
}

// Clear the current shortcut
function clearShortcut() {
  emit('update:modelValue', '');
  error.value = '';
}

// Record key combinations
function recordKeyCombo(event: KeyboardEvent) {
  if (!isRecording.value) return;
  
  const keyCombo = formatKeyCombo(event);
  
  // Validate the key combo
  if (keyCombo.split('+').length < 2) {
    error.value = t('settings.shortcuts.needsModifier');
    return;
  }
  
  // Check if this shortcut is already used
  if (shortcutsStore.isShortcutUsed(keyCombo, props.excludeId)) {
    error.value = t('settings.shortcuts.alreadyUsed');
    return;
  }
  
  emit('update:modelValue', keyCombo);
  error.value = '';
}
</script>

<style lang="scss" scoped>
.shortcut-recorder {
  .recording {
    background-color: rgba(0, 0, 0, 0.05);
    
    .body--dark & {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
}
</style>
```

### 4. Keyboard Shortcuts Manager UI

We'll create a component to manage keyboard shortcuts:

```vue
<!-- src/components/settings/KeyboardShortcutsManager.vue -->
<template>
  <div class="keyboard-shortcuts-manager">
    <div class="q-mb-md">
      <div class="text-h6">{{ $t('settings.shortcuts.title') }}</div>
      <p>{{ $t('settings.shortcuts.description') }}</p>
      
      <q-btn 
        color="primary" 
        :label="$t('settings.shortcuts.resetDefaults')" 
        icon="restore"
        @click="confirmReset"
        class="q-mb-md"
      />
    </div>
    
    <q-list bordered separator>
      <q-item v-for="shortcut in shortcuts" :key="shortcut.id">
        <q-item-section>
          <q-item-label>{{ shortcut.description }}</q-item-label>
          <q-item-label caption>{{ shortcut.command }}</q-item-label>
        </q-item-section>
        
        <q-item-section side>
          <shortcut-recorder 
            v-model="shortcut.keys" 
            :exclude-id="shortcut.id"
            @update:model-value="updateShortcut(shortcut.id, $event)"
          />
        </q-item-section>
      </q-item>
    </q-list>
    
    <q-dialog v-model="resetConfirmDialog">
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar icon="warning" color="warning" text-color="white" />
          <span class="q-ml-sm">{{ $t('settings.shortcuts.resetConfirm') }}</span>
        </q-card-section>
        
        <q-card-actions align="right">
          <q-btn flat :label="$t('common.cancel')" color="primary" v-close-popup />
          <q-btn flat :label="$t('common.confirm')" color="negative" @click="resetToDefaults" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useKeyboardShortcutsStore } from 'src/stores/keyboardShortcutsStore';
import ShortcutRecorder from './ShortcutRecorder.vue';

const shortcutsStore = useKeyboardShortcutsStore();
const shortcuts = computed(() => shortcutsStore.shortcuts);
const resetConfirmDialog = ref(false);

// Update a shortcut
function updateShortcut(id: string, keys: string) {
  shortcutsStore.updateShortcut(id, { keys });
}

// Show reset confirmation dialog
function confirmReset() {
  resetConfirmDialog.value = true;
}

// Reset shortcuts to defaults
function resetToDefaults() {
  shortcutsStore.resetToDefaults();
}
</script>
```

### 5. Integration with Main Layout

We'll integrate the keyboard shortcuts listener into the main layout:

```vue
<!-- src/layouts/MainLayout.vue (partial) -->
<script setup lang="ts">
// ... existing imports
import { useKeyboardShortcuts } from 'src/composables/useKeyboardShortcuts';

// ... existing code

// Initialize keyboard shortcuts
useKeyboardShortcuts();

// ... rest of the code
</script>
```

### 6. Add to Settings Panel

We'll add the keyboard shortcuts manager to the settings panel:

```vue
<!-- src/components/settings/SettingsPanel.vue (partial) -->
<template>
  <div class="settings-panel">
    <q-tabs
      v-model="activeTab"
      dense
      class="text-grey"
      active-color="primary"
      indicator-color="primary"
      align="left"
      narrow-indicator
    >
      <q-tab name="general" :label="$t('settings.tabs.general')" />
      <q-tab name="appearance" :label="$t('settings.tabs.appearance')" />
      <q-tab name="shortcuts" :label="$t('settings.tabs.shortcuts')" />
      <!-- Other tabs -->
    </q-tabs>

    <q-separator />

    <q-tab-panels v-model="activeTab" animated>
      <!-- General settings panel -->
      <q-tab-panel name="general">
        <!-- General settings content -->
      </q-tab-panel>
      
      <!-- Appearance settings panel -->
      <q-tab-panel name="appearance">
        <!-- Appearance settings content -->
      </q-tab-panel>
      
      <!-- Keyboard shortcuts panel -->
      <q-tab-panel name="shortcuts">
        <keyboard-shortcuts-manager />
      </q-tab-panel>
      
      <!-- Other panels -->
    </q-tab-panels>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import KeyboardShortcutsManager from './KeyboardShortcutsManager.vue';

const activeTab = ref('general');
</script>
```

### 7. Add Translation Keys

We'll add the necessary translation keys:

```typescript
// src/i18n/en-US.ts (partial)
export default {
  // ... existing translations
  settings: {
    // ... existing settings translations
    tabs: {
      // ... existing tabs
      shortcuts: 'Keyboard Shortcuts'
    },
    shortcuts: {
      title: 'Keyboard Shortcuts',
      description: 'Customize keyboard shortcuts for frequently used commands.',
      resetDefaults: 'Reset to Defaults',
      resetConfirm: 'Are you sure you want to reset all shortcuts to their default values?',
      pressKeys: 'Press keys...',
      needsModifier: 'Shortcut must include a modifier key (Ctrl, Alt, Shift, or Meta)',
      alreadyUsed: 'This shortcut is already assigned to another command'
    }
  }
};
```

```typescript
// src/i18n/hr-HR.ts (partial)
export default {
  // ... existing translations
  settings: {
    // ... existing settings translations
    tabs: {
      // ... existing tabs
      shortcuts: 'Tipkovnički prečaci'
    },
    shortcuts: {
      title: 'Tipkovnički prečaci',
      description: 'Prilagodite tipkovničke prečace za često korištene naredbe.',
      resetDefaults: 'Vrati na zadano',
      resetConfirm: 'Jeste li sigurni da želite vratiti sve prečace na njihove zadane vrijednosti?',
      pressKeys: 'Pritisnite tipke...',
      needsModifier: 'Prečac mora uključivati modifikacijsku tipku (Ctrl, Alt, Shift ili Meta)',
      alreadyUsed: 'Ovaj prečac je već dodijeljen drugoj naredbi'
    }
  }
};
```

## Technical Considerations

### 1. Conflict Resolution

- The `isShortcutUsed` function in the store prevents duplicate shortcuts
- The ShortcutRecorder component validates shortcuts and shows error messages
- We ignore shortcuts when focus is in input fields to avoid conflicts

### 2. Context Awareness

- The ShortcutDefinition interface includes an optional `context` property
- This can be used to enable/disable shortcuts based on the current view/state
- Future enhancement: Filter active shortcuts based on current route or app state

### 3. Documentation

- Tooltips can be added to show keyboard shortcuts for actions
- A help dialog can be added to show all available shortcuts

## Implementation Plan

### Phase 1: Core Functionality

1. Create the keyboard shortcuts store
2. Implement the global shortcut listener
3. Create the shortcut recorder component
4. Build the keyboard shortcuts manager UI
5. Integrate with the main layout

### Phase 2: Enhancements

1. Add context awareness to shortcuts
2. Implement shortcut hints in tooltips
3. Create a keyboard shortcuts help dialog
4. Add more default shortcuts for common actions

### Phase 3: Advanced Features

1. Allow multiple shortcuts per command
2. Add support for sequence-based shortcuts (e.g., "g" then "d" for "Go to Dashboard")
3. Implement shortcut categories for better organization
4. Add import/export functionality for sharing shortcut configurations

## Conclusion

The Keyboard Shortcuts Manager will significantly enhance the user experience for desktop users by providing quick access to frequently used commands. The implementation is straightforward and can be integrated with the existing command system.

By allowing users to customize their shortcuts, we empower them to work more efficiently and tailor the application to their specific needs and preferences.
