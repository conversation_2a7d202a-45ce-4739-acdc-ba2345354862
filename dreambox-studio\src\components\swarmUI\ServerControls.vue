<template>
  <div class="server-controls">
    <q-card class="q-mb-md">
      <q-card-section>
        <div class="text-h6">SwarmUI Server</div>
        <div class="text-subtitle2">Manage your image generation server</div>
      </q-card-section>

      <q-card-section>
        <div class="row items-center q-mb-md">
          <div class="col">{{ t('swarmUI.serverStatus') }}:</div>
          <div class="col-auto">
            <q-badge
              :color="statusColor"
              :label="t('swarmUI.' + serverStatus.status)"
              class="q-px-sm"
            />
          </div>
        </div>

        <div v-if="serverStatus.message" class="q-mb-md">
          {{ serverStatus.message }}
        </div>

        <div v-if="serverStatus.apiEndpoint" class="q-mb-md text-caption">
          API Endpoint: {{ serverStatus.apiEndpoint }}
        </div>

        <div class="row q-gutter-sm">
          <create-run-pod-button v-if="!isServerRunning && serverStatus.status !== 'starting'" />
          <q-btn
            v-if="isServerRunning"
            color="negative"
            :label="t('swarmUI.stopServer')"
            :loading="isStopping"
            @click="stopServer"
          />
          <q-btn
            v-if="isServerRunning"
            color="secondary"
            :label="t('swarmUI.testConnection')"
            :loading="isTesting"
            @click="testConnection"
          />
        </div>
      </q-card-section>
    </q-card>

    <q-card v-if="isServerRunning">
      <q-card-section>
        <div class="text-h6">Generate Test Image</div>
        <div class="text-subtitle2">Test image generation with SwarmUI</div>
      </q-card-section>

      <q-card-section>
        <q-form @submit="generateImage">
          <q-input
            v-model="generationParams.prompt"
            :label="t('swarmUI.prompt')"
            filled
            class="q-mb-md"
            :rules="[val => !!val || 'Prompt is required']"
          />

          <q-input
            v-model="generationParams.negative_prompt"
            :label="t('swarmUI.negativePrompt')"
            filled
            class="q-mb-md"
          />

          <div class="row q-col-gutter-md q-mb-md">
            <div class="col-12 col-sm-6">
              <q-select
                v-model="generationParams.model"
                :options="modelOptions"
                label="Model"
                filled
                :rules="[val => !!val || 'Model is required']"
              />
            </div>
            <div class="col-12 col-sm-6">
              <q-select
                v-model="generationParams.sampler"
                :options="samplerOptions"
                label="Sampler"
                filled
                :rules="[val => !!val || 'Sampler is required']"
              />
            </div>
          </div>

          <div class="row q-col-gutter-md q-mb-md">
            <div class="col-12 col-sm-6">
              <q-input
                v-model.number="generationParams.width"
                type="number"
                label="Width"
                filled
                :rules="[val => val > 0 || 'Width must be positive']"
              />
            </div>
            <div class="col-12 col-sm-6">
              <q-input
                v-model.number="generationParams.height"
                type="number"
                label="Height"
                filled
                :rules="[val => val > 0 || 'Height must be positive']"
              />
            </div>
          </div>

          <div class="row q-col-gutter-md q-mb-md">
            <div class="col-12 col-sm-6">
              <q-input
                v-model.number="generationParams.steps"
                type="number"
                label="Steps"
                filled
                :rules="[val => val > 0 || 'Steps must be positive']"
              />
            </div>
            <div class="col-12 col-sm-6">
              <q-input
                v-model.number="generationParams.cfg_scale"
                type="number"
                label="CFG Scale"
                filled
                :rules="[val => val > 0 || 'CFG Scale must be positive']"
              />
            </div>
          </div>

          <div class="row q-col-gutter-md q-mb-md">
            <div class="col-12 col-sm-6">
              <q-input
                v-model.number="generationParams.seed"
                type="number"
                label="Seed (optional)"
                filled
              />
            </div>
            <div class="col-12 col-sm-6">
              <q-select
                v-model="generationParams.scheduler"
                :options="schedulerOptions"
                label="Scheduler (optional)"
                filled
              />
            </div>
          </div>

          <div class="row q-gutter-sm">
            <q-btn
              type="submit"
              color="primary"
              :label="t('swarmUI.generateImage')"
              :loading="isGenerating"
            />
            <q-btn
              type="reset"
              flat
              :label="t('swarmUI.reset')"
              @click="resetForm"
            />
          </div>
        </q-form>
      </q-card-section>
    </q-card>

    <q-dialog v-model="showGeneratedImage">
      <q-card style="width: 700px; max-width: 90vw;">
        <q-card-section class="row items-center">
          <div class="text-h6">Generated Image</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section>
          <div v-if="generationResult && generationResult.images && generationResult.images.length > 0">
            <q-img
              :src="generationResult.images?.[0]?.url || ''"
              spinner-color="primary"
              style="max-height: 70vh;"
            />
            <div class="q-mt-md">
              <q-btn
                color="primary"
                :label="t('swarmUI.saveToS3')"
                :loading="isSaving"
                @click="saveToS3"
              />
            </div>
          </div>
          <div v-else-if="generationError">
            <q-banner class="bg-negative text-white">
              {{ generationError }}
            </q-banner>
          </div>
          <div v-else>
            <q-spinner
              color="primary"
              size="3em"
              class="q-ma-md"
            />
            <div>Generating image...</div>
          </div>
        </q-card-section>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import { swarmUIService } from 'src/services/swarmUI';
import type { ServerStatus, SwarmUIGenerationParams, SwarmUIGenerationResponse } from 'src/services/swarmUI/types';
import CreateRunPodButton from './CreateRunPodButton.vue';

const $q = useQuasar();
const { t } = useI18n();

// Server status
const serverStatus = ref<ServerStatus>({ status: 'no-server' });
const isStopping = ref(false);
const isTesting = ref(false);

// Image generation
const isGenerating = ref(false);
const isSaving = ref(false);
const generationResult = ref<SwarmUIGenerationResponse | null>(null);
const generationError = ref<string | null>(null);
const showGeneratedImage = ref(false);

// Models and samplers
const models = ref<string[]>([]);
const samplers = ref<string[]>([]);
const schedulers = ref<string[]>([]);

// Form data
const generationParams = ref<SwarmUIGenerationParams>({
  prompt: '',
  negative_prompt: 'ugly, deformed, noisy, blurry, distorted, out of focus',
  model: 'Flux Schnell',
  width: 1024,
  height: 1024,
  steps: 30,
  cfg_scale: 7.0,
  sampler: 'dpmpp_2m',
  batch_size: 1,
  seed: undefined,
  scheduler: 'karras'
} as SwarmUIGenerationParams);

// Computed properties
const isServerRunning = computed(() => {
  return serverStatus.value.status === 'running';
});

const statusColor = computed(() => {
  switch (serverStatus.value.status) {
    case 'running': return 'positive';
    case 'starting': return 'warning';
    case 'stopping': return 'warning';
    case 'error': return 'negative';
    default: return 'grey';
  }
});

// We're now using i18n directly in the template with:
// $t('swarmUI.' + serverStatus.status)

const modelOptions = computed(() => {
  return models.value.length > 0 ? models.value : ['Flux Schnell'];
});

const samplerOptions = computed(() => {
  return samplers.value.length > 0 ? samplers.value : ['dpmpp_2m', 'euler_a', 'euler', 'ddim'];
});

const schedulerOptions = computed(() => {
  return schedulers.value.length > 0 ? schedulers.value : ['karras', 'normal', 'simple', 'sgm_uniform'];
});

// Methods
async function updateServerStatus() {
  serverStatus.value = await swarmUIService.checkServerStatus();

  // If server is running, fetch models and samplers
  if (isServerRunning.value) {
    await fetchServerInfo();
  }
}

// startServer function removed - now using CreateRunPodButton component instead

async function stopServer() {
  isStopping.value = true;
  try {
    const success = await swarmUIService.stopServer();
    if (success) {
      $q.notify({
        color: 'positive',
        message: t('swarmUI.stopping'),
        icon: 'cloud_off'
      });
    } else {
      $q.notify({
        color: 'negative',
        message: t('swarmUI.errorCreatingServer'),
        icon: 'error'
      });
    }
    await updateServerStatus();
  } catch (error) {
    $q.notify({
      color: 'negative',
      message: `Error stopping server: ${error instanceof Error ? error.message : 'Unknown error'}`,
      icon: 'error'
    });
  } finally {
    isStopping.value = false;
  }
}

async function testConnection() {
  isTesting.value = true;
  try {
    const info = await swarmUIService.getServerInfo();
    if (info) {
      $q.notify({
        color: 'positive',
        message: `Connected to SwarmUI v${info.version}`,
        icon: 'check_circle'
      });
    } else {
      $q.notify({
        color: 'negative',
        message: t('swarmUI.errorCreatingServer'),
        icon: 'error'
      });
    }
  } catch (error) {
    $q.notify({
      color: 'negative',
      message: `Connection error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      icon: 'error'
    });
  } finally {
    isTesting.value = false;
  }
}

async function fetchServerInfo() {
  try {
    const info = await swarmUIService.getServerInfo();
    if (info) {
      models.value = info.models.map(model => model.name);
      samplers.value = info.samplers;
      schedulers.value = info.schedulers;
    }
  } catch (error) {
    console.error('Error fetching server info:', error);
  }
}

async function generateImage() {
  isGenerating.value = true;
  generationError.value = null;
  generationResult.value = null;
  showGeneratedImage.value = true;

  try {
    const result = await swarmUIService.generateImage(generationParams.value);
    if (result) {
      generationResult.value = result;

      // Poll for status if not completed
      if (result.status !== 'completed') {
        pollGenerationStatus(result.id);
      }
    } else {
      generationError.value = t('swarmUI.generationError');
    }
  } catch (error) {
    generationError.value = error instanceof Error ? error.message : 'Unknown error';
  } finally {
    isGenerating.value = false;
  }
}

function pollGenerationStatus(generationId: string): void {
  const maxAttempts = 30; // 30 attempts * 2 seconds = 60 seconds max wait
  let attempts = 0;

  const poll = async (): Promise<void> => {
    if (attempts >= maxAttempts) {
      generationError.value = t('swarmUI.generationError');
      return;
    }

    attempts++;

    try {
      const status = await swarmUIService.getGenerationStatus(generationId);
      if (!status) {
        setTimeout(() => { void poll(); }, 2000);
        return;
      }

      if (status.status === 'completed') {
        generationResult.value = status;
      } else if (status.status === 'failed') {
        generationError.value = status.error || t('swarmUI.generationError');
      } else {
        // Still processing, poll again
        setTimeout(() => { void poll(); }, 2000);
      }
    } catch (error) {
      console.error('Error polling generation status:', error);
      setTimeout(() => { void poll(); }, 2000);
    }
  };

  // Start polling
  setTimeout(() => { void poll(); }, 2000);
}

async function saveToS3() {
  if (!generationResult.value || !generationResult.value.images || generationResult.value.images.length === 0) {
    $q.notify({
      color: 'negative',
      message: t('swarmUI.failedToSave'),
      icon: 'error'
    });
    return;
  }

  isSaving.value = true;
  try {
    const image = generationResult.value?.images?.[0];
    if (!image || !image.url) {
      $q.notify({
        color: 'negative',
        message: t('swarmUI.failedToSave'),
        icon: 'error'
      });
      return;
    }

    const metadata = {
      prompt: generationResult.value?.params?.prompt || '',
      negative_prompt: generationResult.value?.params?.negative_prompt || '',
      model: generationResult.value?.params?.model || '',
      width: generationResult.value?.params?.width || 0,
      height: generationResult.value?.params?.height || 0,
      steps: generationResult.value?.params?.steps || 0,
      cfg_scale: generationResult.value?.params?.cfg_scale || 0,
      sampler: generationResult.value?.params?.sampler || '',
      seed: image.seed || 0
    };

    const imageId = await swarmUIService.saveGeneratedImage(image.url, metadata);
    if (imageId) {
      $q.notify({
        color: 'positive',
        message: t('swarmUI.imageSaved'),
        icon: 'check_circle'
      });
      showGeneratedImage.value = false;
    } else {
      $q.notify({
        color: 'negative',
        message: t('swarmUI.failedToSave'),
        icon: 'error'
      });
    }
  } catch (error) {
    $q.notify({
      color: 'negative',
      message: `Error saving image: ${error instanceof Error ? error.message : 'Unknown error'}`,
      icon: 'error'
    });
  } finally {
    isSaving.value = false;
  }
}

function resetForm() {
  generationParams.value = {
    prompt: '',
    negative_prompt: 'ugly, deformed, noisy, blurry, distorted, out of focus',
    model: 'Flux Schnell',
    width: 1024,
    height: 1024,
    steps: 30,
    cfg_scale: 7.0,
    sampler: 'dpmpp_2m',
    batch_size: 1,
    seed: undefined,
    scheduler: 'karras'
  } as SwarmUIGenerationParams;
}

// Lifecycle hooks
onMounted(() => {
  // Initial status check
  void updateServerStatus();

  // Set up interval to check server status
  const intervalId = setInterval(() => { void updateServerStatus(); }, 10000);

  // Clean up interval on component unmount
  onBeforeUnmount(() => {
    if (intervalId) {
      clearInterval(intervalId);
    }

    // Also stop the service's status check
    if (swarmUIService.statusCheckInterval) {
      swarmUIService.stopStatusCheck();
    }
  });
});

// Additional cleanup is handled in the onMounted callback
</script>

<style scoped>
.server-controls {
  max-width: 800px;
  margin: 0 auto;
}
</style>
