apt-get update && \
apt-get install -y git wget curl aria2 libgl1-mesa-glx libglib2.0-0 python3-pip python3-dev build-essential && \
# Upgrade pip to latest version
pip install --root-user-action=ignore --upgrade pip && \
cd /workspace && \
# Install pip packages for Hugging Face downloads
pip install --root-user-action=ignore huggingface_hub ipywidgets hf_transfer && \
pip install --root-user-action=ignore --upgrade huggingface_hub && \
export HF_HUB_ENABLE_HF_TRANSFER=1 && \
# Install ComfyUI with minimal dependencies
git clone https://github.com/comfyanonymous/ComfyUI.git && \
cd ComfyUI && \
pip install --root-user-action=ignore torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118 && \
pip install --root-user-action=ignore -r requirements.txt && \
mkdir -p models/checkpoints && \
cd models/checkpoints && \
# Download a truly small model (about 1.4GB) using Hugging Face's faster transfer tool
python -c "from huggingface_hub import hf_hub_download; hf_hub_download('runwayml/stable-diffusion-v1-5', 'v1-5-pruned-emaonly.safetensors', local_dir='.', local_dir_use_symlinks=False)" && \
mv v1-5-pruned-emaonly.safetensors sd_v1_5.safetensors && \
cd /workspace && \
# Create model directories
mkdir -p models/diffusion_models models/clip models/vae && \
# Download models using Hugging Face
cd /workspace/models/diffusion_models && \
python -c "from huggingface_hub import hf_hub_download; hf_hub_download('Kijai/flux-fp8', 'flux1-schnell-fp8-e4m3fn.safetensors', local_dir='.', local_dir_use_symlinks=False)" && \
mv flux1-schnell-fp8-e4m3fn.safetensors FLUX1-Schnell-FP8.safetensors && \
cd ../clip && \
python -c "from huggingface_hub import hf_hub_download; hf_hub_download('OwlMaster/SD3New', 't5xxl_fp8_e4m3fn_scaled.safetensors', local_dir='.', local_dir_use_symlinks=False)" && \
mv t5xxl_fp8_e4m3fn_scaled.safetensors t5xxl_enconly.safetensors && \
cd ../vae && \
aria2c --file-allocation=none --continue=true -x 16 -s 16 -k 1M "https://huggingface.co/stabilityai/sdxl-vae/resolve/main/sdxl_vae.safetensors" -o "ae.safetensors" && \
cd /workspace && \
git clone https://github.com/mcmonkeyprojects/SwarmUI && \
cd SwarmUI/launchtools && \
rm -f dotnet-install.sh && \
wget https://dot.net/v1/dotnet-install.sh -O dotnet-install.sh && \
chmod +x dotnet-install.sh && \
./dotnet-install.sh --channel 8.0 --runtime aspnetcore && \
./dotnet-install.sh --channel 8.0 && \
export DOTNET_ROOT=$HOME/.dotnet && \
export PATH=$PATH:$HOME/.dotnet:$HOME/.dotnet/tools && \
echo 'export DOTNET_ROOT=$HOME/.dotnet' >> ~/.bashrc && \
echo 'export PATH=$PATH:$HOME/.dotnet:$HOME/.dotnet/tools' >> ~/.bashrc && \
cd /workspace && \
mkdir -p swarmui_data outputs && \
# Create SwarmUI model directories
mkdir -p SwarmUI/Models/Stable-Diffusion && \
mkdir -p SwarmUI/Models/clip && \
mkdir -p SwarmUI/Models/vae && \

# Create symbolic links for SwarmUI to find models
ln -sf /workspace/models/diffusion_models/FLUX1-Schnell-FP8.safetensors /workspace/SwarmUI/Models/Stable-Diffusion/flux1-schnell-fp8.safetensors && \
ln -sf /workspace/models/vae/ae.safetensors /workspace/SwarmUI/Models/Stable-Diffusion/flux1-schnell-fp8.vae.safetensors && \
ln -sf /workspace/models/clip/t5xxl_enconly.safetensors /workspace/SwarmUI/Models/Stable-Diffusion/flux1-schnell-fp8.clip.safetensors && \

# Create symbolic links for CLIP models specifically (to prevent re-downloading)
ln -sf /workspace/models/clip/t5xxl_enconly.safetensors /workspace/SwarmUI/Models/clip/t5xxl_enconly.safetensors && \

# Create symbolic links for ComfyUI to find models
ln -sf /workspace/ComfyUI/models/checkpoints/sd_v1_5.safetensors /workspace/SwarmUI/Models/Stable-Diffusion/sd_v1_5.safetensors && \
ln -sf /workspace/models/clip/t5xxl_enconly.safetensors /workspace/ComfyUI/models/clip/t5xxl_enconly.safetensors && \
ln -sf /workspace/models/vae/ae.safetensors /workspace/ComfyUI/models/vae/ae.safetensors && \
mkdir -p ~/.swarmui && \
API_KEY=$(openssl rand -hex 16) && \
echo "Your SwarmUI API key is: $API_KEY" && \
echo '{
  "BackendUrl": "http://0.0.0.0:7801",
  "FrontendUrl": "http://0.0.0.0:7800",
  "ModelRoot": "/workspace/models",
  "OutputsPath": "/workspace/outputs",
  "DisableUpdateCheck": true,
  "AutoInstall": {
    "Torch": false,
    "Comfy": false,
    "Models": false
  },
  "Auth": {
    "Enabled": true,
    "ApiKey": "'$API_KEY'"
  },
  "ComfyUI": {
    "Path": "/workspace/ComfyUI",
    "Url": "http://127.0.0.1:7860",
    "EnableWorkflows": true,
    "ModelPaths": {
      "checkpoints": "/workspace/ComfyUI/models/checkpoints",
      "clip": "/workspace/models/clip",
      "vae": "/workspace/models/vae"
    }
  }
}' > ~/.swarmui/config.json && \
echo '{
  "checkpoints": [
    {
      "name": "Flux Schnell",
      "path": "/workspace/models/diffusion_models/FLUX1-Schnell-FP8.safetensors",
      "vae": "/workspace/models/vae/ae.safetensors",
      "clip": "/workspace/models/clip/t5xxl_enconly.safetensors",
      "config": "configs/stable-diffusion/sdxl/sdxl_1_0.yaml",
      "default": true
    }
  ]
}' > ~/.swarmui/models.json && \
echo '#!/bin/bash
export DOTNET_ROOT=$HOME/.dotnet
export PATH=$PATH:$HOME/.dotnet:$HOME/.dotnet/tools
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128
export CUDA_LAUNCH_BLOCKING=0
export CUDA_VISIBLE_DEVICES=0

# Start ComfyUI in the background
cd /workspace/ComfyUI
python -c "import torch; torch.backends.cuda.matmul.allow_tf32 = True; torch.backends.cudnn.allow_tf32 = True"
nohup python main.py --listen 0.0.0.0 --port 7860 > /workspace/comfyui.log 2>&1 &

# Wait for ComfyUI to start
echo "Starting ComfyUI and waiting for it to initialize..."
sleep 15

# Start SwarmUI
cd /workspace/SwarmUI
./launch-linux.sh --launch_mode none --host 0.0.0.0 --comfy_path /workspace/ComfyUI
' > /workspace/start_swarmui.sh && \
chmod +x /workspace/start_swarmui.sh && \
echo "Installation complete! Starting SwarmUI automatically..." && \
/workspace/start_swarmui.sh


# /workspace/start_swarmui.sh
# /workspace/ComfyUI/main.py (this is backend)