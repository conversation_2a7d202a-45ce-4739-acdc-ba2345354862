# Supabase Setup for SwarmUI Integration

This document provides instructions for setting up the necessary Supabase resources for the SwarmUI integration.

## 1. Create Edge Functions

### 1.1 Create swarmui-server Edge Function

1. Go to your Supabase dashboard: https://app.supabase.com/
2. Select your project (ppzaqhuaqzjofsorlmbn)
3. Navigate to "Edge Functions" in the left sidebar
4. Click "Create a new function"
5. Name it "swarmui-server"
6. Click "Create function"
7. In the editor, paste the code from `supabase/functions/swarmui-server/index.ts`
8. Click "Deploy"

### 1.2 Create swarmui-generation Edge Function

1. Click "Create a new function"
2. Name it "swarmui-generation"
3. Click "Create function"
4. In the editor, paste the code from `supabase/functions/swarmui-generation/index.ts`
5. Click "Deploy"

### 1.3 Create image-management Edge Function

1. Click "Create a new function"
2. Name it "image-management"
3. Click "Create function"
4. In the editor, paste the code from `supabase/functions/image-management/index.ts`
5. Click "Deploy"

## 2. Set Environment Variables

1. Go to "Settings" > "API" in the left sidebar
2. <PERSON>roll down to "Edge Functions"
3. Click "Add a new secret"
4. Add the following secrets:
   - `RUNPOD_API_KEY`: Your RunPod API key
   - `SWARMUI_TEMPLATE_ID`: Your SwarmUI template ID (you can leave this blank for now if you don't have it yet)

## 3. Create Database Tables

Execute the following SQL in the Supabase SQL Editor:

```sql
-- SwarmUI Server Management Tables

-- Server instances table
CREATE TABLE IF NOT EXISTS swarmui_servers (
  id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  pod_id TEXT NOT NULL,
  api_endpoint TEXT NOT NULL,
  api_key TEXT NOT NULL,
  status TEXT NOT NULL,
  gpu_type TEXT NOT NULL,
  started_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  stopped_at TIMESTAMP WITH TIME ZONE,
  last_activity TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Add RLS policies for swarmui_servers
ALTER TABLE swarmui_servers ENABLE ROW LEVEL SECURITY;

-- Policy for users to view only their own servers
CREATE POLICY "Users can view their own servers"
  ON swarmui_servers
  FOR SELECT
  USING (auth.uid() = user_id);

-- Policy for users to insert their own servers
CREATE POLICY "Users can insert their own servers"
  ON swarmui_servers
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Policy for users to update their own servers
CREATE POLICY "Users can update their own servers"
  ON swarmui_servers
  FOR UPDATE
  USING (auth.uid() = user_id);

-- Policy for users to delete their own servers
CREATE POLICY "Users can delete their own servers"
  ON swarmui_servers
  FOR DELETE
  USING (auth.uid() = user_id);

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update the updated_at timestamp
CREATE TRIGGER update_swarmui_servers_updated_at
BEFORE UPDATE ON swarmui_servers
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Function to auto-shutdown inactive servers
CREATE OR REPLACE FUNCTION auto_shutdown_inactive_servers()
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
  v_shutdown_count INTEGER := 0;
  v_inactive_threshold INTERVAL := INTERVAL '30 minutes';
  v_server RECORD;
BEGIN
  FOR v_server IN
    SELECT * FROM swarmui_servers
    WHERE status = 'running'
    AND NOW() - last_activity > v_inactive_threshold
  LOOP
    -- Update the status to stopping
    UPDATE swarmui_servers
    SET status = 'stopping', stopped_at = NOW(), updated_at = NOW()
    WHERE id = v_server.id;
    
    v_shutdown_count := v_shutdown_count + 1;
  END LOOP;
  
  RETURN v_shutdown_count;
END;
$$;
```

## 4. Create Storage Bucket

1. Go to "Storage" in the left sidebar
2. Click "Create a new bucket"
3. Name it "images"
4. Enable public access if you want the images to be publicly accessible
5. Click "Create bucket"

## 5. Set Up RLS Policies for Storage

Execute the following SQL in the Supabase SQL Editor:

```sql
-- Allow users to read public images
CREATE POLICY "Public images are viewable by everyone"
  ON storage.objects
  FOR SELECT
  USING (bucket_id = 'images' AND storage.foldername(name) = 'public');

-- Allow authenticated users to upload images
CREATE POLICY "Users can upload images"
  ON storage.objects
  FOR INSERT
  WITH CHECK (
    bucket_id = 'images' AND
    auth.uid() = owner AND
    (storage.foldername(name) = 'generated/' || auth.uid()::text OR
     storage.foldername(name) = 'uploads/' || auth.uid()::text)
  );

-- Allow users to update their own images
CREATE POLICY "Users can update their own images"
  ON storage.objects
  FOR UPDATE
  USING (
    bucket_id = 'images' AND
    auth.uid() = owner AND
    (storage.foldername(name) = 'generated/' || auth.uid()::text OR
     storage.foldername(name) = 'uploads/' || auth.uid()::text)
  );

-- Allow users to delete their own images
CREATE POLICY "Users can delete their own images"
  ON storage.objects
  FOR DELETE
  USING (
    bucket_id = 'images' AND
    auth.uid() = owner AND
    (storage.foldername(name) = 'generated/' || auth.uid()::text OR
     storage.foldername(name) = 'uploads/' || auth.uid()::text)
  );
```

## 6. Verify Setup

After completing the setup, verify that everything is working correctly:

1. Check that the Edge Functions are deployed and have the correct environment variables
2. Verify that the database tables are created with the correct structure
3. Ensure that the storage bucket is created and has the correct RLS policies
4. Test the API endpoints from your application

If you encounter any issues, check the Edge Function logs in the Supabase dashboard for error messages.
