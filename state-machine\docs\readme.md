# StateMachine

## Documentation

### Overview

The StateMachine docs are best-used in conjunction with the [demos](../demo) which contains a wide selection of examples of all the main [API](http://statemachine.davestewart.io/html/api) features, as well as real world [examples](http://statemachine.davestewart.io/html/examples) such as forms and components. 

The demos also cover the [StateHelper](http://statemachine.davestewart.io/html/setup) class which is designed to quickly wire the relationships between the StateMachine and a UI, freeing you to get on with building your app / component


### Links

To begin, familiarise yourself with some state machine concepts:

- [Intro](main/intro.md)
- [Transitions](main/transitions.md)

Next, see installation, setup and basic usage:

- [Setup](main/setup.md)
- [Usage](main/usage.md)

Once up and running, configure StateMachine:

- [Options](config/options.md)
- [Transitions](config/transitions.md)
- [Handlers](config/handlers.md)

Finally, explore the API and supporting classes:

- [API overview](api/readme.md)
- [StateMachine](api/statemachine.md)
- [StateHelper](http://statemachine.davestewart.io/html/setup/setup/overview.html)
- [Events](api/events.md)
- [TransitionMap](api/transitionmap.md)

