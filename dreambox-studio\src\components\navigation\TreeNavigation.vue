<template>
  <q-list padding>
    <q-item-label header>{{ title }}</q-item-label>

    <template v-for="item in items" :key="item.id">
      <!-- Regular menu item without children -->
      <q-item
        v-if="!item.children"
        clickable
        :active="isActive(item)"
        @click="navigateTo(item)"
        v-ripple
      >
        <q-item-section avatar v-if="item.icon">
          <q-icon :name="item.icon" />
        </q-item-section>
        <q-item-section>
          {{ item.label }}
        </q-item-section>
      </q-item>

      <!-- Parent item with children -->
      <q-expansion-item
        v-else
        :label="item.label"
        :default-opened="isExpanded(item)"
        :header-class="isActive(item) ? 'bg-primary-1' : ''"
        expand-icon-class="text-primary"
        expand-icon-toggle
        switch-toggle-side
        @update:model-value="updateExpanded(item, $event)"
      >
        <!-- Recursively render children -->
        <q-list class="q-pl-md">
          <template v-for="child in item.children" :key="child.id">
            <!-- Regular child item -->
            <q-item
              v-if="!child.children"
              clickable
              :active="isActive(child)"
              @click="navigateTo(child)"
              v-ripple
            >
              <q-item-section avatar v-if="child.icon">
                <q-icon :name="child.icon" />
              </q-item-section>
              <q-item-section>
                {{ child.label }}
              </q-item-section>
            </q-item>

            <!-- Nested expansion item -->
            <q-expansion-item
              v-else
              :label="child.label"
              :default-opened="isExpanded(child)"
              :header-class="isActive(child) ? 'bg-primary-1' : ''"
              expand-icon-class="text-primary"
              expand-icon-toggle
              switch-toggle-side
              @update:model-value="updateExpanded(child, $event)"
            >
              <!-- Third level items -->
              <q-list class="q-pl-md">
                <q-item
                  v-for="grandchild in child.children"
                  :key="grandchild.id"
                  clickable
                  :active="isActive(grandchild)"
                  @click="navigateTo(grandchild)"
                  v-ripple
                >
                  <q-item-section avatar v-if="grandchild.icon">
                    <q-icon :name="grandchild.icon" />
                  </q-item-section>
                  <q-item-section>
                    {{ grandchild.label }}
                  </q-item-section>
                </q-item>
              </q-list>
            </q-expansion-item>
          </template>
        </q-list>
      </q-expansion-item>
    </template>
  </q-list>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { useLocalStorage } from '@vueuse/core';

// Define the navigation item type
export interface NavItem {
  id: string;
  label: string;
  icon?: string;
  route?: string;
  children?: NavItem[];
}

// Define props
const props = defineProps<{
  title: string;
  items: NavItem[];
}>();

// Emit events
const emit = defineEmits<{
  (e: 'navigate', route: string): void;
}>();

// Setup current route for active item detection
const route = useRoute();

// Store expanded state in localStorage
const expandedItems = useLocalStorage<string[]>('nav-expanded-items', []);

// Check if an item is active based on the current route
const isActive = (item: NavItem): boolean => {
  if (!item.route) return false;

  // Exact match
  if (route.path === item.route) return true;

  // Parent match (current route is a child of this item)
  if (item.children && route.path.startsWith(item.route)) return true;

  return false;
};

// Check if an item should be expanded
const isExpanded = (item: NavItem): boolean => {
  // If any child is active, expand the parent
  if (item.children && item.children.some(child => isActive(child) ||
      (child.children && child.children.some(grandchild => isActive(grandchild))))) {
    return true;
  }

  // Check if it was manually expanded by the user
  return expandedItems.value.includes(item.id);
};

// Update expanded state
const updateExpanded = (item: NavItem, expanded: boolean) => {
  if (expanded) {
    if (!expandedItems.value.includes(item.id)) {
      expandedItems.value.push(item.id);
    }
  } else {
    const index = expandedItems.value.indexOf(item.id);
    if (index !== -1) {
      expandedItems.value.splice(index, 1);
    }
  }
};

// Navigate to a route
const navigateTo = (item: NavItem) => {
  if (item.route) {
    emit('navigate', item.route);
  }
};

// Initialize - expand items that should be expanded based on current route
onMounted(() => {
  props.items.forEach(item => {
    if (item.children && isActive(item) && !expandedItems.value.includes(item.id)) {
      expandedItems.value.push(item.id);
    }
  });
});
</script>

<style lang="scss" scoped>
@import 'src/css/design-tokens.scss';

.q-item {
  border-radius: $border-radius-sm;

  &.q-item--active {
    background-color: rgba($primary, 0.1);

    .q-icon {
      color: $primary;
    }
  }
}

// Add some subtle hover effect
.q-item:not(.q-item--active):hover {
  background-color: rgba($primary, 0.05);
}

// Style for expansion items
:deep(.q-expansion-item) {
  .q-expansion-item__container {
    border-radius: $border-radius-sm;
  }

  .q-item__section--avatar .q-icon {
    font-size: 1.2rem;
  }

  // Style for left-side toggle icons
  .q-expansion-item__toggle-icon {
    font-size: 1.2rem;
    margin-right: 8px;
  }

  // No additional padding needed
}

// Add some spacing between groups
.q-list + .q-expansion-item,
.q-item + .q-expansion-item,
.q-expansion-item + .q-expansion-item,
.q-expansion-item + .q-item {
  margin-top: $space-xs;
}
</style>
