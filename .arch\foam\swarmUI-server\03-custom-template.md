# Step 3: Creating a Custom RunPod Template

This document outlines the process for creating a custom RunPod template with SwarmUI pre-installed. This template will be used for on-demand provisioning of SwarmUI servers.

## 3.1 Prepare the Pod for Templating

Before creating a template, ensure your SwarmUI installation with Flux is complete and working properly:

1. Verify SwarmUI is running correctly
2. Test the API endpoints
3. Make sure the Flux model and other necessary models are installed and working
4. Verify that the Flux model appears in the SwarmUI models list
5. Test image generation with the Flux model
6. Clean up any temporary files or logs

```bash
# Clean up unnecessary files
rm -rf /tmp/*
rm -rf /var/log/*.gz
rm -rf ~/.bash_history

# Clear apt cache
apt-get clean

# Clear pip cache
pip cache purge
```

## 3.2 Create a Template Initialization Script

Create a script that will run when a new pod is created from the template:

```bash
cat > /workspace/init-swarmui.sh << 'EOL'
#!/bin/bash

# Generate a new API key if not already set
if [ -z "$SWARMUI_API_KEY" ]; then
  export SWARMUI_API_KEY=$(openssl rand -hex 16)
  echo "Generated new SwarmUI API key: $SWARMUI_API_KEY"
else
  echo "Using provided SwarmUI API key"
fi

# Update the SwarmUI config with the new API key
sed -i "s/\"ApiKey\": \".*\"/\"ApiKey\": \"$SWARMUI_API_KEY\"/" ~/.swarmui/config.json

# Update the backend URL if RUNPOD_HTTP_HOST is set
if [ ! -z "$RUNPOD_HTTP_HOST" ]; then
  PUBLIC_URL="https://$RUNPOD_HTTP_HOST"
  sed -i "s|\"BackendUrl\": \".*\"|\"BackendUrl\": \"$PUBLIC_URL\"|" ~/.swarmui/config.json
  sed -i "s|\"FrontendUrl\": \".*\"|\"FrontendUrl\": \"$PUBLIC_URL\"|" ~/.swarmui/config.json
  echo "Updated SwarmUI URLs to use $PUBLIC_URL"
fi

# Start SwarmUI
cd ~/StableSwarmUI
source venv/bin/activate
python3 -m swarm.main
EOL

chmod +x /workspace/init-swarmui.sh
```

## 3.3 Create a RunPod Handler Script

RunPod requires a specific handler script to manage HTTP endpoints:

```bash
cat > /workspace/handler.py << 'EOL'
#!/usr/bin/env python3
import os
import json
import requests
import subprocess
from http.server import BaseHTTPRequestHandler, HTTPServer

# Get the SwarmUI API key
SWARMUI_API_KEY = os.environ.get('SWARMUI_API_KEY', '')
SWARMUI_API_URL = "http://localhost:7801/api"

class Handler(BaseHTTPRequestHandler):
    def do_GET(self):
        # Forward GET requests to SwarmUI API
        if self.path.startswith('/api/'):
            api_path = self.path[4:]  # Remove '/api' prefix
            url = f"{SWARMUI_API_URL}{api_path}"

            # Get headers from the request
            headers = {
                'Authorization': f'Bearer {SWARMUI_API_KEY}'
            }

            try:
                response = requests.get(url, headers=headers)
                self.send_response(response.status_code)

                # Forward response headers
                for key, value in response.headers.items():
                    self.send_header(key, value)
                self.end_headers()

                # Send response body
                self.wfile.write(response.content)
            except Exception as e:
                self.send_error(500, str(e))
        else:
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({
                'status': 'running',
                'api_key': SWARMUI_API_KEY
            }).encode())

    def do_POST(self):
        # Read request body
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length).decode('utf-8')

        try:
            request_json = json.loads(post_data)
        except:
            request_json = {}

        # Forward POST requests to SwarmUI API
        if self.path.startswith('/api/'):
            api_path = self.path[4:]  # Remove '/api' prefix
            url = f"{SWARMUI_API_URL}{api_path}"

            # Get headers from the request
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {SWARMUI_API_KEY}'
            }

            try:
                response = requests.post(url, json=request_json, headers=headers)
                self.send_response(response.status_code)

                # Forward response headers
                for key, value in response.headers.items():
                    self.send_header(key, value)
                self.end_headers()

                # Send response body
                self.wfile.write(response.content)
            except Exception as e:
                self.send_error(500, str(e))
        else:
            self.send_response(400)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({
                'error': 'Invalid endpoint'
            }).encode())

if __name__ == '__main__':
    # Start SwarmUI in the background
    subprocess.Popen(['/workspace/init-swarmui.sh'])

    # Start HTTP server
    server = HTTPServer(('0.0.0.0', 8000), Handler)
    print('Starting server on port 8000...')
    server.serve_forever()
EOL

chmod +x /workspace/handler.py
```

## 3.4 Create a Startup Script for RunPod

Create a script that will be executed when the pod starts:

```bash
cat > /workspace/start.sh << 'EOL'
#!/bin/bash

# Start the handler
python3 /workspace/handler.py
EOL

chmod +x /workspace/start.sh
```

## 3.5 Test the Handler and Initialization Scripts

Before creating the template, test that the handler and initialization scripts work correctly:

```bash
# Test the initialization script
/workspace/init-swarmui.sh &

# Wait for SwarmUI to start
sleep 30

# Test the handler
python3 -c "
import requests
response = requests.get('http://localhost:7801/api/models',
                       headers={'Authorization': 'Bearer $SWARMUI_API_KEY'})
print(response.status_code)
print(response.json())
"

# Kill the SwarmUI process
pkill -f "python3 -m swarm.main"
```

## 3.6 Create the Template

Now that everything is set up, create a template from the pod:

1. In the RunPod console, navigate to your pod
2. Click "Template" in the top-right corner
3. Fill in the template details:
   - Name: `SwarmUI-Template`
   - Description: `SwarmUI pre-installed for on-demand image generation`
   - Container Disk: `50GB` (or the size you chose)
   - Volume Size: `100GB` (or the size you chose)
   - Expose HTTP Port: `8000` (the port used by our handler)
   - Start Script: `/workspace/start.sh`
4. Click "Create Template"

## 3.7 Test the Template

To ensure the template works correctly:

1. Navigate to [RunPod Templates](https://www.runpod.io/console/user/templates)
2. Find your `SwarmUI-Template`
3. Click "Deploy"
4. Once the pod is running, click on the HTTP endpoint URL
5. You should see a JSON response with `{"status":"running","api_key":"your-api-key"}`
6. Test the API by appending `/api/models` to the URL

## 3.8 Document Template ID and Configuration

Take note of the template ID, as you'll need it for programmatic deployment:

1. Navigate to [RunPod Templates](https://www.runpod.io/console/user/templates)
2. Find your `SwarmUI-Template`
3. The template ID is in the URL when you click on the template (e.g., `https://www.runpod.io/console/user/templates/template/abcd1234`)
4. Document this ID for use in your application

## 3.9 Clean Up

Once you've successfully created and tested the template, you can delete the original pod to avoid ongoing charges.

## Next Steps

Now that you have a custom RunPod template with SwarmUI pre-installed, proceed to [Step 4: Database Schema for Server Management](./04-database-schema.md) to set up the database structure for tracking server instances.
