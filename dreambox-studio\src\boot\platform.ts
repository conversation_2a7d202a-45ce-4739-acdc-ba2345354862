import { boot } from 'quasar/wrappers';
import PlatformUtil from 'src/utils/platform';

// This boot file adds platform-specific classes to the body element
// and initializes platform-specific behaviors

export default boot(({ app }) => {
  // Add platform class to body
  const platformClass = PlatformUtil.getPlatformClass();
  document.body.classList.add(platformClass);
  
  // Add platform info to app config for use in components
  app.config.globalProperties.$platform = PlatformUtil;
  
  // Add platform-specific meta tags for mobile
  if (PlatformUtil.isMobile()) {
    // Set viewport for mobile devices
    const viewport = document.querySelector('meta[name=viewport]');
    if (viewport) {
      viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
    }
    
    // Add mobile web app capable meta tag for iOS
    if (PlatformUtil.isIos()) {
      const metaAppleWebApp = document.createElement('meta');
      metaAppleWebApp.setAttribute('name', 'apple-mobile-web-app-capable');
      metaAppleWebApp.setAttribute('content', 'yes');
      document.head.appendChild(metaAppleWebApp);
      
      const metaAppleWebAppStatus = document.createElement('meta');
      metaAppleWebAppStatus.setAttribute('name', 'apple-mobile-web-app-status-bar-style');
      metaAppleWebAppStatus.setAttribute('content', 'black-translucent');
      document.head.appendChild(metaAppleWebAppStatus);
    }
  }
  
  // Add platform-specific behaviors for Electron
  if (PlatformUtil.isElectron()) {
    // Electron-specific initialization
    // This could include things like custom window controls, menu integration, etc.
  }
});
