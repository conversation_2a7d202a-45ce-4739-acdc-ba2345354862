/**
 * FilterFormatConverter.ts
 *
 * This module provides utilities for converting between old and new filter formats.
 * It handles the migration of saved filters and active filters.
 */

/**
 * Old saved filter interface
 */
export interface OldSavedFilter {
  id: number;
  user_id: string;
  name: string;
  description?: string | null;
  table_name?: string;
  context: string;
  is_default: boolean;
  created_at: string;
  updated_at: string;
  filter_data?: {
    filters?: Record<string, unknown>;
    promptElements?: unknown;
    relatedItems?: unknown;
  };
}

/**
 * New saved filter interface
 */
export interface NewSavedFilter {
  id: number;
  user_id: string;
  name: string;
  description: string | null;
  table_name: string;
  context: string | null;
  is_default: boolean;
  created_at: string;
  updated_at: string;
  configuration: {
    columns: {
      id: string;
      value: unknown;
      visible: boolean;
      section: string;
    }[];
    specializedFilters: Record<string, unknown>;
  };
}

/**
 * Filter Format Converter class
 */
export class FilterFormatConverter {
  /**
   * Convert from old filter format to new filter format
   */
  static oldToNew(oldFilter: OldSavedFilter): NewSavedFilter {
    // Create base structure for new filter
    const newFilter: NewSavedFilter = {
      id: oldFilter.id,
      user_id: oldFilter.user_id,
      name: oldFilter.name,
      description: oldFilter.description || null,
      table_name: oldFilter.table_name || oldFilter.context,
      context: oldFilter.context,
      is_default: oldFilter.is_default,
      created_at: oldFilter.created_at,
      updated_at: oldFilter.updated_at,
      configuration: {
        columns: [],
        specializedFilters: {}
      }
    };

    // Convert filter data if available
    if (oldFilter.filter_data) {
      // Extract standard filters
      const standardFilters = oldFilter.filter_data.filters || {};

      // Convert standard filters to columns
      Object.entries(standardFilters).forEach(([key, value]) => {
        newFilter.configuration.columns.push({
          id: key,
          value,
          visible: true,
          section: 'main'
        });
      });

      // Extract specialized filters
      const promptElements = oldFilter.filter_data.promptElements;
      if (promptElements) {
        newFilter.configuration.specializedFilters.promptElements = promptElements;
      }

      const relatedItems = oldFilter.filter_data.relatedItems;
      if (relatedItems) {
        newFilter.configuration.specializedFilters.relatedItems = relatedItems;
      }
    }

    return newFilter;
  }

  /**
   * Convert from new filter format to old filter format
   */
  static newToOld(newFilter: NewSavedFilter): OldSavedFilter {
    // Create base structure for old filter
    const oldFilter: OldSavedFilter = {
      id: newFilter.id,
      user_id: newFilter.user_id,
      name: newFilter.name,
      description: newFilter.description,
      table_name: newFilter.table_name,
      context: newFilter.context || newFilter.table_name,
      is_default: newFilter.is_default,
      created_at: newFilter.created_at,
      updated_at: newFilter.updated_at,
      filter_data: {
        filters: {},
        promptElements: null,
        relatedItems: null
      }
    };

    // Convert columns to standard filters
    newFilter.configuration.columns.forEach(column => {
      if (column.value !== null && column.value !== undefined) {
        if (oldFilter.filter_data && oldFilter.filter_data.filters) {
          oldFilter.filter_data.filters[column.id] = column.value;
        }
      }
    });

    // Convert specialized filters
    if (oldFilter.filter_data) {
      if ('promptElements' in newFilter.configuration.specializedFilters &&
          newFilter.configuration.specializedFilters.promptElements) {
        oldFilter.filter_data.promptElements = newFilter.configuration.specializedFilters.promptElements;
      }

      if ('relatedItems' in newFilter.configuration.specializedFilters &&
          newFilter.configuration.specializedFilters.relatedItems) {
        oldFilter.filter_data.relatedItems = newFilter.configuration.specializedFilters.relatedItems;
      }
    }

    return oldFilter;
  }

  /**
   * Convert active filters from old format to new format
   */
  static convertActiveFilters(
    oldActiveFilters: Record<string, unknown>,
    oldSpecializedFilters: Record<string, unknown>
  ): {
    activeFilters: Record<string, unknown>;
    specializedFilters: Record<string, unknown>;
  } {
    // Standard filters can be used as-is
    const activeFilters = { ...oldActiveFilters };

    // Convert specialized filters
    const specializedFilters: Record<string, unknown> = {};

    if (oldSpecializedFilters && typeof oldSpecializedFilters === 'object') {
      if ('promptElements' in oldSpecializedFilters && oldSpecializedFilters.promptElements) {
        specializedFilters.promptElements = oldSpecializedFilters.promptElements;
      }

      if ('relatedItems' in oldSpecializedFilters && oldSpecializedFilters.relatedItems) {
        specializedFilters.relatedItems = oldSpecializedFilters.relatedItems;
      }
    }

    return { activeFilters, specializedFilters };
  }
}

// Export default for module imports
export default FilterFormatConverter;
