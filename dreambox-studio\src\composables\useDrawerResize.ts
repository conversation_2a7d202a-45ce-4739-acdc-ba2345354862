import { ref, watch, onMounted, onUnmounted } from 'vue';

// Local storage keys
const LEFT_DRAWER_WIDTH_KEY = 'dreambox-left-drawer-width';
const RIGHT_DRAWER_WIDTH_KEY = 'dreambox-right-drawer-width';

// Default constraints
const DEFAULT_MIN_WIDTH = 200;

/**
 * Composable for drawer resizing functionality
 *
 * @param defaultWidth Default width for both drawers
 * @param leftMinWidth Minimum width for left drawer
 * @param leftMaxWidth Maximum width for left drawer (ignored, kept for backward compatibility)
 * @param rightMinWidth Minimum width for right drawer
 * @param rightMaxWidth Maximum width for right drawer (ignored, kept for backward compatibility)
 * @returns Drawer width refs and resize functions
 */
export function useDrawerResize(
  defaultWidth = 300,
  leftMinWidth = DEFAULT_MIN_WIDTH,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  leftMaxWidth = 500, // Ignored, kept for backward compatibility
  rightMinWidth = DEFAULT_MIN_WIDTH,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  rightMaxWidth = 800 // Ignored, kept for backward compatibility
) {
  const leftDrawerWidth = ref(defaultWidth);
  const rightDrawerWidth = ref(defaultWidth);

  // Load saved widths from localStorage
  onMounted(() => {
    try {
      const savedLeftWidth = localStorage.getItem(LEFT_DRAWER_WIDTH_KEY);
      const savedRightWidth = localStorage.getItem(RIGHT_DRAWER_WIDTH_KEY);

      if (savedLeftWidth) {
        const parsedWidth = parseInt(savedLeftWidth, 10);
        // Ensure the loaded width is at least the minimum width
        leftDrawerWidth.value = Math.max(leftMinWidth, parsedWidth);
      }

      if (savedRightWidth) {
        const parsedWidth = parseInt(savedRightWidth, 10);
        // Ensure the loaded width is at least the minimum width
        rightDrawerWidth.value = Math.max(rightMinWidth, parsedWidth);
      }
    } catch (error) {
      console.error('Failed to load drawer widths from localStorage:', error);
    }
  });

  // Save widths to localStorage when they change
  watch(leftDrawerWidth, (newWidth) => {
    try {
      localStorage.setItem(LEFT_DRAWER_WIDTH_KEY, newWidth.toString());
    } catch (error) {
      console.error('Failed to save left drawer width to localStorage:', error);
    }
  });

  watch(rightDrawerWidth, (newWidth) => {
    try {
      localStorage.setItem(RIGHT_DRAWER_WIDTH_KEY, newWidth.toString());
    } catch (error) {
      console.error('Failed to save right drawer width to localStorage:', error);
    }
  });

  // Resize functions
  function resizeLeftDrawer(delta: number) {
    const newWidth = leftDrawerWidth.value + delta;
    // Only constrain to minimum width, no maximum
    leftDrawerWidth.value = Math.max(leftMinWidth, newWidth);
  }

  function resizeRightDrawer(delta: number) {
    const newWidth = rightDrawerWidth.value + delta;
    // Only constrain to minimum width, no maximum
    rightDrawerWidth.value = Math.max(rightMinWidth, newWidth);
  }

  // Add window resize event listener
  onMounted(() => {
    window.addEventListener('resize', handleWindowResize);
  });

  // Clean up event listener on component unmount
  onUnmounted(() => {
    window.removeEventListener('resize', handleWindowResize);
  });

  function handleWindowResize() {
    // No need to adjust drawer widths on window resize
    // since we no longer have maximum width constraints
  }

  return {
    leftDrawerWidth,
    rightDrawerWidth,
    resizeLeftDrawer,
    resizeRightDrawer
  };
}
