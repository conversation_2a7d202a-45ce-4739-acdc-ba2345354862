/**
 * Represents a physical product that can be printed on (t-shirt, mug, etc.)
 */
export interface ProductType {
  id: number;
  name: string;
  description: string | null;
  category: string | null;
  print_area_width: number | null;
  print_area_height: number | null;
  thumbnail_url: string | null;
  shopify_product_id: string | null;
  active: boolean;
  created_at: string;
  updated_at: string;
  recommended?: boolean; // Only used when associated with a template
}

/**
 * Represents a product type with minimal information
 */
export interface ProductTypeBasic {
  id: number;
  name: string;
  category: string | null;
  thumbnail_url: string | null;
  recommended?: boolean;
}
