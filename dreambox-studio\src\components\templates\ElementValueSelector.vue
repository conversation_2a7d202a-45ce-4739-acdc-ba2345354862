<template>
  <div class="element-value-selector q-pa-md">
    <q-list separator>
      <template v-for="typeId in elementTypes" :key="typeId">
        <q-expansion-item
          :label="getElementTypeName(typeId)"
          header-class="text-primary"
          expand-separator
        >
          <q-card>
            <q-card-section>
              <div class="row items-center q-mb-sm">
                <q-checkbox
                  :model-value="isAllValuesSelected(typeId)"
                  label="Select All"
                  @update:model-value="toggleAllValuesForType(typeId, $event)"
                />
              </div>

              <q-list dense>
                <q-item v-for="value in getValuesForType(typeId)" :key="value.id">
                  <q-item-section avatar>
                    <q-checkbox
                      v-model="selectedValues"
                      :val="value.id"
                      :class="{ 'custom-element': !value.is_system }"
                    />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ value.value }}</q-item-label>
                    <q-item-label caption v-if="value.description">{{ value.description }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>

              <q-separator class="q-my-md" />

              <div class="row">
                <q-btn
                  outline
                  color="primary"
                  icon="add"
                  label="Add Custom Value"
                  @click="showAddValueDialog(typeId)"
                />
              </div>
            </q-card-section>
          </q-card>
        </q-expansion-item>
      </template>
    </q-list>

    <!-- Dialog for adding custom values -->
    <q-dialog v-model="addValueDialog">
      <q-card style="min-width: 350px">
        <q-card-section>
          <div class="text-h6">Add Custom Value</div>
        </q-card-section>

        <q-card-section>
          <q-input
            v-model="newValueText"
            label="Value"
            autofocus
            :rules="[val => !!val || 'Value is required']"
          />
          <q-input
            v-model="newValueDescription"
            label="Description (optional)"
            type="textarea"
            autogrow
          />
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="Cancel" color="primary" v-close-popup />
          <q-btn
            label="Add"
            color="primary"
            @click="addCustomValue"
            :disable="!newValueText"
            v-close-popup
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { supabase } from 'src/boot/supabase';

const props = defineProps({
  elementTypes: {
    type: Array as () => number[],
    required: true
  },
  modelValue: {
    type: Array as () => number[],
    default: () => []
  }
});

const emit = defineEmits(['update:modelValue']);

// We don't need the auth store for now

// Selected values
const selectedValues = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
});

// Element values by type
const elementValuesByType = ref<Record<number, { id: number, value: string, description: string | null, is_system: boolean }[]>>({});

// Dialog state
const addValueDialog = ref(false);
const newValueText = ref('');
const newValueDescription = ref('');
const currentTypeId = ref<number | null>(null);

// Element type names
const elementTypeNames = ref<Record<number, string>>({});

// Fetch element type names
async function fetchElementTypeNames() {
  try {
    // Create a Promise to ensure we're awaiting a Promise
    const { data, error } = await Promise.resolve(
      supabase
        .from('prompt_element_types')
        .select('id, name')
    );

    if (error) throw new Error(error.message || 'Error fetching element type names');

    if (data) {
      data.forEach((type: { id: number, name: string }) => {
        elementTypeNames.value[type.id] = formatElementTypeName(type.name);
      });
    }
  } catch (error) {
    console.error('Error fetching element type names:', error);
  }
}

// Fetch element values for a type
async function fetchElementValuesForType(typeId: number) {
  try {
    const { data, error } = await supabase
      .from('prompt_elements')
      .select('id, value, description')
      .eq('type_id', typeId);

    if (error) throw new Error(error.message || 'Error fetching element values');

    if (data) {
      // For now, we'll show all values to all users
      elementValuesByType.value[typeId] = data.map((item: { id: number, value: string, description: string | null }) => ({
        ...item,
        is_system: true // Assume all elements are system elements for now
      }));
    }
  } catch (error) {
    console.error(`Error fetching element values for type ${typeId}:`, error);
  }
}

// Format element type name (convert snake_case to Title Case)
function formatElementTypeName(name: string): string {
  return name
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// Get element type name
function getElementTypeName(typeId: number): string {
  return elementTypeNames.value[typeId] || `Type ${typeId}`;
}

// Get values for a type
function getValuesForType(typeId: number) {
  if (!elementValuesByType.value[typeId]) {
    // Fetch values if not already loaded
    void fetchElementValuesForType(typeId);
    return [];
  }
  return elementValuesByType.value[typeId] || [];
}

// Check if all values for a type are selected
function isAllValuesSelected(typeId: number): boolean {
  const values = getValuesForType(typeId);
  if (values.length === 0) return false;

  return values.every(value => selectedValues.value.includes(value.id));
}

// Toggle all values for a type
function toggleAllValuesForType(typeId: number, selected: boolean) {
  const values = getValuesForType(typeId);
  const valueIds = values.map(value => value.id);

  if (selected) {
    // Add all values that aren't already selected
    const newSelectedValues = [...selectedValues.value];
    valueIds.forEach(id => {
      if (!newSelectedValues.includes(id)) {
        newSelectedValues.push(id);
      }
    });
    selectedValues.value = newSelectedValues;
  } else {
    // Remove all values of this type
    selectedValues.value = selectedValues.value.filter(id => !valueIds.includes(id));
  }
}

// Show dialog to add a custom value
function showAddValueDialog(typeId: number) {
  currentTypeId.value = typeId;
  newValueText.value = '';
  newValueDescription.value = '';
  addValueDialog.value = true;
}

// Add a custom value
async function addCustomValue() {
  if (!currentTypeId.value || !newValueText.value) return;

  try {
    // Insert the new element
    const { data, error } = await supabase
      .from('prompt_elements')
      .insert({
        type_id: currentTypeId.value,
        value: newValueText.value,
        description: newValueDescription.value || null
      })
      .select();

    if (error) throw new Error(error.message || 'Error adding custom value');

    if (data && data.length > 0 && data[0] && currentTypeId.value !== undefined) {
      const typeId = currentTypeId.value;

      // Add the new element to the list
      if (!elementValuesByType.value[typeId]) {
        elementValuesByType.value[typeId] = [];
      }

      // Add is_system property to the new element and ensure all required properties are present
      const newElement = {
        id: data[0].id,
        value: data[0].value,
        description: data[0].description,
        is_system: true // For simplicity, we'll mark all elements as system elements
      };

      // TypeScript needs this check even though we already checked above
      if (elementValuesByType.value[typeId]) {
        elementValuesByType.value[typeId].push(newElement);
      }

      // Select the new value
      selectedValues.value = [...selectedValues.value, newElement.id];
    }
  } catch (error) {
    console.error('Error adding custom value:', error);
  }
}

// Initialize
void fetchElementTypeNames();

// Fetch values for all types
function initializeElementValues() {
  props.elementTypes.forEach(typeId => {
    void fetchElementValuesForType(typeId);
  });
}

// Initialize element values
void initializeElementValues();
</script>

<style lang="scss" scoped>
.custom-element {
  background-color: rgba(var(--q-primary), 0.1);
}
</style>
