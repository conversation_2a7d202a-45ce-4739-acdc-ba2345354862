<template>
  <q-page :padding="padding" :class="[platformClass, containerClass]">
    <div v-if="showHeader" class="page-header q-mb-md">
      <div class="row items-center justify-between">
        <div>
          <h1 class="page-title q-my-none">{{ title }}</h1>
          <p v-if="subtitle" class="page-subtitle q-mt-sm q-mb-none text-grey-7">{{ subtitle }}</p>
        </div>
        <div>
          <slot name="actions"></slot>
        </div>
      </div>
      <q-separator class="q-my-md" v-if="showDivider" />
    </div>
    
    <slot></slot>
  </q-page>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import PlatformUtil from 'src/utils/platform';

// Props
interface Props {
  title?: string;
  subtitle?: string;
  padding?: boolean;
  fluid?: boolean;
  showHeader?: boolean;
  showDivider?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  subtitle: '',
  padding: true,
  fluid: false,
  showHeader: true,
  showDivider: true
});

// Platform detection
const platformClass = computed(() => PlatformUtil.getPlatformClass());

// Container class based on fluid prop
const containerClass = computed(() => props.fluid ? 'container-fluid' : 'container');
</script>

<style lang="scss" scoped>
.page-title {
  font-size: 1.5rem;
  font-weight: 500;
  line-height: 1.2;
  
  @media (max-width: 599px) {
    font-size: 1.25rem;
  }
}

.page-subtitle {
  font-size: 1rem;
  font-weight: 400;
  
  @media (max-width: 599px) {
    font-size: 0.875rem;
  }
}

// Platform-specific styles
.platform-mobile {
  padding: 12px !important;
  
  .page-header {
    margin-bottom: 12px !important;
  }
}

.platform-electron {
  // Add any Electron-specific styles here
}
</style>
