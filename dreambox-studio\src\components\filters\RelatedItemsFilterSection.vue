<template>
  <div class="related-items-filter-section">
    <div class="section-header q-mb-sm">
      <div class="text-subtitle1">Related Items</div>
    </div>

    <q-list bordered separator class="related-items-list rounded-borders q-mb-md">
      <!-- Images Filter -->
      <q-expansion-item
        label="Images"
        header-class="related-item-header"
        expand-icon-class="text-primary"
      >
        <q-card>
          <q-card-section>
            <q-select
              v-model="filters.images.count"
              :options="countOptions"
              outlined
              dense
              label="Filter by image count"
              @update:model-value="updateImageCountFilter"
            >
              <template v-slot:prepend>
                <q-icon name="filter_list" />
              </template>
            </q-select>

            <q-checkbox
              v-model="filters.images.hasAny"
              label="Has any images"
              class="q-mt-sm"
              @update:model-value="updateImageHasAnyFilter"
            />
          </q-card-section>
        </q-card>
      </q-expansion-item>

      <!-- Products Filter -->
      <q-expansion-item
        label="Products"
        header-class="related-item-header"
        expand-icon-class="text-primary"
      >
        <q-card>
          <q-card-section>
            <q-select
              v-model="filters.products.count"
              :options="countOptions"
              outlined
              dense
              label="Filter by product count"
              @update:model-value="updateProductCountFilter"
            >
              <template v-slot:prepend>
                <q-icon name="filter_list" />
              </template>
            </q-select>

            <q-checkbox
              v-model="filters.products.hasAny"
              label="Has any products"
              class="q-mt-sm"
              @update:model-value="updateProductHasAnyFilter"
            />

            <div class="q-mt-md">
              <q-select
                v-model="selectedProducts"
                :options="filteredProductOptions"
                option-label="name"
                option-value="id"
                multiple
                outlined
                dense
                use-chips
                stack-label
                use-input
                hide-selected
                fill-input
                input-debounce="300"
                popup-content-class="custom-scrollbar"
                label="Filter by specific products"
                @update:model-value="updateProductFilter"
                @filter="filterProducts"
              >
                <template v-slot:selected-item="scope">
                  <q-chip
                    removable
                    dense
                    @remove="scope.removeAtIndex(scope.index)"
                    :tabindex="scope.tabindex"
                    color="primary"
                    text-color="white"
                  >
                    {{ scope.opt.name }}
                  </q-chip>
                </template>
              </q-select>
            </div>
          </q-card-section>
        </q-card>
      </q-expansion-item>

      <!-- Events Filter -->
      <q-expansion-item
        label="Events"
        header-class="related-item-header"
        expand-icon-class="text-primary"
      >
        <q-card>
          <q-card-section>
            <q-select
              v-model="filters.events.count"
              :options="countOptions"
              outlined
              dense
              label="Filter by event count"
              @update:model-value="updateEventCountFilter"
            >
              <template v-slot:prepend>
                <q-icon name="filter_list" />
              </template>
            </q-select>

            <q-checkbox
              v-model="filters.events.hasAny"
              label="Has any events"
              class="q-mt-sm"
              @update:model-value="updateEventHasAnyFilter"
            />

            <div class="q-mt-md">
              <q-select
                v-model="selectedEvents"
                :options="filteredEventOptions"
                option-label="name"
                option-value="id"
                multiple
                outlined
                dense
                use-chips
                stack-label
                use-input
                hide-selected
                fill-input
                input-debounce="300"
                popup-content-class="custom-scrollbar"
                label="Filter by specific events"
                @update:model-value="updateEventFilter"
                @filter="filterEvents"
              >
                <template v-slot:selected-item="scope">
                  <q-chip
                    removable
                    dense
                    @remove="scope.removeAtIndex(scope.index)"
                    :tabindex="scope.tabindex"
                    color="primary"
                    text-color="white"
                  >
                    {{ scope.opt.name }}
                  </q-chip>
                </template>
              </q-select>
            </div>
          </q-card-section>
        </q-card>
      </q-expansion-item>

      <!-- Collections Filter -->
      <q-expansion-item
        label="Collections"
        header-class="related-item-header"
        expand-icon-class="text-primary"
      >
        <q-card>
          <q-card-section>
            <q-select
              v-model="filters.collections.count"
              :options="countOptions"
              outlined
              dense
              label="Filter by collection count"
              @update:model-value="updateCollectionCountFilter"
            >
              <template v-slot:prepend>
                <q-icon name="filter_list" />
              </template>
            </q-select>

            <q-checkbox
              v-model="filters.collections.hasAny"
              label="Has any collections"
              class="q-mt-sm"
              @update:model-value="updateCollectionHasAnyFilter"
            />

            <div class="q-mt-md">
              <q-select
                v-model="selectedCollections"
                :options="filteredCollectionOptions"
                option-label="name"
                option-value="id"
                multiple
                outlined
                dense
                use-chips
                stack-label
                use-input
                hide-selected
                fill-input
                input-debounce="300"
                popup-content-class="custom-scrollbar"
                label="Filter by specific collections"
                @update:model-value="updateCollectionFilter"
                @filter="filterCollections"
              >
                <template v-slot:selected-item="scope">
                  <q-chip
                    removable
                    dense
                    @remove="scope.removeAtIndex(scope.index)"
                    :tabindex="scope.tabindex"
                    color="primary"
                    text-color="white"
                  >
                    {{ scope.opt.name }}
                  </q-chip>
                </template>
              </q-select>
            </div>
          </q-card-section>
        </q-card>
      </q-expansion-item>
    </q-list>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { notify } from 'src/boot/notifications';

// Types
interface Product {
  id: number;
  name: string;
}

interface Event {
  id: number;
  name: string;
}

interface Collection {
  id: number;
  name: string;
}

interface CountOption {
  label: string;
  value: string;
}

// Count filter options
const countOptions = [
  { label: 'Any', value: 'any' },
  { label: 'None', value: 'none' },
  { label: '1+', value: 'gt0' },
  { label: '2+', value: 'gt1' },
  { label: '5+', value: 'gt4' },
  { label: '10+', value: 'gt9' }
];

// State
const productOptions = ref<Product[]>([]);
const eventOptions = ref<Event[]>([]);
const collectionOptions = ref<Collection[]>([]);
const originalProductOptions = ref<Product[]>([]);
const originalEventOptions = ref<Event[]>([]);
const originalCollectionOptions = ref<Collection[]>([]);
const filteredProductOptions = ref<Product[]>([]);
const filteredEventOptions = ref<Event[]>([]);
const filteredCollectionOptions = ref<Collection[]>([]);
const selectedProducts = ref<number[]>([]);
const selectedEvents = ref<number[]>([]);
const selectedCollections = ref<number[]>([]);

// Filter state
const filters = ref({
  images: {
    count: { label: 'Any', value: 'any' },
    hasAny: false
  },
  products: {
    count: { label: 'Any', value: 'any' },
    hasAny: false
  },
  events: {
    count: { label: 'Any', value: 'any' },
    hasAny: false
  },
  collections: {
    count: { label: 'Any', value: 'any' },
    hasAny: false
  }
});

// Computed active filters
const activeFilters = computed(() => {
  const result: Record<string, unknown> = {};

  // Image filters
  if (filters.value.images.count.value !== 'any') {
    result.image_count = filters.value.images.count.value;
  }
  if (filters.value.images.hasAny) {
    result.has_images = true;
  }

  // Product filters
  if (filters.value.products.count.value !== 'any') {
    result.product_count = filters.value.products.count.value;
  }
  if (filters.value.products.hasAny) {
    result.has_products = true;
  }
  if (selectedProducts.value.length > 0) {
    result.product_ids = selectedProducts.value;
  }

  // Event filters
  if (filters.value.events.count.value !== 'any') {
    result.event_count = filters.value.events.count.value;
  }
  if (filters.value.events.hasAny) {
    result.has_events = true;
  }
  if (selectedEvents.value.length > 0) {
    result.event_ids = selectedEvents.value;
  }

  // Collection filters
  if (filters.value.collections.count.value !== 'any') {
    result.collection_count = filters.value.collections.count.value;
  }
  if (filters.value.collections.hasAny) {
    result.has_collections = true;
  }
  if (selectedCollections.value.length > 0) {
    result.collection_ids = selectedCollections.value;
  }

  return result;
});

// Watch for changes in active filters
watch(activeFilters, (newFilters) => {
  // Dispatch a global filter event with the updated filters
  dispatchFilterEvent(newFilters);
});

// Initialize
onMounted(async () => {
  await Promise.all([
    fetchProducts(),
    fetchEvents(),
    fetchCollections()
  ]);
});

// Fetch products from the database
function fetchProducts() {
  try {
    // Mock data for products since we don't have the actual tables yet
    // In a real implementation, this would fetch from the database
    const mockProducts: Product[] = [
      { id: 1, name: 'Product 1' },
      { id: 2, name: 'Product 2' },
      { id: 3, name: 'Product 3' }
    ];

    // Set the product options
    productOptions.value = mockProducts;
    originalProductOptions.value = [...mockProducts];
    filteredProductOptions.value = [...mockProducts];
  } catch (error) {
    console.error('Error fetching products:', error);
    void notify({
      color: 'negative',
      message: 'Failed to load products',
      icon: 'error'
    });
  }
}

// Fetch events from the database
function fetchEvents() {
  try {
    // Mock data for events since we don't have the actual tables yet
    // In a real implementation, this would fetch from the database
    const mockEvents: Event[] = [
      { id: 1, name: 'Event 1' },
      { id: 2, name: 'Event 2' },
      { id: 3, name: 'Event 3' }
    ];

    // Set the event options
    eventOptions.value = mockEvents;
    originalEventOptions.value = [...mockEvents];
    filteredEventOptions.value = [...mockEvents];
  } catch (error) {
    console.error('Error fetching events:', error);
    void notify({
      color: 'negative',
      message: 'Failed to load events',
      icon: 'error'
    });
  }
}

// Update filter functions
function updateImageCountFilter(value: CountOption) {
  filters.value.images.count = value;
}

function updateImageHasAnyFilter(value: boolean) {
  filters.value.images.hasAny = value;
}

function updateProductCountFilter(value: CountOption) {
  filters.value.products.count = value;
}

function updateProductHasAnyFilter(value: boolean) {
  filters.value.products.hasAny = value;
}

function updateProductFilter(value: number[]) {
  selectedProducts.value = value;
}

function updateEventCountFilter(value: CountOption) {
  filters.value.events.count = value;
}

function updateEventHasAnyFilter(value: boolean) {
  filters.value.events.hasAny = value;
}

function updateEventFilter(value: number[]) {
  selectedEvents.value = value;
}

// Filter products based on input
function filterProducts(val: string, update: (callback: () => void) => void) {
  update(() => {
    // If search is empty, restore original values
    if (val === '') {
      filteredProductOptions.value = [...originalProductOptions.value];
      return;
    }

    const needle = val.toLowerCase();
    // Filter the options based on the input value
    filteredProductOptions.value = originalProductOptions.value.filter(
      (product) => product.name.toLowerCase().indexOf(needle) > -1
    );
  });
}

// Filter events based on input
function filterEvents(val: string, update: (callback: () => void) => void) {
  update(() => {
    // If search is empty, restore original values
    if (val === '') {
      filteredEventOptions.value = [...originalEventOptions.value];
      return;
    }

    const needle = val.toLowerCase();
    // Filter the options based on the input value
    filteredEventOptions.value = originalEventOptions.value.filter(
      (event) => event.name.toLowerCase().indexOf(needle) > -1
    );
  });
}

// Fetch collections from the database
function fetchCollections() {
  try {
    // Mock data for collections since we don't have the actual tables yet
    // In a real implementation, this would fetch from the database
    const mockCollections: Collection[] = [
      { id: 1, name: 'Collection 1' },
      { id: 2, name: 'Collection 2' },
      { id: 3, name: 'Collection 3' }
    ];

    // Set the collection options
    collectionOptions.value = mockCollections;
    originalCollectionOptions.value = [...mockCollections];
    filteredCollectionOptions.value = [...mockCollections];
  } catch (error) {
    console.error('Error fetching collections:', error);
    void notify({
      color: 'negative',
      message: 'Failed to load collections',
      icon: 'error'
    });
  }
}

// Update collection filter functions
function updateCollectionCountFilter(value: CountOption) {
  filters.value.collections.count = value;
}

function updateCollectionHasAnyFilter(value: boolean) {
  filters.value.collections.hasAny = value;
}

function updateCollectionFilter(value: number[]) {
  selectedCollections.value = value;
}

// Filter collections based on input
function filterCollections(val: string, update: (callback: () => void) => void) {
  update(() => {
    // If search is empty, restore original values
    if (val === '') {
      filteredCollectionOptions.value = [...originalCollectionOptions.value];
      return;
    }

    const needle = val.toLowerCase();
    // Filter the options based on the input value
    filteredCollectionOptions.value = originalCollectionOptions.value.filter(
      (collection) => collection.name.toLowerCase().indexOf(needle) > -1
    );
  });
}

// Dispatch a global filter event with the current filters
function dispatchFilterEvent(filters: Record<string, unknown>) {
  console.log('Dispatching related items filter event with filters:', filters);

  // Dispatch the event
  document.dispatchEvent(new CustomEvent('global-filter-changed', {
    detail: {
      name: 'related_items',
      filters
    }
  }));
}
</script>

<style lang="scss" scoped>
.related-items-filter-section {
  .related-item-header {
    font-weight: 500;
  }

  .related-items-list {
    border: 1px solid rgba(0, 0, 0, 0.12);
    background-color: white;
    margin-top: 8px;

    // Empty state styling
    &:empty {
      padding: 16px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #999;
      font-style: italic;

      &::after {
        content: 'No related items available';
      }
    }
  }
}

// Dark mode support
.q-dark {
  .related-items-list {
    border-color: rgba(255, 255, 255, 0.12);
    background-color: #1d1d1d;
  }
}

// Scrollbar styling is now handled by the global .custom-scrollbar class
</style>
