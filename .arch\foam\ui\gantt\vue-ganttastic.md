# Vue-Ganttastic Integration

This document outlines how to integrate the Vue-Ganttastic library into our Quasar project to complement the calendar view with a Gantt chart view.

## Library Overview

**Vue-Ganttastic** (@infectoone/vue-ganttastic) is a simple, interactive, and highly customizable Gantt chart component for Vue 3.

- **GitHub**: [https://github.com/zunnzunn/vue-ganttastic](https://github.com/zunnzunn/vue-ganttastic)
- **Documentation**: [https://zunnzunn.github.io/vue-ganttastic/](https://zunnzunn.github.io/vue-ganttastic/)
- **Version**: 2.3.2 (as of assessment)
- **License**: MIT

## Features

- **Vue 3 Support**: Built specifically for Vue 3
- **TypeScript Support**: Ships with out-of-the-box type declarations
- **Interactive**: Dynamic, movable, and pushable bars with drag-and-drop functionality
- **Reactive/Responsive**: When changes occur, bars are repositioned accordingly
- **Customizable**: Extensive styling options for both the chart and individual bars
- **Event Handling**: Comprehensive event system for interactions

## Integration with Quasar

### Installation

```bash
# Using bun
bun add @infectoone/vue-ganttastic

# Or using npm
npm install @infectoone/vue-ganttastic
```

### Registration

Register the components in a Quasar boot file (e.g., `src/boot/ganttastic.js`):

```javascript
import { boot } from 'quasar/wrappers'
import ganttastic from '@infectoone/vue-ganttastic'

export default boot(({ app }) => {
  app.use(ganttastic)
})
```

Then add this boot file to the `quasar.config.js`:

```javascript
boot: [
  // ... other boot files
  'ganttastic'
],
```

### Basic Usage

```vue
<template>
  <g-gantt-chart
    chart-start="2023-01-01"
    chart-end="2023-01-31"
    precision="day"
    bar-start="startDate"
    bar-end="endDate"
    grid
    width="100%"
  >
    <g-gantt-row 
      v-for="project in projects" 
      :key="project.id"
      :label="project.name"
      :bars="project.tasks"
      highlight-on-hover
    />
  </g-gantt-chart>
</template>

<script setup>
import { ref } from 'vue'

const projects = ref([
  {
    id: 1,
    name: 'Project Alpha',
    tasks: [
      {
        startDate: '2023-01-05',
        endDate: '2023-01-15',
        ganttBarConfig: {
          id: 'task-1',
          label: 'Research Phase',
          style: {
            background: '#42b883' // Vue green
          }
        }
      },
      {
        startDate: '2023-01-16',
        endDate: '2023-01-25',
        ganttBarConfig: {
          id: 'task-2',
          label: 'Development Phase',
          style: {
            background: '#35495e' // Vue dark blue
          }
        }
      }
    ]
  },
  // More projects...
])
</script>
```

## Data Structure

Each bar in the Gantt chart requires:

1. A start date property (name specified by `bar-start` prop)
2. An end date property (name specified by `bar-end` prop)
3. A `ganttBarConfig` object with at least:
   - A unique `id`
   - Optional `label` for display text
   - Optional `style` object for custom styling

Example bar object:

```javascript
{
  startDate: '2023-01-05', // Property name must match bar-start
  endDate: '2023-01-15',   // Property name must match bar-end
  ganttBarConfig: {
    id: 'unique-id-123',   // Required
    label: 'Task Name',    // Optional
    hasHandles: true,      // Optional: enables resize handles
    style: {               // Optional: custom styling
      background: '#42b883',
      borderRadius: '5px',
      color: 'white'
    },
    class: 'custom-class'  // Optional: custom CSS class
  },
  // You can add any other custom properties
  priority: 'High',
  assignee: 'John Doe'
}
```

## Key Props for g-gantt-chart

| Prop | Type | Description |
|------|------|-------------|
| `chart-start` | String/Date | Start date of the chart |
| `chart-end` | String/Date | End date of the chart |
| `precision` | String | Time unit ('hour', 'day', 'week', 'month') |
| `bar-start` | String | Property name for bar start date |
| `bar-end` | String | Property name for bar end date |
| `width` | String | Chart width (e.g., '100%') |
| `row-height` | Number | Height of each row (default: 40) |
| `grid` | Boolean | Show grid lines |
| `current-time` | Boolean | Show current time indicator |
| `date-format` | String | Format for dates (using dayjs) |
| `color-scheme` | String | Predefined color scheme ('default', 'dark', 'vue') |
| `push-on-overlap` | Boolean | Push bars when they overlap |
| `no-overlap` | Boolean | Prevent bars from overlapping |

## Events

The component provides various events for interaction:

```vue
<g-gantt-chart
  @click-bar="onClickBar"
  @dblclick-bar="onDblClickBar"
  @mouseenter-bar="onMouseEnterBar"
  @mouseleave-bar="onMouseLeaveBar"
  @dragstart-bar="onDragStartBar"
  @drag-bar="onDragBar"
  @dragend-bar="onDragEndBar"
  @contextmenu-bar="onContextMenuBar"
>
  <!-- rows -->
</g-gantt-chart>
```

## Customization

### Bar Styling

Each bar can be individually styled:

```javascript
ganttBarConfig: {
  id: 'task-1',
  label: 'Important Task',
  style: {
    background: 'linear-gradient(to right, #42b883, #35495e)',
    borderRadius: '10px',
    boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
    color: 'white',
    fontSize: '12px',
    fontWeight: 'bold'
  }
}
```

### Slots

The component provides various slots for customization:

- `bar-label`: Customize the content inside bars
- `bar-tooltip`: Customize the tooltip shown on hover
- `label-column-title`: Customize the label column header
- `label-column-row`: Customize the label column content
- `upper-timeunit`: Customize the upper time unit display
- `timeunit`: Customize the time unit display
- `current-time-label`: Customize the current time indicator label

## Integration with Quasar Calendar

When integrating with Quasar Calendar, consider:

1. **Shared Data Model**: Use a consistent data model that works for both views
2. **Synchronized Date Range**: Keep the date ranges in sync between views
3. **Toggle Mechanism**: Implement a toggle to switch between Calendar and Gantt views
4. **Consistent Styling**: Apply consistent styling across both components

## Example Integration with Quasar Calendar

```vue
<template>
  <div>
    <q-btn-toggle
      v-model="viewType"
      :options="[
        { label: 'Calendar', value: 'calendar' },
        { label: 'Gantt', value: 'gantt' }
      ]"
      class="q-mb-md"
    />
    
    <q-calendar
      v-if="viewType === 'calendar'"
      v-model="selectedDate"
      view="month"
      :events="calendarEvents"
    />
    
    <g-gantt-chart
      v-else
      :chart-start="ganttStart"
      :chart-end="ganttEnd"
      precision="day"
      bar-start="startDate"
      bar-end="endDate"
      grid
      width="100%"
    >
      <g-gantt-row 
        v-for="project in projects" 
        :key="project.id"
        :label="project.name"
        :bars="project.tasks"
        highlight-on-hover
      />
    </g-gantt-chart>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { date } from 'quasar'

const viewType = ref('calendar')
const selectedDate = ref(new Date())

// Data that works for both views
const projects = ref([/* project data */])

// Computed properties for Gantt view
const ganttStart = computed(() => {
  return date.formatDate(date.startOfMonth(selectedDate.value), 'YYYY-MM-DD')
})

const ganttEnd = computed(() => {
  return date.formatDate(date.endOfMonth(selectedDate.value), 'YYYY-MM-DD')
})

// Transform project tasks to calendar events
const calendarEvents = computed(() => {
  const events = []
  projects.value.forEach(project => {
    project.tasks.forEach(task => {
      events.push({
        title: task.ganttBarConfig.label,
        start: task.startDate,
        end: task.endDate,
        bgcolor: task.ganttBarConfig.style?.background || '#42b883'
      })
    })
  })
  return events
})
</script>
```

## Performance Considerations

- For large datasets, consider pagination or lazy loading of Gantt rows
- Use `v-show` instead of `v-if` when toggling between views to preserve component state
- Consider using `v-once` for static parts of the Gantt chart

## Conclusion

Vue-Ganttastic is a well-built, maintained component that integrates well with Quasar. It provides comprehensive Gantt chart functionality and should complement the Quasar calendar view effectively. The library's interactive features and customization options make it suitable for our project requirements.
