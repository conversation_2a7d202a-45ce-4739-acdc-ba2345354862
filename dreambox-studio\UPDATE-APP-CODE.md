# Updating App Code for New Database Structure

This document provides step-by-step instructions for updating the application code to work with the new database structure.

## 1. Test the Database Connection

First, test the connection to the new database:

```bash
cd dreambox-studio/scripts
bun test-db-connection.js
```

This script will verify that the application can connect to the new database and access the required tables. The script has been updated to use hardcoded values if it can't find the environment variables, so it should work even if the `.env` file is not in the expected location.

## 2. Update the Database Types

Replace the existing database types with the new ones:

```bash
# Backup the existing types
cp src/types/supabase.ts src/types/supabase.backup.ts

# Copy the new types
cp src/types/supabase-new.ts src/types/supabase.ts
```

## 3. Update the Auth Types

Replace the existing auth types with the new ones:

```bash
# Backup the existing types
cp src/types/auth.ts src/types/auth.backup.ts

# Copy the new types
cp src/types/auth-new.ts src/types/auth.ts
```

## 4. Update the Auth Store

Replace the existing auth store with the new one:

```bash
# Backup the existing store
cp src/stores/auth.ts src/stores/auth.backup.ts

# Copy the new store
cp src/stores/auth-new.ts src/stores/auth.ts
```

## 5. Update the Supabase Boot File

Replace the existing Supabase boot file with the new one:

```bash
# Backup the existing boot file
cp src/boot/supabase.ts src/boot/supabase.backup.ts

# Copy the new boot file
cp src/boot/supabase-updated.ts src/boot/supabase.ts
```

The updated boot file includes fallback to hardcoded values if the environment variables are not found, so it should work even if the `.env` file is not properly configured.

## 6. Update the Scripts

Update the scripts that connect directly to the database:

```bash
# Backup the existing scripts
cp scripts/check-db.js scripts/check-db.backup.js
cp scripts/check-db-tables.js scripts/check-db-tables.backup.js
cp scripts/add-users-from-csv.js scripts/add-users-from-csv.backup.js
cp scripts/add-test-user.js scripts/add-test-user.backup.js

# Copy the new scripts
cp scripts/check-db-new.js scripts/check-db.js
```

## 7. Test the Application

Start the application and test the login flow:

```bash
cd dreambox-studio
bun dev
```

1. Try logging in with a user that exists in the allowed_emails table
2. Verify that the user is assigned the correct roles and companies
3. Test role-based access control

## 8. Troubleshooting

If you encounter issues:

1. Check the browser console for errors
2. The scripts and boot files have been updated to use hardcoded values if the environment variables are not found, so they should work even if the `.env` file is not properly configured
3. Run the test-db-connection.js script to verify the database connection
4. Check the app_logs table for any error messages
5. If you're still having issues, try running the scripts with the `--inspect` flag to debug them:
   ```bash
   bun --inspect test-db-connection.js
   ```

## 9. Rollback

If necessary, you can roll back to the previous version:

```bash
# Restore the original files
cp src/types/supabase.backup.ts src/types/supabase.ts
cp src/types/auth.backup.ts src/types/auth.ts
cp src/stores/auth.backup.ts src/stores/auth.ts
cp src/boot/supabase.backup.ts src/boot/supabase.ts
cp scripts/check-db.backup.js scripts/check-db.js
cp scripts/check-db-tables.backup.js scripts/check-db-tables.js
cp scripts/add-users-from-csv.backup.js scripts/add-users-from-csv.js
cp scripts/add-test-user.backup.js scripts/add-test-user.js
```
