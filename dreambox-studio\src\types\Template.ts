import type { ProductTypeBasic } from 'src/types/ProductType';

/**
 * Template element interface
 */
export interface TemplateElement {
  id: number;
  value: string;
  description: string | null;
}

/**
 * Map of element types to elements
 */
export interface TemplateElementsMap {
  [key: string]: TemplateElement[];
}

/**
 * Template image interface
 */
export interface TemplateImage {
  id: number;
  title: string;
  image_url: string;
  status: string;
  metadata: Record<string, unknown>;
}

/**
 * Template event interface
 */
export interface TemplateEvent {
  id: number;
  title: string;
  start_date: string;
  end_date: string | null;
  status: string;
}

/**
 * Template product interface
 */
export interface TemplateProduct {
  id: number;
  name: string;
  sku: string;
  base_price: number;
  active: boolean;
}

/**
 * Template collection interface
 */
export interface TemplateCollection {
  id: number;
  name: string;
  description: string | null;
}

/**
 * Template interface
 */
export interface Template {
  id: number;
  name: string;
  description: string | null;
  version: number;
  created_at: string;
  updated_at: string;
  tmpl_company_id: number; // Changed from company_id
  company_name: string;
  initiated_by: number;
  initiated_by_name: string;
  approved_by: number | null;
  approved_by_name: string | null;
  approval_date: string | null;
  agent_id: number | null;
  agent_name: string | null;
  status: 'draft' | 'pending' | 'active' | 'archived';
  published: boolean;
  image_server_data: Record<string, unknown> | null;
  elements: TemplateElementsMap;
  element_types?: number[];
  element_values?: number[];
  images: TemplateImage[];
  events: TemplateEvent[];
  products: TemplateProduct[];
  collections: TemplateCollection[];
  product_types?: ProductTypeBasic[]; // New field for product types
}

/**
 * Template filter interface
 */
export interface TemplateFilter {
  tmpl_company_id?: number | undefined; // Changed from company_id
  designer_id?: number | undefined;
  status?: string | undefined;
  product_type_id?: number | undefined; // New field for filtering by product type
  search?: string | undefined;
  limit?: number | undefined;
  offset?: number | undefined;
}

/**
 * Template pagination interface
 */
export interface TemplatePagination {
  page: number;
  rowsPerPage: number;
  rowsNumber: number;
}

/**
 * Template status history interface
 */
export interface TemplateStatusHistory {
  id: number;
  template_id: number;
  previous_status: 'draft' | 'pending' | 'active' | 'archived';
  new_status: 'draft' | 'pending' | 'active' | 'archived';
  changed_by: number;
  changed_by_name: string;
  change_date: string;
  notes: string | null;
}
