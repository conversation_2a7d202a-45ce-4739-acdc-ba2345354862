-- SwarmUI Server Management Tables

-- Server instances table
CREATE TABLE IF NOT EXISTS swarmui_servers (
  id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  pod_id TEXT NOT NULL,
  api_endpoint TEXT NOT NULL,
  api_key TEXT NOT NULL,
  status TEXT NOT NULL,
  gpu_type TEXT NOT NULL,
  started_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  stopped_at TIMESTAMP WITH TIME ZONE,
  last_activity TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Add RLS policies for swarmui_servers
ALTER TABLE swarmui_servers ENABLE ROW LEVEL SECURITY;

-- Policy for users to view only their own servers
CREATE POLICY "Users can view their own servers"
  ON swarmui_servers
  FOR SELECT
  USING (auth.uid() = user_id);

-- Policy for users to insert their own servers
CREATE POLICY "Users can insert their own servers"
  ON swarmui_servers
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Policy for users to update their own servers
CREATE POLICY "Users can update their own servers"
  ON swarmui_servers
  FOR UPDATE
  USING (auth.uid() = user_id);

-- Policy for users to delete their own servers
CREATE POLICY "Users can delete their own servers"
  ON swarmui_servers
  FOR DELETE
  USING (auth.uid() = user_id);

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update the updated_at timestamp
CREATE TRIGGER update_swarmui_servers_updated_at
BEFORE UPDATE ON swarmui_servers
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Create images storage bucket if it doesn't exist
-- Note: This needs to be done manually in the Supabase dashboard
-- Go to Storage > Create a new bucket > Name it "images" > Enable public access

-- Set up RLS policies for storage
-- Allow users to read public images
CREATE POLICY "Public images are viewable by everyone"
  ON storage.objects
  FOR SELECT
  USING (bucket_id::text = 'images' AND storage.foldername(name) = 'public');

-- Allow authenticated users to upload images
CREATE POLICY "Users can upload images"
  ON storage.objects
  FOR INSERT
  WITH CHECK (
    bucket_id::text = 'images' AND
    auth.uid() = owner AND
    (storage.foldername(name) = 'generated/' || auth.uid()::text OR
     storage.foldername(name) = 'uploads/' || auth.uid()::text)
  );

-- Allow users to update their own images
CREATE POLICY "Users can update their own images"
  ON storage.objects
  FOR UPDATE
  USING (
    bucket_id::text = 'images' AND
    auth.uid() = owner AND
    (storage.foldername(name) = 'generated/' || auth.uid()::text OR
     storage.foldername(name) = 'uploads/' || auth.uid()::text)
  );

-- Allow users to delete their own images
CREATE POLICY "Users can delete their own images"
  ON storage.objects
  FOR DELETE
  USING (
    bucket_id::text = 'images' AND
    auth.uid() = owner AND
    (storage.foldername(name) = 'generated/' || auth.uid()::text OR
     storage.foldername(name) = 'uploads/' || auth.uid()::text)
  );
