# Context Manager Architecture Proposal

## Core Concept

The Context Manager is a central system that controls the UI components and their behavior based on the current application context (e.g., "templates", "users", "products"). It ensures that all parts of the UI respond appropriately to the current context, providing a consistent and intuitive user experience.

## Context Manager Structure

### 1. Core Context Manager (Pinia Store)

```typescript
// stores/contextStore.ts
import { defineStore } from 'pinia';
import { ref, reactive, computed } from 'vue';
import { contexts } from './contexts'; // Import all predefined contexts

export const useContextStore = defineStore('context', () => {
  // Current context ID
  const currentContextId = ref('none');
  
  // Get the current context configuration
  const currentContext = computed(() => 
    contexts[currentContextId.value] || contexts.default
  );
  
  // Active components based on current context
  const leftDrawerTabs = computed(() => currentContext.value.leftDrawer.tabs);
  const rightDrawerTabs = computed(() => currentContext.value.rightDrawer.tabs);
  const searchConfig = computed(() => currentContext.value.search);
  const bottomBarActions = computed(() => currentContext.value.bottomBarActions);
  const mainContent = computed(() => currentContext.value.mainContent);
  
  // Switch context
  function setContext(contextId: string) {
    // Cleanup previous context if needed
    
    // Set new context
    currentContextId.value = contextId;
    
    // Initialize new context
    // - Update search store
    // - Set appropriate drawer tabs
    // - Configure bottom bar actions
  }
  
  return {
    currentContextId,
    currentContext,
    leftDrawerTabs,
    rightDrawerTabs,
    searchConfig,
    bottomBarActions,
    mainContent,
    setContext
  };
});
```

### 2. Context Configuration Interface

```typescript
// types/context.ts
import { Component } from 'vue';

export interface ContextConfig {
  id: string;                 // Unique identifier (e.g., "templates")
  name: string;               // Display name (e.g., "Templates")
  
  // Left drawer configuration
  leftDrawer: {
    tabs: {                   // Which tabs to show
      filters: boolean;
      tools: boolean;
      // Additional dynamic tabs
      customTabs?: Array<{
        id: string;
        label: string;
        icon: string;
        component: Component;
      }>;
    };
    // Components to load in each tab
    components: {
      filters?: Component;
      tools?: Component;
      // Other tab components
    };
  };
  
  // Right drawer configuration
  rightDrawer: {
    tabs: {                   // Which tabs to show
      preview: boolean;
      settings: boolean;
      chat: boolean;
      // Additional dynamic tabs
    };
    // Components to load in each tab
    components: {
      preview?: Component;
      settings?: Component;
      chat?: Component;
    };
  };
  
  // Search configuration
  search: {
    visible: boolean;         // Whether to show search
    placeholder: string;
    targetCollection: string; // What to search (e.g., "templates")
    filters?: Array<Filter>;  // Available filters
  };
  
  // Bottom bar actions
  bottomBarActions: Array<{
    id: string;
    icon: string;
    label: string;
    action: Function;
    condition?: Function;     // When to show/enable this action
  }>;
  
  // Main content configuration
  mainContent: {
    component: Component;     // The main component to render
    props?: Record<string, any>; // Props to pass to the component
  };
}
```

### 3. Context Definitions

Create separate files for each context configuration:

```typescript
// stores/contexts/templates.ts
import TemplateFilters from '@/components/templates/TemplateFilters.vue';
import TemplateTools from '@/components/templates/TemplateBuilderTools.vue';
import TemplatePreview from '@/components/templates/TemplateBuilderPreview.vue';
import TemplateSettings from '@/components/templates/TemplateBuilderSettings.vue';
import TemplateChat from '@/components/templates/TemplateChat.vue';
import TemplateTable from '@/components/templates/TemplateTable.vue';

export const templatesContext: ContextConfig = {
  id: 'templates',
  name: 'Templates',
  
  leftDrawer: {
    tabs: {
      filters: true,
      tools: true
    },
    components: {
      filters: TemplateFilters,
      tools: TemplateTools
    }
  },
  
  rightDrawer: {
    tabs: {
      preview: true,
      settings: true,
      chat: true
    },
    components: {
      preview: TemplatePreview,
      settings: TemplateSettings,
      chat: TemplateChat
    }
  },
  
  search: {
    visible: true,
    placeholder: 'Search templates...',
    targetCollection: 'templates',
    filters: [
      {
        name: 'status',
        label: 'Status',
        options: [
          { label: 'Draft', value: 'draft' },
          { label: 'Published', value: 'published' },
          { label: 'Archived', value: 'archived' }
        ]
      }
    ]
  },
  
  bottomBarActions: [
    {
      id: 'new-template',
      icon: 'add',
      label: 'New Template',
      action: (router) => router.push('/templates/builder/new')
    },
    {
      id: 'edit-template',
      icon: 'edit',
      label: 'Edit',
      action: (router, template) => router.push(`/templates/builder/${template.id}`),
      condition: (state) => state.selectedTemplates.length === 1
    },
    // More actions...
  ],
  
  mainContent: {
    component: TemplateTable
  }
};
```

### 4. Context Registry

```typescript
// stores/contexts/index.ts
import { templatesContext } from './templates';
import { usersContext } from './users';
import { productsContext } from './products';
import { dashboardContext } from './dashboard';

// Default context (fallback)
import { defaultContext } from './default';

export const contexts = {
  templates: templatesContext,
  users: usersContext,
  products: productsContext,
  dashboard: dashboardContext,
  default: defaultContext
};
```

## Integration with MainLayout

```vue
<template>
  <q-layout>
    <!-- Top bar -->
    <q-header>
      <!-- Left side with menu button and app title -->
      <q-btn
        flat
        round
        dense
        icon="menu"
        aria-label="Menu"
        @click="toggleLeftDrawer"
      />
      
      <!-- App title and icon (hidden on mobile if search is visible) -->
      <q-toolbar-title 
        v-if="!contextStore.searchConfig.visible || $q.screen.gt.xs"
        :style="`padding: 0; min-height: 0; margin-left: ${$q.screen.lt.sm ? '12px' : '50px'};`" 
        :class="{'gt-xs': contextStore.searchConfig.visible}"
      >
        <q-avatar size="24px" class="q-mr-sm">
          <img src="https://cdn.quasar.dev/logo-v2/svg/logo-mono-white.svg">
        </q-avatar>
        <span>{{ $t('app.name') }}</span>
      </q-toolbar-title>
      
      <q-space />
      
      <!-- Search input (only shown when needed) -->
      <q-input
        v-if="contextStore.searchConfig.visible"
        v-model="searchStore.searchText"
        dense
        borderless
        dark
        color="white"
        class="search-input q-mr-sm"
        :placeholder="contextStore.searchConfig.placeholder"
        clearable
        debounce="300"
        @update:model-value="onSearch"
      >
        <template v-slot:append>
          <q-icon name="search" />
        </template>
      </q-input>
      
      <!-- Right drawer toggle -->
      <q-btn
        flat
        round
        dense
        icon="menu"
        color="white"
        aria-label="Toggle Right Drawer"
        @click="toggleRightDrawer"
        :style="$q.screen.lt.sm ? 'margin-left: 4px;' : 'margin-left: 50px;'"
      />
    </q_header>
    
    <!-- Left drawer with dynamic tabs -->
    <q-drawer>
      <q-tabs>
        <q-tab name="nav" icon="home" />
        <q-tab 
          v-if="contextStore.leftDrawerTabs.filters" 
          name="filters" 
          icon="filter_list" 
        />
        <q-tab 
          v-if="contextStore.leftDrawerTabs.tools" 
          name="tools" 
          icon="build" 
        />
        <!-- Dynamic tabs -->
        <q-tab 
          v-for="tab in contextStore.leftDrawerTabs.customTabs" 
          :key="tab.id"
          :name="tab.id"
          :icon="tab.icon"
        />
      </q_tabs>
      
      <!-- Dynamic tab panels -->
      <q_tab-panels>
        <!-- Navigation panel -->
        <q-tab-panel name="nav">
          <tree-navigation />
        </q-tab-panel>
        
        <!-- Filters panel - dynamically loaded component -->
        <q-tab-panel 
          v-if="contextStore.leftDrawerTabs.filters" 
          name="filters"
        >
          <component :is="contextStore.leftDrawer.components.filters" />
        </q_tab-panel>
        
        <!-- Tools panel - dynamically loaded component -->
        <q-tab-panel 
          v-if="contextStore.leftDrawerTabs.tools" 
          name="tools"
        >
          <component :is="contextStore.leftDrawer.components.tools" />
        </q_tab-panel>
        
        <!-- Dynamic custom tabs -->
        <q_tab-panel
          v-for="tab in contextStore.leftDrawerTabs.customTabs"
          :key="tab.id"
          :name="tab.id"
        >
          <component :is="tab.component" />
        </q_tab-panel>
      </q_tab-panels>
    </q_drawer>
    
    <!-- Right drawer with dynamic tabs -->
    <!-- Similar structure to left drawer -->
    
    <!-- Main content area -->
    <q-page-container>
      <component 
        :is="contextStore.mainContent.component"
        v-bind="contextStore.mainContent.props"
      />
    </q_page-container>
    
    <!-- Bottom bar with dynamic actions -->
    <q_footer v-if="contextStore.bottomBarActions.length > 0">
      <!-- Mobile-only expanded content -->
      <div v-if="footerExpanded && $q.screen.lt.md" class="footer-expanded-content">
        <div class="row q-col-gutter-md">
          <div 
            v-for="action in contextStore.bottomBarActions"
            :key="action.id"
            class="col-3"
          >
            <q-btn
              :icon="action.icon"
              :label="$q.screen.gt.xs ? action.label : undefined"
              :disable="action.condition ? !action.condition(state) : false"
              @click="action.action(router, selectedItems[0])"
            />
          </div>
        </div>
      </div>
    </q_footer>
  </q_layout>
</template>

<script setup>
import { useContextStore } from 'src/stores/contextStore';
import { useSearchStore } from 'src/stores/searchStore';
import { useRouter, useRoute } from 'vue-router';

const contextStore = useContextStore();
const searchStore = useSearchStore();
const router = useRouter();
const route = useRoute();

// Watch for route changes to set the appropriate context
watch(() => route.path, (newPath) => {
  if (newPath.includes('/templates')) {
    contextStore.setContext('templates');
  } else if (newPath.includes('/users')) {
    contextStore.setContext('users');
  } else if (newPath.includes('/products')) {
    contextStore.setContext('products');
  } else if (newPath === '/' || newPath.includes('/dashboard')) {
    contextStore.setContext('dashboard');
  }
  // Add more route-based context switching
});
</script>
```

## Sub-Contexts

For more complex scenarios, we can implement sub-contexts. For example, the "templates" context might have sub-contexts for "browse" and "builder":

```typescript
// stores/contextStore.ts
function setContext(contextId: string, subContextId?: string) {
  currentContextId.value = contextId;
  currentSubContextId.value = subContextId || 'default';
  
  // Initialize context and sub-context
}

// Usage
contextStore.setContext('templates', 'builder');
```

## Benefits

1. **Centralized Configuration**: All UI elements for a specific context are defined in one place
2. **Consistency**: Ensures all parts of the UI respond correctly to context changes
3. **Maintainability**: Makes it easier to add new contexts or modify existing ones
4. **Flexibility**: Allows for completely different UI configurations based on context
5. **Responsive Design**: Can easily incorporate different behaviors for mobile vs. desktop

## Implementation Strategy

1. Start by defining the context store and basic structure
2. Create configurations for the most important contexts (templates, users, etc.)
3. Gradually refactor the MainLayout to use the context store
4. Add responsive behavior for desktop vs. mobile
5. Implement sub-contexts for more complex scenarios
