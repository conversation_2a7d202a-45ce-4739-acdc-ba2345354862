const fs = require('fs');
const path = require('path');

// Function to comment out console.log statements in a file
function commentConsoleLogs(filePath) {
  try {
    // Read the file
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Replace console.log with // console.log
    const updatedContent = content.replace(/console\.log\(/g, '// console.log(');
    
    // Replace console.error with // console.error
    const finalContent = updatedContent.replace(/console\.error\(/g, '// console.error(');
    
    // Write the updated content back to the file
    fs.writeFileSync(filePath, finalContent, 'utf8');
    
    console.log(`Commented out console logs in ${filePath}`);
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error);
  }
}

// Function to process a directory recursively
function processDirectory(directory) {
  const files = fs.readdirSync(directory);
  
  for (const file of files) {
    const filePath = path.join(directory, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isDirectory()) {
      // Recursively process subdirectories
      processDirectory(filePath);
    } else if (stats.isFile() && (file.endsWith('.js') || file.endsWith('.ts') || file.endsWith('.vue'))) {
      // Process JavaScript, TypeScript, and Vue files
      commentConsoleLogs(filePath);
    }
  }
}

// Start processing from the src directory
processDirectory('dreambox-studio/src');
