# Prompt Element Type Order Implementation Plan

## Problem

Currently, when reordering prompt element types in templates, the order is not persisted in the database. The UI shows a toast message indicating "Element type order updated", but after reloading the page, the element types revert to their original order.

## Root Cause

The issue is that the code is trying to use the `reorderTemplateElements` function to save the element type order, but this function is designed to work with element IDs from the `prompt_elements_usage` table, not element type IDs. There is no dedicated table or mechanism to store the order of element types for a template.

## Solution

Create a new database table and associated functions to store and manage the order of element types for each template.

## Implementation Steps

### 1. Database Changes

1. Create a new table `prompt_element_type_order` to store the order of element types for each template
2. Create functions to manage this order:
   - `get_highest_element_type_order`: Returns the highest order index for element types in a template
   - `reorder_element_types`: Reorders element types within a template
   - `get_ordered_element_types`: Returns ordered element types for a template
   - `admin_reorder_element_types`: Admin function for reordering element types

### 2. TypeScript Service

Create a new TypeScript service `elementTypeOrderService.ts` with the following functions:
- `reorderElementTypes`: Reorders element types in a template
- `getOrderedElementTypes`: Gets ordered element types for a template

### 3. Component Updates

Update the `PromptElementSelector.vue` component to:
1. Import and use the new element type order service
2. Update the `onTypeReorderEnd` function to use the new `reorderElementTypes` function
3. Update the `fetchTemplateElements` function to load and apply the ordered element types

### 4. RLS Policies

Create RLS policies for the new `prompt_element_type_order` table to ensure proper access control.

## Files to Create/Modify

### New Files:
1. `sql/create_prompt_element_type_order.sql`: SQL script to create the new table and functions
2. `dreambox-studio/src/services/elementTypeOrderService.ts`: TypeScript service for element type ordering
3. `sql/prompt_element_type_order_rls.sql`: SQL script to create RLS policies for the new table

### Files to Modify:
1. `dreambox-studio/src/components/templates/PromptElementSelector.vue`: Update to use the new service

## Implementation Details

### Database Table Structure

```sql
CREATE TABLE IF NOT EXISTS prompt_element_type_order (
    id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    template_id BIGINT NOT NULL REFERENCES templates(id),
    element_type_id BIGINT NOT NULL REFERENCES prompt_element_types(id),
    order_index INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(template_id, element_type_id)
);
```

### TypeScript Service Interface

```typescript
export interface ElementTypeOrder {
  elementTypeId: number;
  order: number;
}

export interface OrderedElementType {
  element_type_id: number;
  name: string;
  description: string | null;
  is_array: boolean;
  is_required: boolean;
  order_index: number;
}
```

## Deployment Steps

1. Run the SQL script to create the new table and functions
2. Run the SQL script to create RLS policies for the new table
3. Deploy the new TypeScript service
4. Deploy the updated component

## Testing

1. Create a new template and add multiple element types
2. Reorder the element types
3. Verify that the toast message appears
4. Reload the page and verify that the element types maintain their order
5. Test with different user roles to ensure RLS policies are working correctly

## Rollback Plan

If issues are encountered:
1. Revert the component changes
2. Drop the new table and functions
3. Remove the new TypeScript service
