// app global css in SCSS form
@use "sass:color";

// CSS variables for table text wrapping
:root {
  --table-text-wrap-mode: normal; // Default to wrapping text
  --table-text-overflow: visible; // Default to visible overflow
}

// Global class for table cell text wrapping
.q-table td {
  white-space: var(--table-text-wrap-mode) !important;
  overflow: hidden !important;
  text-overflow: var(--table-text-overflow) !important;
  max-width: 0; // This forces the cell to respect the ellipsis
}

// Apply text wrapping to textarea inputs in tables
.q-table td .q-field__native textarea {
  white-space: var(--table-text-wrap-mode) !important;
  overflow: hidden !important;
  text-overflow: var(--table-text-overflow) !important;
}

// Add specific styles for table text ellipsis mode
.table-text-ellipsis .q-table td {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

// Make sure the popup edit content is not affected by the ellipsis setting
.q-popup-edit {
  .q-field__native,
  .q-field__native textarea {
    white-space: normal !important;
    overflow: visible !important;
    text-overflow: clip !important;
  }
}

// Hide 'Records per page' text on mobile devices
@media (max-width: 599px) {
  .q-table__bottom-item:not(.q-table__select) {
    display: none !important;
  }

  // Reduce the number of visible page buttons on mobile
  .q-pagination {
    .q-btn:not(.q-btn--round) {
      padding: 0 5px;
      min-width: 30px;
    }

    // Show fewer page numbers on mobile
    .q-btn:not(.q-btn--round):nth-child(n+5):not(:last-child):not(:nth-last-child(2)) {
      display: none;
    }
  }
}

// Import design tokens and variables
@import './design-tokens.scss';

// Global reset and base styles
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  width: 100%;
  font-family: $typography-font-family;
  font-size: 16px;
  line-height: $line-height-md;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// Light mode styles
.body--light {
  background-color: $light-page;
  color: rgba(0, 0, 0, 0.87);
}

// Dark mode styles
.body--dark {
  background-color: $dark-page;
  color: rgba(255, 255, 255, 0.87);
}

// Typography styles
h1, h2, h3, h4, h5, h6 {
  margin-bottom: $space-md;
  font-weight: $font-weight-medium;
  line-height: $line-height-sm;
}

h1 { font-size: $font-size-xxl; }
h2 { font-size: $font-size-xl; }
h3 { font-size: $font-size-lg; }
h4 { font-size: $font-size-md; }
h5 { font-size: $font-size-sm; }
h6 { font-size: $font-size-xs; }

p {
  margin-bottom: $space-md;
}

// Link styles
a {
  color: $primary;
  text-decoration: none;
  transition: color $transition-quick;

  &:hover {
    color: $primary-dark;
    text-decoration: underline;
  }

  .body--dark & {
    color: $primary-light;

    &:hover {
      color: color.adjust($primary-light, $lightness: 10%);
    }
  }
}

// Utility classes
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-weight-light { font-weight: $font-weight-light; }
.font-weight-regular { font-weight: $font-weight-regular; }
.font-weight-medium { font-weight: $font-weight-medium; }
.font-weight-bold { font-weight: $font-weight-bold; }

// Responsive container classes
.container {
  width: 100%;
  padding-right: $space-md;
  padding-left: $space-md;
  margin-right: auto;
  margin-left: auto;

  @media (min-width: $breakpoint-sm) {
    max-width: 540px;
  }

  @media (min-width: $breakpoint-md) {
    max-width: 720px;
  }

  @media (min-width: $breakpoint-lg) {
    max-width: 960px;
  }

  @media (min-width: $breakpoint-xl) {
    max-width: 1140px;
  }
}

.container-fluid {
  width: 100%;
  padding-right: $space-md;
  padding-left: $space-md;
  margin-right: auto;
  margin-left: auto;
}

// Platform-specific styles
.platform-mobile {
  font-size: 14px;

  h1 { font-size: $font-size-xl; }
  h2 { font-size: $font-size-lg; }
  h3 { font-size: $font-size-md; }

  .container, .container-fluid {
    padding-right: $space-sm;
    padding-left: $space-sm;
  }
}

.platform-electron {
  // Electron-specific global styles
}

// Quasar component overrides
.q-card {
  border-radius: $border-radius-md;
  transition: box-shadow $transition-medium, transform $transition-medium;

  &:hover {
    transform: translateY(-2px);
  }
}

// Hide all loading indicators globally
.q-inner-loading {
  .q-loading-bar {
    display: none !important;
  }

  .text-center {
    display: none !important;
  }
}

.q-btn {
  font-weight: $font-weight-medium;
  letter-spacing: 0.5px;
}

.q-table {
  border-radius: $border-radius-md;
  overflow: hidden;
}

.q-drawer {
  .q-item {
    border-radius: $border-radius-sm;
    margin: 0 8px;

    &.q-item--active {
      background-color: rgba($primary, 0.1);

      .body--dark & {
        background-color: rgba($primary-light, 0.2);
      }
    }
  }
}

// Ensure drawer resizers are always visible
.drawer-resizer {
  position: fixed !important;
  z-index: 2002 !important;

  // Left drawer resizer positioning
  &--left {
    right: 0;
    top: 50px; // Adjust based on your header height
    bottom: 0;
  }

  // Right drawer resizer positioning
  &--right {
    left: 0;
    top: 50px; // Adjust based on your header height
    bottom: 0;
  }
}

// Custom scrollbar styling for dropdowns
.custom-scrollbar {
  // Firefox scrollbar styling
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;

  // Webkit scrollbar styling (Chrome, Safari, Edge)
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.3);
  }

  // Dark mode scrollbar styling
  .body--dark & {
    scrollbar-color: rgba(255, 255, 255, 0.2) transparent;

    &::-webkit-scrollbar-thumb {
      background-color: rgba(255, 255, 255, 0.2);
    }

    &::-webkit-scrollbar-thumb:hover {
      background-color: rgba(255, 255, 255, 0.3);
    }
  }
}
