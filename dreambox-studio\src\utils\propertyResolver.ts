/**
 * Property Resolver Utility
 *
 * Provides functions for resolving component properties using context variables.
 * Replaces placeholders like [name] with values from variables.
 */

/**
 * Resolve properties using extracted variables
 * Replaces placeholders like [name] with values from variables
 *
 * @param properties The properties object with placeholders
 * @param variables The variables to use for replacement
 * @returns The resolved properties object
 */
export function resolveProperties(
  properties: Record<string, unknown>,
  variables: Record<string, unknown>
): Record<string, unknown> {
  const resolved: Record<string, unknown> = {};

  // Clone the properties object
  for (const [key, value] of Object.entries(properties)) {
    if (typeof value === 'string') {
      // Replace placeholders in string values
      resolved[key] = value.replace(/\[(\w+)\]/g, (_, name) => {
        // eslint-disable-next-line @typescript-eslint/no-base-to-string
        return variables[name] !== undefined ? String(variables[name]) : `[${name}]`;
      });
    } else if (Array.isArray(value)) {
      // Recursively resolve array values
      resolved[key] = value.map(item => {
        if (typeof item === 'string') {
          return item.replace(/\[(\w+)\]/g, (_, name) => {
            // eslint-disable-next-line @typescript-eslint/no-base-to-string
            return variables[name] !== undefined ? String(variables[name]) : `[${name}]`;
          });
        } else if (typeof item === 'object' && item !== null) {
          return resolveProperties(item, variables);
        }
        return item;
      });
    } else if (typeof value === 'object' && value !== null) {
      // Recursively resolve object values
      resolved[key] = resolveProperties(value as Record<string, unknown>, variables);
    } else {
      // Copy primitive values as is
      resolved[key] = value;
    }
  }

  return resolved;
}

/**
 * Resolve a single property value using variables
 *
 * @param value The property value to resolve
 * @param variables The variables to use for replacement
 * @returns The resolved value
 */
export function resolvePropertyValue(value: unknown, variables: Record<string, unknown>): unknown {
  if (typeof value === 'string') {
    // Replace placeholders in string values
    return value.replace(/\[(\w+)\]/g, (_, name) => {
      // eslint-disable-next-line @typescript-eslint/no-base-to-string
      return variables[name] !== undefined ? String(variables[name]) : `[${name}]`;
    });
  } else if (Array.isArray(value)) {
    // Recursively resolve array values
    return value.map(item => resolvePropertyValue(item, variables));
  } else if (typeof value === 'object' && value !== null) {
    // Recursively resolve object values
    return resolveProperties(value as Record<string, unknown>, variables);
  }

  // Return primitive values as is
  return value;
}
