/**
 * Supabase Database Type Declarations
 * 
 * This file extends the Supabase TypeScript definitions to include tables
 * that exist in the database but are not included in the generated types.
 */

// Extend the Database interface from Supabase
declare module '@supabase/supabase-js' {
  interface Database {
    public: {
      Tables: {
        // Add missing tables here
        collections: {
          Row: {
            id: number;
            name: string;
            description: string | null;
            user_id: string;
            created_at: string;
            updated_at: string;
          };
          Insert: {
            id?: number;
            name: string;
            description?: string | null;
            user_id: string;
            created_at?: string;
            updated_at?: string;
          };
          Update: {
            id?: number;
            name?: string;
            description?: string | null;
            user_id?: string;
            created_at?: string;
            updated_at?: string;
          };
        };
        filter_engine_filters: {
          Row: {
            id: number;
            user_id: string;
            name: string;
            description: string | null;
            table_name: string;
            context: string;
            is_default: boolean;
            configuration: {
              columns: {
                id: string;
                value: unknown;
                visible: boolean;
                section: string;
              }[];
              specializedFilters: Record<string, unknown>;
            };
            created_at: string;
            updated_at: string;
          };
          Insert: {
            id?: number;
            user_id: string;
            name: string;
            description?: string | null;
            table_name: string;
            context: string;
            is_default?: boolean;
            configuration: {
              columns: {
                id: string;
                value: unknown;
                visible: boolean;
                section: string;
              }[];
              specializedFilters: Record<string, unknown>;
            };
            created_at?: string;
            updated_at?: string;
          };
          Update: {
            id?: number;
            user_id?: string;
            name?: string;
            description?: string | null;
            table_name?: string;
            context?: string;
            is_default?: boolean;
            configuration?: {
              columns: {
                id: string;
                value: unknown;
                visible: boolean;
                section: string;
              }[];
              specializedFilters: Record<string, unknown>;
            };
            created_at?: string;
            updated_at?: string;
          };
        };
        products: {
          Row: {
            id: number;
            name: string;
            description: string | null;
            user_id: string;
            created_at: string;
            updated_at: string;
          };
          Insert: {
            id?: number;
            name: string;
            description?: string | null;
            user_id: string;
            created_at?: string;
            updated_at?: string;
          };
          Update: {
            id?: number;
            name?: string;
            description?: string | null;
            user_id?: string;
            created_at?: string;
            updated_at?: string;
          };
        };
      };
    };
  }
}
