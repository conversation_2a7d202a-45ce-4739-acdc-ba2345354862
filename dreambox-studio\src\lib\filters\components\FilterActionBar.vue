<template>
  <div class="filter-action-bar q-mb-md">
    <q-bar>
      <q-space />

      <q-btn
        flat
        dense
        icon="save"
        label="Save Filter"
        @click="$emit('save')"
      />

      <q-btn
        flat
        dense
        icon="clear_all"
        label="Clear All"
        @click="$emit('clear')"
      />

      <q-btn
        flat
        dense
        icon="visibility"
        label="Columns"
        @click="showColumnDialog = true"
      />
    </q-bar>

    <!-- Column Visibility Dialog -->
    <q-dialog v-model="showColumnDialog">
      <q-card style="min-width: 350px">
        <q-card-section>
          <div class="text-h6">Column Visibility</div>
        </q-card-section>

        <q-card-section>
          <q-list separator>
            <q-item v-for="column in columns" :key="column.id">
              <q-item-section>
                <q-item-label>{{ column.name }}</q-item-label>
              </q-item-section>

              <q-item-section side>
                <q-toggle
                  v-model="column.visible"
                  @update:model-value="updateColumnVisibility(column.id, $event)"
                />
              </q-item-section>
            </q-item>
          </q-list>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="Close" color="primary" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type { ColumnDefinition } from '../../../stores/filterStore';

// Props
defineProps<{
  columns: ColumnDefinition[];
}>();

// Emits
const emit = defineEmits<{
  (e: 'save'): void;
  (e: 'clear'): void;
  (e: 'update-column-visibility', columnId: string, visible: boolean): void;
}>();

// State
const showColumnDialog = ref(false);

// Methods
function updateColumnVisibility(columnId: string, visible: boolean) {
  emit('update-column-visibility', columnId, visible);
}
</script>

<style scoped>
.filter-action-bar {
  width: 100%;
}
</style>
