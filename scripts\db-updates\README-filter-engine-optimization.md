# Filter Engine Optimization

This document describes the optimizations made to the filter engine to improve performance, particularly for the templates table and prompt elements loading.

## SQL Function Optimizations

### 1. `template_elements_optimized_view`

A new view that efficiently joins templates with their elements, collections, and products. This view pre-aggregates the data into JSON arrays, reducing the need for multiple queries.

### 2. `filter_engine_templates_optimized`

An optimized version of the `filter_engine_templates` function that:
- Uses the new optimized view
- Adds a parameter to filter elements by type
- Adds a parameter to control whether elements are included in the response
- Improves query performance by using more efficient filtering

### 3. `get_template_element_types`

A new function that returns only the element types used in a specific template. This reduces the amount of data transferred when loading template details.

### 4. `get_elements_by_types`

A new function that returns elements for multiple types in a single query. This reduces the number of database calls when loading element values.

## Client-Side Optimizations

### 1. Element Cache Store

A new Pinia store (`elementCache.ts`) that provides caching for:
- Element types
- Element values by type
- Template element types

The cache has a configurable expiry time (default: 5 minutes) and can be cleared manually when needed.

### 2. Batch Loading

Updated components to load multiple element types and values in a single request, reducing the number of network calls.

### 3. Optimized Filtering

Modified the SupabaseAdapter to use the new optimized SQL function, with parameters to control element filtering and inclusion.

## Usage

### SQL Functions

To use the optimized SQL functions, run the `optimize-filter-engine-templates.sql` script in your database.

### Element Cache Store

```typescript
import { useElementCacheStore } from 'src/stores/elementCache';

const elementCacheStore = useElementCacheStore();

// Load all element types (with caching)
const types = await elementCacheStore.loadElementTypes();

// Load element values for a specific type (with caching)
const values = await elementCacheStore.loadElementValuesForType(typeId);

// Load element types for a specific template (with caching)
const templateTypes = await elementCacheStore.loadTemplateElementTypes(templateId);

// Load elements for multiple types at once (with caching)
const elementsByType = await elementCacheStore.loadElementsForTypes([typeId1, typeId2]);

// Clear the cache when needed
elementCacheStore.clearCache();
```

## Performance Improvements

These optimizations should result in:
1. Faster initial loading of templates
2. Reduced lag when applying filters
3. More efficient loading of element types and values
4. Reduced network traffic and database load

## Future Improvements

1. Add more specialized SQL functions for other filter types
2. Implement server-side caching for frequently used queries
3. Add pagination support for element values to handle very large datasets
