<template>
  <div class="shortcut-recorder">
    <div
      class="recorder-input q-pa-sm q-my-xs"
      :class="{ 'recording': isRecording, 'disabled': disable }"
      @click="startRecording"
      tabindex="0"
      ref="recorderRef"
    >
      <div class="row items-center no-wrap">
        <q-icon
          name="keyboard"
          :color="isRecording ? 'primary' : 'grey'"
          class="q-mr-sm"
          size="sm"
        />

        <div class="col text-center">
          <span v-if="displayValue" class="shortcut-display">{{ displayValue }}</span>
          <span v-else class="text-grey">{{ $t('settings.shortcuts.pressKeys') }}</span>
        </div>

        <q-btn
          v-if="modelValue"
          flat
          round
          dense
          icon="close"
          color="grey-7"
          @click.stop="() => clearShortcut()"
          :disable="disable"
        />
      </div>
    </div>
    <div v-if="error" class="text-negative q-mt-xs text-caption">
      {{ error }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted } from 'vue';
import { useKeyboardShortcutsStore } from 'src/stores/keyboardShortcutsStore';
import { useI18n } from 'vue-i18n';
import { useQuasar } from 'quasar';

const props = defineProps<{
  modelValue: string;
  excludeId?: string;
  disable?: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void;
}>();

const $q = useQuasar();
const { t } = useI18n();
const shortcutsStore = useKeyboardShortcutsStore();

const isRecording = ref(false);
const displayValue = computed(() => {
  if (!props.modelValue) return '';

  // Format the key combo for display
  const keys = props.modelValue.split('+');
  return keys.map(key => {
    // Add special formatting for modifier keys
    switch (key) {
      case 'Ctrl': return $q.platform.is.mac ? '⌘' : 'Ctrl';
      case 'Alt': return $q.platform.is.mac ? '⌥' : 'Alt';
      case 'Shift': return 'Shift';
      case 'Meta': return $q.platform.is.mac ? '⌃' : 'Win';
      default: return key;
    }
  }).join(' + ');
});

const error = ref('');
const recorderRef = ref<HTMLElement | null>(null);
const keysPressed = ref<Set<string>>(new Set());
const lastKeyCombo = ref('');

// Start recording keyboard input
function startRecording() {
  if (props.disable) return;

  // Reset state
  isRecording.value = true;
  error.value = '';
  keysPressed.value.clear();
  lastKeyCombo.value = '';

  // Add global event listeners
  window.addEventListener('keydown', handleKeyDown);
  window.addEventListener('keyup', handleKeyUp);

  // Add click outside listener to stop recording
  document.addEventListener('click', handleClickOutside);

  // Show a notification
  $q.notify({
    message: t('settings.shortcuts.pressKeys'),
    color: 'info',
    position: 'top',
    timeout: 2000
  });
}

// Stop recording keyboard input
function stopRecording() {
  if (!isRecording.value) return;

  isRecording.value = false;

  // Remove event listeners
  window.removeEventListener('keydown', handleKeyDown);
  window.removeEventListener('keyup', handleKeyUp);
  document.removeEventListener('click', handleClickOutside);

  // Process the last key combo if it's valid
  if (lastKeyCombo.value && lastKeyCombo.value.split('+').length >= 2) {
    processKeyCombo(lastKeyCombo.value);
  }

  // Clear state
  keysPressed.value.clear();
}

// Handle click outside to stop recording
function handleClickOutside(event: MouseEvent) {
  if (isRecording.value && recorderRef.value && !recorderRef.value.contains(event.target as Node)) {
    stopRecording();
  }
}

// Clear the current shortcut
function clearShortcut() {
  emit('update:modelValue', '');
  error.value = '';
}

// Handle keydown events
function handleKeyDown(event: KeyboardEvent) {
  if (!isRecording.value) return;

  // Prevent default browser shortcuts
  event.preventDefault();
  event.stopPropagation();

  // Get the key name
  let key = event.key;

  // Normalize modifier keys
  if (key === 'Control') key = 'Ctrl';
  if (key === 'Meta') key = 'Meta';
  if (key === 'Alt') key = 'Alt';
  if (key === 'Shift') key = 'Shift';

  // Skip if it's a duplicate key press
  if (keysPressed.value.has(key)) return;

  // Add the key to the set of pressed keys
  keysPressed.value.add(key);

  // Create the key combo string
  const modifiers = [];
  if (event.ctrlKey) modifiers.push('Ctrl');
  if (event.metaKey) modifiers.push('Meta');
  if (event.altKey) modifiers.push('Alt');
  if (event.shiftKey) modifiers.push('Shift');

  // Add the key if it's not a modifier
  if (!['Ctrl', 'Meta', 'Alt', 'Shift'].includes(key)) {
    lastKeyCombo.value = [...modifiers, key].join('+');
  } else {
    // If only modifiers are pressed so far
    lastKeyCombo.value = modifiers.join('+');
  }

  // If we have a valid combo (at least one modifier + one key), process it
  if (lastKeyCombo.value.split('+').length >= 2 &&
      !['Ctrl', 'Meta', 'Alt', 'Shift'].includes(lastKeyCombo.value.split('+').pop() || '')) {
    processKeyCombo(lastKeyCombo.value);
  }
}

// Handle keyup events
function handleKeyUp(event: KeyboardEvent) {
  if (!isRecording.value) return;

  // Get the key name
  let key = event.key;

  // Normalize modifier keys
  if (key === 'Control') key = 'Ctrl';
  if (key === 'Meta') key = 'Meta';
  if (key === 'Alt') key = 'Alt';
  if (key === 'Shift') key = 'Shift';

  // Remove the key from the set of pressed keys
  keysPressed.value.delete(key);
}

// Process a key combination
function processKeyCombo(keyCombo: string) {
  // Validate the key combo
  if (keyCombo.split('+').length < 2) {
    error.value = t('settings.shortcuts.needsModifier');
    return;
  }

  // Check if this shortcut is already used
  if (shortcutsStore.isShortcutUsed(keyCombo, props.excludeId)) {
    error.value = t('settings.shortcuts.alreadyUsed');
    return;
  }

  // Update the model
  emit('update:modelValue', keyCombo);
  error.value = '';

  // Stop recording
  stopRecording();
}

// Clean up event listeners when component is unmounted
onUnmounted(() => {
  window.removeEventListener('keydown', handleKeyDown);
  window.removeEventListener('keyup', handleKeyUp);
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style lang="scss" scoped>
.shortcut-recorder {
  .recorder-input {
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-radius: 4px;
    min-height: 36px;
    min-width: 180px;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      border-color: var(--q-primary);
    }

    &.recording {
      background-color: rgba(0, 0, 0, 0.05);
      border-color: var(--q-primary);
      box-shadow: 0 0 0 1px var(--q-primary);

      .body--dark & {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }

    &.disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .body--dark & {
      border-color: rgba(255, 255, 255, 0.28);
    }
  }

  .shortcut-display {
    font-family: monospace;
    font-weight: bold;
    letter-spacing: 0.5px;
  }
}
</style>
