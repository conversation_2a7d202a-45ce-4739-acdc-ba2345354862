// Import from supabase-js
// Use direct import from the module file to avoid TypeScript errors
import { createClient } from '@supabase/supabase-js/dist/module/index.js'
import type { DatabaseWithFilterEngine } from '../types/supabase-filter-engine'
import type { TypedSupabaseClientWithFilters } from '../types/supabase-client'

// Try to get from environment variables first
let supabaseUrl = import.meta.env.VITE_SUPABASE_URL as string;
let supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY as string;

// If not found, use the hardcoded values
if (!supabaseUrl || !supabaseKey) {
  console.warn('Environment variables not found, using hardcoded values');
  supabaseUrl = 'https://ppzaqhuaqzjofsorlmbn.supabase.co';
  supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBwemFxaHVhcXpqb2Zzb3JsbWJuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQzNzI1NTUsImV4cCI6MjA1OTk0ODU1NX0.cbBqMtvsq86gd1XyRRTsFcFql1Cc_m4H6o0K1y-ZyF4';
}

// Create Supabase client with session persistence enabled
export const supabase: TypedSupabaseClientWithFilters = createClient<DatabaseWithFilterEngine>(supabaseUrl, supabaseKey, {
  auth: {
    persistSession: true,     // Enable session persistence
    storageKey: 'dreambox-supabase-auth', // Custom storage key
    storage: localStorage,    // Use localStorage for session storage
    autoRefreshToken: true,   // Automatically refresh token
    detectSessionInUrl: true  // Detect session in URL for OAuth login
  }
})

// Set up auth state change listener
supabase.auth.onAuthStateChange((event: string, session: unknown) => {
  console.log('Supabase auth state changed:', event, session ? 'Session exists' : 'No session');

  // You could dispatch actions here based on auth state changes
  // For example, if you wanted to refresh user data when the token is refreshed
  if (event === 'TOKEN_REFRESHED') {
    console.log('Token was refreshed, session should be maintained');
  }
})
