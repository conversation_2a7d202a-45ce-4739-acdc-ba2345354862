import { onMounted, onUnmounted } from 'vue';
import { useKeyboardShortcutsStore } from 'src/stores/keyboardShortcutsStore';
import { mockWebSocket } from 'src/services/commands/mock-websocket';

export function useKeyboardShortcuts() {
  const shortcutsStore = useKeyboardShortcutsStore();

  // Convert key event to standardized format
  function formatKeyCombo(event: KeyboardEvent): string {
    const keys: string[] = [];

    if (event.ctrlKey) keys.push('Ctrl');
    if (event.shiftKey) keys.push('Shift');
    if (event.altKey) keys.push('Alt');
    if (event.metaKey) keys.push('Meta');

    // Add the key if it's not a modifier
    if (!['Control', 'Shift', 'Alt', 'Meta'].includes(event.key)) {
      // Capitalize first letter of key for better readability
      let key = event.key;
      if (key.length === 1) {
        key = key.toUpperCase();
      }
      keys.push(key);
    }

    return keys.join('+');
  }

  // We're using a simpler approach with a timestamp check instead of debounce

  // Track last executed command and time to prevent duplicates
  const lastCommand = {
    name: '',
    time: 0
  };

  // Handle keyboard events
  function handleKeyDown(event: KeyboardEvent) {
    // Ignore if in an input field
    if (event.target instanceof HTMLInputElement ||
        event.target instanceof HTMLTextAreaElement ||
        event.target instanceof HTMLSelectElement ||
        (event.target instanceof HTMLElement && event.target.isContentEditable)) {
      return;
    }

    const keyCombo = formatKeyCombo(event);
    const shortcut = shortcutsStore.getShortcutByKeys(keyCombo);

    if (shortcut) {
      event.preventDefault();

      // Prevent duplicate executions within 500ms
      const now = Date.now();
      if (lastCommand.name === shortcut.command && now - lastCommand.time < 500) {
        console.log(`Ignoring duplicate command: ${shortcut.command}`);
        return;
      }

      // Update last command
      lastCommand.name = shortcut.command;
      lastCommand.time = now;

      console.log(`Executing command: ${shortcut.command}`);
      mockWebSocket.simulateCommand(shortcut.command);
    }
  }

  // Set up and tear down event listeners
  onMounted(() => {
    window.addEventListener('keydown', handleKeyDown);
  });

  onUnmounted(() => {
    window.removeEventListener('keydown', handleKeyDown);
  });

  // Format a shortcut for display in UI
  function formatShortcutForDisplay(keys: string): string {
    return keys.split('+').map(key => {
      // Map special keys to symbols or shorter representations
      switch (key) {
        case 'Control':
        case 'Ctrl':
          return '⌃';
        case 'Alt':
          return '⌥';
        case 'Shift':
          return '⇧';
        case 'Meta':
        case 'Command':
          return '⌘';
        case 'ArrowUp':
          return '↑';
        case 'ArrowDown':
          return '↓';
        case 'ArrowLeft':
          return '←';
        case 'ArrowRight':
          return '→';
        case 'Enter':
          return '↵';
        case 'Escape':
          return 'Esc';
        default:
          return key;
      }
    }).join(' + ');
  }

  // Add shortcut hint to tooltip
  function addShortcutToTooltip(tooltip: string, command: string): string {
    const shortcut = shortcutsStore.getShortcutByCommand(command);

    if (shortcut) {
      return `${tooltip} (${formatShortcutForDisplay(shortcut.keys)})`;
    }

    return tooltip;
  }

  return {
    formatKeyCombo,
    formatShortcutForDisplay,
    addShortcutToTooltip
  };
}
