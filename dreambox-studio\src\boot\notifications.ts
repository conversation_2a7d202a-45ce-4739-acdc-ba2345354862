import { boot } from 'quasar/wrappers';
// No need to import Notify as we're using notificationService
import type { QNotifyCreateOptions } from 'quasar';
import { notificationService } from 'src/services/notificationService';

// Create a wrapper for the Quasar notify function
const notify = async (options: QNotifyCreateOptions): Promise<void> => {
  await notificationService.notify(options);
};

export default boot(({ app }) => {
  // Add the notify function to the global properties
  // This allows us to use this.$notify in components
  app.config.globalProperties.$notify = notify;
});

// Export the notify function for use in composition API
export { notify };
