import { describe, it, expect, beforeEach } from 'vitest';
import { filterRegistry, type FilterTypeDefinition } from '../../../../src/lib/filters/FilterRegistry';

describe('FilterRegistry', () => {
  let mockFilterType: FilterTypeDefinition;

  beforeEach(() => {
    // Clear existing filter types
    filterRegistry.clearFilterTypes();

    // Create a mock filter type for testing
    mockFilterType = {
      id: 'test-filter',
      name: 'Test Filter',
      section: 'main',
      component: {} as any, // Mock component
      tableTypes: ['templates'],
      stateExtractor: (state) => state,
      stateApplier: () => true,
      clearHandler: () => null
    };
  });

  it('should register a filter type', () => {
    filterRegistry.registerFilterType(mockFilterType);
    const result = filterRegistry.getFilterTypeById('test-filter');
    expect(result).toEqual(mockFilterType);
  });

  it('should update an existing filter type', () => {
    // Register initial filter type
    filterRegistry.registerFilterType(mockFilterType);

    // Create updated version
    const updatedFilterType = {
      ...mockFilterType,
      name: 'Updated Test Filter'
    };

    // Register updated version
    filterRegistry.registerFilterType(updatedFilterType);

    // Get the filter type
    const result = filterRegistry.getFilterTypeById('test-filter');

    // Verify it was updated
    expect(result).toEqual(updatedFilterType);
    expect(result?.name).toBe('Updated Test Filter');
  });

  it('should get all registered filter types', () => {
    // Register multiple filter types
    filterRegistry.registerFilterType(mockFilterType);
    filterRegistry.registerFilterType({
      ...mockFilterType,
      id: 'test-filter-2',
      name: 'Test Filter 2'
    });

    const allTypes = filterRegistry.getAllFilterTypes();

    expect(allTypes).toHaveLength(2);
    expect(allTypes[0].id).toBe('test-filter');
    expect(allTypes[1].id).toBe('test-filter-2');
  });

  it('should get filter types for a specific table', () => {
    // Register filter types for different tables
    filterRegistry.registerFilterType(mockFilterType); // For 'templates'
    filterRegistry.registerFilterType({
      ...mockFilterType,
      id: 'test-filter-2',
      tableTypes: ['products']
    });
    filterRegistry.registerFilterType({
      ...mockFilterType,
      id: 'test-filter-3',
      tableTypes: ['templates', 'products']
    });

    const templateFilters = filterRegistry.getFilterTypesForTable('templates');

    expect(templateFilters).toHaveLength(2);
    expect(templateFilters[0].id).toBe('test-filter');
    expect(templateFilters[1].id).toBe('test-filter-3');
  });

  it('should get filter types for a specific section and table', () => {
    // Register filter types for different sections
    filterRegistry.registerFilterType(mockFilterType); // 'main' section
    filterRegistry.registerFilterType({
      ...mockFilterType,
      id: 'test-filter-2',
      section: 'expanded'
    });
    filterRegistry.registerFilterType({
      ...mockFilterType,
      id: 'test-filter-3',
      section: 'specialized'
    });

    const mainSectionFilters = filterRegistry.getFilterTypesForSection('templates', 'main');

    expect(mainSectionFilters).toHaveLength(1);
    expect(mainSectionFilters[0].id).toBe('test-filter');
  });

  it('should handle wildcard table types', () => {
    // Register a filter type with wildcard table type
    filterRegistry.registerFilterType({
      ...mockFilterType,
      id: 'wildcard-filter',
      tableTypes: ['*']
    });

    // Check if it's returned for any table
    const templateFilters = filterRegistry.getFilterTypesForTable('templates');
    const productFilters = filterRegistry.getFilterTypesForTable('products');
    const userFilters = filterRegistry.getFilterTypesForTable('users');

    expect(templateFilters).toHaveLength(1);
    expect(productFilters).toHaveLength(1);
    expect(userFilters).toHaveLength(1);
    expect(templateFilters[0].id).toBe('wildcard-filter');
  });

  it('should return undefined for non-existent filter type', () => {
    const result = filterRegistry.getFilterTypeById('non-existent');
    expect(result).toBeUndefined();
  });

  it('should clear all registered filter types', () => {
    // Register some filter types
    filterRegistry.registerFilterType(mockFilterType);
    filterRegistry.registerFilterType({
      ...mockFilterType,
      id: 'test-filter-2'
    });

    // Verify they're registered
    expect(filterRegistry.getAllFilterTypes()).toHaveLength(2);

    // Clear all filter types
    filterRegistry.clearFilterTypes();

    // Verify they're cleared
    expect(filterRegistry.getAllFilterTypes()).toHaveLength(0);
  });
});
