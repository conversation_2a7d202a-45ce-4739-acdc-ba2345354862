import { supabase } from '../boot/supabase';
import { v4 as uuidv4 } from 'uuid';

/**
 * Service for handling image uploads and storage
 */
export class ImageStorageService {
  private readonly bucketName = 'images';

  /**
   * Upload an image to the storage bucket
   * @param file The file to upload
   * @param folder Optional folder path within the bucket
   * @returns The URL of the uploaded image
   */
  async uploadImage(file: File, folder = 'uploads'): Promise<string> {
    try {
      // Generate a unique filename
      const fileExt = file.name.split('.').pop();
      const fileName = `${folder}/${uuidv4()}.${fileExt}`;

      // Upload the file
      const { error } = await supabase.storage
        .from(this.bucketName)
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) {
        throw error;
      }

      // Get the public URL
      const { data: urlData } = supabase.storage
        .from(this.bucketName)
        .getPublicUrl(fileName);

      return urlData.publicUrl;
    } catch (error) {
      console.error('Error uploading image:', error);
      throw error;
    }
  }

  /**
   * Delete an image from the storage bucket
   * @param url The URL of the image to delete
   * @returns True if the image was deleted successfully
   */
  async deleteImage(url: string): Promise<boolean> {
    try {
      // Extract the path from the URL
      const path = this.getPathFromUrl(url);

      if (!path) {
        throw new Error('Invalid image URL');
      }

      // Delete the file
      const { error } = await supabase.storage
        .from(this.bucketName)
        .remove([path]);

      if (error) {
        throw error;
      }

      return true;
    } catch (error) {
      console.error('Error deleting image:', error);
      throw error;
    }
  }

  /**
   * Get a temporary signed URL for an image
   * @param url The public URL of the image
   * @param expiresIn Expiration time in seconds (default: 60 minutes)
   * @returns The signed URL
   */
  async getSignedUrl(url: string, expiresIn = 3600): Promise<string> {
    try {
      // Extract the path from the URL
      const path = this.getPathFromUrl(url);

      if (!path) {
        throw new Error('Invalid image URL');
      }

      // Get a signed URL
      const { data, error } = await supabase.storage
        .from(this.bucketName)
        .createSignedUrl(path, expiresIn);

      if (error) {
        throw error;
      }

      return data?.signedUrl || '';
    } catch (error) {
      console.error('Error getting signed URL:', error);
      throw error;
    }
  }

  /**
   * Save an image record in the database
   * @param imageUrl The URL of the image
   * @param templateId The ID of the template
   * @param companyId The ID of the company
   * @param title The title of the image
   * @param description Optional description of the image
   * @param eventId Optional ID of the associated event
   * @returns The ID of the created image record
   */
  /* Commented out until the images table is properly defined in the database schema
  async saveImageRecord(
    imageUrl: string,
    templateId: number,
    companyId: number,
    title: string,
    description?: string,
    eventId?: number
  ): Promise<number> {
    try {
      // Insert the image record
      const { data, error } = await supabase
        .from('images')
        .insert({
          template_id: templateId,
          img_company_id: companyId,
          event_id: eventId,
          title,
          description,
          image_url: imageUrl,
          status: 'draft',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select('id')
        .single();

      if (error) {
        throw error;
      }

      return data.id;
    } catch (error) {
      console.error('Error saving image record:', error);
      throw error;
    }
  }
  */

  /**
   * Get images for a company
   * @param companyId The ID of the company
   * @param status Optional status filter
   * @returns Array of image records
   */
  /* Commented out until the images table is properly defined in the database schema
  async getCompanyImages(companyId: number, status?: string) {
    try {
      let query = supabase
        .from('images')
        .select(`
          id,
          title,
          description,
          image_url,
          status,
          created_at,
          updated_at,
          template:template_id(id, name),
          event:event_id(id, title)
        `)
        .eq('img_company_id', companyId);

      if (status) {
        query = query.eq('status', status);
      }

      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error getting company images:', error);
      throw error;
    }
  }
  */

  /**
   * Update the status of an image
   * @param imageId The ID of the image
   * @param status The new status
   * @param approvedBy Optional ID of the user who approved the image
   * @returns True if the update was successful
   */
  /* Commented out until the images table is properly defined in the database schema
  // Define the UpdateImageStatus type
  interface UpdateImageStatus {
    status: string;
    updated_at: string;
    approved_by?: number;
    approval_date?: string;
  }

  async updateImageStatus(imageId: number, status: string, approvedBy?: number): Promise<boolean> {
    try {
      const updates: UpdateImageStatus = {
        status,
        updated_at: new Date().toISOString()
      };

      if (status === 'approved' && approvedBy) {
        updates.approved_by = approvedBy;
        updates.approval_date = new Date().toISOString();
      }

      const { error } = await supabase
        .from('images')
        .update(updates)
        .eq('id', imageId);

      if (error) {
        throw error;
      }

      return true;
    } catch (error) {
      console.error('Error updating image status:', error);
      throw error;
    }
  }
  */

  /**
   * Extract the path from a Supabase storage URL
   * @param url The URL to parse
   * @returns The path within the bucket
   */
  private getPathFromUrl(url: string): string | null {
    try {
      // Get the base URL for the storage bucket
      const { data } = supabase.storage.from(this.bucketName).getPublicUrl('');
      const storageBaseUrl = data.publicUrl;

      if (url.startsWith(storageBaseUrl)) {
        return url.replace(storageBaseUrl, '');
      }

      return null;
    } catch (error) {
      console.error('Error parsing URL:', error);
      return null;
    }
  }
}

// Create a singleton instance
export const imageStorage = new ImageStorageService();
