import { ref } from 'vue';
import { supabase } from 'src/boot/supabase';
import type { ProductType } from 'src/types/ProductType';

/**
 * Service for managing product types
 */
export const useProductTypeService = () => {
  const loading = ref(false);
  const error = ref<string | null>(null);
  const productTypes = ref<ProductType[]>([]);

  /**
   * Get all product types
   */
  const getProductTypes = async (activeOnly = true): Promise<ProductType[]> => {
    try {
      loading.value = true;
      error.value = null;

      let query = supabase
        .from('product_types')
        .select('*');

      if (activeOnly) {
        query = query.eq('active', true);
      }

      const { data, error: err } = await query.order('category', { ascending: true }).order('name');

      if (err) {
        throw err;
      }

      if (data) {
        productTypes.value = data as ProductType[];
      } else {
        productTypes.value = [];
      }

      return productTypes.value;
    } catch (err) {
      console.error('Error fetching product types:', err);
      error.value = err instanceof Error ? err.message : 'Failed to fetch product types';
      return [];
    } finally {
      loading.value = false;
    }
  };

  /**
   * Get a single product type by ID
   */
  const getProductType = async (id: number): Promise<ProductType | null> => {
    try {
      loading.value = true;
      error.value = null;

      const { data, error: err } = await supabase
        .from('product_types')
        .select('*')
        .eq('id', id)
        .single();

      if (err) {
        throw err;
      }

      return data as ProductType;
    } catch (err) {
      console.error(`Error fetching product type with ID ${id}:`, err);
      error.value = err instanceof Error ? err.message : `Failed to fetch product type with ID ${id}`;
      return null;
    } finally {
      loading.value = false;
    }
  };

  /**
   * Create a new product type
   */
  const createProductType = async (productType: {
    name: string;
    description?: string | null;
    category?: string | null;
    print_area_width?: number | null;
    print_area_height?: number | null;
    thumbnail_url?: string | null;
    shopify_product_id?: string | null;
    active?: boolean;
  }): Promise<ProductType | null> => {
    try {
      loading.value = true;
      error.value = null;

      const { data, error: err } = await supabase
        .from('product_types')
        .insert(productType)
        .select()
        .single();

      if (err) {
        throw err;
      }

      return data as ProductType;
    } catch (err) {
      console.error('Error creating product type:', err);
      error.value = err instanceof Error ? err.message : 'Failed to create product type';
      return null;
    } finally {
      loading.value = false;
    }
  };

  /**
   * Update an existing product type
   */
  const updateProductType = async (id: number, productType: {
    name?: string;
    description?: string | null;
    category?: string | null;
    print_area_width?: number | null;
    print_area_height?: number | null;
    thumbnail_url?: string | null;
    shopify_product_id?: string | null;
    active?: boolean;
  }): Promise<ProductType | null> => {
    try {
      loading.value = true;
      error.value = null;

      const { data, error: err } = await supabase
        .from('product_types')
        .update(productType)
        .eq('id', id)
        .select()
        .single();

      if (err) {
        throw err;
      }

      return data as ProductType;
    } catch (err) {
      console.error(`Error updating product type with ID ${id}:`, err);
      error.value = err instanceof Error ? err.message : `Failed to update product type with ID ${id}`;
      return null;
    } finally {
      loading.value = false;
    }
  };

  /**
   * Delete a product type
   */
  const deleteProductType = async (id: number): Promise<boolean> => {
    try {
      loading.value = true;
      error.value = null;

      const { error: err } = await supabase
        .from('product_types')
        .delete()
        .eq('id', id);

      if (err) {
        throw new Error(`Failed to create product type: ${err.message || JSON.stringify(err)}`);
      }

      return true;
    } catch (err) {
      console.error(`Error deleting product type with ID ${id}:`, err);
      error.value = err instanceof Error ? err.message : `Failed to delete product type with ID ${id}`;
      return false;
    } finally {
      loading.value = false;
    }
  };

  /**
   * Get product types for a template
   */
  const getTemplateProductTypes = async (templateId: number): Promise<ProductType[]> => {
    try {
      loading.value = true;
      error.value = null;

      const { data, error: err } = await supabase
        .rpc('get_template_product_types', { p_template_id: templateId });

      if (err) {
        throw new Error(`Failed to get product types: ${err.message || JSON.stringify(err)}`);
      }

      return data as ProductType[];
    } catch (err) {
      console.error(`Error fetching product types for template with ID ${templateId}:`, err);
      error.value = err instanceof Error ? err.message : `Failed to fetch product types for template with ID ${templateId}`;
      return [];
    } finally {
      loading.value = false;
    }
  };

  /**
   * Associate product types with a template
   */
  const associateTemplateProductTypes = async (
    templateId: number,
    productTypeIds: number[],
    recommendedIds: number[] = []
  ): Promise<boolean> => {
    try {
      loading.value = true;
      error.value = null;

      // First, delete existing associations
      const { error: deleteErr } = await supabase
        .from('templates_product_types')
        .delete()
        .eq('template_id', templateId);

      if (deleteErr) {
        throw new Error(`Failed to delete template product types: ${deleteErr.message || JSON.stringify(deleteErr)}`);
      }

      // Then, create new associations
      if (productTypeIds.length > 0) {
        const data = productTypeIds.map(productTypeId => ({
          template_id: templateId,
          product_type_id: productTypeId,
          recommended: recommendedIds.includes(productTypeId)
        }));

        // Wrap in Promise.resolve to ensure we're awaiting a promise
        const { error: insertErr } = await Promise.resolve(
          supabase.from('templates_product_types').insert(data)
        );

        if (insertErr) {
          throw new Error(`Failed to insert template product types: ${insertErr.message || JSON.stringify(insertErr)}`);
        }
      }

      return true;
    } catch (err) {
      console.error(`Error associating product types with template ID ${templateId}:`, err);
      error.value = err instanceof Error ? err.message : `Failed to associate product types with template ID ${templateId}`;
      return false;
    } finally {
      loading.value = false;
    }
  };

  /**
   * Get product types for a product
   */
  const getProductProductTypes = async (productId: number): Promise<ProductType[]> => {
    try {
      loading.value = true;
      error.value = null;

      const { data, error: err } = await supabase
        .rpc('get_product_product_types', { p_product_id: productId });

      if (err) {
        throw new Error(`Failed to get product types: ${err.message || JSON.stringify(err)}`);
      }

      return data as ProductType[];
    } catch (err) {
      console.error(`Error fetching product types for product with ID ${productId}:`, err);
      error.value = err instanceof Error ? err.message : `Failed to fetch product types for product with ID ${productId}`;
      return [];
    } finally {
      loading.value = false;
    }
  };

  /**
   * Associate product types with a product
   */
  const associateProductProductTypes = async (
    productId: number,
    productTypeIds: number[]
  ): Promise<boolean> => {
    try {
      loading.value = true;
      error.value = null;

      // First, delete existing associations
      const { error: deleteErr } = await supabase
        .from('products_product_types')
        .delete()
        .eq('product_id', productId);

      if (deleteErr) {
        throw new Error(`Failed to delete product product types: ${deleteErr.message || JSON.stringify(deleteErr)}`);
      }

      // Then, create new associations
      if (productTypeIds.length > 0) {
        const data = productTypeIds.map(productTypeId => ({
          product_id: productId,
          product_type_id: productTypeId
        }));

        // Wrap in Promise.resolve to ensure we're awaiting a promise
        const { error: insertErr } = await Promise.resolve(
          supabase.from('products_product_types').insert(data)
        );

        if (insertErr) {
          throw new Error(`Failed to insert product product types: ${insertErr.message || JSON.stringify(insertErr)}`);
        }
      }

      return true;
    } catch (err) {
      console.error(`Error associating product types with product ID ${productId}:`, err);
      error.value = err instanceof Error ? err.message : `Failed to associate product types with product ID ${productId}`;
      return false;
    } finally {
      loading.value = false;
    }
  };

  return {
    loading,
    error,
    productTypes,
    getProductTypes,
    getProductType,
    createProductType,
    updateProductType,
    deleteProductType,
    getTemplateProductTypes,
    associateTemplateProductTypes,
    getProductProductTypes,
    associateProductProductTypes
  };
};
