/**
 * elementCache.ts
 *
 * This store provides caching for element types and values to improve performance.
 * It reduces the number of database calls by storing previously fetched data.
 */

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { usePromptElementsService, type PromptElementType, type PromptElement } from 'src/services/promptElementsService';
import { useAuthStore } from 'src/stores/auth';
import { supabase } from 'src/boot/supabase';

export const useElementCacheStore = defineStore('elementCache', () => {
  // Services
  const promptElementsService = usePromptElementsService();
  const authStore = useAuthStore();

  // State
  const elementTypes = ref<PromptElementType[]>([]);
  const elementValuesByType = ref<Record<number, PromptElement[]>>({});
  const templateElementTypes = ref<Record<number, PromptElementType[]>>({});
  const isTypesLoaded = ref(false);
  const isLoading = ref(false);
  const lastTypesLoadTime = ref<number | null>(null);
  const lastValueLoadTimes = ref<Record<number, number>>({});
  const cacheExpiryTime = 5 * 60 * 1000; // 5 minutes in milliseconds

  // Computed
  const currentUserId = computed(() => {
    return authStore.state.user?.appUserId || undefined;
  });

  // Methods

  /**
   * Load all element types with caching
   */
  async function loadElementTypes(forceRefresh = false): Promise<PromptElementType[]> {
    // Check if we already have cached data that's not expired
    const now = Date.now();
    const isCacheValid = isTypesLoaded.value &&
                         lastTypesLoadTime.value &&
                         (now - lastTypesLoadTime.value < cacheExpiryTime);

    if (!forceRefresh && isCacheValid) {
      // console.log('Using cached element types');
      return elementTypes.value;
    }

    try {
      // console.log('Loading element types from service');
      isLoading.value = true;

      const types = await promptElementsService.getPromptElementTypes();
      elementTypes.value = types;
      isTypesLoaded.value = true;
      lastTypesLoadTime.value = now;

      // console.log(`Loaded ${types.length} element types`);
      return types;
    } catch (error) {
      console.error('Error loading element types:', error);
      return [];
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * Load element values for a specific type with caching
   */
  async function loadElementValuesForType(typeId: number, forceRefresh = false): Promise<PromptElement[]> {
    // Check if we already have cached data that's not expired
    const now = Date.now();
    const lastLoadTime = lastValueLoadTimes.value[typeId] || 0;
    const isCacheValid = elementValuesByType.value[typeId] &&
                         (now - lastLoadTime < cacheExpiryTime);

    if (!forceRefresh && isCacheValid) {
      // console.log(`Using cached values for type ${typeId}`);
      return elementValuesByType.value[typeId] || [];
    }

    try {
      isLoading.value = true;

      let values: PromptElement[] = [];

      // Check if we have a current user ID
      if (currentUserId.value) {
        // If we have a user ID, get visible elements (system + user's custom)
        // console.log(`Loading visible elements for type ${typeId} with user ID ${currentUserId.value}`);
        values = await promptElementsService.getVisiblePromptElements(typeId, currentUserId.value);
      } else {
        // Fallback: If no user ID, just get all elements
        // console.log(`No user ID available, loading all elements for type ${typeId}`);
        values = await promptElementsService.getPromptElements(typeId);
      }

      // Store the values in the cache
      elementValuesByType.value[typeId] = values;
      lastValueLoadTimes.value[typeId] = now;

      // console.log(`Loaded ${values.length} values for type ${typeId}`);
      return values;
    } catch (error) {
      console.error(`Error loading values for type ${typeId}:`, error);
      return [];
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * Load element types for a specific template with caching
   */
  async function loadTemplateElementTypes(templateId: number, forceRefresh = false): Promise<PromptElementType[]> {
    // Check if we already have cached data
    if (!forceRefresh && templateElementTypes.value[templateId]) {
      // console.log(`Using cached element types for template ${templateId}`);
      return templateElementTypes.value[templateId];
    }

    try {
      isLoading.value = true;

      // Call the new database function to get only the element types for this template
      const { data, error } = await supabase.rpc('get_template_element_types', {
        p_template_id: templateId
      });

      if (error) {
        throw error;
      }

      // Store the types in the cache
      const typesData = data as PromptElementType[] || [];
      templateElementTypes.value[templateId] = typesData;

      // console.log(`Loaded ${typesData.length} element types for template ${templateId}`);
      return typesData;
    } catch (error) {
      console.error(`Error loading element types for template ${templateId}:`, error);
      return [];
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * Load elements for multiple types at once
   */
  async function loadElementsForTypes(typeIds: number[]): Promise<Record<number, PromptElement[]>> {
    if (!typeIds.length) return {};

    try {
      isLoading.value = true;

      // Check which types we need to load (not in cache or cache expired)
      const now = Date.now();
      const typesToLoad = typeIds.filter(typeId => {
        const lastLoadTime = lastValueLoadTimes.value[typeId] || 0;
        return !elementValuesByType.value[typeId] || (now - lastLoadTime >= cacheExpiryTime);
      });

      if (typesToLoad.length === 0) {
        // console.log('All requested types are already in cache');
        // Return only the requested types from cache
        const result: Record<number, PromptElement[]> = {};
        typeIds.forEach(typeId => {
          result[typeId] = elementValuesByType.value[typeId] || [];
        });
        return result;
      }

      // console.log(`Loading elements for ${typesToLoad.length} types: ${typesToLoad.join(', ')}`);

      // Call the new database function to get elements for multiple types at once
      // Prepare parameters with proper type handling
      const params: { p_type_ids: number[], p_user_id?: number } = {
        p_type_ids: typesToLoad
      };

      // Only add p_user_id if it's a valid number
      if (typeof currentUserId.value === 'number') {
        params.p_user_id = currentUserId.value;
      }

      const { data, error } = await supabase.rpc('get_elements_by_types', params);

      if (error) {
        throw error;
      }

      // Group the elements by type
      const elementsByType: Record<number, PromptElement[]> = {};
      const elementsData = data as PromptElement[] || [];

      elementsData.forEach((element: PromptElement) => {
        // Make sure type_id exists and is valid
        if (element && element.type_id !== undefined && element.type_id !== null) {
          // Create the array if it doesn't exist
          if (!elementsByType[element.type_id]) {
            elementsByType[element.type_id] = [];
          }

          // Now we're sure the array exists
          const elementsArray = elementsByType[element.type_id];
          if (elementsArray) {
            elementsArray.push(element);
          }
        } else {
          console.warn('Element with missing type_id:', element);
        }
      });

      // Update the cache with the new data
      const now2 = Date.now(); // Get a new timestamp for the cache
      typesToLoad.forEach(typeId => {
        elementValuesByType.value[typeId] = elementsByType[typeId] || [];
        lastValueLoadTimes.value[typeId] = now2;
      });

      // Return only the requested types (from cache + newly loaded)
      const result: Record<number, PromptElement[]> = {};
      typeIds.forEach(typeId => {
        result[typeId] = elementValuesByType.value[typeId] || [];
      });

      return result;
    } catch (error) {
      console.error('Error loading elements for multiple types:', error);
      return {};
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * Clear the cache
   */
  function clearCache() {
    elementTypes.value = [];
    elementValuesByType.value = {};
    templateElementTypes.value = {};
    isTypesLoaded.value = false;
    lastTypesLoadTime.value = null;
    lastValueLoadTimes.value = {};
  }

  return {
    // State
    elementTypes,
    elementValuesByType,
    templateElementTypes,
    isTypesLoaded,
    isLoading,

    // Methods
    loadElementTypes,
    loadElementValuesForType,
    loadTemplateElementTypes,
    loadElementsForTypes,
    clearCache
  };
});
