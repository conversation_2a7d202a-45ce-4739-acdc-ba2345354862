<template>
  <base-page
    title="Dashboard"
    subtitle="Welcome to Dreambox Studio"
  >
    <!-- Stats Row -->
    <div class="row q-col-gutter-md q-mb-md">
      <div class="col-12 col-sm-6 col-md-3">
        <base-card elevated elevation-level="1" class="stat-card">
          <template #header>
            <q-icon name="design_services" size="md" color="primary" />
          </template>
          <div class="text-h4 q-mb-xs">{{ stats.designs }}</div>
          <div class="text-caption">Total Designs</div>
        </base-card>
      </div>

      <div class="col-12 col-sm-6 col-md-3">
        <base-card elevated elevation-level="1" class="stat-card">
          <template #header>
            <q-icon name="folder" size="md" color="secondary" />
          </template>
          <div class="text-h4 q-mb-xs">{{ stats.projects }}</div>
          <div class="text-caption">Active Projects</div>
        </base-card>
      </div>

      <div class="col-12 col-sm-6 col-md-3">
        <base-card elevated elevation-level="1" class="stat-card">
          <template #header>
            <q-icon name="people" size="md" color="accent" />
          </template>
          <div class="text-h4 q-mb-xs">{{ stats.users }}</div>
          <div class="text-caption">Team Members</div>
        </base-card>
      </div>

      <div class="col-12 col-sm-6 col-md-3">
        <base-card elevated elevation-level="1" class="stat-card">
          <template #header>
            <q-icon name="check_circle" size="md" color="positive" />
          </template>
          <div class="text-h4 q-mb-xs">{{ stats.completed }}</div>
          <div class="text-caption">Completed Tasks</div>
        </base-card>
      </div>
    </div>

    <!-- Main Content Row -->
    <div class="row q-col-gutter-md">
      <!-- Recent Activity -->
      <div class="col-12 col-lg-8">
        <base-card
          title="Recent Activity"
          subtitle="Latest updates from your team"
          elevated
          elevation-level="1"
        >
          <q-list separator>
            <q-item v-for="(activity, index) in recentActivity" :key="index" class="q-py-md">
              <q-item-section avatar>
                <q-avatar :color="activity.color" text-color="white">
                  <q-icon :name="activity.icon" />
                </q-avatar>
              </q-item-section>

              <q-item-section>
                <q-item-label>{{ activity.title }}</q-item-label>
                <q-item-label caption>{{ activity.description }}</q-item-label>
              </q-item-section>

              <q-item-section side>
                <q-item-label caption>{{ activity.time }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>

          <template #footer>
            <q-space />
            <q-btn flat color="primary" label="View All" />
          </template>
        </base-card>
      </div>

      <!-- Quick Actions -->
      <div class="col-12 col-lg-4">
        <base-card
          title="Quick Actions"
          subtitle="Common tasks"
          elevated
          elevation-level="1"
        >
          <div class="row q-col-gutter-sm">
            <div class="col-6" v-for="(action, index) in quickActions" :key="index">
              <q-btn
                class="full-width q-py-sm"
                :color="action.color"
                :icon="action.icon"
                :label="action.label"
                @click="action.action"
              />
            </div>
          </div>
        </base-card>

        <!-- System Status -->
        <base-card
          title="System Status"
          subtitle="All systems operational"
          elevated
          elevation-level="1"
          class="q-mt-md"
        >
          <q-list dense>
            <q-item v-for="(service, index) in systemStatus" :key="index">
              <q-item-section>
                <q-item-label>{{ service.name }}</q-item-label>
              </q-item-section>

              <q-item-section side>
                <q-badge :color="service.status === 'Operational' ? 'positive' : 'negative'">
                  {{ service.status }}
                </q-badge>
              </q-item-section>
            </q-item>
          </q-list>
        </base-card>
      </div>
    </div>
  </base-page>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import BasePage from 'src/components/BasePage.vue';
import BaseCard from 'src/components/BaseCard.vue';

const router = useRouter();

// Sample data
const stats = ref({
  designs: 128,
  projects: 24,
  users: 12,
  completed: 342
});

const recentActivity = ref([
  {
    title: 'New design uploaded',
    description: 'John Doe uploaded a new design "Product Landing Page"',
    time: '2 hours ago',
    icon: 'upload',
    color: 'primary'
  },
  {
    title: 'Project status updated',
    description: 'E-commerce Website project is now in review',
    time: '4 hours ago',
    icon: 'update',
    color: 'secondary'
  },
  {
    title: 'New comment',
    description: 'Jane Smith commented on "Mobile App Design"',
    time: 'Yesterday',
    icon: 'comment',
    color: 'accent'
  },
  {
    title: 'Task completed',
    description: 'Homepage redesign task marked as complete',
    time: 'Yesterday',
    icon: 'check_circle',
    color: 'positive'
  }
]);

const quickActions = ref([
  {
    label: 'New Design',
    icon: 'add',
    color: 'primary',
    action: () => router.push('/designer/designs/new')
  },
  {
    label: 'New Project',
    icon: 'create_new_folder',
    color: 'secondary',
    action: () => router.push('/admin/projects/new')
  },
  {
    label: 'Invite User',
    icon: 'person_add',
    color: 'accent',
    action: () => router.push('/admin/users/invite')
  },
  {
    label: 'Settings',
    icon: 'settings',
    color: 'grey-7',
    action: () => router.push('/settings')
  }
]);

const systemStatus = ref([
  { name: 'Design Service', status: 'Operational' },
  { name: 'Database', status: 'Operational' },
  { name: 'API', status: 'Operational' },
  { name: 'Storage', status: 'Operational' }
]);
</script>

<style lang="scss" scoped>
.stat-card {
  height: 100%;

  :deep(.q-card__section) {
    padding: 16px;
  }

  :deep(.header) {
    display: flex;
    justify-content: flex-end;
  }
}

// Platform-specific styles
:deep(.platform-mobile) {
  .stat-card {
    :deep(.q-card__section) {
      padding: 12px;
    }
  }
}
</style>
