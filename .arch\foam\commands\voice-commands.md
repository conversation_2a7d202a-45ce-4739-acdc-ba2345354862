# Voice Command Implementation Architecture

This document outlines the architecture for implementing voice commands in the Dreambox Studio application, working across both PWA and Electron versions.

## Overview

The voice command system works as follows:

1. User speaks a command to the application
2. Voice data is sent to n8n workflow
3. n8n forwards the audio to ElevenLabs for speech-to-text conversion
4. n8n processes the text command and parameters
5. n8n calls the appropriate command in our application
6. The application executes the command and returns a result

## Current Implementation

The voice command system has been implemented with the following components:

1. **Command Registry**: A central registry that manages all available commands
2. **Command Setup**: Registers commands with the registry during application initialization
3. **Mock WebSocket**: Simulates the WebSocket connection to n8n for development and testing
4. **Mock Voice Command Panel**: A UI component for testing voice commands during development

## System Flow Diagram

```mermaid
sequenceDiagram
    participant User
    participant App as Dreambox Studio App
    participant n8n as n8n Workflow
    participant ElevenLabs as ElevenLabs API
    participant CommandRegistry as Command Registry

    User->>App: Speaks voice command
    App->>n8n: Sends audio data
    n8n->>ElevenLabs: Forwards audio for STT
    ElevenLabs-->>n8n: Returns text command

    Note over n8n: Processes command text
    Note over n8n: Extracts intent and parameters

    n8n->>App: Sends command with parameters
    App->>CommandRegistry: Routes to command registry
    CommandRegistry->>CommandRegistry: Looks up command handler
    CommandRegistry->>App: Executes command
    App-->>User: Visual/audio feedback
```

## Component Architecture

```mermaid
graph TD
    User[User] --> |Voice Input| App[Dreambox Studio App]
    App --> |Audio Data| WebSocket[WebSocket Connection]
    WebSocket --> n8n[n8n Workflow Engine]
    n8n --> |Audio| ElevenLabs[ElevenLabs STT API]
    ElevenLabs --> |Text| n8n
    n8n --> |Command + Params| WebSocket
    WebSocket --> CommandRegistry[Command Registry]

    CommandRegistry --> |Lookup| CommandHandlers[Command Handlers]
    CommandHandlers --> AppFeatures[Application Features]

    subgraph "Dreambox Studio Application"
        WebSocket
        CommandRegistry
        CommandHandlers
        AppFeatures
        FeedbackSystem[User Feedback System]
    end

    AppFeatures --> FeedbackSystem
    FeedbackSystem --> User
```

## Development Architecture (Mock Implementation)

```mermaid
graph TD
    User[User] --> |Text Input| MockPanel[Mock Voice Command Panel]
    MockPanel --> |Command Text| Parser[Command Parser]
    Parser --> MockWebSocket[Mock WebSocket]
    MockWebSocket --> CommandRegistry[Command Registry]

    CommandRegistry --> |Lookup| CommandHandlers[Command Handlers]
    CommandHandlers --> AppFeatures[Application Features]

    subgraph "Dreambox Studio Application"
        MockPanel
        Parser
        MockWebSocket
        CommandRegistry
        CommandHandlers
        AppFeatures
        FeedbackSystem[User Feedback System]
    end

    AppFeatures --> FeedbackSystem
    FeedbackSystem --> User
```

## Using Voice Commands

### Available Commands

The system currently supports the following commands:

1. **Navigation Commands**
   - `openDashboard` - Navigate to the dashboard
   - `openSettings` - Navigate to settings

2. **UI Commands**
   - `toggleDarkMode` - Toggle between light and dark mode
   - `toggleLeftDrawer` - Open or close the navigation drawer
   - `toggleRightDrawer` - Open or close the preview drawer

3. **Auth Commands**
   - `logout` - Log out of the application

4. **Role-Specific Commands**
   - For designers:
     - `openDesigns` - Navigate to the designs page
     - `openTemplates` - Navigate to the templates page
   - For admins:
     - `openProjects` - Navigate to the projects page
   - For super-admins:
     - `openUserManagement` - Navigate to the user management page

5. **System Commands**
   - `help` - Show available commands

### Using the Mock Voice Command Panel

During development, you can use the Mock Voice Command Panel to test voice commands:

1. Click the microphone icon in the footer to open the panel
2. Type a command in the searchable dropdown or select one from the list
3. Click "Execute" or press Enter to run the command
4. View the result in the "Last Result" section

The panel supports natural language input, so you can type phrases like "toggle dark mode" or "open dashboard" instead of the exact command names.

### Adding New Commands

To add a new command to the system:

1. Open `src/services/commands/setup.ts`
2. Add a new command registration in the appropriate section:

```typescript
commandRegistry.register({
  name: 'commandName',
  handler: (params): CommandResult => {
    // Implementation goes here
    return { success: true, message: 'Command executed' };
  },
  description: 'Description of the command',
  examples: ['Example phrase 1', 'Example phrase 2']
});
```

3. The command will be automatically available in the Mock Voice Command Panel

## Command System Architecture

The voice command system is built with a modular architecture consisting of the following components:

### 1. Command Types (`src/services/commands/types.ts`)

```typescript
export interface CommandParameter {
  name: string;
  type: string;
  required: boolean;
  description: string;
}

export interface CommandDefinition {
  name: string;
  handler: (...args: unknown[]) => unknown;
  description: string;
  parameters?: CommandParameter[];
  examples?: string[];
}

export interface CommandResult {
  success: boolean;
  message?: string;
  data?: unknown;
}
```

### 2. Command Registry (`src/services/commands/registry.ts`)

The registry is a singleton that manages all available commands:

```typescript
class CommandRegistry {
  private commands: CommandDefinition[] = [];

  register(command: CommandDefinition): void {
    // Add or update command
  }

  async execute(commandName: string, params: Record<string, unknown> = {}): Promise<CommandResult> {
    // Find and execute the command
  }

  getAvailableCommands(): Array<{ name: string; description: string; parameters?: CommandParameter[]; examples?: string[]; }> {
    // Return all available commands
  }

  getCommand(commandName: string): CommandDefinition | undefined {
    // Get a specific command
  }

  clear(): void {
    // Clear all commands
  }
}

// Create a singleton instance
export const commandRegistry = new CommandRegistry();
```

### 3. Command Setup (`src/services/commands/setup.ts`)

This module registers all commands with the registry:

```typescript
export function createCommandSetup() {
  return function setupCommands() {
    const router = useRouter();
    const $q = useQuasar();
    const authStore = useAuthStore();

    // Register navigation commands
    commandRegistry.register({
      name: 'openDashboard',
      handler: () => { /* implementation */ },
      description: 'Navigate to the dashboard',
      examples: ['Open dashboard', 'Go to dashboard', 'Show dashboard']
    });

    // Register more commands...
  };
}
```

### 4. Mock WebSocket (`src/services/commands/mock-websocket.ts`)

Simulates the WebSocket connection to n8n:

```typescript
class MockCommandWebSocket {
  private listeners: Record<string, Function[]> = {};

  simulateCommand(command: string, params: Record<string, unknown> = {}): void {
    // Simulate receiving a command from n8n
  }

  on(event: string, callback: Function): void {
    // Register event listener
  }

  // Other methods...
}

// Create a singleton instance
export const mockWebSocket = new MockCommandWebSocket();
```

### 5. Mock Voice Command Panel (`src/components/development/MockVoiceCommandPanel.vue`)

A UI component for testing voice commands during development:

```vue
<template>
  <q-dialog :model-value="modelValue" @update:model-value="updateModelValue" position="right">
    <q-card class="mock-voice-panel">
      <!-- UI for testing voice commands -->
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
// Implementation of the mock panel
</script>
```

### 6. Boot File (`src/boot/commands.ts`)

Initializes the command system during application startup:

```typescript
export default boot(({ app }) => {
  // Register a global property for command setup
  app.config.globalProperties.$setupCommands = createCommandSetup();

  // Make it available in the Vue instance
  app.provide('setupCommands', createCommandSetup());
});
```

## Integration with n8n (Future Implementation)

When integrating with n8n in the future, the following steps will be needed:

1. Set up a WebSocket server in n8n to receive audio data from the application
2. Configure n8n to send the audio to ElevenLabs for speech-to-text conversion
3. Process the text in n8n to extract the command and parameters
4. Send the command back to the application via WebSocket
5. Replace the mock WebSocket with a real WebSocket connection

The current architecture is designed to make this transition as smooth as possible, with minimal changes to the application code.

