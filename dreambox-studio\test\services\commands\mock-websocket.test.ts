import { describe, it, expect, vi, beforeEach, afterEach, afterAll } from 'vitest';
import { MockCommandWebSocket } from '../../../src/services/commands/mock-websocket';
import { commandRegistry } from '../../../src/services/commands/registry';

// Create a mock for commandRegistry.execute
const originalExecute = commandRegistry.execute;
commandRegistry.execute = vi.fn().mockResolvedValue({ success: true, data: 'test result' });

// Mock console methods to avoid cluttering test output
const originalConsoleLog = console.log;
const originalConsoleError = console.error;

describe('MockCommandWebSocket', () => {
  let mockWebSocket: MockCommandWebSocket;

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();

    // Mock console methods
    console.log = vi.fn();
    console.error = vi.fn();

    // Create a new instance for each test
    mockWebSocket = new MockCommandWebSocket();
  });

  afterEach(() => {
    // Restore console methods
    console.log = originalConsoleLog;
    console.error = originalConsoleError;
  });

  afterAll(() => {
    // Restore original commandRegistry.execute
    commandRegistry.execute = originalExecute;
  });

  it('connects and emits open event', () => {
    const openCallback = vi.fn();
    mockWebSocket.on('open', openCallback);

    mockWebSocket.connect();

    expect(openCallback).toHaveBeenCalled();
    expect(console.log).toHaveBeenCalledWith('[MockWebSocket] Connected');
  });

  it('disconnects and emits close event', () => {
    const closeCallback = vi.fn();
    mockWebSocket.on('close', closeCallback);

    mockWebSocket.disconnect();

    expect(closeCallback).toHaveBeenCalled();
    expect(console.log).toHaveBeenCalledWith('[MockWebSocket] Disconnected');
  });

  it('simulates a command and executes it', async () => {
    // Connect first
    mockWebSocket.connect();

    // Set up result listener
    const resultCallback = vi.fn();
    mockWebSocket.on('result', resultCallback);

    // Simulate command
    mockWebSocket.simulateCommand('testCommand', { param: 'value' });

    // Wait for async execution
    await new Promise(resolve => setTimeout(resolve, 0));

    // Verify command was executed
    expect(commandRegistry.execute).toHaveBeenCalledWith('testCommand', { param: 'value' });

    // Verify result was emitted
    expect(resultCallback).toHaveBeenCalled();
    expect(resultCallback).toHaveBeenCalledWith({
      command: 'testCommand',
      params: { param: 'value' },
      result: { success: true, data: 'test result' }
    });
  });

  it('handles command execution errors', async () => {
    // Mock commandRegistry to throw an error
    (commandRegistry.execute as any).mockRejectedValueOnce(new Error('Test error'));

    // Connect first
    mockWebSocket.connect();

    // Set up result listener
    const resultCallback = vi.fn();
    mockWebSocket.on('result', resultCallback);

    // Simulate command
    mockWebSocket.simulateCommand('errorCommand');

    // Wait for async execution
    await new Promise(resolve => setTimeout(resolve, 0));

    // Verify error was logged
    expect(console.error).toHaveBeenCalled();

    // Verify error result was emitted
    expect(resultCallback).toHaveBeenCalled();
    expect(resultCallback).toHaveBeenCalledWith({
      command: 'errorCommand',
      params: {},
      error: 'Test error'
    });
  });

  it('does not simulate command when not connected', () => {
    // Do not connect

    // Simulate command
    mockWebSocket.simulateCommand('testCommand');

    // Verify error was logged
    expect(console.error).toHaveBeenCalledWith('[MockWebSocket] Cannot send command: not connected');

    // Verify command was not executed
    expect(commandRegistry.execute).not.toHaveBeenCalled();
  });

  it('registers and removes event listeners', () => {
    const callback1 = vi.fn();
    const callback2 = vi.fn();

    // Register listeners
    mockWebSocket.on('test', callback1);
    mockWebSocket.on('test', callback2);

    // Emit event (using private method via any)
    (mockWebSocket as any).emit('test', { data: 'test' });

    // Verify both callbacks were called
    expect(callback1).toHaveBeenCalledWith({ data: 'test' });
    expect(callback2).toHaveBeenCalledWith({ data: 'test' });

    // Remove one listener
    mockWebSocket.off('test', callback1);

    // Emit event again
    (mockWebSocket as any).emit('test', { data: 'test2' });

    // Verify only callback2 was called again
    expect(callback1).toHaveBeenCalledTimes(1);
    expect(callback2).toHaveBeenCalledTimes(2);
    expect(callback2).toHaveBeenLastCalledWith({ data: 'test2' });
  });

  it('emits message event when receiving a message', async () => {
    // Connect first
    mockWebSocket.connect();

    // Set up message listener
    const messageCallback = vi.fn();
    mockWebSocket.on('message', messageCallback);

    // Simulate command to trigger receiveMessage
    mockWebSocket.simulateCommand('testCommand');

    // Verify message event was emitted
    expect(messageCallback).toHaveBeenCalled();
    expect(messageCallback.mock.calls[0][0]).toHaveProperty('data');

    // Parse the JSON data to verify it contains the expected command
    const parsedData = JSON.parse((messageCallback.mock.calls[0][0] as any).data);
    expect(parsedData).toEqual({
      type: 'command',
      command: 'testCommand',
      params: {}
    });
  });
});
