# Context Manager Utilities

This file contains the utility functions used by the Context Manager for pattern matching and property resolution.

## File: `utils/contextMatcher.ts`

```typescript
/**
 * Match a context pattern against the current context
 * Supports wildcards (*) and placeholders ([name])
 */
export function matchContextPattern(pattern: string, context: string): boolean {
  // Special case for ALL pattern
  if (pattern === 'ALL') return true;
  
  // Convert pattern to regex
  // - Replace * with .*
  // - Replace placeholders like [table_type] with capturing groups
  const regexPattern = pattern
    .replace(/\./g, '\\.')
    .replace(/\*/g, '.*')
    .replace(/\[(\w+)\]/g, '([\\w-]+)');
    
  const regex = new RegExp(`^${regexPattern}$`);
  return regex.test(context);
}

/**
 * Extract variables from a context match
 * Returns an object with variable names as keys and matched values as values
 */
export function extractContextVariables(pattern: string, context: string): Record<string, string> {
  const variables: Record<string, string> = {};
  
  // Special case for ALL pattern
  if (pattern === 'ALL') return variables;
  
  // Extract named placeholders
  const placeholderRegex = /\[(\w+)\]/g;
  let placeholderMatch;
  const placeholders: string[] = [];
  
  while ((placeholderMatch = placeholderRegex.exec(pattern)) !== null) {
    placeholders.push(placeholderMatch[1]);
  }
  
  // Convert pattern to regex with capturing groups
  const regexPattern = pattern
    .replace(/\./g, '\\.')
    .replace(/\*/g, '(.*)')
    .replace(/\[(\w+)\]/g, '([\\w-]+)');
    
  const regex = new RegExp(`^${regexPattern}$`);
  const matches = regex.exec(context);
  
  if (matches) {
    // First match is the full string, skip it
    for (let i = 1; i < matches.length; i++) {
      if (i <= placeholders.length) {
        variables[placeholders[i-1]] = matches[i];
      } else {
        // For anonymous wildcards (*), use index as key
        variables[`wildcard${i-placeholders.length-1}`] = matches[i];
      }
    }
  }
  
  return variables;
}
```

## File: `utils/propertyResolver.ts`

```typescript
/**
 * Resolve properties using extracted variables
 * Replaces placeholders like [name] with values from variables
 */
export function resolveProperties(
  properties: Record<string, any>,
  variables: Record<string, any>
): Record<string, any> {
  const resolved: Record<string, any> = {};
  
  // Clone the properties object
  for (const [key, value] of Object.entries(properties)) {
    if (typeof value === 'string') {
      // Replace placeholders in string values
      resolved[key] = value.replace(/\[(\w+)\]/g, (_, name) => {
        return variables[name] !== undefined ? variables[name] : `[${name}]`;
      });
    } else if (Array.isArray(value)) {
      // Recursively resolve array values
      resolved[key] = value.map(item => {
        if (typeof item === 'string') {
          return item.replace(/\[(\w+)\]/g, (_, name) => {
            return variables[name] !== undefined ? variables[name] : `[${name}]`;
          });
        } else if (typeof item === 'object' && item !== null) {
          return resolveProperties(item, variables);
        }
        return item;
      });
    } else if (typeof value === 'object' && value !== null) {
      // Recursively resolve object values
      resolved[key] = resolveProperties(value, variables);
    } else {
      // Copy primitive values as is
      resolved[key] = value;
    }
  }
  
  return resolved;
}
```

## File: `utils/deviceDetection.ts`

```typescript
export enum DeviceType {
  DESKTOP_BROWSER = 'Desktop',
  MOBILE_BROWSER = 'Mobile',
  DESKTOP_ELECTRON = 'Electron'
}

/**
 * Detect the current device type
 */
export function detectDeviceType(): DeviceType {
  // Check if running in Electron
  if (window.navigator.userAgent.includes('Electron')) {
    return DeviceType.DESKTOP_ELECTRON;
  }
  
  // Check if mobile browser
  if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
    return DeviceType.MOBILE_BROWSER;
  }
  
  // Default to desktop browser
  return DeviceType.DESKTOP_BROWSER;
}
```
