/**
 * promptElementsStore.ts
 *
 * This store manages prompt elements data.
 * It provides methods for loading, filtering, and manipulating prompt elements.
 */

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { supabase } from '../boot/supabase';
import { useAuthStore } from './auth';

/**
 * Prompt element interface
 */
export interface PromptElement {
  id: number;
  type_id: number;
  value: string;
  description: string | null;
  created_at: string;
  updated_at: string;
  // Additional properties needed for the UI
  name?: string;
  category?: string;
  is_system?: boolean;
  designer_id?: number | null;
}

/**
 * Prompt elements store definition
 */
export const usePromptElementsStore = defineStore('promptElements', () => {
  // State
  const authStore = useAuthStore();
  const elements = ref<PromptElement[]>([]);
  const loading = ref(false);

  // Getters
  const categories = computed(() => {
    const cats = new Set<string>();
    elements.value.forEach(el => {
      if (el.category) cats.add(el.category);
    });
    return Array.from(cats).sort();
  });

  const systemElements = computed(() => {
    return elements.value.filter(el => el.is_system === true);
  });

  const userElements = computed(() => {
    const user = authStore.getUser();
    return elements.value.filter(el =>
      el.is_system === false &&
      user &&
      el.designer_id !== undefined &&
      el.designer_id === user.appUserId
    );
  });

  const elementsByCategory = computed(() => {
    const result: Record<string, PromptElement[]> = {};

    categories.value.forEach(cat => {
      result[cat] = elements.value.filter(el => el.category === cat);
    });

    return result;
  });

  // Actions
  /**
   * Load all prompt elements
   */
  async function loadElements() {
    const user = authStore.getUser();
    if (!user) return;

    loading.value = true;

    try {
      // Get system elements and user's custom elements
      const { data, error } = await supabase
        .from('prompt_elements')
        .select('*')
        .or(`is_system.eq.true,designer_id.eq.${user.appUserId}`);

      if (error) throw new Error(`Failed to fetch prompt elements: ${error.message}`);

      // Process the data to add UI properties
      elements.value = (data || []).map((item: { value: string }) => ({
        ...item,
        // Add default values for UI properties
        name: item.value,
        category: 'Default',
        is_system: true // Default to system elements
      })) as PromptElement[];
    } catch (err) {
      console.error('Error loading prompt elements:', err);
      elements.value = [];
    } finally {
      loading.value = false;
    }
  }

  /**
   * Get elements by IDs
   */
  function getElementsByIds(ids: number[]): PromptElement[] {
    return elements.value.filter(el => ids.includes(el.id));
  }

  /**
   * Get elements by category
   */
  function getElementsByCategory(category: string): PromptElement[] {
    return elements.value.filter(el => el.category === category);
  }

  return {
    // State
    elements,
    loading,

    // Getters
    categories,
    systemElements,
    userElements,
    elementsByCategory,

    // Actions
    loadElements,
    getElementsByIds,
    getElementsByCategory
  };
});
