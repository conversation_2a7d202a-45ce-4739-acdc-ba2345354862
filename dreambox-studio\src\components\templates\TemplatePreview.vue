<template>
  <div class="template-preview q-pa-md">
    <div class="text-h6 q-mb-md">Template Preview</div>

    <div v-if="loading" class="text-center q-pa-lg">
      <q-spinner color="primary" size="3em" />
      <div class="q-mt-sm">Generating preview...</div>
    </div>

    <div v-else-if="previewError" class="text-center q-pa-lg text-negative">
      <q-icon name="error" size="3em" />
      <div class="q-mt-sm">{{ previewError }}</div>
      <q-btn
        color="primary"
        label="Try Again"
        class="q-mt-md"
        @click="generatePreview"
      />
    </div>

    <div v-else-if="previewUrl" class="text-center">
      <q-img
        :src="previewUrl"
        spinner-color="primary"
        style="max-width: 100%"
        :ratio="getAspectRatio()"
      />
      <div class="row justify-center q-mt-md">
        <q-btn
          color="primary"
          icon="refresh"
          label="Regenerate"
          @click="generatePreview"
        />
      </div>
    </div>

    <div v-else class="text-center q-pa-lg text-grey">
      <q-icon name="image" size="3em" />
      <div class="q-mt-sm">
        Select template elements to generate a preview
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, watch } from 'vue';

const props = defineProps({
  selectedElementTypes: {
    type: Array as () => number[],
    required: true
  },
  selectedElementValues: {
    type: Array as () => number[],
    required: true
  },
  imageServerData: {
    type: Object as () => {
      model: string;
      steps: number;
      aspect_ratio: string;
      seed: number | null;
    },
    required: true
  }
});

const loading = ref(false);
const previewError = ref<string | null>(null);
const previewUrl = ref<string | null>(null);

// Watch for changes in selected values or image server data
watch([() => props.selectedElementValues, () => props.imageServerData], () => {
  // Only generate preview if we have selected values
  if (props.selectedElementValues.length > 0) {
    void generatePreview();
  }
}, { deep: true });

// Generate a preview image
async function generatePreview() {
  if (props.selectedElementValues.length === 0) {
    previewUrl.value = null;
    return;
  }

  loading.value = true;
  previewError.value = null;

  try {
    // This is a placeholder for the actual image generation API call
    // In a real implementation, you would call your image generation service

    // Simulate API call with a timeout
    await new Promise(resolve => setTimeout(resolve, 2000));

    // For demo purposes, use a placeholder image
    previewUrl.value = `https://picsum.photos/seed/${Date.now()}/800/800`;
  } catch (error) {
    console.error('Error generating preview:', error);
    previewError.value = 'Failed to generate preview image';
  } finally {
    loading.value = false;
  }
}

// Get aspect ratio from image server data
function getAspectRatio(): number {
  const ratio = props.imageServerData.aspect_ratio;

  if (ratio === '1:1') return 1;
  if (ratio === '4:3') return 4/3;
  if (ratio === '3:4') return 3/4;
  if (ratio === '16:9') return 16/9;
  if (ratio === '9:16') return 9/16;

  return 1; // Default to 1:1
}
</script>

<style lang="scss" scoped>
.template-preview {
  // Add any specific styles here
}
</style>
