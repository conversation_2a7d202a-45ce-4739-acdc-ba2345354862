// Mock localStorage
const localStorageMock = (() => {
  let store = {};
  return {
    getItem: (key) => store[key] || null,
    setItem: (key, value) => {
      store[key] = value.toString();
    },
    removeItem: (key) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    }
  };
})();

// Set up global mocks
Object.defineProperty(global, 'localStorage', {
  value: localStorageMock,
  writable: true
});

// This file will be automatically loaded by Bun before running tests
