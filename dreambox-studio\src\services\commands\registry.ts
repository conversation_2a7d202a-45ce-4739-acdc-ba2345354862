/**
 * Command Registry
 *
 * This module provides a central registry for all voice commands in the application.
 * It allows for registering, executing, and discovering commands.
 */

import type { CommandDefinition, CommandResult } from './types';

class CommandRegistry {
  private commands: CommandDefinition[] = [];

  /**
   * Register a new command
   */
  register(command: CommandDefinition): void {
    // Check if command with this name already exists
    const existingIndex = this.commands.findIndex(cmd => cmd.name === command.name);
    if (existingIndex >= 0) {
      // Replace existing command
      this.commands[existingIndex] = command;
      // console.log(`Command '${command.name}' updated`);
    } else {
      // Add new command
      this.commands.push(command);
      // console.log(`Command '${command.name}' registered`);
    }
  }

  /**
   * Execute a command by name with optional parameters
   */
  async execute(commandName: string, params: Record<string, unknown> = {}): Promise<CommandResult> {
    try {
      const command = this.commands.find(cmd => cmd.name === commandName);
      if (!command) {
        return {
          success: false,
          message: `Command not found: ${commandName}`
        };
      }

      // Validate required parameters
      if (command.parameters) {
        const missingParams = command.parameters
          .filter(param => param.required && params[param.name] === undefined)
          .map(param => param.name);

        if (missingParams.length > 0) {
          return {
            success: false,
            message: `Missing required parameters: ${missingParams.join(', ')}`
          };
        }
      }

      // Execute the command
      const result = await command.handler(params);

      // If the handler returns a CommandResult, use it directly
      if (result && typeof result === 'object' && 'success' in result) {
        return result as CommandResult;
      }

      // Otherwise, wrap the result in a success response
      return {
        success: true,
        data: result
      };
    } catch (error) {
      console.error(`Error executing command '${commandName}':`, error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get all available commands with their metadata
   */
  getAvailableCommands(): Array<{
    name: string;
    description: string;
    parameters?: CommandDefinition['parameters'];
    examples?: CommandDefinition['examples'];
  }> {
    return this.commands.map(({ name, description, parameters, examples }) => ({
      name,
      description,
      parameters,
      examples
    }));
  }

  /**
   * Get a specific command by name
   */
  getCommand(commandName: string): CommandDefinition | undefined {
    return this.commands.find(cmd => cmd.name === commandName);
  }

  /**
   * Clear all registered commands
   */
  clear(): void {
    this.commands = [];
  }
}

// Create a singleton instance
export const commandRegistry = new CommandRegistry();
