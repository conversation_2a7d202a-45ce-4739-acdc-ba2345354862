{"name": "@infectoone/vue-ganttastic", "version": "2.3.2", "description": "A simple and customizable Gantt chart component for Vue.js", "author": "<PERSON><PERSON> (@zunnzunn)", "scripts": {"serve": "vite", "build": "npm run build:types && npm run build:lib", "build:lib": "vite build", "build:types": "vue-tsc --declaration --emitDeclarationOnly --outDir lib_types", "typecheck": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --ignore-path .gitignore", "lint:fix": "npm run lint --fix", "docs:build": "vitepress build docs", "docs:dev": "vitepress dev docs", "docs:preview": "vitepress preview docs"}, "type": "module", "exports": {".": {"types": "./lib_types/vue-ganttastic.d.ts", "import": "./lib/vue-ganttastic.js", "require": "./lib/vue-ganttastic.umd.cjs"}}, "main": "./lib/vue-ganttastic.umd.cjs", "types": "./lib_types/vue-ganttastic.d.ts", "files": ["lib_types", "lib/vue-ganttastic.js", "lib/vue-ganttastic.umd.cjs"], "devDependencies": {"@rushstack/eslint-patch": "^1.2.0", "@senojs/rollup-plugin-style-inject": "^0.1.1", "@types/node": "^16.11.58", "@types/postcss-preset-env": "^7.7.0", "@vitejs/plugin-vue": "^3.1.2", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-typescript": "^11.0.2", "@vue/tsconfig": "^0.1.3", "@vuepress/plugin-search": "2.0.0-beta.51", "eslint": "^8.25.0", "eslint-plugin-vue": "^9.6.0", "npm-run-all": "^4.1.5", "postcss": "^8.4.17", "postcss-preset-env": "^7.8.2", "prettier": "^2.7.1", "typescript": "~4.8.4", "vite": "^3.1.6", "vitepress": "^1.0.0-rc.4", "vue-tsc": "^2.2.8"}, "peerDependencies": {"dayjs": "^1.11.5", "vue": "^3.2.40"}, "homepage": "https://zunnzunn.github.io/vue-ganttastic/", "keywords": ["gantt", "chart", "bar", "diagram", "vue", "v<PERSON><PERSON><PERSON>", "ganttastic"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/zunnzunn/vue-ganttastic"}, "dependencies": {"@vueuse/core": "^9.1.1"}}