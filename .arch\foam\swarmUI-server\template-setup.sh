#!/bin/bash
# SwarmUI Template Setup Script
# This script installs SwarmUI and ComfyUI for creating a RunPod template

# Set environment variables
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128
export CUDA_LAUNCH_BLOCKING=0
export CUDA_VISIBLE_DEVICES=0

# Create workspace directory if it doesn't exist
mkdir -p /workspace
cd /workspace

# Install system dependencies
apt-get update
apt-get install -y git wget curl libgl1 libglib2.0-0 python3-pip python3-venv

# Upgrade pip
pip install --upgrade pip --root-user-action=ignore

# Install SwarmUI
echo "Installing SwarmUI..."
git clone https://github.com/swarmui/swarmui.git SwarmUI
cd SwarmUI
pip install -r requirements.txt --root-user-action=ignore

# Install ComfyUI
echo "Installing ComfyUI..."
cd /workspace
git clone https://github.com/comfyanonymous/ComfyUI.git
cd ComfyUI
pip install -r requirements.txt --root-user-action=ignore

# Create model directories
mkdir -p /workspace/models/diffusion_models
mkdir -p /workspace/models/clip
mkdir -p /workspace/models/vae
mkdir -p /workspace/outputs

# Create symbolic links for ComfyUI models
mkdir -p /workspace/ComfyUI/models/checkpoints
ln -s /workspace/models/diffusion_models /workspace/ComfyUI/models/checkpoints
ln -s /workspace/models/clip /workspace/ComfyUI/models/clip
ln -s /workspace/models/vae /workspace/ComfyUI/models/vae

# Create the SwarmUI config directory
mkdir -p ~/.swarmui

# Create the auto-backend-startup.sh script
cat > /workspace/auto-backend-startup.sh << 'EOL'
#!/bin/bash
# SwarmUI Auto-Backend Startup Script
# This script automatically configures and starts SwarmUI with ComfyUI as a backend

# Set environment variables
export DOTNET_ROOT=$HOME/.dotnet
export PATH=$PATH:$HOME/.dotnet:$HOME/.dotnet/tools
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128
export CUDA_LAUNCH_BLOCKING=0
export CUDA_VISIBLE_DEVICES=0

# Generate a secure API key
API_KEY=$(openssl rand -hex 16)
echo "Your SwarmUI API key is: $API_KEY"

# Create the SwarmUI config directory if it doesn't exist
mkdir -p ~/.swarmui

# Create the config.json file with ComfyUI backend pre-configured
cat > ~/.swarmui/config.json << EOL
{
  "BackendUrl": "http://0.0.0.0:7801",
  "FrontendUrl": "http://0.0.0.0:7800",
  "ModelRoot": "/workspace/models",
  "OutputsPath": "/workspace/outputs",
  "DisableUpdateCheck": true,
  "AutoInstall": {
    "Torch": false,
    "Comfy": false,
    "Models": false
  },
  "Auth": {
    "Enabled": true,
    "ApiKey": "${API_KEY}"
  },
  "ComfyUI": {
    "Path": "/workspace/ComfyUI",
    "Url": "http://127.0.0.1:7860",
    "EnableWorkflows": true,
    "ModelPaths": {
      "checkpoints": "/workspace/ComfyUI/models/checkpoints",
      "clip": "/workspace/models/clip",
      "vae": "/workspace/models/vae"
    }
  },
  "Backend": {
    "Type": "ComfyUI-SelfStarting",
    "ComfyUIPath": "/workspace/ComfyUI",
    "ComfyUIPort": 7860,
    "EnableWorkflows": true
  }
}
EOL

# Create models.json file if it doesn't exist
if [ ! -f ~/.swarmui/models.json ]; then
  echo "Creating models.json file..."
  cat > ~/.swarmui/models.json << EOL
{
  "checkpoints": [
    {
      "name": "Flux Schnell",
      "path": "/workspace/models/diffusion_models/FLUX1-Schnell-FP8.safetensors",
      "vae": "/workspace/models/vae/ae.safetensors",
      "clip": "/workspace/models/clip/t5xxl_enconly.safetensors",
      "config": "configs/stable-diffusion/sdxl/sdxl_1_0.yaml",
      "default": true
    }
  ]
}
EOL
fi

# Update the backend URL if RUNPOD_HTTP_HOST is set
if [ ! -z "$RUNPOD_HTTP_HOST" ]; then
  PUBLIC_URL="https://$RUNPOD_HTTP_HOST"
  sed -i "s|\"BackendUrl\": \".*\"|\"BackendUrl\": \"$PUBLIC_URL\"|" ~/.swarmui/config.json
  sed -i "s|\"FrontendUrl\": \".*\"|\"FrontendUrl\": \"$PUBLIC_URL\"|" ~/.swarmui/config.json
  echo "Updated SwarmUI URLs to use $PUBLIC_URL"
fi

# Start ComfyUI in the background
cd /workspace/ComfyUI
python -c "import torch; torch.backends.cuda.matmul.allow_tf32 = True; torch.backends.cudnn.allow_tf32 = True"
nohup python main.py --listen 0.0.0.0 --port 7860 > /workspace/comfyui.log 2>&1 &

# Wait for ComfyUI to start
echo "Starting ComfyUI and waiting for it to initialize..."
sleep 15

# Start SwarmUI with ComfyUI backend
cd /workspace/SwarmUI
./launch-linux.sh --launch_mode none --host 0.0.0.0 --comfy_path /workspace/ComfyUI
EOL

# Make the startup script executable
chmod +x /workspace/auto-backend-startup.sh

# Create a README file with instructions
cat > /workspace/README.md << 'EOL'
# SwarmUI with ComfyUI Template

This template includes:

- SwarmUI: A user-friendly interface for AI image generation
- ComfyUI: A powerful node-based backend for stable diffusion

## Startup

The template automatically starts:
1. ComfyUI on port 7860
2. SwarmUI on port 7800 (web interface) and 7801 (API)

## API Access

You can access the SwarmUI API at:
- https://YOUR_POD_ID-7801.proxy.runpod.net/api

The API key is generated on startup and printed to the console.

## Models

To use the template, you need to download models to:
- /workspace/models/diffusion_models/ (for checkpoints)
- /workspace/models/clip/ (for CLIP models)
- /workspace/models/vae/ (for VAE models)

## Troubleshooting

If you encounter issues:
- Check the ComfyUI log: `cat /workspace/comfyui.log`
- Check the SwarmUI log: `cat /workspace/SwarmUI/logs/swarm.log`
EOL

echo "Template setup complete!"
echo "You can now create a template from this pod."
echo "Make sure to set the startup script to: /workspace/auto-backend-startup.sh"
