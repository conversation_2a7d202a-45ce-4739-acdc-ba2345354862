#!/bin/bash

# Check if the filter_engine_templates_with_elements function exists in the database
# This script should be run from the root directory of the project

# Get the database connection string from the environment
DB_CONNECTION_STRING=$(grep -o 'SUPABASE_DB_CONNECTION_STRING=.*' .env | cut -d '=' -f2)

if [ -z "$DB_CONNECTION_STRING" ]; then
  echo "Error: SUPABASE_DB_CONNECTION_STRING not found in .env file"
  exit 1
fi

echo "Checking if filter_engine_templates_with_elements function exists in the database..."

# Run the SQL query to check if the function exists
FUNCTION_EXISTS=$(psql "$DB_CONNECTION_STRING" -t -c "SELECT EXISTS(SELECT 1 FROM pg_proc WHERE proname = 'filter_engine_templates_with_elements');")

if [ "$FUNCTION_EXISTS" = " t" ]; then
  echo "Function filter_engine_templates_with_elements exists in the database."
  
  # Get the function definition
  echo "Function definition:"
  psql "$DB_CONNECTION_STRING" -c "SELECT pg_get_functiondef(oid) FROM pg_proc WHERE proname = 'filter_engine_templates_with_elements';"
else
  echo "Function filter_engine_templates_with_elements does NOT exist in the database."
fi

echo "Done!"
