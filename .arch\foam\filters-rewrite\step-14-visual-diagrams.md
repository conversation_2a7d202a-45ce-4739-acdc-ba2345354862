# Step 14: Visual Diagrams

## Overview

This document provides visual representations of the filter engine architecture using Mermaid diagrams. These diagrams complement the written documentation by illustrating component relationships, data flow, and system architecture.

## Component Architecture Diagram

This diagram shows the main components of the filter engine and their relationships:

```mermaid
graph TD
    subgraph "Core Components"
        FR[FilterRegistry]
        FS[FilterStore]
        FE[FilterEngine]
        FEB[FilterEventBus]
    end
    
    subgraph "Plugins"
        VM[ViewManagementPlugin]
        LM[LayoutManagementPlugin]
        SF[SpecializedFiltersPlugin]
    end
    
    subgraph "Table Adapters"
        TA_T[TemplatesAdapter]
        TA_P[ProductsAdapter]
        TA_U[UsersAdapter]
    end
    
    subgraph "UI Components"
        FP[FilterPanel]
        FCS[FilterControl]
        FSS[FilterSection]
        SFS[SavedFilterSelector]
        FAB[FilterActionBar]
        DS[DraggableSection]
    end
    
    subgraph "Specialized Components"
        PEF[PromptElementsFilter]
        RIF[RelatedItemsFilter]
    end
    
    subgraph "External Systems"
        CM[ContextManager]
        SB[Supabase]
    end
    
    FE --> FR
    FE --> FS
    FE --> FEB
    
    FE --> VM
    FE --> LM
    FE --> SF
    
    VM --> FE
    LM --> FE
    SF --> FE
    
    TA_T --> FE
    TA_P --> FE
    TA_U --> FE
    
    TA_T --> PEF
    TA_T --> RIF
    
    FP --> FS
    FP --> FEB
    FP --> FSS
    FP --> FCS
    FP --> FAB
    FP --> DS
    
    FSS --> FS
    FCS --> FS
    FAB --> FS
    DS --> FS
    
    FS --> SB
    
    CM --> FS
    FS --> CM
```

## Data Flow Diagram

This diagram illustrates how filter data flows through the system:

```mermaid
sequenceDiagram
    participant User
    participant UI as UI Components
    participant Store as FilterStore
    participant Engine as FilterEngine
    participant Registry as FilterRegistry
    participant DB as Supabase
    participant Table as Table Component
    
    User->>UI: Interacts with filter
    UI->>Store: Update filter value
    Store->>Store: Update internal state
    
    alt Save Filter
        UI->>Store: Save current filter
        Store->>DB: Save filter configuration
        DB-->>Store: Confirm save
        Store-->>UI: Update saved filters list
    else Load Filter
        UI->>Store: Load saved filter
        Store->>DB: Fetch filter configuration
        DB-->>Store: Return filter data
        Store->>Store: Apply filter configuration
        Store-->>UI: Update UI state
    else Apply Filter
        UI->>Store: Apply filter
        Store->>Engine: Apply filters to data
        Engine->>Registry: Get filter types
        Registry-->>Engine: Return filter types
        Engine->>Engine: Apply filters to data
        Engine-->>Store: Return filtered data
        Store-->>Table: Update with filtered data
        Table-->>User: Display filtered results
    end
```

## Context Integration Diagram

This diagram shows how the filter engine integrates with the context manager:

```mermaid
sequenceDiagram
    participant User
    participant App as Application
    participant CM as ContextManager
    participant CFM as ContextFilterManager
    participant FS as FilterStore
    participant FE as FilterEngine
    participant Table as Table Component
    
    User->>App: Navigate to page
    App->>CM: Update context
    CM->>CFM: Context changed event
    CFM->>FS: Set current context
    
    alt Context has associated filter
        CFM->>FS: Load filter for context
        FS->>FE: Apply filter
        FE-->>Table: Update with filtered data
    else No associated filter
        CFM->>FS: Load default filter for table
        FS->>FE: Apply filter
        FE-->>Table: Update with filtered data
    end
    
    User->>App: Save filter for context
    App->>CFM: Save filter association
    CFM->>FS: Associate filter with context
    FS->>DB: Save context-filter mapping
```

## Column Visibility and Drag/Drop Diagram

This diagram illustrates the column visibility and drag/drop functionality:

```mermaid
sequenceDiagram
    participant User
    participant FP as FilterPanel
    participant DS as DraggableSection
    participant FS as FilterStore
    participant Table as Table Component
    
    User->>FP: Toggle column visibility
    FP->>FS: Update column visibility
    FS->>FS: Update internal state
    FS-->>Table: Update visible columns
    
    User->>DS: Drag column to different section
    DS->>FS: Update column section
    FS->>FS: Update internal state
    FS-->>Table: Update column organization
    
    User->>DS: Reorder columns within section
    DS->>FS: Update column order
    FS->>FS: Update internal state
    FS-->>Table: Update column order
```

## Modular Architecture Diagram

This diagram shows the modular architecture with plugins and adapters:

```mermaid
graph TD
    subgraph "Core Engine"
        FE[FilterEngine]
        FR[FilterRegistry]
        FS[FilterStore]
    end
    
    subgraph "Plugin System"
        PI[PluginInterface]
        VM[ViewManagementPlugin]
        LM[LayoutManagementPlugin]
        SF[SpecializedFiltersPlugin]
    end
    
    subgraph "Adapter System"
        AI[AdapterInterface]
        TA_T[TemplatesAdapter]
        TA_P[ProductsAdapter]
        TA_U[UsersAdapter]
    end
    
    subgraph "Extension Points"
        EP1[New Filter Types]
        EP2[Custom UI Components]
        EP3[Table-Specific Behavior]
    end
    
    FE --> FR
    FE --> FS
    
    PI --> FE
    VM --> PI
    LM --> PI
    SF --> PI
    
    AI --> FE
    TA_T --> AI
    TA_P --> AI
    TA_U --> AI
    
    VM --> EP2
    SF --> EP1
    AI --> EP3
```

## Server-Side Filtering Diagram

This diagram illustrates the server-side filtering process:

```mermaid
sequenceDiagram
    participant Client
    participant API as API Layer
    participant QB as QueryBuilder
    participant SQL as SQL Functions
    participant DB as Database
    
    Client->>API: Request filtered data
    API->>QB: Convert filter params to SQL
    
    alt Standard Filters
        QB->>SQL: Build standard filter conditions
    else Specialized Filters
        QB->>SQL: Build specialized filter conditions
    end
    
    SQL->>DB: Execute query with filters
    DB-->>SQL: Return filtered results
    SQL-->>API: Return data with pagination info
    API-->>Client: Return filtered data
```

## Filter Lifecycle Diagram

This diagram shows the lifecycle of a filter:

```mermaid
stateDiagram-v2
    [*] --> Initialized: Initialize Filter Engine
    
    Initialized --> TableSelected: Set Current Table
    TableSelected --> DefaultLoaded: Load Default Filter
    
    state "Filter Interaction" as FI
    DefaultLoaded --> FI
    
    FI --> FilterModified: Update Filter Value
    FilterModified --> FilterApplied: Apply Filter
    FilterApplied --> DataFiltered: Filter Data
    DataFiltered --> FI
    
    FI --> FilterSaved: Save Filter
    FilterSaved --> FilterStored: Store in Database
    FilterStored --> FI
    
    FI --> FilterLoaded: Load Saved Filter
    FilterLoaded --> FilterApplied
    
    FI --> FilterCleared: Clear Filter
    FilterCleared --> DefaultLoaded
    
    FI --> [*]: Cleanup
```

## Migration Process Diagram

This diagram illustrates the migration process from the old filter system to the new one:

```mermaid
graph TD
    subgraph "Phase 1: Parallel Implementation"
        P1_1[Implement New Filter Engine]
        P1_2[Keep Old Filter System]
        P1_3[Create Feature Flags]
    end
    
    subgraph "Phase 2: New Templates Filter"
        P2_1[Create New Templates Filter]
        P2_2[Test in Isolation]
    end
    
    subgraph "Phase 3: Migration"
        P3_1[Create Filter Format Converter]
        P3_2[Create Adapter Components]
        P3_3[Update TemplateBrowser]
    end
    
    subgraph "Phase 4: Rollout"
        P4_1[Enable for Developers]
        P4_2[Enable for Staging]
        P4_3[Gradual Production Rollout]
    end
    
    subgraph "Phase 5: Cleanup"
        P5_1[Remove Old Code]
        P5_2[Remove Feature Flags]
        P5_3[Finalize Documentation]
    end
    
    P1_1 --> P1_3
    P1_2 --> P1_3
    P1_3 --> P2_1
    P2_1 --> P2_2
    P2_2 --> P3_1
    P3_1 --> P3_2
    P3_2 --> P3_3
    P3_3 --> P4_1
    P4_1 --> P4_2
    P4_2 --> P4_3
    P4_3 --> P5_1
    P5_1 --> P5_2
    P5_2 --> P5_3
```

## UI Component Hierarchy

This diagram shows the hierarchy of UI components:

```mermaid
graph TD
    FP[FilterPanel]
    SFS[SavedFilterSelector]
    MFS[MainFilterSection]
    EFS[ExpandedFilterSection]
    SPS[SpecializedFilterSection]
    FAB[FilterActionBar]
    
    FC_T[TextFilterControl]
    FC_N[NumberFilterControl]
    FC_D[DateFilterControl]
    FC_S[SelectFilterControl]
    FC_MS[MultiSelectFilterControl]
    
    PEF[PromptElementsFilter]
    RIF[RelatedItemsFilter]
    
    FP --> SFS
    FP --> MFS
    FP --> EFS
    FP --> SPS
    FP --> FAB
    
    MFS --> FC_T
    MFS --> FC_N
    MFS --> FC_D
    MFS --> FC_S
    MFS --> FC_MS
    
    EFS --> FC_T
    EFS --> FC_N
    EFS --> FC_D
    EFS --> FC_S
    EFS --> FC_MS
    
    SPS --> PEF
    SPS --> RIF
```

## Templates Table Layout

This diagram illustrates the specific layout for the Templates table:

```mermaid
graph TD
    subgraph "Templates Table Layout"
        MT[Main Table Section]
        
        subgraph "Expanded Section"
            DT[Details Tab]
            PET[Prompt Elements Tab]
            RIT[Related Items Tab]
        end
    end
    
    MT --> DT
    
    subgraph "Drag/Drop Behavior"
        DD1[Drag between Main and Details]
        DD2[No Drag to/from Prompt Elements]
        DD3[No Drag to/from Related Items]
    end
```

## Implementation Dependencies

This diagram shows the implementation dependencies between components:

```mermaid
graph TD
    subgraph "Foundation Layer"
        FR[FilterRegistry]
        FEB[FilterEventBus]
        FF[Feature Flags]
    end
    
    subgraph "Core Layer"
        FS[FilterStore]
        FE[FilterEngine]
        SQL[SQL Functions]
    end
    
    subgraph "Component Layer"
        FC[FilterControl]
        FSC[FilterSection]
        SFS[SavedFilterSelector]
        FAB[FilterActionBar]
        FP[FilterPanel]
    end
    
    subgraph "Specialized Layer"
        PEF[PromptElementsFilter]
        RIF[RelatedItemsFilter]
        FRM[FilterRegistrationModule]
        FPL[FilterPlugin]
    end
    
    subgraph "Integration Layer"
        QB[QueryBuilder]
        CSA[ClientSideAdapter]
        FFC[FilterFormatConverter]
        AC[AdapterComponents]
        TB[TemplateBrowser]
        TR[TestRoute]
    end
    
    FR --> FS
    FR --> FE
    FEB --> FS
    
    FS --> FC
    FS --> FSC
    FS --> SFS
    FS --> FAB
    FS --> FP
    
    FC --> FSC
    FSC --> FP
    SFS --> FP
    FAB --> FP
    
    FR --> PEF
    FR --> RIF
    FR --> FRM
    FRM --> FPL
    
    SQL --> QB
    QB --> CSA
    FF --> AC
    FF --> TB
    FF --> TR
```

These visual diagrams provide a clear representation of the filter engine architecture, component relationships, data flow, and implementation dependencies. They complement the written documentation and help visualize the system design.
