# Introduction
Vue Ganttastic is a simple, interactive and highly customizable Gantt chart component for Vue 3.   

## Features
- **[Vue 3](https://v3.vuejs.org/) support**
- **[TypeScript](https://www.typescriptlang.org/) support** *(ships with out of the box type declarations)*
- **Interactivity** *(dynamic, movable and pushable bars)*
- **Reactivity / Responsiveness** (*when changes occur, bars are repositioned accordingly*)
- **Customization options** (*chart/bar styling, slots, event handlers etc.*)

## About
**License** [MIT](https://choosealicense.com/licenses/mit/)  
**Author**: <PERSON><PERSON>, BSc  
[GitHub Repository](https://github.com/InfectoOne/vue-ganttastic)

## Support the project!
In case you found the library useful, a little tip would be much appreciated!

<form action="https://www.paypal.com/donate" method="post" target="_top">
<input type="hidden" name="hosted_button_id" value="M63C8DAMV5YDJ" />
<input type="image" src="https://pics.paypal.com/00/s/MTdhMWZmNTUtOWQ1Yi00YmRjLWJjMjgtY2Y0NTNhODM0OTJl/file.PNG" border="0" name="submit" title="PayPal - The safer, easier way to pay online!" alt="Donate with PayPal button" style="max-width:200px"/>
<img alt="" border="0" src="https://www.paypal.com/en_AT/i/scr/pixel.gif" width="1" height="1" />
</form>

BTC address:   
![image](https://user-images.githubusercontent.com/28678851/233090745-a0a6d8a4-6df6-4b82-ac0c-90e69551786e.png)  


