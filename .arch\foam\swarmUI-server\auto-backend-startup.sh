#!/bin/bash
# SwarmUI Auto-Backend Startup Script
# This script automatically configures and starts Swarm<PERSON> with ComfyUI as a backend
# without requiring manual setup through the web interface

# Set environment variables
export DOTNET_ROOT=$HOME/.dotnet
export PATH=$PATH:$HOME/.dotnet:$HOME/.dotnet/tools
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128
export CUDA_LAUNCH_BLOCKING=0
export CUDA_VISIBLE_DEVICES=0

# Generate a secure API key
API_KEY=$(openssl rand -hex 16)
echo "Your SwarmUI API key is: $API_KEY"

# Create the SwarmUI config directory if it doesn't exist
mkdir -p ~/.swarmui

# Create the config.json file with ComfyUI backend pre-configured
cat > ~/.swarmui/config.json << EOL
{
  "BackendUrl": "http://0.0.0.0:7801",
  "FrontendUrl": "http://0.0.0.0:7800",
  "ModelRoot": "/workspace/models",
  "OutputsPath": "/workspace/outputs",
  "DisableUpdateCheck": true,
  "AutoInstall": {
    "Torch": false,
    "Comfy": false,
    "Models": false
  },
  "Auth": {
    "Enabled": true,
    "ApiKey": "${API_KEY}"
  },
  "ComfyUI": {
    "Path": "/workspace/ComfyUI",
    "Url": "http://127.0.0.1:7860",
    "EnableWorkflows": true,
    "ModelPaths": {
      "checkpoints": "/workspace/ComfyUI/models/checkpoints",
      "clip": "/workspace/models/clip",
      "vae": "/workspace/models/vae"
    }
  },
  "Backend": {
    "Type": "ComfyUI-SelfStarting",
    "ComfyUIPath": "/workspace/ComfyUI",
    "ComfyUIPort": 7860,
    "EnableWorkflows": true
  }
}
EOL

# Create models.json file if it doesn't exist
if [ ! -f ~/.swarmui/models.json ]; then
  echo "Creating models.json file..."
  cat > ~/.swarmui/models.json << EOL
{
  "checkpoints": [
    {
      "name": "Flux Schnell",
      "path": "/workspace/models/diffusion_models/FLUX1-Schnell-FP8.safetensors",
      "vae": "/workspace/models/vae/ae.safetensors",
      "clip": "/workspace/models/clip/t5xxl_enconly.safetensors",
      "config": "configs/stable-diffusion/sdxl/sdxl_1_0.yaml",
      "default": true
    }
  ]
}
EOL
fi

# Update the backend URL if RUNPOD_HTTP_HOST is set
if [ ! -z "$RUNPOD_HTTP_HOST" ]; then
  PUBLIC_URL="https://$RUNPOD_HTTP_HOST"
  sed -i "s|\"BackendUrl\": \".*\"|\"BackendUrl\": \"$PUBLIC_URL\"|" ~/.swarmui/config.json
  sed -i "s|\"FrontendUrl\": \".*\"|\"FrontendUrl\": \"$PUBLIC_URL\"|" ~/.swarmui/config.json
  echo "Updated SwarmUI URLs to use $PUBLIC_URL"
fi

# Start ComfyUI in the background
cd /workspace/ComfyUI
python -c "import torch; torch.backends.cuda.matmul.allow_tf32 = True; torch.backends.cudnn.allow_tf32 = True"
nohup python main.py --listen 0.0.0.0 --port 7860 > /workspace/comfyui.log 2>&1 &

# Wait for ComfyUI to start
echo "Starting ComfyUI and waiting for it to initialize..."
sleep 15

# Start SwarmUI with ComfyUI backend
cd /workspace/SwarmUI
./launch-linux.sh --launch_mode none --host 0.0.0.0 --comfy_path /workspace/ComfyUI

# Note: The --comfy_path parameter tells SwarmUI where to find ComfyUI
# This automatically configures ComfyUI as a backend without needing the web interface
