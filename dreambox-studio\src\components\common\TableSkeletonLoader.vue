<template>
  <div class="table-skeleton-loader" :class="{ 'dark-mode': $q.dark.isActive }">
    <!-- Header row -->
    <div class="skeleton-row header-row">
      <div
        v-for="(col, index) in columns"
        :key="index"
        class="skeleton-cell"
        :style="{ width: getColumnWidth(col) }"
      >
        <q-skeleton type="text" class="full-width" />
      </div>
    </div>

    <!-- Data rows -->
    <div
      v-for="n in rowCount"
      :key="n"
      class="skeleton-row"
    >
      <div
        v-for="(col, index) in columns"
        :key="index"
        class="skeleton-cell"
        :style="{ width: getColumnWidth(col) }"
      >
        <q-skeleton type="text" class="full-width" animation="wave" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';

// Get access to Quasar's dark mode
const $q = useQuasar();

// Props definition
defineProps({
  columns: {
    type: Array,
    required: true
  },
  rowCount: {
    type: Number,
    default: 10
  }
});

// Function to determine column width based on column definition
function getColumnWidth(col: unknown) {
  // Type guard to check if col has the expected structure
  const hasWidth = col !== null && typeof col === 'object' && 'width' in col;
  const hasName = col !== null && typeof col === 'object' && 'name' in col;

  // Handle width if it exists
  if (hasWidth && col.width !== undefined) {
    return typeof col.width === 'number' ? `${col.width}px` : col.width as string;
  }
  // Default widths based on column type
  if (hasName) {
    const name = col.name as string;
    switch (name) {
      case 'actions':
        return '80px';
      case 'role':
      case 'company':
        return '150px';
      case 'email':
        return '200px';
      default:
        return '120px';
    }
  }

  // Default fallback
  return '120px';
}
</script>

<style scoped>
.table-skeleton-loader {
  width: 100%;
  padding: 8px;
  background-color: var(--q-table-bgcolor, transparent);
  color: var(--q-primary-text-color);
}

.skeleton-row {
  display: flex;
  padding: 8px 16px;
  border-bottom: 1px solid var(--q-separator-color, rgba(0, 0, 0, 0.12));
}

.header-row {
  background-color: var(--q-table-header-bgcolor, rgba(0, 0, 0, 0.02));
  font-weight: 500;
}

:deep(.q-skeleton) {
  --q-skeleton-dark-color: var(--q-dark-page);
  --q-skeleton-light-color: var(--q-light-page);
}

/* Dark mode specific styles */
.dark-mode {
  background-color: var(--q-dark-page) !important;
  color: var(--q-dark-text) !important;
}

.dark-mode .header-row {
  background-color: rgba(255, 255, 255, 0.05) !important;
}

.dark-mode .skeleton-row {
  border-bottom-color: rgba(255, 255, 255, 0.1) !important;
}

.dark-mode :deep(.q-skeleton) {
  --q-skeleton-dark-color: rgba(255, 255, 255, 0.05) !important;
  --q-skeleton-light-color: rgba(255, 255, 255, 0.1) !important;
  opacity: 0.8;
}

.skeleton-cell {
  flex: 1;
  padding: 8px;
  min-width: 100px;
}

/* Mobile-specific styles */
@media (max-width: 599px) {
  .skeleton-row {
    padding: 4px 8px;
  }

  .skeleton-cell {
    padding: 4px;
    min-width: 60px;
  }
}
</style>
