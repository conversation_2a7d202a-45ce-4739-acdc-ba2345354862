<template>
  <div class="prompt-element-selector">
    <!-- Element Type Selector -->
    <div class="element-type-selector q-mb-md">
      <!--
        Using a standard select with manual rendering of options to have full control
      -->
      <q-select
        v-model="selectedTypeObjects"
        :options="elementTypes"
        option-label="name"
        option-value="id"
        :label="t('templates.selectElementTypes')"
        outlined
        dense
        multiple
        use-chips
        use-input
        hide-selected
        fill-input
        input-debounce="300"
        popup-content-class="custom-scrollbar"
        @filter="filterElementTypes"
        @update:model-value="onTypeObjectsChange"
      >
        <!-- Individual options -->
        <template v-slot:option="scope">
          <q-item v-bind="scope.itemProps">
            <q-item-section side>
              <q-checkbox
                v-model="scope.selected"
                @click.stop.prevent="scope.toggleOption(scope.opt)"
              />
            </q-item-section>
            <q-item-section>
              <q-item-label>
                {{ formatElementTypeName(scope.opt.name) }}
              </q-item-label>
              <q-item-label caption v-if="scope.opt.description">{{ scope.opt.description }}</q-item-label>
            </q-item-section>
          </q-item>
        </template>
        <template v-slot:selected-item="scope">
          <q-chip
            removable
            dense
            @remove="scope.removeAtIndex(scope.index)"
            :tabindex="scope.tabindex"
            color="primary"
            text-color="white"
          >
            {{ formatElementTypeName(scope.opt.name) }}
          </q-chip>
        </template>
        <template v-slot:no-option>
          <q-item>
            <q-item-section class="text-grey">
              {{ t('templates.noMatchingElementTypes') }}
            </q-item-section>
          </q-item>
        </template>
      </q-select>
    </div>

    <!-- Type-specific Value Selectors with Draggable Types -->
    <div v-if="selectedTypeObjects && selectedTypeObjects.length > 0" class="type-value-selectors q-mb-lg">
      <!-- Debug info -->
      <div class="text-caption q-mb-md">
        {{ t('templates.selectedTypes', { count: selectedTypeObjects.length }) }} | {{ t('templates.typesShown', { count: filteredOrderedTypes.length }) }}
        <q-btn flat dense size="xs" icon="refresh" @click="refreshData" class="q-ml-sm" />
      </div>

      <!-- Draggable wrapper for element types -->
      <draggable
        :list="filteredOrderedTypes"
        item-key="id"
        handle=".type-handle"
        @end="onTypeReorderEnd"
        class="responsive-grid"
      >
        <template #item="{ element: typeId }">
          <div class="type-value-selector q-mb-md fade-slide-item position-relative">
            <!-- Order Badge -->
            <q-badge class="order-badge" color="primary" rounded>
              {{ filteredOrderedTypes.indexOf(typeId) + 1 }}
            </q-badge>

            <!-- Type Header with Drag Handle and Type Name -->
            <div class="row items-center q-mb-sm">
              <!-- Drag Handle -->
              <div class="col-auto">
                <q-icon name="drag_indicator" class="type-handle cursor-move q-mr-sm" />
              </div>

              <!-- Type Name -->
              <div class="col text-subtitle1">
                {{ getTypeNameById(typeId) }}
              </div>

              <!-- Remove Button -->
              <div class="col-auto">
                <q-btn
                  flat
                  round
                  dense
                  icon="close"
                  @click="removeType(typeId)"
                />
              </div>
            </div>

            <!-- Value Selector for this Type -->
            <div class="row q-col-gutter-sm">
              <!-- Value Selector for this Type - Takes remaining space -->
              <div class="col-12 col-sm">
                <q-select
                  v-model="typeValuesMap[typeId]"
                  :options="getFilteredValuesForType(typeId)"
                  option-label="value"
                  option-value="id"
                  :label="t('templates.selectValues')"
                  outlined
                  dense
                  multiple
                  use-chips
                  use-input
                  hide-selected
                  fill-input
                  input-debounce="300"
                  popup-content-class="custom-scrollbar"
                  @filter="(val, update) => filterTypeValues(typeId, val, update)"
                  @update:model-value="(ids) => onTypeValuesChange(typeId, ids)"
                  use-checkbox
                  emit-value
                  map-options
                >
                  <!-- Individual options -->
                  <template v-slot:option="scope">
                    <q-item v-bind="scope.itemProps">
                      <q-item-section side>
                        <q-checkbox
                          v-model="scope.selected"
                          @click.stop.prevent="scope.toggleOption(scope.opt)"
                        />
                      </q-item-section>
                      <q-item-section>
                        <q-item-label>{{ scope.opt.value }}</q-item-label>
                        <q-item-label caption v-if="scope.opt.description">{{ scope.opt.description }}</q-item-label>
                      </q-item-section>
                    </q-item>
                  </template>
                  <template v-slot:no-option>
                    <q-item>
                      <q-item-section class="text-grey">
                        {{ t('templates.noMatchingValues') }}
                      </q-item-section>
                    </q-item>
                  </template>
                </q-select>
              </div>
            </div>

            <!-- Selected Values for this Type (without the label) -->
            <div v-if="typeValuesMap[typeId] && typeValuesMap[typeId].length > 0" class="selected-values q-mt-sm">
              <div class="selected-values-list">
                <div class="q-mt-xs">
                  <!-- Debug info -->
                  <div class="text-caption q-mb-xs">
                    {{ t('templates.selectedValues', { count: typeValuesMap[typeId].length }) }}
                  </div>

                  <!-- Draggable list of selected values -->
                  <draggable
                    v-model="typeValuesMap[typeId]"
                    item-key="id"
                    handle=".handle"
                    @end="(event) => onValueReorderEnd(event, typeId)"
                    class="draggable-container"
                    :data-type-id="typeId"
                  >
                    <template #item="{ element: valueId }">
                      <q-item dense class="q-py-xs selected-value-item fade-move-item">
                        <q-item-section avatar class="q-pr-none">
                          <q-icon
                            name="drag_indicator"
                            size="xs"
                            class="handle cursor-move"
                          />
                        </q-item-section>
                        <q-item-section>
                          <q-item-label>{{ getElementValueById(typeId, valueId) }}</q-item-label>
                        </q-item-section>
                        <q-item-section side>
                          <q-btn
                            flat
                            round
                            dense
                            icon="close"
                            size="xs"
                            @click="removeElementValue(typeId, valueId)"
                          />
                        </q-item-section>
                      </q-item>
                    </template>
                  </draggable>
                </div>
              </div>
            </div>
          </div>
        </template>
      </draggable>
    </div>

    <!-- Empty state message when no types are selected -->
    <div v-else class="text-center q-pa-md text-grey">
      {{ t('templates.selectElementTypesPrompt') }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { usePromptElementsService, type PromptElementType, type PromptElement } from 'src/services/promptElementsService';
import { useElementTypeOrderService } from 'src/services/elementTypeOrderService';
import { useElementCacheStore } from 'src/stores/elementCache';
import draggable from 'vuedraggable';
import { supabase } from 'src/boot/supabase';
import { notificationService } from 'src/services/notificationService';
import { useI18n } from 'vue-i18n';

// Extended interface for template elements that includes type_name
interface TemplateElement extends PromptElement {
  type_name?: string | undefined;
  order?: number | undefined;
  usage_id?: number | undefined;
  type_description?: string | null | undefined;
}

const props = defineProps({
  templateId: {
    type: [Number, String],
    required: true
  },
  // For new templates or when we want to manage elements without saving to DB yet
  initialElementTypes: {
    type: Array as () => number[],
    default: () => []
  },
  initialElementValues: {
    type: Array as () => number[],
    default: () => []
  },
  // If true, changes will be emitted but not saved to the database
  localMode: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits<{
  (e: 'update', elements: Array<PromptElement & { [key: string]: unknown }>): void;
}>();

// Services
const promptElementsService = usePromptElementsService();
const elementTypeOrderService = useElementTypeOrderService();
// We don't need authStore directly anymore since we're using elementCacheStore
// which already has access to the current user ID
const elementCacheStore = useElementCacheStore();

// Initialize i18n
const { t } = useI18n();

// Destructure only the methods we need to avoid unused variable warnings
const {
  getTemplateElementsWithTypes
} = promptElementsService;

// State
const elementTypes = ref<PromptElementType[]>([]);
const filteredElementTypes = ref<PromptElementType[]>([]);
const selectedTypes = ref<number[]>([]);
const selectedTypeObjects = ref<PromptElementType[]>([]);
const orderedSelectedTypes = ref<number[]>([]);

// Computed property to get all selected types, regardless of whether they have values
const filteredOrderedTypes = computed(() => {
  // console.log('Computing filteredOrderedTypes');
  // console.log('- orderedSelectedTypes:', orderedSelectedTypes.value);
  // console.log('- selectedTypes:', selectedTypes.value);
  // console.log('- typeValuesMap:', typeValuesMap.value);

  // Filter orderedSelectedTypes to only include types that are selected
  const filtered = orderedSelectedTypes.value.filter(typeId => {
    const isSelected = selectedTypes.value.includes(typeId);

    // Show all selected types, even if they don't have values yet
    return isSelected;
  });

  // console.log('- filtered selected types:', filtered);

  // If there are selected types that aren't in orderedSelectedTypes, add them to the end
  const missingTypes = selectedTypes.value.filter(typeId => {
    const isInOrdered = orderedSelectedTypes.value.includes(typeId);
    return !isInOrdered;
  });

  // console.log('- missing selected types:', missingTypes);

  // Return the combined array
  const result = [...filtered, ...missingTypes];
  // console.log('- final result (all selected types):', result);
  return result;
});
const typeValuesMap = ref<Record<number, number[]>>({});
const typeElementsMap = ref<Record<number, TemplateElement[]>>({});
const elementValuesByType = ref<Record<number, PromptElement[]>>({});
const filteredElementValuesByType = ref<Record<number, PromptElement[]>>({});
const selectedElements = ref<TemplateElement[]>([]);
const loading = ref(false);
const isInitialized = ref(false);

// Computed
const validTemplateId = computed(() => {
  if (props.templateId === 'new') return null;
  const id = Number(props.templateId);
  return isNaN(id) ? null : id;
});

// Methods

async function loadElementValuesForType(typeId: number) {
  try {
    // console.log(`Loading element values for type ${typeId}`);

    // Use the cache store to get values
    const values = await elementCacheStore.loadElementValuesForType(typeId);
    // console.log(`Loaded ${values.length} values for type ${typeId} from cache`);

    // Ensure all values have valid IDs
    const validValues = values.filter(value => {
      if (!value.id || isNaN(Number(value.id))) {
        console.warn(`Invalid element value ID: ${value.id}, type: ${typeof value.id}`, value);
        return false;
      }
      return true;
    });

    // Update the refs
    elementValuesByType.value[typeId] = validValues;
    filteredElementValuesByType.value[typeId] = [...validValues];

    return validValues;
  } catch (err) {
    console.error(`Error loading element values for type ${typeId}:`, err);
    return [];
  }
}

async function loadTemplateElements() {
  if (!validTemplateId.value) return;

  try {
    loading.value = true;
    // console.log(`Loading template elements for template ID ${validTemplateId.value}`);

    // Use our optimized method to get template elements with types in a single query
    const { types } = await getTemplateElementsWithTypes(validTemplateId.value);
    // console.log('Loaded template elements with types:', types.length);

    // Reset state
    selectedTypes.value = [];
    selectedTypeObjects.value = [];
    orderedSelectedTypes.value = [];
    typeValuesMap.value = {};
    typeElementsMap.value = {};

    // Process the types and elements
    if (types && types.length > 0) {
      // Extract type IDs in order, but only for types that have elements
      const typesWithElements = types.filter(type => type.elements && type.elements.length > 0);
      const typeIds = typesWithElements.map(type => type.id);
      // console.log('Type IDs with elements in order:', typeIds);

      // Update selectedTypes and orderedSelectedTypes with only types that have elements
      selectedTypes.value = [...typeIds];
      orderedSelectedTypes.value = [...typeIds];

      // Process each type and its elements
      types.forEach(type => {
        // Initialize typeValuesMap for this type
        typeValuesMap.value[type.id] = [];

        // Initialize typeElementsMap for this type
        typeElementsMap.value[type.id] = [];

        // Process elements for this type
        if (type.elements && type.elements.length > 0) {
          // Add element IDs to typeValuesMap
          typeValuesMap.value[type.id] = type.elements.map(element => element.id);

          // Add elements to typeElementsMap
          typeElementsMap.value[type.id] = type.elements.map(element => ({
            id: element.id,
            type_id: type.id,
            value: element.value,
            description: element.description,
            type_name: type.name,
            order: element.order
          } as TemplateElement));
        }
      });
    }

    // Load all element types for the dropdown using the cache
    try {
      // Loading element types from cache
      const allTypes = await elementCacheStore.loadElementTypes();

      // Store all types for reference
      elementTypes.value = allTypes;

      // Now that we have all types loaded, we can set selectedTypeObjects
      // Only include types that have elements
      selectedTypeObjects.value = allTypes.filter(type =>
        selectedTypes.value.includes(type.id)
      );

      // Set filtered element types for dropdown
      filteredElementTypes.value = [...allTypes];
    } catch (err) {
      console.error('Error loading element types:', err);
    }

    // Load values for all selected types at once using the cache
    if (selectedTypes.value.length > 0) {
      // Loading values for all selected types at once
      const elementsByType = await elementCacheStore.loadElementsForTypes(selectedTypes.value);

      // Update our local state with the loaded values
      for (const [typeId, values] of Object.entries(elementsByType)) {
        const numericTypeId = Number(typeId);
        elementValuesByType.value[numericTypeId] = values;
        filteredElementValuesByType.value[numericTypeId] = [...values];
      }
    }

    // Update selectedElements
    updateSelectedElements();
  } catch (err) {
    console.error('Error loading template elements:', err);
  } finally {
    loading.value = false;
  }
}

function filterElementTypes(val: string, update: (callback: () => void) => void) {
  if (val === '') {
    update(() => {
      // Show all types in the dropdown
      filteredElementTypes.value = [...elementTypes.value];
      // Filtered element types updated for empty search
    });
    return;
  }

  update(() => {
    const needle = val.toLowerCase();
    // Filter by search term only
    filteredElementTypes.value = elementTypes.value.filter(
      type => (
        // Match search term
        type.name.toLowerCase().indexOf(needle) > -1 ||
        (type.description && type.description.toLowerCase().indexOf(needle) > -1)
      )
    );
    // Filtered element types updated for search term
  });
}

function filterTypeValues(typeId: number, val: string, update: (callback: () => void) => void) {
  if (val === '') {
    update(() => {
      filteredElementValuesByType.value[typeId] = [...(elementValuesByType.value[typeId] || [])];
    });
    return;
  }

  update(() => {
    const needle = val.toLowerCase();
    filteredElementValuesByType.value[typeId] = (elementValuesByType.value[typeId] || []).filter(
      element => element.value.toLowerCase().indexOf(needle) > -1 ||
                (element.description && element.description.toLowerCase().indexOf(needle) > -1)
    );
  });
}

// Keep track of which types we're currently loading to prevent infinite loops
const loadingTypes = ref<Record<number, boolean>>({});

function getFilteredValuesForType(typeId: number) {
  // If we don't have values for this type yet, try to load them
  if (!elementValuesByType.value[typeId] && !loadingTypes.value[typeId]) {
    // console.log(`No values found for type ${typeId}, loading them now...`);

    // Mark this type as currently loading
    loadingTypes.value[typeId] = true;

    loadElementValuesForType(typeId)
      .then(() => {
        // Values loaded for type
        // Mark as no longer loading
        loadingTypes.value[typeId] = false;
      })
      .catch(error => {
        console.error(`Error loading values for type ${typeId}:`, error);
        // Mark as no longer loading even if there was an error
        loadingTypes.value[typeId] = false;
      });
    return [];
  }

  return filteredElementValuesByType.value[typeId] || [];
}

function getTypeNameById(typeId: number) {
  const type = elementTypes.value.find(t => t.id === typeId);
  if (type && type.name) {
    return formatElementTypeName(type.name);
  }

  // If we can't find the type or it doesn't have a name, try to fetch it
  promptElementsService.getPromptElementTypes()
    .then(types => {
      const foundType = types.find(t => t.id === typeId);
      if (foundType && foundType.name) {
        // Update the elementTypes ref with the new data
        const existingTypeIndex = elementTypes.value.findIndex(t => t.id === typeId);
        if (existingTypeIndex >= 0) {
          elementTypes.value[existingTypeIndex] = foundType;
        } else {
          elementTypes.value.push(foundType);
        }
      }
    })
    .catch(error => {
      console.error(`Error fetching type name for ID ${typeId}:`, error);
    });

  return `Type ${typeId}`;
}

function getElementValueById(typeId: number, valueId: number) {
  // First try to find the element in typeElementsMap
  if (typeElementsMap.value[typeId]) {
    const element = typeElementsMap.value[typeId].find(el => el.id === valueId);
    if (element) {
      return element.value;
    }
  }

  // If not found in typeElementsMap, try to find it in elementValuesByType
  if (elementValuesByType.value[typeId]) {
    const element = elementValuesByType.value[typeId].find(el => el.id === valueId);
    if (element) {
      return element.value;
    }
  }

  // If still not found, return a placeholder
  return `Value ${valueId}`;
}

function formatElementTypeName(name: string) {
  return name
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

function onTypeObjectsChange(newTypeObjects: PromptElementType[]) {
  // console.log('Type objects selection changed:', newTypeObjects);

  // Extract the type IDs from the selected objects
  const newTypeIds = newTypeObjects.map(type => type.id);

  // Call the original onTypesChange function with the extracted IDs
  void onTypesChange(newTypeIds);
}

async function onTypesChange(newTypeIds: number[]) {
  // console.log('Type selection changed:', newTypeIds);

  // Find types that were added
  const addedTypeIds = newTypeIds.filter(id => !selectedTypes.value.includes(id));

  // Find types that were removed
  const removedTypeIds = selectedTypes.value.filter(id => !newTypeIds.includes(id));

  // console.log('Added types:', addedTypeIds);
  // console.log('Removed types:', removedTypeIds);

  // Update selectedTypes immediately to reflect the change in the UI
  selectedTypes.value = newTypeIds;

  // Update selectedTypeObjects to match selectedTypes
  selectedTypeObjects.value = elementTypes.value.filter(type =>
    selectedTypes.value.includes(type.id)
  );

  // Update filteredElementTypes to show all types
  filteredElementTypes.value = [...elementTypes.value];
  // console.log('Updated filteredElementTypes after type selection change:', filteredElementTypes.value);

  // Always keep orderedSelectedTypes in sync with selectedTypes
  // This ensures that all selected types are ordered

  // First, preserve the order of existing types
  const existingOrderedTypes = orderedSelectedTypes.value.filter(typeId =>
    newTypeIds.includes(typeId)
  );

  // Then add any new types that aren't already ordered
  const newTypesToAdd = newTypeIds.filter(typeId =>
    !existingOrderedTypes.includes(typeId)
  );

  // Combine them to create the new ordered list
  orderedSelectedTypes.value = [...existingOrderedTypes, ...newTypesToAdd];

  // console.log('Updated orderedSelectedTypes:', orderedSelectedTypes.value);

  // Load values for newly added types all at once
  if (addedTypeIds.length > 0) {
    // console.log('Loading values for newly added types:', addedTypeIds);

    // Initialize maps for all added types
    for (const typeId of addedTypeIds) {
      // Initialize typeValuesMap for this type if not already done
      if (!typeValuesMap.value[typeId]) {
        typeValuesMap.value[typeId] = [];
      }

      // Initialize typeElementsMap for this type if not already done
      if (!typeElementsMap.value[typeId]) {
        typeElementsMap.value[typeId] = [];
      }

      // If we have a valid template ID and not in local mode, add the type to the template
      // This is just to register that the type is used, even if no values are selected yet
      if (validTemplateId.value && !props.localMode) {
        try {
          // We need to register the type in the database by adding it to the type order
          // console.log(`Registering type ${typeId} for template ${validTemplateId.value}`);

          // Get the current highest order index for this template
          const orderedTypes = await elementTypeOrderService.getOrderedElementTypes(validTemplateId.value);
          // console.log('Current ordered types:', orderedTypes);

          // Calculate the next order index
          const nextOrderIndex = orderedTypes.length;
          // console.log('Next order index:', nextOrderIndex);

          // Create the order data for this type
          const orderData = [{
            elementTypeId: typeId,
            order: nextOrderIndex
          }];

          // console.log('Order data for new type:', orderData);

          // Save the type order to register the type
          const result = await elementTypeOrderService.reorderElementTypes(
            validTemplateId.value,
            orderData
          );

          // console.log('Type registration result:', result);

          if (result) {
            // Show success notification for adding the type
            await notificationService.notify({
              type: 'positive',
              message: `Added ${getTypeNameById(typeId)} type`,
              position: 'bottom-right',
              timeout: 2000
            });
          } else {
            throw new Error('Failed to register type in database');
          }
        } catch (err) {
          console.error(`Error registering type ${typeId} for template ${validTemplateId.value}:`, err);

          // Show error notification
          await notificationService.notify({
            type: 'negative',
            message: `Failed to add ${getTypeNameById(typeId)} type`,
            position: 'bottom-right',
            timeout: 3000
          });
        }
      }
    }

    // Load values for all added types at once using the cache
    const elementsByType = await elementCacheStore.loadElementsForTypes(addedTypeIds);

    // Update our local state with the loaded values
    for (const [typeId, values] of Object.entries(elementsByType)) {
      const numericTypeId = Number(typeId);
      elementValuesByType.value[numericTypeId] = values;
      filteredElementValuesByType.value[numericTypeId] = [...values];
    }

    // console.log('Loaded values for newly added types:', elementValuesByType.value);
  }

  // Remove values for removed types
  for (const typeId of removedTypeIds) {
    // If we have a valid template ID and not in local mode, remove all elements of this type from database
    if (validTemplateId.value && !props.localMode && typeElementsMap.value[typeId]) {
      for (const element of typeElementsMap.value[typeId]) {
        try {
          // console.log(`Removing element ${element.id} from template ${validTemplateId.value}`);
          const success = await promptElementsService.removeElementFromTemplate(validTemplateId.value, element.id);
          console.log(`Element removal result:`, success);
        } catch (error) {
          console.error(`Error removing element ${element.id} from template ${validTemplateId.value}:`, error);
        }
      }
    }

    delete typeValuesMap.value[typeId];
    delete typeElementsMap.value[typeId];
  }

  // Update selectedElements
  updateSelectedElements();

  // Save type order if we have a valid template ID
  if (validTemplateId.value && !props.localMode) {
    try {
      // Save the complete type order with sequential indices starting from 0
      const orderData = orderedSelectedTypes.value.map((typeId, index) => ({
        elementTypeId: typeId,
        order: index
      }));

      // console.log('Saving complete type order:', orderData);

      await elementTypeOrderService.reorderElementTypes(
        validTemplateId.value,
        orderData
      );

      // Type order saved
    } catch (error) {
      console.error('Error saving complete type order:', error);
    }
  }

  // Emit update event
  emit('update', selectedElements.value);
}

async function onTypeValuesChange(typeId: number, valueIds: number[]) {
  // Find values that were added
  const currentValues = typeValuesMap.value[typeId] || [];
  const addedValueIds = valueIds.filter(id => !currentValues.includes(id));

  // Find values that were removed
  const removedValueIds = currentValues.filter(id => !valueIds.includes(id));

  // console.log('Type values changed for type ID:', typeId);
  // console.log('- Current values:', currentValues);
  // console.log('- New values:', valueIds);
  // console.log('- Added values:', addedValueIds);
  // console.log('- Removed values:', removedValueIds);

  // Check the current state of the database
  if (validTemplateId.value) {
    // console.log(`Checking current database state for template ${validTemplateId.value}`);

    // We used to check prompt_elements_usage, but it's not needed anymore
    try {
      await supabase
        .from('prompt_elements_usage')
        .select('*')
        .eq('template_id', validTemplateId.value);

      // Current prompt_elements_usage data fetched
    } catch (err) {
      console.error('Error checking prompt_elements_usage:', err);
    }

    // We used to check element type order, but it's not needed anymore
    try {
      await elementTypeOrderService.getOrderedElementTypes(validTemplateId.value);
      // Current ordered element types fetched
    } catch (err) {
      console.error('Error checking element type order:', err);
    }
  }

  // We'll update typeValuesMap after processing added and removed values

  // Log the current state before making changes
  // console.log('Before adding values:');
  // console.log('- typeValuesMap[typeId]:', typeValuesMap.value[typeId]);
  // console.log('- typeElementsMap[typeId]:', typeElementsMap.value[typeId]);

  // Add new values to typeElementsMap
  for (const valueId of addedValueIds) {
    // console.log(`Adding value ID ${valueId} to type ID ${typeId}`);

    const element = elementValuesByType.value[typeId]?.find(el => el.id === valueId);
    if (element) {
      // Initialize typeElementsMap for this type if not already done
      if (!typeElementsMap.value[typeId]) {
        typeElementsMap.value[typeId] = [];
      }

      // Add the element with type_name
      const typeName = elementTypes.value.find(t => t.id === typeId)?.name;
      const elementWithTypeName = {
        ...element,
        type_name: typeName
      } as TemplateElement;

      // console.log('Adding element to typeElementsMap:', elementWithTypeName);

      // Add the element to typeElementsMap
      typeElementsMap.value[typeId].push(elementWithTypeName);

      // If we have a valid template ID and not in local mode, add to database
      if (validTemplateId.value && !props.localMode) {
        try {
          // console.log(`Adding element ${valueId} to template ${validTemplateId.value}`);

          // First, ensure the type is registered in the database
          // This is important for new types that might not have been saved yet
          const orderedTypes = await elementTypeOrderService.getOrderedElementTypes(validTemplateId.value);
          const typeExists = orderedTypes.some(t => t.element_type_id === typeId);

          if (!typeExists) {
            // console.log(`Type ${typeId} not found in database, registering it first`);

            // Calculate the next order index
            const nextOrderIndex = orderedTypes.length;

            // Create the order data for this type
            const orderData = [{
              elementTypeId: typeId,
              order: nextOrderIndex
            }];

            // Save the type order to register the type
            const typeResult = await elementTypeOrderService.reorderElementTypes(
              validTemplateId.value,
              orderData
            );

            // console.log('Type registration result:', typeResult);

            if (!typeResult) {
              throw new Error(`Failed to register type ${typeId} in database`);
            }
          }

          // Now add the element to the template
          // console.log(`ADDING ELEMENT: Template ID: ${validTemplateId.value}, Element ID: ${valueId}, Type: ${typeId}`);

          // Generate a random order value between 1 and 1000 to avoid conflicts
          const randomOrder = Math.floor(Math.random() * 1000) + 1;
          // console.log(`Using random order value: ${randomOrder}`);

          try {
            // console.log(`Adding element ${valueId} to template ${validTemplateId.value}`);

            // We used to get element details for logging, but it's not needed anymore

            // We used to check if the element already exists in the database, but it's not needed anymore

            // Call the service function to add the element
            // console.log(`Calling addElementToTemplate with params:`, {
            //   templateId: validTemplateId.value,
            //   elementId: valueId,
            //   order: randomOrder
            // });

            const success = await promptElementsService.addElementToTemplate(validTemplateId.value, valueId, randomOrder);
            // console.log(`Element ${valueId} added to template ${validTemplateId.value}:`, success);

            if (!success) {
              throw new Error(`Failed to add element ${valueId} to template ${validTemplateId.value}`);
            }

            // Verify the element was added by checking the database
            const { data } = await supabase
              .from('prompt_elements_usage')
              .select('*')
              .eq('template_id', validTemplateId.value)
              .eq('element_id', valueId);

            // Database verification completed

            // Check if we need to try a direct insert
            if (!data || data.length === 0) {
              // Element not found, trying direct insert

              await supabase
                .from('prompt_elements_usage')
                .insert({
                  template_id: validTemplateId.value,
                  element_id: valueId,
                  order: randomOrder
                });

              // Direct insert completed
            }
          } catch (error) {
            console.error('Error in addElementToTemplate:', error);

            // Show error notification
            await notificationService.notify({
              type: 'negative',
              message: 'Error adding element to template',
              position: 'bottom-right',
              timeout: 3000
            });

            return; // Exit early on error
          }

          // Show success notification
          await notificationService.notify({
            type: 'positive',
            message: 'Element added to template',
            position: 'bottom-right',
            timeout: 2000
          });
        } catch (error) {
          console.error(`Error adding element ${valueId} to template ${validTemplateId.value}:`, error);
          // Show error notification
          await notificationService.notify({
            type: 'negative',
            message: 'Error adding element to template',
            position: 'bottom-right',
            timeout: 3000
          });
        }
      }
    }
  }

  // Remove values from typeElementsMap
  for (const valueId of removedValueIds) {
    if (typeElementsMap.value[typeId]) {
      const index = typeElementsMap.value[typeId].findIndex(el => el.id === valueId);
      if (index !== -1) {
        typeElementsMap.value[typeId].splice(index, 1);

        // If we have a valid template ID and not in local mode, remove from database
        if (validTemplateId.value && !props.localMode) {
          try {
            // console.log(`Removing element ${valueId} from template ${validTemplateId.value}`);
            const success = await promptElementsService.removeElementFromTemplate(validTemplateId.value, valueId);
            // console.log(`Element ${valueId} removed from template ${validTemplateId.value}:`, success);

            if (!success) {
              console.error(`Failed to remove element ${valueId} from template ${validTemplateId.value}`);
              // Show error notification
              await notificationService.notify({
                type: 'negative',
                message: 'Failed to remove element from template',
                position: 'bottom-right',
                timeout: 3000
              });
            } else {
              // Show success notification
              await notificationService.notify({
                type: 'positive',
                message: 'Element removed from template',
                position: 'bottom-right',
                timeout: 2000
              });
            }
          } catch (error) {
            console.error(`Error removing element ${valueId} from template ${validTemplateId.value}:`, error);
            // Show error notification
            await notificationService.notify({
              type: 'negative',
              message: 'Error removing element from template',
              position: 'bottom-right',
              timeout: 3000
            });
          }
        }
      }
    }
  }

  // Now update typeValuesMap with the new values
  typeValuesMap.value[typeId] = valueIds;

  // Force save all values to the database
  if (validTemplateId.value && !props.localMode) {
    try {
      // console.log('Force saving all values to database');

      // Clear existing values first
      const { error: deleteErr } = await supabase
        .from('prompt_elements_usage')
        .delete()
        .eq('template_id', validTemplateId.value)
        .in('element_id', valueIds);

      if (deleteErr) {
        console.error('Error clearing existing values:', deleteErr);
      }

      // Add all values
      for (let i = 0; i < valueIds.length; i++) {
        const valueId = valueIds[i];
        // Use the index as the order to maintain the order from the UI
        const orderIndex = i;

        // Ensure we have valid IDs
        if (!validTemplateId.value || valueId === undefined) {
          console.error('Invalid template ID or value ID', { templateId: validTemplateId.value, valueId });
          continue;
        }

        // console.log(`Force adding element ${valueId} to template ${validTemplateId.value} with order ${orderIndex}`);

        await supabase
          .from('prompt_elements_usage')
          .insert({
            template_id: validTemplateId.value,
            element_id: valueId,
            order: orderIndex
          });

        // Force insert completed

        // Also save to prompt_element_value_order table to maintain order
        // console.log(`Saving element ${valueId} order to prompt_element_value_order table`);

        // First check if the entry already exists
        const { data: existingOrderData } = await supabase
          .from('prompt_element_value_order')
          .select('*')
          .eq('template_id', validTemplateId.value)
          .eq('element_type_id', typeId)
          .eq('element_id', valueId);

        // Existing order data checked

        if (existingOrderData && existingOrderData.length > 0) {
          // Update existing entry
          await supabase
            .from('prompt_element_value_order')
            .update({ order_index: orderIndex })
            .eq('template_id', validTemplateId.value)
            .eq('element_type_id', typeId)
            .eq('element_id', valueId);

          // Update order completed
        } else {
          // Insert new entry
          await supabase
            .from('prompt_element_value_order')
            .insert({
              template_id: validTemplateId.value,
              element_type_id: typeId,
              element_id: valueId,
              order_index: orderIndex
            });

          // Insert order completed
        }
      }

      // Verify the database state
      const { data: verifyData, error: verifyErr } = await supabase
        .from('prompt_elements_usage')
        .select('*')
        .eq('template_id', validTemplateId.value);

      console.log('Database verification after force save:', verifyData, verifyErr);

      // Verify the order state
      const { data: verifyOrderData, error: verifyOrderErr } = await supabase
        .from('prompt_element_value_order')
        .select('*')
        .eq('template_id', validTemplateId.value)
        .eq('element_type_id', typeId)
        .order('order_index');

      console.log('Order verification after force save:', verifyOrderData, verifyOrderErr);
    } catch (err) {
      console.error('Error force saving values to database:', err);
    }
  }

  // Force a reactive update to typeElementsMap
  typeElementsMap.value = { ...typeElementsMap.value };

  // Log the state after making changes
  // console.log('After adding/removing values:');
  // console.log('- typeValuesMap[typeId]:', typeValuesMap.value[typeId]);
  // console.log('- typeElementsMap[typeId]:', typeElementsMap.value[typeId]);

  // Update selectedElements
  updateSelectedElements();

  // Log the updated selectedElements
  // console.log('Updated selectedElements:', selectedElements.value);

  // Emit update event
  emit('update', selectedElements.value);
}

function updateSelectedElements() {
  // Combine all elements from all types
  selectedElements.value = Object.values(typeElementsMap.value).flat();
}

async function removeType(typeId: number) {
  // console.log(`Removing type ${typeId}`);

  // Remove from selectedTypes
  const index = selectedTypes.value.indexOf(typeId);
  if (index !== -1) {
    // Create a new array to trigger reactivity
    const newSelectedTypes = [...selectedTypes.value];
    newSelectedTypes.splice(index, 1);

    // This will trigger the onTypesChange function which handles all the cleanup
    await onTypesChange(newSelectedTypes);

    // Show notification
    await notificationService.notify({
      type: 'positive',
      message: `Removed ${getTypeNameById(typeId)} type`,
      position: 'bottom-right',
      timeout: 2000
    });
  }
}

async function removeElementValue(typeId: number, elementId: number) {
  // Remove from typeValuesMap
  const valueIndex = typeValuesMap.value[typeId]?.indexOf(elementId);
  if (valueIndex !== undefined && valueIndex !== -1 && typeValuesMap.value[typeId]) {
    typeValuesMap.value[typeId].splice(valueIndex, 1);
  }

  // Remove from typeElementsMap
  const elementIndex = typeElementsMap.value[typeId]?.findIndex(el => el.id === elementId);
  if (elementIndex !== undefined && elementIndex !== -1 && typeElementsMap.value[typeId]) {
    typeElementsMap.value[typeId].splice(elementIndex, 1);

    // If we have a valid template ID and not in local mode, remove from database
    if (validTemplateId.value && !props.localMode) {
      // Remove from prompt_elements_usage
      await promptElementsService.removeElementFromTemplate(validTemplateId.value, elementId);

      // Also remove from prompt_element_value_order
      try {
        // Ensure we have valid IDs
        if (!validTemplateId.value || typeId === undefined || elementId === undefined) {
          console.error('Invalid IDs for removing from prompt_element_value_order', {
            templateId: validTemplateId.value,
            typeId,
            elementId
          });
          return;
        }

        // console.log(`Removing element ${elementId} from prompt_element_value_order`);
        const { error } = await supabase
          .from('prompt_element_value_order')
          .delete()
          .eq('template_id', validTemplateId.value)
          .eq('element_type_id', typeId)
          .eq('element_id', elementId);

        if (error) {
          console.error('Error removing from prompt_element_value_order:', error);
        } else {
          console.log('Successfully removed from prompt_element_value_order');
        }
      } catch (err) {
        console.error('Exception removing from prompt_element_value_order:', err);
      }
    }
  }
}

// Function to refresh the data
async function refreshData() {
  // console.log('Refreshing data...');

  // Show loading indicator
  loading.value = true;

  try {
    // Clear existing data
    selectedTypes.value = [];
    selectedTypeObjects.value = [];
    orderedSelectedTypes.value = [];
    typeValuesMap.value = {};
    typeElementsMap.value = {};
    elementValuesByType.value = {};
    filteredElementValuesByType.value = {};

    // Reload data from database
    if (validTemplateId.value) {
      await loadTemplateElements();

      // Show success notification
      await notificationService.notify({
        type: 'positive',
        message: 'Data refreshed successfully',
        position: 'bottom-right',
        timeout: 2000
      });
    }
  } catch (err) {
    console.error('Error refreshing data:', err);

    // Show error notification
    await notificationService.notify({
      type: 'negative',
      message: 'Failed to refresh data',
      position: 'bottom-right',
      timeout: 3000
    });
  } finally {
    loading.value = false;
  }
}

async function onTypeReorderEnd() {
  // console.log('Type reorder end, filteredOrderedTypes:', filteredOrderedTypes.value);

  // Since we're using :list instead of v-model, we need to manually update orderedSelectedTypes
  // First, get the current order from filteredOrderedTypes
  const reorderedTypes = [...filteredOrderedTypes.value];
  // console.log('Reordered types:', reorderedTypes);

  // We need to preserve the non-selected types in orderedSelectedTypes
  const nonSelectedTypes = orderedSelectedTypes.value.filter(typeId => !selectedTypes.value.includes(typeId));

  // Create a new array to trigger reactivity
  const newOrderedSelectedTypes = [...reorderedTypes, ...nonSelectedTypes];
  // console.log('New ordered selected types:', newOrderedSelectedTypes);

  // Update the ref
  orderedSelectedTypes.value = newOrderedSelectedTypes;

  // console.log('Updated orderedSelectedTypes:', orderedSelectedTypes.value);

  // Save the new order if we have a valid template ID
  if (validTemplateId.value && !props.localMode) {
    try {
      // Call saveTypeOrder directly with the new order
      // console.log('Saving type order for template ID:', validTemplateId.value);
      // console.log('Ordered types:', reorderedTypes);

      // Make sure we use sequential indices starting from 0
      const orderData = reorderedTypes.map((typeId, index) => ({
        elementTypeId: typeId,
        order: index
      }));

      // console.log('Order data with sequential indices:', orderData);

      // Call the service directly to ensure it's working
      const result = await elementTypeOrderService.reorderElementTypes(
        validTemplateId.value,
        orderData
      );

      // console.log('Type order saved result:', result);

      if (result) {
        // Show success notification
        await notificationService.notify({
          type: 'positive',
          message: 'Type order saved successfully',
          position: 'bottom-right',
          timeout: 2000
        });

        // Reload the ordered types from the database to ensure UI is in sync
        try {
          const orderedTypes = await elementTypeOrderService.getOrderedElementTypes(validTemplateId.value);
          // console.log('Loaded ordered types from database:', orderedTypes);

          if (orderedTypes && orderedTypes.length > 0) {
            // Extract just the type IDs in order
            const dbOrderedTypeIds = orderedTypes.map(ot => ot.element_type_id);
            // console.log('Database ordered type IDs:', dbOrderedTypeIds);

            // Update orderedSelectedTypes with the database order
            // but keep any types that aren't in the database
            const typesNotInDb = orderedSelectedTypes.value.filter(
              typeId => !dbOrderedTypeIds.includes(typeId)
            );

            // Combine the database order with any types not in the database
            orderedSelectedTypes.value = [...dbOrderedTypeIds, ...typesNotInDb];
            // console.log('Final orderedSelectedTypes after DB sync:', orderedSelectedTypes.value);
          }
        } catch (loadError) {
          console.error('Error loading ordered types from database:', loadError);
        }
      } else {
        // Show error notification
        await notificationService.notify({
          type: 'negative',
          message: 'Failed to save type order',
          position: 'bottom-right',
          timeout: 3000
        });
      }
    } catch (error) {
      console.error('Error in onTypeReorderEnd:', error);

      // Show error notification
      await notificationService.notify({
        type: 'negative',
        message: 'Error saving type order',
        position: 'bottom-right',
        timeout: 3000
      });
    }
  }

  // Emit update event
  emit('update', selectedElements.value);
}

// Note: The saveTypeOrder function has been removed as it's no longer used.
// Type order saving is now handled directly in onTypeReorderEnd and onTypesChange functions.

async function onValueReorderEnd(_event: { to?: { dataset?: { typeId?: string } } }, typeId: number) {
  // console.log('Value reorder end for type ID:', typeId);
  // console.log('New order:', typeValuesMap.value[typeId]);

  // If we have a valid template ID and not in local mode, save the new order
  if (validTemplateId.value && !props.localMode) {
    try {
      // Get the element IDs in the new order
      const elementIds = typeValuesMap.value[typeId];

      if (elementIds && elementIds.length > 0) {
        console.log('Saving element orders to prompt_element_value_order table');

        // First, delete all existing orders for this template and type
        const { error: deleteErr } = await supabase
          .from('prompt_element_value_order')
          .delete()
          .eq('template_id', validTemplateId.value)
          .eq('element_type_id', typeId);

        if (deleteErr) {
          console.error('Error deleting existing orders:', deleteErr);
          throw deleteErr;
        }

        // Ensure we have a valid template ID
        if (!validTemplateId.value) {
          throw new Error('Invalid template ID');
        }

        // Now insert new orders
        const orderData = elementIds.map((id, index) => {
          // Ensure we have valid IDs
          if (id === undefined) {
            console.error('Invalid element ID', id);
            return null;
          }

          return {
            template_id: validTemplateId.value,
            element_type_id: typeId,
            element_id: id,
            order_index: index
          };
        }).filter(item => item !== null) as {
          template_id: number;
          element_type_id: number;
          element_id: number;
          order_index: number;
        }[];

        console.log('Inserting new orders:', orderData);

        const { data, error } = await supabase
          .from('prompt_element_value_order')
          .insert(orderData)
          .select();

        if (error) {
          console.error('Error inserting new orders:', error);
          throw error;
        }

        console.log('New orders inserted:', data);

        // Also update the order in prompt_elements_usage for compatibility
        // First, delete all existing entries for these elements
        console.log('Deleting existing prompt_elements_usage entries for reordering');

        // Filter out any undefined element IDs
        const validElementIds = elementIds.filter(id => id !== undefined);

        if (validElementIds.length > 0 && validTemplateId.value) {
          const { error: deleteUsageErr } = await supabase
            .from('prompt_elements_usage')
            .delete()
            .eq('template_id', validTemplateId.value)
            .in('element_id', validElementIds);

          if (deleteUsageErr) {
            console.error('Error deleting existing usage entries:', deleteUsageErr);
            // Continue anyway to try the insert
          }

          // Now insert all elements with their new orders
          // Ensure we have a valid template ID
          if (!validTemplateId.value) {
            throw new Error('Invalid template ID');
          }

          const usageData = validElementIds.map((id, index) => ({
            template_id: validTemplateId.value,
            element_id: id,
            order: index
          })) as {
            template_id: number;
            element_id: number;
            order: number;
          }[];

          console.log('Inserting new prompt_elements_usage entries:', usageData);

          const { data: insertUsageData, error: insertUsageErr } = await supabase
            .from('prompt_elements_usage')
            .insert(usageData)
            .select();

          if (insertUsageErr) {
            console.error('Error inserting new usage entries:', insertUsageErr);
          } else {
            console.log('Successfully inserted new usage entries:', insertUsageData);
          }
        }

        // Show success notification
        await notificationService.notify({
          type: 'positive',
          message: 'Element order saved',
          position: 'bottom-right',
          timeout: 2000
        });

        // Update typeElementsMap to match the new order
        if (typeElementsMap.value[typeId]) {
          // Create a map of element ID to element for quick lookup
          const elementsMap = new Map();
          typeElementsMap.value[typeId].forEach(element => {
            elementsMap.set(element.id, element);
          });

          // Create a new array with elements in the new order
          const orderedElements = elementIds.map(id => elementsMap.get(id)).filter(Boolean) as TemplateElement[];

          // Update typeElementsMap
          typeElementsMap.value[typeId] = orderedElements;

          // Force a reactive update
          typeElementsMap.value = { ...typeElementsMap.value };
        }
      }
    } catch (err) {
      console.error('Error saving element order:', err);

      // Show error notification
      await notificationService.notify({
        type: 'negative',
        message: 'Failed to save element order',
        position: 'bottom-right',
        timeout: 3000
      });
    }
  }

  // Update selectedElements
  updateSelectedElements();

  // Emit update event
  emit('update', selectedElements.value);
}

// Lifecycle
onMounted(async () => {
  // console.log('FilterPromptElementSelector mounted for template ID:', props.templateId);

  try {
    // Load element types directly from the service to ensure we get fresh data
    try {
      const types = await promptElementsService.getPromptElementTypes();

      // Update the elementTypes ref
      elementTypes.value = types;

      // Only show types that are not already selected in the dropdown
      filteredElementTypes.value = types.filter(type => !selectedTypes.value.includes(type.id));
    } catch (err) {
      console.error('Error loading element types:', err);
    }

    // If we have a valid template ID, load template elements
    if (validTemplateId.value) {
      await loadTemplateElements();
    } else if (props.initialElementTypes.length > 0) {
      // If we have initial element types, use them
      selectedTypes.value = [...props.initialElementTypes];
      orderedSelectedTypes.value = [...props.initialElementTypes];

      // Load values for each type
      for (const typeId of selectedTypes.value) {
        // Load values directly from the service
        const values = await promptElementsService.getPromptElements(typeId);

        // Update the elementValuesByType ref
        elementValuesByType.value[typeId] = values;
        filteredElementValuesByType.value[typeId] = [...values];

        // If we have initial element values, use them
        if (props.initialElementValues.length > 0) {
          // Find values for this type
          const valueIds = values
            .filter(v => props.initialElementValues.includes(v.id))
            .map(v => v.id);

          if (valueIds.length > 0) {
            typeValuesMap.value[typeId] = valueIds;

            // Add to typeElementsMap
            const typeName = elementTypes.value.find(t => t.id === typeId)?.name;
            typeElementsMap.value[typeId] = values
              .filter(v => valueIds.includes(v.id))
              .map(v => ({
                ...v,
                type_name: typeName
              } as TemplateElement));
          }
        }
      }

      // Update selectedElements
      updateSelectedElements();
    }

    // Mark as initialized
    isInitialized.value = true;
  } catch (error) {
    console.error('Error initializing FilterPromptElementSelector:', error);
  }
});

// Watch for changes in templateId
watch(() => props.templateId, async (newId) => {
  if (newId && newId !== 'new') {
    console.log(`Template ID changed to ${newId}, loading elements`);
    await loadTemplateElements();
    // Mark as initialized after loading elements
    isInitialized.value = true;
  }
});

// No need for onActivated hook anymore since we're using v-show
// The component will stay mounted when switching tabs
</script>

<style lang="scss" scoped>
.prompt-element-selector {
  width: 100%;
}

.type-value-selector {
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  padding: 12px;
  position: relative;
}

.order-badge {
  position: absolute;
  top: -10px;
  left: 5px; /* Changed from -10px to 5px to prevent it from being cut off */
  z-index: 1;
}

.selected-value-item {
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  margin-bottom: 4px;
}

.draggable-container {
  min-height: 30px;
}

.responsive-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.type-handle, .handle {
  opacity: 0.6;
  transition: opacity 0.2s;
  cursor: grab;
}

.type-handle:active, .handle:active {
  cursor: grabbing !important;
}

.type-value-selector:hover .type-handle,
.selected-value-item:hover .handle {
  opacity: 1;
}

.fade-slide-item {
  animation: fade-in 0.3s ease-out;
}

.fade-move-item {
  transition: all 0.3s ease;
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Dark mode support
.body--dark {
  .type-value-selector {
    border-color: rgba(255, 255, 255, 0.12);
  }

  .selected-value-item {
    border-color: rgba(255, 255, 255, 0.12);
  }
}
</style>
