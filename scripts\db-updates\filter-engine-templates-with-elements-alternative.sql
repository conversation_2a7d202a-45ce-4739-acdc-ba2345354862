-- Create a new function that extends filter_engine_templates_optimized to handle filtering by element values
-- This version uses a different approach to filter by element values

-- Drop the function if it exists to avoid type mismatch errors
DROP FUNCTION IF EXISTS filter_engine_templates_with_elements_alternative;

-- Create the new function with element value filtering capability
CREATE FUNCTION filter_engine_templates_with_elements_alternative(
    p_company_id BIGINT DEFAULT NULL,
    p_designer_id BIGINT DEFAULT NULL,
    p_status VARCHAR DEFAULT NULL,
    p_published BOOLEAN DEFAULT NULL,
    p_search VARCHAR DEFAULT NULL,
    p_limit INTEGER DEFAULT 10,
    p_offset INTEGER DEFAULT 0,
    p_template_id BIGINT DEFAULT NULL,
    p_sort_by VARCHAR DEFAULT 'updated_at',
    p_sort_desc BOOLEAN DEFAULT TRUE,
    -- Element type filters - only include types that are actually needed
    p_element_types BIGINT[] DEFAULT NULL,
    -- Element value filters - array of element IDs to filter by
    p_element_values BIGINT[] DEFAULT NULL,
    -- Match type - 'any' or 'all' (default: 'any')
    p_element_match_type <PERSON><PERSON>HAR DEFAULT 'any',
    p_include_elements BOOLEAN DEFAULT TRUE
)
RETURNS TABLE (
    id BIGINT,
    name VARCHAR,
    description VARCHAR,
    status VARCHAR,
    version INTEGER,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    tmpl_company_id BIGINT,
    designer_id BIGINT,
    designer_name VARCHAR,
    approved_by BIGINT,
    approval_date TIMESTAMP,
    agent_id BIGINT,
    published BOOLEAN,
    company_name VARCHAR,
    approver_name VARCHAR,
    agent_name VARCHAR,
    elements JSONB,
    collections JSONB,
    products JSONB,
    total_count BIGINT
) AS $$
DECLARE
    filtered_template_ids BIGINT[];
    query_text TEXT;
    count_query TEXT;
    sort_direction TEXT;
    total BIGINT;
BEGIN
    -- If element values are provided, get the template IDs that have these elements
    IF p_element_values IS NOT NULL AND array_length(p_element_values, 1) > 0 THEN
        IF p_element_match_type = 'all' THEN
            -- Match ALL: Template must have ALL specified element values
            -- For each element value, get the templates that have it
            WITH template_elements AS (
                SELECT 
                    t.template_id,
                    array_agg(DISTINCT (elem->>'id')::BIGINT) AS element_ids
                FROM 
                    template_elements_optimized_view t,
                    jsonb_array_elements(t.elements) AS elem
                GROUP BY 
                    t.template_id
            )
            SELECT 
                array_agg(template_id)
            INTO 
                filtered_template_ids
            FROM 
                template_elements
            WHERE 
                p_element_values <@ element_ids;
        ELSE
            -- Match ANY: Template must have ANY of the specified element values
            -- Get templates that have any of the specified element values
            SELECT 
                array_agg(DISTINCT t.template_id)
            INTO 
                filtered_template_ids
            FROM 
                template_elements_optimized_view t,
                jsonb_array_elements(t.elements) AS elem
            WHERE 
                (elem->>'id')::BIGINT = ANY(p_element_values);
        END IF;
    END IF;

    -- Determine sort direction
    IF p_sort_desc THEN
        sort_direction := 'DESC';
    ELSE
        sort_direction := 'ASC';
    END IF;

    -- Build the base query
    query_text := 'SELECT 
        t.template_id AS id,
        t.template_name::VARCHAR AS name,
        t.template_description::VARCHAR AS description,
        t.status::VARCHAR,
        t.version,
        t.created_at,
        t.updated_at,
        t.tmpl_company_id,
        t.designer_id,
        t.designer_name::VARCHAR,
        t.approved_by,
        t.approval_date,
        t.agent_id,
        t.published,
        t.company_name::VARCHAR,
        t.approver_name::VARCHAR,
        t.agent_name::VARCHAR,';
    
    -- Only include elements if requested
    IF p_include_elements THEN
        -- If specific element types are requested, filter the elements
        IF p_element_types IS NOT NULL AND array_length(p_element_types, 1) > 0 THEN
            query_text := query_text || '
            (
                SELECT jsonb_agg(elem)
                FROM jsonb_array_elements(t.elements) AS elem
                WHERE (elem->>''type_id'')::BIGINT = ANY($1)
            ) AS elements,';
        ELSE
            query_text := query_text || 't.elements,';
        END IF;
    ELSE
        query_text := query_text || '''[]''::jsonb AS elements,';
    END IF;
    
    query_text := query_text || '
        t.collections,
        t.products,
        COUNT(*) OVER() AS total_count
    FROM 
        template_elements_optimized_view t
    WHERE 1=1';

    -- Add filters
    IF p_company_id IS NOT NULL THEN
        query_text := query_text || ' AND t.tmpl_company_id = ' || p_company_id;
    END IF;

    IF p_designer_id IS NOT NULL THEN
        query_text := query_text || ' AND t.designer_id = ' || p_designer_id;
    END IF;

    IF p_status IS NOT NULL THEN
        query_text := query_text || ' AND t.status = ''' || p_status || '''';
    END IF;

    IF p_published IS NOT NULL THEN
        query_text := query_text || ' AND t.published = ' || p_published;
    END IF;

    IF p_search IS NOT NULL AND p_search <> '' THEN
        query_text := query_text || ' AND (t.template_name ILIKE ''%' || p_search || '%'' OR t.template_description ILIKE ''%' || p_search || '%'')';
    END IF;

    IF p_template_id IS NOT NULL THEN
        query_text := query_text || ' AND t.template_id = ' || p_template_id;
    END IF;

    -- Add element value filtering if filtered_template_ids is not empty
    IF filtered_template_ids IS NOT NULL AND array_length(filtered_template_ids, 1) > 0 THEN
        query_text := query_text || ' AND t.template_id = ANY($2)';
    END IF;

    -- Add sorting
    query_text := query_text || ' ORDER BY t.' || p_sort_by || ' ' || sort_direction;

    -- Add pagination
    query_text := query_text || ' LIMIT ' || p_limit || ' OFFSET ' || p_offset;

    -- Get the count first
    count_query := 'SELECT COUNT(*) FROM template_elements_optimized_view t WHERE 1=1';
    
    -- Add the same filters to the count query
    IF p_company_id IS NOT NULL THEN
        count_query := count_query || ' AND t.tmpl_company_id = ' || p_company_id;
    END IF;

    IF p_designer_id IS NOT NULL THEN
        count_query := count_query || ' AND t.designer_id = ' || p_designer_id;
    END IF;

    IF p_status IS NOT NULL THEN
        count_query := count_query || ' AND t.status = ''' || p_status || '''';
    END IF;

    IF p_published IS NOT NULL THEN
        count_query := count_query || ' AND t.published = ' || p_published;
    END IF;

    IF p_search IS NOT NULL AND p_search <> '' THEN
        count_query := count_query || ' AND (t.template_name ILIKE ''%' || p_search || '%'' OR t.template_description ILIKE ''%' || p_search || '%'')';
    END IF;

    IF p_template_id IS NOT NULL THEN
        count_query := count_query || ' AND t.template_id = ' || p_template_id;
    END IF;

    -- Add element value filtering to count query if filtered_template_ids is not empty
    IF filtered_template_ids IS NOT NULL AND array_length(filtered_template_ids, 1) > 0 THEN
        count_query := count_query || ' AND t.template_id = ANY($2)';
    END IF;

    -- Execute the count query
    IF filtered_template_ids IS NOT NULL AND array_length(filtered_template_ids, 1) > 0 THEN
        IF p_element_types IS NOT NULL AND array_length(p_element_types, 1) > 0 THEN
            EXECUTE count_query INTO total USING p_element_types, filtered_template_ids;
        ELSE
            EXECUTE count_query INTO total USING filtered_template_ids;
        END IF;
    ELSIF p_element_types IS NOT NULL AND array_length(p_element_types, 1) > 0 THEN
        EXECUTE count_query INTO total USING p_element_types;
    ELSE
        EXECUTE count_query INTO total;
    END IF;

    -- Execute the main query
    IF filtered_template_ids IS NOT NULL AND array_length(filtered_template_ids, 1) > 0 THEN
        -- Both element types and filtered template IDs are provided
        IF p_element_types IS NOT NULL AND array_length(p_element_types, 1) > 0 THEN
            RETURN QUERY EXECUTE query_text USING p_element_types, filtered_template_ids;
        -- Only filtered template IDs are provided
        ELSE
            -- Modify the query to not use $1 since we don't have element types
            query_text := REPLACE(query_text, '(elem->>''type_id'')::BIGINT = ANY($1)', '1=1');
            RETURN QUERY EXECUTE query_text USING filtered_template_ids;
        END IF;
    -- Only element types are provided
    ELSIF p_element_types IS NOT NULL AND array_length(p_element_types, 1) > 0 THEN
        RETURN QUERY EXECUTE query_text USING p_element_types;
    -- Neither element types nor filtered template IDs are provided
    ELSE
        RETURN QUERY EXECUTE query_text;
    END IF;
END;
$$ LANGUAGE plpgsql;
