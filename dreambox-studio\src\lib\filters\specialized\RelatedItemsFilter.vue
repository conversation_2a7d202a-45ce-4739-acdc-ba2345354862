<template>
  <div class="related-items-filter">
    <div class="row q-col-gutter-md">
      <div class="col-12 col-md-6">
        <q-select
          v-model="selectedType"
          :options="relationTypes"
          label="Relation Type"
          outlined
          dense
          emit-value
          map-options
          @update:model-value="updateType"
        />
      </div>

      <div class="col-12 col-md-6">
        <q-select
          v-model="selectedItems"
          :options="filteredItems"
          label="Related Items"
          outlined
          dense
          multiple
          emit-value
          map-options
          use-chips
          clearable
          :loading="loading"
          @update:model-value="updateItems"
        >
          <template v-slot:no-option>
            <q-item>
              <q-item-section class="text-grey">
                No items available
              </q-item-section>
            </q-item>
          </template>
        </q-select>
      </div>
    </div>

    <div class="row q-col-gutter-md q-mt-md">
      <div class="col-12">
        <q-option-group
          v-model="matchType"
          :options="matchOptions"
          inline
          @update:model-value="updateMatchType"
        />
      </div>
    </div>

    <div v-if="selectedItems.length > 0" class="row q-col-gutter-md q-mt-md">
      <div class="col-12">
        <q-list bordered separator>
          <q-item-label header>Selected Items</q-item-label>
          <q-item v-for="item in selectedItemsDetails" :key="item.id">
            <q-item-section>
              <q-item-label>{{ item.name }}</q-item-label>
              <q-item-label caption>{{ getTypeLabel(selectedType) }}</q-item-label>
            </q-item-section>

            <q-item-section side>
              <q-btn
                flat
                round
                dense
                icon="close"
                @click="removeItem(item.id)"
              />
            </q-item-section>
          </q-item>
        </q-list>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { useCollectionsStore } from '../../../stores/collectionsStore';
import { useProductsStore } from '../../../stores/productsStore';
import { useI18n } from 'vue-i18n';

// Props
const props = defineProps<{
  modelValue?: {
    type?: 'collections' | 'products',
    items?: number[],
    matchType?: 'all' | 'any'
  } | null;
}>();

// Emits
const emit = defineEmits<{
  (e: 'update:modelValue', value: {
    type: string,
    items: number[],
    matchType: string
  } | null): void;
}>();

// Stores
const collectionsStore = useCollectionsStore();
const productsStore = useProductsStore();

// i18n
const { t } = useI18n();

// State
const selectedType = ref('collections');
const selectedItems = ref<number[]>([]);
const matchType = ref('any');
const loading = ref(false);

// Relation types
const relationTypes = [
  { label: 'Collections', value: 'collections' },
  { label: 'Products', value: 'products' }
];

// Match options
const matchOptions = [
  { label: t('common.matchAny'), value: 'any' },
  { label: t('common.matchAll'), value: 'all' }
];

// Computed
const filteredItems = computed(() => {
  if (selectedType.value === 'collections') {
    return collectionsStore.collections.map(col => ({
      label: col.name,
      value: col.id
    }));
  } else if (selectedType.value === 'products') {
    return productsStore.products.map(prod => ({
      label: prod.name,
      value: prod.id
    }));
  }

  return [];
});

const selectedItemsDetails = computed(() => {
  if (selectedType.value === 'collections') {
    return collectionsStore.collections
      .filter(col => selectedItems.value.includes(col.id))
      .map(col => ({
        id: col.id,
        name: col.name
      }));
  } else if (selectedType.value === 'products') {
    return productsStore.products
      .filter(prod => selectedItems.value.includes(prod.id))
      .map(prod => ({
        id: prod.id,
        name: prod.name
      }));
  }

  return [];
});

// Watch for external changes
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    selectedType.value = newValue.type || 'collections';
    selectedItems.value = newValue.items || [];
    matchType.value = newValue.matchType || 'any';
  } else {
    selectedType.value = 'collections';
    selectedItems.value = [];
    matchType.value = 'any';
  }
}, { immediate: true });

// Methods
function updateType(type: string) {
  selectedType.value = type;
  selectedItems.value = []; // Clear selected items when type changes
  updateModelValue();
}

function updateItems(items: number[]) {
  selectedItems.value = items;
  updateModelValue();
}

function updateMatchType(type: string) {
  matchType.value = type;
  updateModelValue();
}

function removeItem(itemId: number) {
  selectedItems.value = selectedItems.value.filter(id => id !== itemId);
  updateModelValue();
}

function getTypeLabel(type: string): string {
  const typeOption = relationTypes.find(t => t.value === type);
  return typeOption ? typeOption.label : type;
}

function updateModelValue() {
  if (selectedItems.value.length === 0) {
    emit('update:modelValue', null);
    return;
  }

  emit('update:modelValue', {
    type: selectedType.value,
    items: selectedItems.value,
    matchType: matchType.value
  });
}

// Initialize
onMounted(async () => {
  loading.value = true;

  try {
    // Load collections if not already loaded
    if (collectionsStore.collections.length === 0) {
      await collectionsStore.loadCollections();
    }

    // Load products if not already loaded
    if (productsStore.products.length === 0) {
      await productsStore.loadProducts();
    }

    // Initialize from props
    if (props.modelValue) {
      selectedType.value = props.modelValue.type || 'collections';
      selectedItems.value = props.modelValue.items || [];
      matchType.value = props.modelValue.matchType || 'any';
    }
  } catch (err) {
    console.error('Error loading related items:', err);
  } finally {
    loading.value = false;
  }
});
</script>

<style scoped>
.related-items-filter {
  width: 100%;
}
</style>
