<template>
  <div class="template-grid">
    <!-- No top section needed as search and filters are in global search -->

    <!-- Show skeleton loader when loading -->
    <div v-if="loading" class="skeleton-container">
      <grid-skeleton-loader :card-count="pagination.rowsPerPage" />
    </div>

    <!-- Grid of template cards when not loading -->
    <div v-else class="row q-col-gutter-md">
      <div
        v-for="template in templates"
        :key="template.id"
        class="col-12 col-sm-6 col-md-4 col-lg-3"
      >
        <q-card class="template-card">
          <!-- Card header with status and actions -->
          <q-card-section class="bg-primary text-white">
            <div class="row items-center no-wrap">
              <div class="col">
                <div class="text-h6 ellipsis">{{ template.name }}</div>
                <div class="text-caption">{{ template.company_name }}</div>
              </div>
              <div class="col-auto">
                <q-btn
                  flat
                  round
                  dense
                  color="white"
                  icon="more_vert"
                >
                  <q-menu>
                    <q-list style="min-width: 100px">
                      <q-item clickable v-close-popup @click="$emit('edit', template)">
                        <q-item-section avatar>
                          <q-icon name="edit" />
                        </q-item-section>
                        <q-item-section>Edit</q-item-section>
                      </q-item>
                      <q-item clickable v-close-popup @click="$emit('view', template, 'view')">
                        <q-item-section avatar>
                          <q-icon name="visibility" />
                        </q-item-section>
                        <q-item-section>View</q-item-section>
                      </q-item>
                      <q-separator />
                      <q-item clickable v-close-popup @click="confirmDelete(template)">
                        <q-item-section avatar>
                          <q-icon name="delete" color="negative" />
                        </q-item-section>
                        <q-item-section>Delete</q-item-section>
                      </q-item>
                    </q-list>
                  </q-menu>
                </q-btn>
              </div>
            </div>
          </q-card-section>

          <!-- Main Card Content (based on Main Table Columns) -->
          <q-card-section>
            <!-- Display visible main columns -->
            <div v-for="column in visibleMainColumns" :key="column.name" class="q-mb-sm">
              <!-- Skip name field as it's already in the header -->
              <template v-if="column.field !== 'name'">
                <!-- Status field with badge -->
                <div v-if="column.field === 'status'" class="row q-col-gutter-sm">
                  <div class="col-auto">
                    <q-badge :color="getStatusColor(template.status)">
                      {{ template.status }}
                    </q-badge>
                  </div>
                </div>

                <!-- Published field with badge -->
                <div v-else-if="column.field === 'published'" class="row q-col-gutter-sm">
                  <div class="col-auto">
                    <q-badge :color="template.published ? 'green' : 'grey'">
                      {{ template.published ? 'Published' : 'Unpublished' }}
                    </q-badge>
                  </div>
                </div>

                <!-- Description field -->
                <div v-else-if="column.field === 'description'" class="text-body2 description-text">
                  {{ template.description || 'No description' }}
                </div>

                <!-- Other fields -->
                <div v-else class="row">
                  <div class="col-5 text-weight-medium">{{ column.label }}:</div>
                  <div class="col-7">{{ getFieldValue(template, column.field) }}</div>
                </div>
              </template>
            </div>

            <!-- Show message if no visible columns -->
            <div v-if="visibleMainColumns.length === 0 || (visibleMainColumns.length === 1 && visibleMainColumns[0]?.field === 'name')" class="text-grey text-italic">
              No visible columns selected. Use the filter panel to select columns to display.
            </div>
          </q-card-section>

          <!-- Card stats -->
          <q-card-section>
            <div class="row q-col-gutter-sm">
              <div class="col-3 text-center">
                <q-icon name="category" color="primary" size="sm" />
                <div class="text-caption">Elements</div>
                <div class="text-body1">{{ getElementCount(template) }}</div>
              </div>
              <div class="col-3 text-center">
                <q-icon name="image" color="purple" size="sm" />
                <div class="text-caption">Images</div>
                <div class="text-body1">{{ template.images?.length || 0 }}</div>
              </div>
              <div class="col-3 text-center">
                <q-icon name="inventory_2" color="orange" size="sm" />
                <div class="text-caption">Products</div>
                <div class="text-body1">{{ template.products?.length || 0 }}</div>
              </div>
              <div class="col-3 text-center">
                <q-icon name="collections_bookmark" color="teal" size="sm" />
                <div class="text-caption">Collections</div>
                <div class="text-body1">{{ template.collections?.length || 0 }}</div>
              </div>
            </div>
          </q-card-section>

          <!-- Collections -->
          <q-card-section v-if="template.collections && template.collections.length > 0" class="q-pt-none">
            <div class="text-caption"><strong>Collections:</strong></div>
            <div class="row q-col-gutter-xs q-mt-xs">
              <div v-for="collection in template.collections" :key="collection.id" class="col-auto">
                <q-chip dense size="sm" color="teal" text-color="white">
                  {{ collection.name }}
                </q-chip>
              </div>
            </div>
          </q-card-section>

          <!-- Card footer with expand toggle -->
          <q-card-section class="q-pt-none">
            <div class="row items-center justify-between">
              <div class="text-caption">
                Updated: {{ formatDate(template.updated_at) }}
              </div>
              <div>
                <q-btn
                  v-if="hasExpandedColumns"
                  flat
                  dense
                  :color="expandedCards[template.id] ? 'primary' : 'grey'"
                  :icon="expandedCards[template.id] ? 'expand_less' : 'expand_more'"
                  :label="expandedCards[template.id] ? 'Less' : 'More'"
                  class="q-mr-sm"
                  @click="toggleExpand(template.id)"
                />
                <q-btn
                  v-if="['draft', 'pending'].includes(template.status)"
                  flat
                  dense
                  color="secondary"
                  icon="edit"
                  label="Edit"
                  class="q-mr-sm"
                  @click="$emit('edit', template)"
                />
                <q-btn
                  flat
                  dense
                  color="primary"
                  icon="visibility"
                  label="View"
                  @click="$emit('view', template, 'view')"
                />
              </div>
            </div>
          </q-card-section>

          <!-- Expanded content (based on Expanded View Columns) -->
          <q-slide-transition>
            <div v-show="expandedCards[template.id]">
              <q-separator />
              <q-card-section>
                <div v-if="visibleExpandedColumns.length > 0">
                  <div v-for="column in visibleExpandedColumns" :key="column.name" class="q-mb-sm row">
                    <div class="col-5 text-weight-medium">{{ column.label }}:</div>
                    <div class="col-7">{{ getFieldValue(template, column.field) }}</div>
                  </div>
                </div>
                <div v-else class="text-grey text-italic">
                  No expanded columns selected. Use the filter panel to add columns to the expanded view.
                </div>
              </q-card-section>
            </div>
          </q-slide-transition>
        </q-card>
      </div>
    </div>

    <!-- Empty state -->
    <div v-if="templates.length === 0 && !loading" class="empty-state q-pa-lg text-center">
      <q-icon name="sentiment_dissatisfied" size="4em" color="grey-5" />
      <div class="text-h6 q-mt-md text-grey-7">No templates found</div>
      <div class="text-body1 q-mt-sm text-grey-6">
        Try adjusting your search or filters, or create a new template.
      </div>
      <q-btn
        color="primary"
        label="Create Template"
        icon="add"
        class="q-mt-md"
        @click="$emit('create')"
      />
    </div>

    <!-- Pagination -->
    <div class="row items-center justify-center q-mt-md">
      <div class="col-auto q-mr-md">
        <q-select
          v-model="pagination.rowsPerPage"
          :options="[12, 24, 36, 48]"
          label="Per page"
          dense
          options-dense
          borderless
          emit-value
          map-options
          style="min-width: 100px"
          @update:model-value="onRowsPerPageChange"
        />
      </div>
      <div class="col-auto">
        <q-pagination
          v-model="pagination.page"
          :max="Math.ceil(totalCount / pagination.rowsPerPage)"
          :max-pages="6"
          boundary-links
          direction-links
          @update:model-value="onPageChange"
        />
      </div>
    </div>

    <!-- Delete confirmation dialog -->
    <q-dialog v-model="confirmDeleteDialog" persistent>
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar icon="warning" color="negative" text-color="white" />
          <span class="q-ml-sm">Are you sure you want to delete this template?</span>
        </q-card-section>
        <q-card-section v-if="templateToDelete">
          <p class="q-mb-none"><strong>{{ templateToDelete.name }}</strong></p>
          <p class="q-mb-none text-caption">
            This action cannot be undone and will remove all associated elements.
          </p>
        </q-card-section>
        <q-card-actions align="right">
          <q-btn flat label="Cancel" color="primary" v-close-popup />
          <q-btn flat label="Delete" color="negative" @click="deleteTemplate" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, reactive } from 'vue';
import { date } from 'quasar';
import type { Template, TemplateFilter, TemplatePagination } from 'src/services/templateService';
import { useTableFilterStore } from 'src/stores/tableFilterStore';
import GridSkeletonLoader from 'src/components/common/GridSkeletonLoader.vue';

// Props
const props = defineProps<{
  templates: Template[];
  loading: boolean;
  totalCount: number;
}>();

// Emits
const emit = defineEmits<{
  (e: 'request', filter: TemplateFilter, pagination: TemplatePagination): void;
  (e: 'edit', template: Template): void;
  (e: 'view', template: Template, mode: 'view' | 'edit'): void;
  (e: 'create'): void;
  (e: 'delete', id: number): void;
}>();

// Pagination
const pagination = ref({
  page: 1,
  rowsPerPage: 12
});

// Table filter store
const filterStore = useTableFilterStore();

// Function to handle global filter events
function handleGlobalFilterEvent(event: Event) {
  const customEvent = event as CustomEvent;
  console.log('TemplateGrid received global filter event:', customEvent.detail);

  // Extract filters from the event
  const { filters } = customEvent.detail;

  if (filters) {
    console.log('Applying filters to grid view:', filters);

    // Create a new filter object with the current pagination and the new filters
    const filterObj: TemplateFilter & Record<string, unknown> = {
      limit: pagination.value.rowsPerPage,
      offset: 0 // Always start at the first page when applying filters
    };

    // Convert filter store format to API format
    if (filters.name) {
      filterObj.search = String(filters.name); // Convert to string
      console.log('Converting name filter to search parameter:', filters.name);
    }

    // Add other filters
    Object.keys(filters).forEach(key => {
      if (key !== 'name') { // Skip name as we handle it separately
        filterObj[key] = filters[key];
      }
    });

    console.log('Sending filter request to server:', filterObj);

    // Reset pagination to first page when filtering
    pagination.value.page = 1;

    // Emit request event to fetch filtered data
    emit('request', filterObj as TemplateFilter, {
      page: 1, // Always start at page 1 when applying a new filter
      rowsPerPage: pagination.value.rowsPerPage,
      rowsNumber: props.totalCount
    });
  }
}

// Initialize filter store with templates table
onMounted(() => {
  console.log('TemplateGrid mounted, initializing filter store');
  void filterStore.setTable('templates');

  // Listen for global filter events
  document.addEventListener('global-filter-changed', handleGlobalFilterEvent);
});

// Clean up event listeners
onBeforeUnmount(() => {
  document.removeEventListener('global-filter-changed', handleGlobalFilterEvent);
});

// Track expanded cards
const expandedCards = reactive<Record<number, boolean>>({});

// Toggle card expansion
function toggleExpand(templateId: number) {
  expandedCards[templateId] = !expandedCards[templateId];
}

// Get visible columns from filter store
const visibleMainColumns = computed(() => {
  return filterStore.visibleMainColumns.map(col => ({
    name: col.id,
    label: col.name,
    field: col.field
  }));
});

const visibleExpandedColumns = computed(() => {
  return filterStore.visibleExpandedColumns.map(col => ({
    name: col.id,
    label: col.name,
    field: col.field
  }));
});

// Check if there are any expanded columns to show
const hasExpandedColumns = computed(() => {
  return visibleExpandedColumns.value.length > 0;
});

// Delete confirmation
const confirmDeleteDialog = ref(false);
const templateToDelete = ref<Template | null>(null);

// Format date
function formatDate(dateStr: string): string {
  if (!dateStr) return '';
  return date.formatDate(dateStr, 'MMM D, YYYY');
}

// Get status color
function getStatusColor(status: string): string {
  switch (status) {
    case 'draft': return 'blue';
    case 'active': return 'positive';
    case 'archived': return 'grey';
    default: return 'grey';
  }
}

// Get element count
function getElementCount(template: Template): number {
  if (!template.elements) return 0;

  return Object.values(template.elements).reduce((count, elements) => {
    return count + elements.length;
  }, 0);
}

// Get field value with formatting
function getFieldValue(template: Template, field: string): string {
  // Use type assertion to access the field
  const value = (template as unknown as Record<string, unknown>)[field];

  if (value === undefined || value === null) {
    return 'N/A';
  }

  // Format based on field type
  if (field.includes('date') || field.includes('_at')) {
    return formatDate(value as string);
  }

  if (typeof value === 'boolean') {
    return value ? 'Yes' : 'No';
  }

  if (typeof value === 'object') {
    // Handle arrays and objects properly
    if (Array.isArray(value)) {
      return value.length > 0 ? `${value.length} items` : 'None';
    }
    return JSON.stringify(value);
  }

  // Handle primitive types
  if (typeof value === 'string' || typeof value === 'number') {
    return String(value);
  }

  // Fallback for any other types
  return 'Complex value';
}

// Handle page change
function onPageChange(page: number) {
  // Create filter object
  const filterObj: TemplateFilter & Record<string, unknown> = {
    limit: pagination.value.rowsPerPage,
    offset: (page - 1) * pagination.value.rowsPerPage
  };

  // Add active filters from filter store
  const activeFilters = filterStore.activeFilters;

  // Convert filter store format to API format
  if (activeFilters.name) {
    filterObj.search = String(activeFilters.name); // Convert to string
  }

  // Add other filters
  Object.keys(activeFilters).forEach(key => {
    if (key !== 'name') { // Skip name as we handle it separately
      filterObj[key] = activeFilters[key];
    }
  });

  console.log('Page change with filters:', filterObj);

  // Emit request event
  emit('request', filterObj as TemplateFilter, {
    page,
    rowsPerPage: pagination.value.rowsPerPage,
    rowsNumber: props.totalCount
  });
}

// Handle rows per page change
function onRowsPerPageChange(rowsPerPage: number) {
  pagination.value.page = 1;

  // Create filter object
  const filterObj: TemplateFilter & Record<string, unknown> = {
    limit: rowsPerPage,
    offset: 0
  };

  // Add active filters from filter store
  const activeFilters = filterStore.activeFilters;

  // Convert filter store format to API format
  if (activeFilters.name) {
    filterObj.search = String(activeFilters.name); // Convert to string
  }

  // Add other filters
  Object.keys(activeFilters).forEach(key => {
    if (key !== 'name') { // Skip name as we handle it separately
      filterObj[key] = activeFilters[key];
    }
  });

  console.log('Rows per page change with filters:', filterObj);

  // Emit request event
  emit('request', filterObj as TemplateFilter, {
    page: 1,
    rowsPerPage,
    rowsNumber: props.totalCount
  });
}

// Confirm delete
function confirmDelete(template: Template) {
  templateToDelete.value = template;
  confirmDeleteDialog.value = true;
}

// Delete template
function deleteTemplate() {
  if (templateToDelete.value) {
    emit('delete', templateToDelete.value.id);
    templateToDelete.value = null;
  }
}

// Initialize
onMounted(() => {
  // Initial request
  const filterObj: TemplateFilter & Record<string, unknown> = {
    limit: pagination.value.rowsPerPage,
    offset: 0
  };

  // Add active filters from filter store
  const activeFilters = filterStore.activeFilters;

  // Convert filter store format to API format
  if (activeFilters.name) {
    filterObj.search = String(activeFilters.name); // Convert to string
  } else {
    // Add other filters
    Object.keys(activeFilters).forEach(key => {
      if (key !== 'name') { // Skip name as we handle it separately
        filterObj[key] = activeFilters[key];
      }
    });
  }

  console.log('Initial grid request with filters:', filterObj);

  emit('request', filterObj as TemplateFilter, {
    page: 1,
    rowsPerPage: pagination.value.rowsPerPage,
    rowsNumber: props.totalCount
  });
});
</script>

<style lang="scss" scoped>
.template-grid {
  .template-card {
    height: 100%;
    min-height: 450px; /* Increased to accommodate additional content */
    transition: transform 0.2s, box-shadow 0.2s;
    display: flex;
    flex-direction: column;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
  }

  .description-text {
    max-height: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }

  .empty-state {
    min-height: 300px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  /* Skeleton loader styles */
  .skeleton-container {
    width: 100%;
    min-height: 450px;
    transition: opacity 0.3s ease-in-out;
  }
}
</style>
