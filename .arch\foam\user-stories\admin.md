Template management:

- [ ] I want to see all templates in the table with data about authors, products and sales
- [ ] I want to filter templates by any field
- [ ] I want to see templates which are in preparation (drafts)
- [ ] I want to see preview of template with designer selected products
- [ ] I want to add/remove products to/from template
- [ ] I want to select one or multiple templates and crate selected number of images for  products specified in template
- [ ] I want to preview created products mockups


Publishing products:
- [ ] I want to be able to select one or multiple product mockups and publish them on shopify
- [ ] I want to see realtime status of publishing process for each product
- [ ] I want to be able to crate new collections (e.g. summer 2025 collection, autumn 2025 collection, sports collection, etc.)
- [ ] I want to scrape internet for popular styles in current and upcoming periods (e.g. summer 2025, autumn 2025, etc.)
- [ ] I want to scrape popular sites (etsy, ebay, amazon, etc.) for trending products
- [ ] I want to save trending products images as material for designers
- [ ] The system should allow me to switch between collection mode to event mode
- [ ] Every product can be part of some collection and can be part of some event
- [ ] Difference between collection and event is that collection is more permanent (e.g. summer 2025 collection) and event is more temporary (e.g. New Year celebration)
- [ ] I want to have a calendar with all events and collections
- [ ] Calendar should also have gantt view (even better)
