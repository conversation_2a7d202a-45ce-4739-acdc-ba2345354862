# Step 13: Modular Architecture

## Overview

This document outlines the modular architecture approach for the new filter engine. The goal is to create a highly flexible and extensible system that can accommodate both simple and complex table requirements while maintaining consistency across the application.

## Modular Architecture Principles

### 1. Separation of Concerns

The filter engine will be divided into distinct modules, each with a specific responsibility:

- **Core Filter Engine**: Basic filtering functionality
- **View Management**: Column visibility and organization
- **Layout Management**: Section definitions and UI layout
- **Table-Specific Adapters**: Table-specific behavior and configuration
- **Specialized Filter Types**: Custom filter implementations

### 2. Plugin Architecture

The filter engine will use a plugin architecture to allow for extensibility:

- **Core Engine**: Provides the foundation and extension points
- **Plugins**: Register with the core engine to extend functionality
- **Adapters**: Customize behavior for specific tables

### 3. Progressive Enhancement

The implementation will follow a progressive enhancement approach:

- **Base Functionality**: Works for simple tables out of the box
- **Extension Points**: Allow for additional functionality
- **Specialized Behavior**: Added through plugins and adapters

## Architecture Components

### Core Filter Engine

The core filter engine handles the fundamental filtering functionality:

```typescript
// src/lib/filters/core/FilterEngine.ts
export class FilterEngine {
  private registry: FilterRegistry;
  private store: FilterStore;
  private plugins: FilterPlugin[] = [];
  
  constructor(registry: FilterRegistry, store: FilterStore) {
    this.registry = registry;
    this.store = store;
  }
  
  // Register a plugin
  registerPlugin(plugin: FilterPlugin): void {
    this.plugins.push(plugin);
    plugin.initialize(this);
  }
  
  // Apply filters to data
  applyFilters(data: any[], tableName: string, filters: any): any[] {
    // Apply standard filters
    let filteredData = this.applyStandardFilters(data, filters.standard);
    
    // Let plugins apply their filters
    for (const plugin of this.plugins) {
      filteredData = plugin.applyFilters(filteredData, tableName, filters);
    }
    
    return filteredData;
  }
  
  // Apply standard filters (text, number, date, etc.)
  private applyStandardFilters(data: any[], filters: any): any[] {
    // Implementation of standard filtering logic
    return data;
  }
}
```

### Plugin Interface

Plugins extend the core functionality:

```typescript
// src/lib/filters/core/FilterPlugin.ts
export interface FilterPlugin {
  id: string;
  name: string;
  
  // Initialize the plugin with the filter engine
  initialize(engine: FilterEngine): void;
  
  // Apply plugin-specific filters
  applyFilters(data: any[], tableName: string, filters: any): any[];
  
  // Get plugin-specific UI components
  getComponents(): Record<string, any>;
  
  // Get plugin-specific filter types
  getFilterTypes(): FilterTypeDefinition[];
}
```

### View Management Module

Handles column visibility and organization:

```typescript
// src/lib/filters/plugins/ViewManagementPlugin.ts
export class ViewManagementPlugin implements FilterPlugin {
  id = 'viewManagement';
  name = 'View Management';
  
  private engine: FilterEngine;
  
  initialize(engine: FilterEngine): void {
    this.engine = engine;
  }
  
  applyFilters(data: any[], tableName: string, filters: any): any[] {
    // View management doesn't modify the data
    return data;
  }
  
  getComponents(): Record<string, any> {
    return {
      ColumnVisibilityManager: ColumnVisibilityManager,
      ColumnSelector: ColumnSelector
    };
  }
  
  getFilterTypes(): FilterTypeDefinition[] {
    return [];
  }
  
  // Toggle column visibility
  toggleColumnVisibility(columnId: string, visible: boolean): void {
    // Implementation
  }
  
  // Update column order
  updateColumnOrder(columnId: string, order: number): void {
    // Implementation
  }
  
  // Move column to section
  moveColumnToSection(columnId: string, section: string): void {
    // Implementation
  }
}
```

### Layout Management Module

Handles section definitions and UI layout:

```typescript
// src/lib/filters/plugins/LayoutManagementPlugin.ts
export class LayoutManagementPlugin implements FilterPlugin {
  id = 'layoutManagement';
  name = 'Layout Management';
  
  private engine: FilterEngine;
  private layouts: Record<string, TableLayout> = {};
  
  initialize(engine: FilterEngine): void {
    this.engine = engine;
  }
  
  applyFilters(data: any[], tableName: string, filters: any): any[] {
    // Layout management doesn't modify the data
    return data;
  }
  
  getComponents(): Record<string, any> {
    return {
      DraggableSection: DraggableSection,
      TabSection: TabSection,
      FilterPanel: FilterPanel
    };
  }
  
  getFilterTypes(): FilterTypeDefinition[] {
    return [];
  }
  
  // Register a layout for a table
  registerLayout(tableName: string, layout: TableLayout): void {
    this.layouts[tableName] = layout;
  }
  
  // Get layout for a table
  getLayout(tableName: string): TableLayout {
    return this.layouts[tableName] || this.getDefaultLayout();
  }
  
  // Get default layout
  private getDefaultLayout(): TableLayout {
    return {
      sections: [
        {
          id: 'main',
          title: 'Main Columns',
          expanded: true
        },
        {
          id: 'expanded',
          title: 'Expanded Columns',
          expanded: false
        }
      ]
    };
  }
}

// Table layout interface
export interface TableLayout {
  sections: Section[];
}

export interface Section {
  id: string;
  title: string;
  expanded: boolean;
  tabs?: Tab[];
}

export interface Tab {
  id: string;
  label: string;
  icon?: string;
}
```

### Table-Specific Adapters

Customize behavior for specific tables:

```typescript
// src/lib/filters/adapters/TableAdapter.ts
export abstract class TableAdapter {
  protected engine: FilterEngine;
  
  constructor(engine: FilterEngine) {
    this.engine = engine;
  }
  
  // Initialize the adapter
  abstract initialize(): void;
  
  // Get table-specific layout
  abstract getLayout(): TableLayout;
  
  // Get table-specific filter types
  abstract getFilterTypes(): FilterTypeDefinition[];
  
  // Get table-specific components
  abstract getComponents(): Record<string, any>;
  
  // Apply table-specific filters
  abstract applyFilters(data: any[], filters: any): any[];
}

// Templates table adapter
// src/lib/filters/adapters/TemplatesAdapter.ts
export class TemplatesAdapter extends TableAdapter {
  initialize(): void {
    // Register layout
    const layoutPlugin = this.engine.getPlugin('layoutManagement') as LayoutManagementPlugin;
    layoutPlugin.registerLayout('templates', this.getLayout());
    
    // Register filter types
    const registry = this.engine.getRegistry();
    for (const filterType of this.getFilterTypes()) {
      registry.registerFilterType(filterType);
    }
  }
  
  getLayout(): TableLayout {
    return {
      sections: [
        {
          id: 'main',
          title: 'Main Columns',
          expanded: true
        },
        {
          id: 'expanded',
          title: 'Expanded Columns',
          expanded: false,
          tabs: [
            {
              id: 'details',
              label: 'Details',
              icon: 'info'
            },
            {
              id: 'promptElements',
              label: 'Prompt Elements',
              icon: 'category'
            },
            {
              id: 'relatedItems',
              label: 'Related Items',
              icon: 'link'
            }
          ]
        }
      ]
    };
  }
  
  getFilterTypes(): FilterTypeDefinition[] {
    return [
      promptElementsFilterType,
      relatedItemsFilterType
    ];
  }
  
  getComponents(): Record<string, any> {
    return {
      PromptElementsFilter: PromptElementsFilter,
      RelatedItemsFilter: RelatedItemsFilter
    };
  }
  
  applyFilters(data: any[], filters: any): any[] {
    // Apply Templates-specific filters
    return data;
  }
}
```

### Specialized Filter Types

Custom filter implementations:

```typescript
// src/lib/filters/plugins/SpecializedFiltersPlugin.ts
export class SpecializedFiltersPlugin implements FilterPlugin {
  id = 'specializedFilters';
  name = 'Specialized Filters';
  
  private engine: FilterEngine;
  
  initialize(engine: FilterEngine): void {
    this.engine = engine;
  }
  
  applyFilters(data: any[], tableName: string, filters: any): any[] {
    // Apply specialized filters
    if (filters.specialized) {
      // Get filter types for this table
      const filterTypes = this.engine.getRegistry().getFilterTypesForTable(tableName)
        .filter(type => type.section === 'specialized');
      
      // Apply each filter type
      for (const filterType of filterTypes) {
        const filterValue = filters.specialized[filterType.id];
        if (filterValue) {
          data = data.filter(item => filterType.stateApplier(item, filterValue));
        }
      }
    }
    
    return data;
  }
  
  getComponents(): Record<string, any> {
    return {};
  }
  
  getFilterTypes(): FilterTypeDefinition[] {
    return [];
  }
}
```

## Filter Engine Initialization

The modular architecture is initialized as follows:

```typescript
// src/lib/filters/initializeFilterEngine.ts
export function initializeFilterEngine() {
  // Create core components
  const registry = new FilterRegistry();
  const store = new FilterStore();
  const engine = new FilterEngine(registry, store);
  
  // Register plugins
  engine.registerPlugin(new ViewManagementPlugin());
  engine.registerPlugin(new LayoutManagementPlugin());
  engine.registerPlugin(new SpecializedFiltersPlugin());
  
  // Register table adapters
  const adapters = {
    templates: new TemplatesAdapter(engine),
    products: new ProductsAdapter(engine),
    users: new UsersAdapter(engine)
  };
  
  // Initialize adapters
  for (const adapter of Object.values(adapters)) {
    adapter.initialize();
  }
  
  return {
    engine,
    registry,
    store,
    adapters
  };
}
```

## Usage Example

Using the modular filter engine in a component:

```vue
<template>
  <div>
    <filter-panel :table-name="tableName" />
    
    <q-table
      :rows="filteredData"
      :columns="visibleColumns"
      row-key="id"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useFilterEngine } from '@/composables/useFilterEngine';
import FilterPanel from '@/components/filters/FilterPanel.vue';

// Props
const props = defineProps({
  tableName: {
    type: String,
    required: true
  }
});

// Data
const data = ref([]);

// Use filter engine
const { filteredData, visibleColumns, isInitialized } = useFilterEngine(props.tableName, data);

// Load data
onMounted(async () => {
  // Load data from API
  const response = await api.getData(props.tableName);
  data.value = response.data;
});
</script>
```

## Extension Points

The modular architecture provides several extension points:

### 1. New Filter Types

Register new filter types with the registry:

```typescript
filterRegistry.registerFilterType({
  id: 'custom',
  name: 'Custom Filter',
  section: 'specialized',
  component: CustomFilterComponent,
  tableTypes: ['myTable'],
  stateExtractor: (state) => state.custom,
  stateApplier: (item, value) => /* custom logic */,
  clearHandler: (state) => {
    delete state.custom;
    return state;
  }
});
```

### 2. New Table Adapters

Create a new adapter for a specific table:

```typescript
class MyTableAdapter extends TableAdapter {
  initialize(): void {
    // Custom initialization
  }
  
  getLayout(): TableLayout {
    // Custom layout
  }
  
  getFilterTypes(): FilterTypeDefinition[] {
    // Custom filter types
  }
  
  getComponents(): Record<string, any> {
    // Custom components
  }
  
  applyFilters(data: any[], filters: any): any[] {
    // Custom filter application
  }
}
```

### 3. New Plugins

Create a new plugin to extend functionality:

```typescript
class MyPlugin implements FilterPlugin {
  id = 'myPlugin';
  name = 'My Plugin';
  
  initialize(engine: FilterEngine): void {
    // Custom initialization
  }
  
  applyFilters(data: any[], tableName: string, filters: any): any[] {
    // Custom filter application
  }
  
  getComponents(): Record<string, any> {
    // Custom components
  }
  
  getFilterTypes(): FilterTypeDefinition[] {
    // Custom filter types
  }
}
```

## Implementation Plan

1. **Core Architecture**:
   - Implement FilterEngine
   - Create Plugin interface
   - Develop extension points

2. **Base Plugins**:
   - Implement ViewManagementPlugin
   - Implement LayoutManagementPlugin
   - Implement SpecializedFiltersPlugin

3. **Table Adapters**:
   - Create base TableAdapter class
   - Implement TemplatesAdapter
   - Implement adapters for other tables

4. **Integration**:
   - Create initialization function
   - Develop useFilterEngine composable
   - Update UI components

## Expected Outcome

After implementing this modular architecture, the filter engine will:

1. Provide a consistent filtering experience across all tables
2. Support both simple and complex table requirements
3. Allow for easy extension with new filter types and behaviors
4. Accommodate table-specific customizations
5. Maintain a clean separation of concerns

This approach ensures that the filter engine can evolve as new requirements emerge, without requiring significant rework of the core functionality.
