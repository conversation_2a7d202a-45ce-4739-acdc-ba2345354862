<template>
  <div class="view-toggle-buttons">
    <div class="view-toggle-container">
      <q-btn
        :color="viewMode === 'table' ? 'primary' : 'grey-4'"
        :text-color="viewMode === 'table' ? 'white' : 'grey-8'"
        icon="table_chart"
        :label="showLabels ? 'Table' : ''"
        @click="onViewChange('table')"
        dense
        no-caps
        unelevated
        :class="{ 'q-px-sm': showLabels, 'view-btn': true }"
      />
      <q-btn
        :color="viewMode === 'grid' ? 'primary' : 'grey-4'"
        :text-color="viewMode === 'grid' ? 'white' : 'grey-8'"
        icon="grid_view"
        :label="showLabels ? 'Grid' : ''"
        @click="onViewChange('grid')"
        dense
        no-caps
        unelevated
        :class="{ 'q-px-sm': showLabels, 'view-btn': true }"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { useContextStore } from 'src/stores/contextStore';

const props = defineProps<{
  modelValue?: string;
  showLabels?: boolean;
  targetCollection?: string;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void;
}>();

const contextStore = useContextStore();
const viewMode = ref(props.modelValue || 'table');

// Handle view mode change
function onViewChange(value: string) {
  // Update context with new view mode
  const currentContext = contextStore.currentContext;
  const newSubContext = value === 'table' ? 'table_view' : 'grid_view';

  // Set the new context
  contextStore.setContext(currentContext || 'default', newSubContext);

  // Emit a custom event that components can listen for
  document.dispatchEvent(new CustomEvent('view-mode-changed', {
    detail: { value, collection: props.targetCollection }
  }));

  // Emit update for v-model compatibility
  emit('update:modelValue', value);
}

// Watch for external model changes
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    viewMode.value = newValue;
  }
});

// Initialize view mode from context
onMounted(() => {
  const subContext = contextStore.currentSubContext;
  if (subContext === 'table_view') {
    viewMode.value = 'table';
  } else if (subContext === 'grid_view') {
    viewMode.value = 'grid';
  }
});
</script>

<style lang="scss" scoped>
.view-toggle-buttons {
  .view-toggle-container {
    display: flex;
    border-radius: 4px;
    overflow: hidden;

    .view-btn {
      min-width: 40px;
      border-radius: 0;

      &:first-child {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
      }

      &:last-child {
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
      }
    }
  }
}
</style>
