export interface Company {
  id: string
  name: string
}

export type UserRole = 'designer' | 'admin' | 'super-admin'

export interface UserRoleWithCompany {
  role: { id: string, name: string }
  company: { id: string, name: string }
}

export interface UserData {
  id: string
  email: string
  firstName?: string | null
  lastName?: string | null
  companies: Company[]
  roles: UserRole[]
  userRoles?: UserRoleWithCompany[]
  currentCompany?: Company
  currentRole?: UserRole
  appUserId?: number // The integer ID from app_users table
}

export interface AuthState {
  user: UserData | null
  loading: boolean
  error: string | null
}
