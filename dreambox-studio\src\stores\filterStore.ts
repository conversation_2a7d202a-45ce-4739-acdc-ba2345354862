/**
 * filterStore.ts
 *
 * This store manages the state of filters across the application.
 * It handles loading, saving, and applying filters for different tables.
 */

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { supabase } from '../boot/supabase';
import { filterRegistry } from '../lib/filters/FilterRegistry';
import { filterEventBus, FILTER_EVENTS } from '../lib/filters/FilterEventBus';
import { useAuthStore } from './auth';

/**
 * Column definition interface
 */
export interface ColumnDefinition {
  id: string;
  name: string;
  field: string;
  section: 'main' | 'expanded';
  visible: boolean;
  order: number;
  filterType: 'text' | 'number' | 'date' | 'select' | 'multi-select' | 'boolean';
  filterOptions?: { label: string, value: unknown }[];
}

/**
 * Saved filter interface
 */
import type { SavedFilterEngine } from '../types/supabase-filter-engine';
export type SavedFilter = SavedFilterEngine;

/**
 * Filter store definition
 */
export const useFilterStore = defineStore('filter', () => {
  // State
  const authStore = useAuthStore();
  const currentTable = ref<string | null>(null);
  const currentFilterId = ref<number | null>(null);
  const savedFilters = ref<SavedFilter[]>([]);
  const columns = ref<ColumnDefinition[]>([]);
  const activeFilters = ref<Record<string, unknown>>({});
  const specializedFilters = ref<Record<string, unknown>>({});
  const loading = ref(false);
  const hasUnsavedChanges = ref(false);

  // Getters
  const filtersByTable = computed(() =>
    savedFilters.value.filter(f => f.table_name === currentTable.value)
  );

  const currentFilter = computed(() =>
    savedFilters.value.find(f => f.id === currentFilterId.value)
  );

  const mainTableColumns = computed(() =>
    columns.value
      .filter(col => col.section === 'main')
      .sort((a, b) => a.order - b.order)
  );

  const expandedViewColumns = computed(() =>
    columns.value
      .filter(col => col.section === 'expanded')
      .sort((a, b) => a.order - b.order)
  );

  const specializedFilterTypes = computed(() => {
    if (!currentTable.value) return [];
    return filterRegistry.getFilterTypesForTable(currentTable.value)
      .filter(def => def.section === 'specialized');
  });

  // Actions
  /**
   * Set the current table and load its filters
   */
  async function setTable(tableName: string) {
    if (currentTable.value === tableName) return;

    currentTable.value = tableName;
    currentFilterId.value = null;
    activeFilters.value = {};
    specializedFilters.value = {};
    hasUnsavedChanges.value = false;

    // Load saved filters for this table
    await loadSavedFilters();

    // Load default filter if available
    const defaultFilter = savedFilters.value.find(f =>
      f.table_name === tableName && f.is_default
    );

    if (defaultFilter) {
      loadFilter(defaultFilter.id);
    }
  }

  /**
   * Load saved filters for the current table
   */
  async function loadSavedFilters() {
    const user = authStore.getUser();
    if (!currentTable.value || !user) return;

    loading.value = true;

    try {
      // Use type assertion to tell TypeScript this table exists
      // We need to use any to bypass TypeScript's strict checking
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const { data, error } = await (supabase.from as any)('filter_engine_filters')
        .select('*')
        .eq('user_id', user.appUserId || 0) // Use appUserId which is a number
        .eq('table_name', currentTable.value);

      if (error) throw error;

      savedFilters.value = data as unknown as SavedFilter[];
    } catch (err) {
      console.error('Error loading saved filters:', err);
      savedFilters.value = [];
    } finally {
      loading.value = false;
    }
  }

  /**
   * Load a specific filter by ID
   */
  function loadFilter(filterId: number) {
    const filter = savedFilters.value.find(f => f.id === filterId);

    if (!filter) {
      console.error(`Filter with ID ${filterId} not found`);
      return;
    }

    // Clear current filters
    activeFilters.value = {};
    specializedFilters.value = {};

    // Apply column filters
    filter.configuration.columns.forEach(col => {
      if (col.value !== undefined && col.value !== null) {
        activeFilters.value[col.id] = col.value;
      }

      // Update column visibility, section, and order
      const columnIndex = columns.value.findIndex(c => c.id === col.id);
      if (columnIndex >= 0) {
        if (columns.value[columnIndex]) {
          columns.value[columnIndex].visible = col.visible ?? true;
          columns.value[columnIndex].section = col.section as 'main' | 'expanded';

          // Restore column order if available
          if (col.order !== undefined) {
            columns.value[columnIndex].order = col.order;
          }
        }
      }
    });

    // Apply specialized filters
    specializedFilters.value = { ...filter.configuration.specializedFilters };

    // Sort columns by their order
    sortColumnsByOrder();

    // Update current filter ID
    currentFilterId.value = filterId;
    hasUnsavedChanges.value = false;

    // Emit filter loaded event
    filterEventBus.emit(FILTER_EVENTS.FILTER_LOADED, {
      tableName: currentTable.value!,
      filterId
    });
  }

  /**
   * Save the current filter
   */
  async function saveCurrentFilter(name: string, description: string | null = null, makeDefault: boolean = false) {
    const user = authStore.getUser();
    if (!currentTable.value || !user) return null;

    loading.value = true;

    try {
      // Prepare filter configuration
      const configuration = {
        columns: columns.value.map(col => ({
          id: col.id,
          value: activeFilters.value[col.id] || null,
          visible: col.visible,
          section: col.section,
          order: col.order // Save the column order
        })),
        specializedFilters: { ...specializedFilters.value }
      };

      // Determine if we're updating or creating
      let filterId = currentFilterId.value;
      let result;

      if (filterId && savedFilters.value.some(f => f.id === filterId)) {
        // Update existing filter
        // Use type assertion to tell TypeScript this table exists
        // We need to use any to bypass TypeScript's strict checking
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const { data, error } = await (supabase.from as any)('filter_engine_filters')
          .update({
            name,
            description,
            is_default: makeDefault,
            configuration,
            updated_at: new Date().toISOString()
          })
          .eq('id', filterId)
          .select('*')
          .single();

        if (error) throw error;
        result = data;
      } else {
        // Create new filter
        // Use type assertion to tell TypeScript this table exists
        // We need to use any to bypass TypeScript's strict checking
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const { data, error } = await (supabase.from as any)('filter_engine_filters')
          .insert({
            user_id: user.appUserId || 0, // Use appUserId which is a number
            name,
            description,
            table_name: currentTable.value,
            context: currentTable.value,
            is_default: makeDefault,
            configuration
          })
          .select('*')
          .single();

        if (error) throw error;
        result = data as unknown as SavedFilter;
        // The result object has an id property
        filterId = result.id;
      }

      // If making this filter default, update other filters
      if (makeDefault) {
        // Use type assertion to tell TypeScript this table exists
        // We need to use any to bypass TypeScript's strict checking
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        await (supabase.from as any)('filter_engine_filters')
          .update({ is_default: false })
          .eq('table_name', currentTable.value)
          .neq('id', filterId);
      }

      // Reload saved filters
      await loadSavedFilters();

      // Update current filter ID
      currentFilterId.value = filterId;
      hasUnsavedChanges.value = false;

      // Emit filter saved event
      filterEventBus.emit(FILTER_EVENTS.FILTER_SAVED, {
        tableName: currentTable.value,
        filterId,
        name
      });

      return filterId;
    } catch (err) {
      console.error('Error saving filter:', err);
      return null;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Delete a filter by ID
   */
  async function deleteFilter(filterId: number) {
    if (!currentTable.value) return false;

    loading.value = true;

    try {
      // Use type assertion to tell TypeScript this table exists
      // We need to use any to bypass TypeScript's strict checking
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const { error } = await (supabase.from as any)('filter_engine_filters')
        .delete()
        .eq('id', filterId);

      if (error) throw error;

      // If we deleted the current filter, clear it
      if (currentFilterId.value === filterId) {
        currentFilterId.value = null;
        activeFilters.value = {};
        specializedFilters.value = {};
      }

      // Reload saved filters
      await loadSavedFilters();

      // Emit filter deleted event
      filterEventBus.emit(FILTER_EVENTS.FILTER_DELETED, {
        tableName: currentTable.value,
        filterId
      });

      return true;
    } catch (err) {
      console.error('Error deleting filter:', err);
      return false;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Set columns for the current table
   */
  function setColumns(tableColumns: ColumnDefinition[]) {
    columns.value = tableColumns;
  }

  /**
   * Update a filter value
   */
  function updateFilter(columnId: string, value: unknown) {
    activeFilters.value[columnId] = value;
    hasUnsavedChanges.value = true;

    // Emit filter changed event
    if (currentTable.value) {
      filterEventBus.emit(FILTER_EVENTS.FILTER_CHANGED, {
        tableName: currentTable.value,
        filterId: currentFilterId.value ? Number(currentFilterId.value) : null,
        columnId,
        value
      });
    }
  }

  /**
   * Clear a specific filter
   */
  function clearFilter(columnId: string) {
    if (activeFilters.value[columnId] !== undefined) {
      delete activeFilters.value[columnId];
      hasUnsavedChanges.value = true;

      // Emit filter cleared event
      if (currentTable.value) {
        filterEventBus.emit(FILTER_EVENTS.FILTER_CLEARED, {
          tableName: currentTable.value,
          filterId: currentFilterId.value ? Number(currentFilterId.value) : null,
          columnId
        });
      }
    }
  }

  /**
   * Clear all filters and reset the current filter ID
   */
  function clearAllFilters() {
    // Clear all filters
    activeFilters.value = {};
    specializedFilters.value = {};

    // Reset the current filter ID to allow creating a new filter
    currentFilterId.value = null;

    hasUnsavedChanges.value = true;

    // Emit filter cleared event
    if (currentTable.value) {
      filterEventBus.emit(FILTER_EVENTS.FILTER_CLEARED, {
        tableName: currentTable.value,
        filterId: null
      });
    }
  }

  /**
   * Update a specialized filter
   */
  function updateSpecializedFilter(filterId: string, value: unknown) {
    console.log('FilterStore: Updating specialized filter:', {
      filterId,
      value,
      currentSpecializedFilters: { ...specializedFilters.value }
    });

    specializedFilters.value[filterId] = value;
    hasUnsavedChanges.value = true;

    console.log('FilterStore: Updated specialized filters:', {
      specializedFilters: { ...specializedFilters.value }
    });

    // Emit specialized filter changed event
    if (currentTable.value) {
      filterEventBus.emit(FILTER_EVENTS.SPECIALIZED_FILTER_CHANGED, {
        tableName: currentTable.value,
        filterId,
        value
      });
    }
  }

  /**
   * Update column visibility
   */
  function updateColumnVisibility(columnId: string, visible: boolean, section: string) {
    const columnIndex = columns.value.findIndex(col => col.id === columnId);

    if (columnIndex >= 0) {
      if (columns.value[columnIndex]) {
        columns.value[columnIndex].visible = visible;
      }
      hasUnsavedChanges.value = true;

      // Emit column visibility changed event
      if (currentTable.value) {
        filterEventBus.emit(FILTER_EVENTS.COLUMN_VISIBILITY_CHANGED, {
          tableName: currentTable.value,
          columnId,
          visible,
          section
        });
      }
    }
  }

  /**
   * Update column order
   */
  function updateColumnOrder(columnId: string, order: number) {
    const columnIndex = columns.value.findIndex(col => col.id === columnId);

    if (columnIndex >= 0) {
      if (columns.value[columnIndex]) {
        columns.value[columnIndex].order = order;
      }
      hasUnsavedChanges.value = true;

      // Emit column order changed event
      if (currentTable.value) {
        filterEventBus.emit(FILTER_EVENTS.COLUMN_ORDER_CHANGED, {
          tableName: currentTable.value,
          columnId,
          order,
          section: columns.value[columnIndex]?.section || 'main'
        });
      }
    }
  }

  /**
   * Move column to a different section
   */
  function moveColumnToSection(columnId: string, section: 'main' | 'expanded') {
    const columnIndex = columns.value.findIndex(col => col.id === columnId);

    if (columnIndex >= 0) {
      if (columns.value[columnIndex]) {
        columns.value[columnIndex].section = section;
      }
      hasUnsavedChanges.value = true;

      // Recalculate order for all columns in both sections
      const mainColumns = columns.value.filter(col => col.section === 'main');
      const expandedColumns = columns.value.filter(col => col.section === 'expanded');

      mainColumns.forEach((col, index) => {
        col.order = index;
      });

      expandedColumns.forEach((col, index) => {
        col.order = index;
      });
    }
  }

  /**
   * Sort columns by their order property
   * This ensures columns are displayed in the correct order after loading a filter
   */
  function sortColumnsByOrder() {
    // Create a copy of the columns array
    const sortedColumns = [...columns.value];

    // Sort the columns by section and order
    sortedColumns.sort((a, b) => {
      // First sort by section
      if (a.section !== b.section) {
        return a.section === 'main' ? -1 : 1;
      }

      // Then sort by order
      return (a.order || 0) - (b.order || 0);
    });

    // Replace the columns array with the sorted one
    columns.value = sortedColumns;
  }

  return {
    // State
    currentTable,
    currentFilterId,
    savedFilters,
    columns,
    activeFilters,
    specializedFilters,
    loading,
    hasUnsavedChanges,

    // Getters
    filtersByTable,
    currentFilter,
    mainTableColumns,
    expandedViewColumns,
    specializedFilterTypes,

    // Actions
    setTable,
    loadSavedFilters,
    loadFilter,
    saveCurrentFilter,
    deleteFilter,
    setColumns,
    updateFilter,
    clearFilter,
    clearAllFilters,
    updateSpecializedFilter,
    updateColumnVisibility,
    updateColumnOrder,
    moveColumnToSection
  };
});
