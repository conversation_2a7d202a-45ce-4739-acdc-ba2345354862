import type { ContextConfig } from '../contextManager';
import { DeviceType } from 'src/types/deviceTypes';
import TemplateBuilderTools from 'src/components/templates/TemplateBuilderTools.vue';
import TemplateBuilderSettings from 'src/components/templates/TemplateBuilderSettings.vue';
import TemplateBuilderPreview from 'src/components/templates/TemplateBuilderPreview.vue';
import type { useRouter } from 'vue-router';

// Define template interface
interface Template {
  id: string;
  name?: string;
  published?: boolean;
}

// Define state interfaces
interface TemplateState {
  selectedTemplates?: Template[];
  template?: Template;
}

// Define Quasar dialog interface
interface QDialog {
  dialog: (options: {
    title: string;
    message: string;
    cancel?: boolean;
    persistent?: boolean;
  }) => {
    onOk: (callback: () => void) => void;
  };
}

// Common actions for templates browse context
const templatesBrowseActions: Array<{
  id: string;
  icon: string;
  label: string;
  action: (...args: unknown[]) => void;
  condition?: (state: unknown) => boolean;
}> = [
  {
    id: 'new-template',
    icon: 'add',
    label: 'New Template',
    action: () => {
      // This will be handled by the handleNewTemplate function in TemplateBrowser.vue
      document.dispatchEvent(new CustomEvent('template-new-click'));
    }
  },
  {
    id: 'edit-template',
    icon: 'edit',
    label: 'Edit',
    action: (...args: unknown[]) => {
      const router = args[0] as ReturnType<typeof useRouter>;
      const template = args[1] as Template | undefined;

      if (!template) return;
      const r = router;
      const prefix = r.currentRoute.value.path.includes('/admin') ? '/admin' : '/designer';
      void r.push(`${prefix}/templates/builder/${template.id}`);
    },
    condition: (state: unknown): boolean => {
      const templateState = state as TemplateState;
      return Boolean(templateState.selectedTemplates && templateState.selectedTemplates.length === 1);
    }
  },
  {
    id: 'delete-template',
    icon: 'delete',
    label: 'Delete',
    action: (...args: unknown[]) => {
      const template = args[1] as Template | undefined;
      const $q = args[2] as QDialog | undefined;

      if (!$q || !template) return;

      $q.dialog({
        title: 'Confirm Deletion',
        message: `Are you sure you want to delete the template "${template?.name || template?.id || 'selected'}"?`,
        cancel: true,
        persistent: true
      }).onOk(() => {
        // Dispatch a custom event for deletion
        document.dispatchEvent(new CustomEvent('template-action', {
          detail: { action: 'delete', template }
        }));
      });
    },
    condition: (state: unknown): boolean => {
      const templateState = state as TemplateState;
      return Boolean(templateState.selectedTemplates && templateState.selectedTemplates.length === 1);
    }
  }
];

// Common actions for templates builder context
const templatesBuilderActions: Array<{
  id: string;
  icon: string;
  label: string;
  action: (...args: unknown[]) => void;
  condition?: (state: unknown) => boolean;
}> = [
  {
    id: 'save-template',
    icon: 'save',
    label: 'Save',
    action: () => {
      document.dispatchEvent(new CustomEvent('template-action', {
        detail: { action: 'save' }
      }));
    }
  },
  {
    id: 'preview-template',
    icon: 'visibility',
    label: 'Preview',
    action: () => {
      document.dispatchEvent(new CustomEvent('template-action', {
        detail: { action: 'preview' }
      }));
    }
  },
  {
    id: 'publish-template',
    icon: 'publish',
    label: 'Publish',
    action: () => {
      document.dispatchEvent(new CustomEvent('template-action', {
        detail: { action: 'publish' }
      }));
    },
    condition: (state: unknown): boolean => {
      const templateState = state as TemplateState;
      return Boolean(templateState.template && !templateState.template.published);
    }
  },
  {
    id: 'delete-template',
    icon: 'delete',
    label: 'Delete',
    action: (...args: unknown[]) => {
      const $q = args[2] as QDialog | undefined;

      if (!$q) return;
      $q.dialog({
        title: 'Confirm Deletion',
        message: `Are you sure you want to delete this template?`,
        cancel: true,
        persistent: true
      }).onOk(() => {
        document.dispatchEvent(new CustomEvent('template-action', {
          detail: { action: 'delete' }
        }));
      });
    }
  }
];

// Templates Browse Context
export const templatesBrowseContext: ContextConfig = {
  id: 'templates',
  name: 'Templates',

  // Base configuration (common for all devices)
  leftDrawer: {
    tabs: {
      filters: true,
      tools: false,
      columns: true
    },
    components: {
      filters: {
        component: 'FilterPanel',
        props: {
          tableName: 'templates'
        }
      },
      columns: null  // Will be implemented later
    }
  },

  rightDrawer: {
    tabs: {
      preview: true,
      settings: false,
      chat: true,
      values: true
    },
    components: {
      preview: null, // Will be implemented later
      values: null   // Will be implemented later
    }
  },

  search: {
    visible: true,
    placeholder: 'Search templates...',
    targetCollection: 'templates',
    filters: [
      {
        name: 'status',
        label: 'Status',
        options: [
          { label: 'Draft', value: 'draft' },
          { label: 'Published', value: 'published' },
          { label: 'Archived', value: 'archived' }
        ]
      }
    ]
  },

  bottomBarActions: templatesBrowseActions,

  // Device-specific overrides
  deviceOverrides: {
    // Mobile browser configuration
    [DeviceType.MOBILE_BROWSER]: {
      leftDrawer: {
        tabs: {
          filters: true,
          tools: false,
          columns: true
        },
        components: {
          filters: {
            component: 'FilterPanel',
            props: {
              tableName: 'templates'
            }
          },
          columns: null
        }
      },
      rightDrawer: {
        tabs: {
          preview: true,
          settings: false,
          chat: true,
          values: true
        },
        components: {
          preview: null,
          values: null
        }
      },
      search: {
        visible: true,
        placeholder: 'Search templates...',
        targetCollection: 'templates'
      },
      bottomBarActions: templatesBrowseActions.filter(action =>
        action.id === 'publish-template' || action.id === 'delete-template'
      )
    },

    // Desktop browser configuration
    [DeviceType.DESKTOP_BROWSER]: {
      leftDrawer: {
        tabs: {
          filters: true,
          tools: false,
          columns: true
        },
        components: {
          filters: {
            component: 'FilterPanel',
            props: {
              tableName: 'templates'
            }
          },
          columns: null
        }
      },
      rightDrawer: {
        tabs: {
          preview: true,
          settings: false,
          chat: true,
          values: true
        },
        components: {
          preview: null,
          values: null
        }
      },
      search: {
        visible: true,
        placeholder: 'Search templates...',
        targetCollection: 'templates'
      },
      bottomBarActions: templatesBrowseActions
    },

    // Electron desktop configuration
    [DeviceType.DESKTOP_ELECTRON]: {
      leftDrawer: {
        tabs: {
          filters: true,
          tools: false,
          columns: true
        },
        components: {
          filters: {
            component: 'FilterPanel',
            props: {
              tableName: 'templates'
            }
          },
          columns: null
        }
      },
      rightDrawer: {
        tabs: {
          preview: true,
          settings: false,
          chat: true,
          values: true
        },
        components: {
          preview: null,
          values: null
        }
      },
      search: {
        visible: true,
        placeholder: 'Search templates...',
        targetCollection: 'templates'
      },
      bottomBarActions: templatesBrowseActions
    }
  }
};

// Templates Builder Context
export const templatesBuilderContext: ContextConfig = {
  id: 'templates',
  name: 'Template Builder',

  // Base configuration (common for all devices)
  leftDrawer: {
    tabs: {
      filters: false,
      tools: false, // Disabled - no longer needed in builder
      elements: false // Disabled - no longer needed in builder
    },
    components: {
      tools: TemplateBuilderTools,
      elements: null // Will be implemented later
    }
  },

  rightDrawer: {
    tabs: {
      preview: true,
      settings: true,
      chat: true,
      values: true
    },
    components: {
      preview: TemplateBuilderPreview,
      settings: TemplateBuilderSettings,
      values: null // Will be implemented later
    }
  },

  search: {
    visible: false,
    placeholder: 'Search elements...',
    targetCollection: 'template-elements'
  },

  bottomBarActions: templatesBuilderActions,

  // Device-specific overrides
  deviceOverrides: {
    // Mobile browser configuration
    [DeviceType.MOBILE_BROWSER]: {
      leftDrawer: {
        tabs: {
          filters: false,
          tools: false, // Disabled - no longer needed in builder
          elements: false // Disabled - no longer needed in builder
        },
        components: {
          tools: TemplateBuilderTools,
          elements: null
        }
      },
      rightDrawer: {
        tabs: {
          preview: true,
          settings: true,
          chat: true,
          values: true
        },
        components: {
          preview: TemplateBuilderPreview,
          settings: TemplateBuilderSettings,
          values: null
        }
      },
      search: {
        visible: false,
        placeholder: 'Search elements...',
        targetCollection: 'template-elements'
      },
      bottomBarActions: templatesBuilderActions.filter(action =>
        action.id === 'save-template' || action.id === 'preview-template'
      )
    },

    // Desktop browser configuration
    [DeviceType.DESKTOP_BROWSER]: {
      leftDrawer: {
        tabs: {
          filters: false,
          tools: false, // Disabled - no longer needed in builder
          elements: false // Disabled - no longer needed in builder
        },
        components: {
          tools: TemplateBuilderTools,
          elements: null
        }
      },
      rightDrawer: {
        tabs: {
          preview: true,
          settings: true,
          chat: true,
          values: true
        },
        components: {
          preview: TemplateBuilderPreview,
          settings: TemplateBuilderSettings,
          values: null
        }
      },
      search: {
        visible: false,
        placeholder: 'Search elements...',
        targetCollection: 'template-elements'
      },
      bottomBarActions: templatesBuilderActions
    },

    // Electron desktop configuration
    [DeviceType.DESKTOP_ELECTRON]: {
      leftDrawer: {
        tabs: {
          filters: false,
          tools: false, // Disabled - no longer needed in builder
          elements: false // Disabled - no longer needed in builder
        },
        components: {
          tools: TemplateBuilderTools,
          elements: null
        }
      },
      rightDrawer: {
        tabs: {
          preview: true,
          settings: true,
          chat: true,
          values: true
        },
        components: {
          preview: TemplateBuilderPreview,
          settings: TemplateBuilderSettings,
          values: null
        }
      },
      search: {
        visible: false,
        placeholder: 'Search elements...',
        targetCollection: 'template-elements'
      },
      bottomBarActions: templatesBuilderActions
    }
  }
};
