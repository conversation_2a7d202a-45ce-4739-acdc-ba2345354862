/**
 * productsStore.ts
 *
 * This store manages products data.
 * It provides methods for loading, filtering, and manipulating products.
 */

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { supabase } from '../boot/supabase';
import { useAuthStore } from './auth';

/**
 * Product interface
 */
export interface Product {
  id: number;
  name: string;
  description: string | null;
  user_id: string;
  created_at: string;
  updated_at: string;
}

/**
 * Products store definition
 */
export const useProductsStore = defineStore('products', () => {
  // State
  const authStore = useAuthStore();
  const products = ref<Product[]>([]);
  const loading = ref(false);

  // Getters
  const userProducts = computed(() => {
    const user = authStore.getUser();
    return products.value.filter(prod => user && prod.user_id === user.id);
  });

  // Actions
  /**
   * Load all products
   */
  async function loadProducts() {
    const user = authStore.getUser();
    if (!user) return;

    loading.value = true;

    try {
      // Use type assertion to tell TypeScript this table exists
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const { data, error } = await supabase.from('products' as any).select('*').order('name');

      if (error) throw error;

      products.value = data as unknown as Product[];
    } catch (err) {
      console.error('Error loading products:', err);
      products.value = [];
    } finally {
      loading.value = false;
    }
  }

  /**
   * Get products by IDs
   */
  function getProductsByIds(ids: number[]): Product[] {
    return products.value.filter(prod => ids.includes(prod.id));
  }

  return {
    // State
    products,
    loading,

    // Getters
    userProducts,

    // Actions
    loadProducts,
    getProductsByIds
  };
});
