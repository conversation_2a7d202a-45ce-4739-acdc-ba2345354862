# Consolidated Edge Functions for S3 Image Management

This document outlines the consolidated Supabase Edge Functions approach for securely managing images in S3, designed to work within the 10 Edge Function limit of Supabase's free plan.

## Overview

Instead of creating multiple single-purpose Edge Functions, we'll create a single, versatile Edge Function that handles all image management operations. This approach:

1. Reduces the number of Edge Functions needed
2. Maintains all the functionality
3. Keeps API keys secure
4. Works within Supabase's free plan limitations

## Consolidated Image Management Edge Function

This single Edge Function handles multiple operations through a query parameter:

```typescript
// supabase/functions/image-management/index.ts
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { S3Client, PutObjectCommand, DeleteObjectCommand, CopyObjectCommand } from 'https://esm.sh/@aws-sdk/client-s3'
import { getSignedUrl } from 'https://esm.sh/@aws-sdk/s3-request-presigner'
import { v4 as uuidv4 } from 'https://esm.sh/uuid@9.0.0'

serve(async (req) => {
  // CORS headers
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Authorization, Content-Type',
      }
    })
  }

  // Create Supabase client
  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
  )

  // Get user from request
  const { data: { user } } = await supabaseClient.auth.getUser()
  if (!user) {
    return new Response(JSON.stringify({ error: 'Unauthorized' }), {
      status: 401,
      headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' }
    })
  }

  try {
    const url = new URL(req.url)
    const action = url.searchParams.get('action')

    // Create S3 client
    const s3Client = new S3Client({
      region: 'us-east-1', // Your S3 bucket region
      credentials: {
        accessKeyId: Deno.env.get('AWS_ACCESS_KEY_ID') ?? '',
        secretAccessKey: Deno.env.get('AWS_SECRET_ACCESS_KEY') ?? ''
      }
    })

    // Handle different actions
    switch (action) {
      case 'get-upload-url':
        return handleGetUploadUrl(req, supabaseClient, s3Client)

      case 'delete-image':
        return handleDeleteImage(req, supabaseClient, s3Client)

      case 'change-storage-class':
        return handleChangeStorageClass(req, supabaseClient, s3Client)

      case 'save-swarmui-image':
        return handleSaveSwarmUIImage(req, supabaseClient, s3Client)

      default:
        return new Response(JSON.stringify({ error: 'Invalid action' }), {
          status: 400,
          headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' }
        })
    }
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' }
    })
  }
})

// Handler for getting upload URL
async function handleGetUploadUrl(req, supabaseClient, s3Client) {
  const {
    filename,
    contentType,
    fileSize,
    width,
    height,
    title,
    description,
    tags,
    templateId,
    resolution,
    isPublic,
    metadata,
    prompt,
    negativePrompt,
    seed,
    modelUsed,
    parentImageId,
    productId,
    position,
    isPrimary
  } = await req.json()

  // Create a unique file key with UUID to avoid collisions
  const uuid = uuidv4()
  const sanitizedFilename = filename.replace(/[^a-zA-Z0-9.-]/g, '_')
  const key = `images/${uuid}-${sanitizedFilename}`

  // Create the command to put an object in S3
  const command = new PutObjectCommand({
    Bucket: 'dreambox-studio-images',
    Key: key,
    ContentType: contentType
  })

  // Generate a pre-signed URL
  const signedUrl = await getSignedUrl(s3Client, command, { expiresIn: 3600 })

  // Create a record in the s3_images table
  const { data: imageData, error } = await supabaseClient.rpc(
    'create_s3_image',
    {
      p_filename: sanitizedFilename,
      p_s3_key: key,
      p_s3_url: `https://dreambox-studio-images.s3.amazonaws.com/${key}`,
      p_mime_type: contentType,
      p_file_size: fileSize,
      p_width: width,
      p_height: height,
      p_title: title || sanitizedFilename,
      p_description: description,
      p_tags: tags,
      p_template_id: templateId,
      p_resolution: resolution || 'standard',
      p_is_public: isPublic || false,
      p_metadata: metadata || {},
      p_prompt: prompt,
      p_negative_prompt: negativePrompt,
      p_seed: seed,
      p_model_used: modelUsed,
      p_parent_image_id: parentImageId
    }
  )

  if (error) throw new Error(`Failed to create image record: ${error.message}`)

  // If a product ID was provided, associate the image with the product
  if (productId) {
    const { error: associationError } = await supabaseClient.rpc(
      'associate_image_with_product',
      {
        p_image_id: imageData.id,
        p_product_id: productId,
        p_position: position,
        p_is_primary: isPrimary || false
      }
    )

    if (associationError) {
      console.error('Error associating image with product:', associationError)
    }
  }

  return new Response(JSON.stringify({
    uploadUrl: signedUrl,
    image: imageData
  }), {
    headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' }
  })
}

// Handler for deleting images
async function handleDeleteImage(req, supabaseClient, s3Client) {
  const { imageId } = await req.json()

  // Get the image details
  const { data: image, error: imageError } = await supabaseClient
    .from('s3_images')
    .select('s3_key')
    .eq('id', imageId)
    .single()

  if (imageError || !image) {
    throw new Error(`Failed to get image: ${imageError?.message || 'Image not found'}`)
  }

  // Delete from S3
  const command = new DeleteObjectCommand({
    Bucket: 'dreambox-studio-images',
    Key: image.s3_key
  })

  await s3Client.send(command)

  // Delete from database
  const { error: deleteError } = await supabaseClient
    .from('s3_images')
    .delete()
    .eq('id', imageId)

  if (deleteError) {
    throw new Error(`Failed to delete image record: ${deleteError.message}`)
  }

  return new Response(JSON.stringify({
    success: true,
    message: 'Image deleted successfully'
  }), {
    headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' }
  })
}

// Handler for changing storage class
async function handleChangeStorageClass(req, supabaseClient, s3Client) {
  const { imageId, storageClass } = await req.json()

  if (!['STANDARD', 'STANDARD_IA', 'GLACIER'].includes(storageClass)) {
    throw new Error('Invalid storage class. Must be one of: STANDARD, STANDARD_IA, GLACIER')
  }

  // Get the image details
  const { data: image, error: imageError } = await supabaseClient
    .from('s3_images')
    .select('s3_key')
    .eq('id', imageId)
    .single()

  if (imageError || !image) {
    throw new Error(`Failed to get image: ${imageError?.message || 'Image not found'}`)
  }

  // Change the storage class by copying the object to itself with a new storage class
  const command = new CopyObjectCommand({
    Bucket: 'dreambox-studio-images',
    CopySource: `dreambox-studio-images/${image.s3_key}`,
    Key: image.s3_key,
    StorageClass: storageClass
  })

  await s3Client.send(command)

  // Update the storage class in the database
  const { error: updateError } = await supabaseClient
    .from('s3_images')
    .update({ storage_class: storageClass })
    .eq('id', imageId)

  if (updateError) {
    throw new Error(`Failed to update image record: ${updateError.message}`)
  }

  return new Response(JSON.stringify({
    success: true,
    message: `Storage class changed to ${storageClass}`
  }), {
    headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' }
  })
}

// Handler for saving SwarmUI generated images
async function handleSaveSwarmUIImage(req, supabaseClient, s3Client) {
  // Parse the request body
  const formData = await req.formData()
  const imageFile = formData.get('image')
  const metadataStr = formData.get('metadata')

  if (!imageFile || !(imageFile instanceof File)) {
    throw new Error('No image file provided')
  }

  if (!metadataStr) {
    throw new Error('No metadata provided')
  }

  const metadata = JSON.parse(metadataStr)

  // Read the file as an array buffer
  const arrayBuffer = await imageFile.arrayBuffer()
  const buffer = new Uint8Array(arrayBuffer)

  // Create a unique file key with UUID
  const uuid = uuidv4()
  const filename = metadata.filename || 'generated-image.png'
  const sanitizedFilename = filename.replace(/[^a-zA-Z0-9.-]/g, '_')
  const key = `images/${uuid}-${sanitizedFilename}`

  // Upload to S3
  const command = new PutObjectCommand({
    Bucket: 'dreambox-studio-images',
    Key: key,
    Body: buffer,
    ContentType: imageFile.type || 'image/png'
  })

  await s3Client.send(command)

  // Extract tags from prompt
  const promptTags = metadata.prompt
    ? extractTagsFromPrompt(metadata.prompt)
    : []

  // Combine with any explicit tags
  const tags = [...new Set([...(metadata.tags || []), ...promptTags])]

  // Create a record in the s3_images table
  const { data: imageData, error } = await supabaseClient.rpc(
    'create_s3_image',
    {
      p_filename: sanitizedFilename,
      p_s3_key: key,
      p_s3_url: `https://dreambox-studio-images.s3.amazonaws.com/${key}`,
      p_mime_type: imageFile.type || 'image/png',
      p_file_size: buffer.length,
      p_width: metadata.width,
      p_height: metadata.height,
      p_title: metadata.title || 'Generated Image',
      p_description: metadata.description || metadata.prompt,
      p_tags: tags,
      p_template_id: metadata.templateId,
      p_resolution: metadata.resolution || 'standard',
      p_is_public: metadata.isPublic || false,
      p_metadata: {
        ...metadata,
        steps: metadata.steps,
        cfgScale: metadata.cfgScale,
        sampler: metadata.sampler
      },
      p_prompt: metadata.prompt,
      p_negative_prompt: metadata.negativePrompt,
      p_seed: metadata.seed,
      p_model_used: metadata.model,
      p_parent_image_id: metadata.parentImageId
    }
  )

  if (error) throw new Error(`Failed to create image record: ${error.message}`)

  // If product ID is provided, associate the image with the product
  if (metadata.productId) {
    await supabaseClient.rpc(
      'associate_image_with_product',
      {
        p_image_id: imageData.id,
        p_product_id: metadata.productId,
        p_position: metadata.position,
        p_is_primary: metadata.isPrimary || false
      }
    )
  }

  return new Response(JSON.stringify({
    success: true,
    image: imageData
  }), {
    headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' }
  })
}

// Helper function to extract potential tags from a prompt
function extractTagsFromPrompt(prompt) {
  // Simple extraction of comma-separated terms
  return prompt
    .split(',')
    .map(term => term.trim())
    .filter(term => term.length > 3 && term.length < 20) // Filter out very short or long terms
    .slice(0, 10) // Limit to 10 tags
}
```

## Client-Side Usage

To use this consolidated Edge Function from the frontend:

```typescript
// src/services/imageService.ts

/**
 * Get a pre-signed URL for uploading an image to S3
 */
async getUploadUrl(params) {
  const { data, error } = await supabase.functions.invoke('image-management', {
    method: 'POST',
    query: { action: 'get-upload-url' },
    body: params
  })

  if (error) throw error
  return data
},

/**
 * Delete an image from S3
 */
async deleteImage(imageId) {
  const { data, error } = await supabase.functions.invoke('image-management', {
    method: 'POST',
    query: { action: 'delete-image' },
    body: { imageId }
  })

  if (error) throw error
  return data
},

/**
 * Change the storage class of an image
 */
async changeStorageClass(imageId, storageClass) {
  const { data, error } = await supabase.functions.invoke('image-management', {
    method: 'POST',
    query: { action: 'change-storage-class' },
    body: { imageId, storageClass }
  })

  if (error) throw error
  return data
},

/**
 * Save a SwarmUI generated image
 */
async saveSwarmUIImage(imageFile, metadata) {
  const formData = new FormData()
  formData.append('image', imageFile)
  formData.append('metadata', JSON.stringify(metadata))

  const { data, error } = await supabase.functions.invoke('image-management', {
    method: 'POST',
    query: { action: 'save-swarmui-image' },
    body: formData
  })

  if (error) throw error
  return data
}
```

## Deployment

Deploy the consolidated Edge Function to your Supabase project:

```bash
# Install Supabase CLI if not already installed
npm install -g supabase

# Login to Supabase
supabase login

# Initialize Supabase in your project (if not already done)
supabase init

# Create the Edge Function directory
mkdir -p supabase/functions/image-management

# Deploy the function
supabase functions deploy image-management
```

## Environment Variables

Set the following environment variables in your Supabase project:

1. `AWS_ACCESS_KEY_ID`: Your AWS access key
2. `AWS_SECRET_ACCESS_KEY`: Your AWS secret key

## Benefits of the Consolidated Approach

1. **Efficiency**: Uses only one Edge Function slot instead of multiple
2. **Maintainability**: All image-related code is in one place
3. **Flexibility**: Easy to add new image operations without deploying new functions
4. **Security**: Still keeps AWS credentials secure
5. **Scalability**: Can handle all image management operations

## Next Steps

Once the consolidated Edge Function is implemented, proceed to [Frontend Services](./05-frontend-services.md) to create the client-side image management services.
