import type { ContextConfig } from '../contextManager';
import { DeviceType } from 'src/types/deviceTypes';

export const defaultContext: ContextConfig = {
  id: 'default',
  name: 'Default',

  // Base configuration (common for all devices)
  leftDrawer: {
    tabs: {
      filters: false,
      tools: false,
      columns: false,
      elements: false
    },
    components: {}
  },

  rightDrawer: {
    tabs: {
      preview: false,
      settings: false,
      chat: true,
      values: false
    },
    components: {}
  },

  search: {
    visible: false,
    placeholder: 'Search...',
    targetCollection: 'none'
  },

  bottomBarActions: [],

  // Device-specific overrides
  deviceOverrides: {
    // Mobile browser configuration
    [DeviceType.MOBILE_BROWSER]: {
      leftDrawer: {
        tabs: {
          filters: false,
          tools: false,
          columns: false,
          elements: false
        },
        components: {}
      },
      rightDrawer: {
        tabs: {
          preview: false,
          settings: false,
          chat: true,
          values: false
        },
        components: {}
      }
    },

    // Desktop browser configuration
    [DeviceType.DESKTOP_BROWSER]: {
      leftDrawer: {
        tabs: {
          filters: false,
          tools: false,
          columns: false,
          elements: false
        },
        components: {}
      },
      rightDrawer: {
        tabs: {
          preview: false,
          settings: false,
          chat: true,
          values: false
        },
        components: {}
      }
    },

    // Electron desktop configuration
    [DeviceType.DESKTOP_ELECTRON]: {
      leftDrawer: {
        tabs: {
          filters: false,
          tools: false,
          columns: false,
          elements: false
        },
        components: {}
      },
      rightDrawer: {
        tabs: {
          preview: false,
          settings: false,
          chat: true,
          values: false
        },
        components: {}
      }
    }
  }
};
