# Step 3: Specialized Filter Types

## Overview
In this step, we'll implement specialized filter types for specific use cases, such as the PromptElementsFilter and RelatedItemsFilter. These specialized filters require custom UI components and logic that goes beyond the standard column filters.

## Tasks

### 3.1 Create the PromptElementsFilter

This specialized filter will allow filtering templates based on their prompt elements.

```typescript
// src/lib/filters/specialized/PromptElementsFilter.ts
import { defineComponent, ref, computed, watch, onMounted } from 'vue';
import { usePromptElementsStore } from '@/stores/promptElementsStore';
import type { FilterTypeDefinition } from '@/lib/filters/FilterRegistry';
import { filterRegistry } from '@/lib/filters/FilterRegistry';

// Define the filter type
export const promptElementsFilterType: FilterTypeDefinition = {
  id: 'promptElements',
  name: 'Prompt Elements',
  section: 'specialized',
  component: defineComponent({
    name: 'PromptElementsFilter',
    props: {
      filterValue: {
        type: Object,
        default: () => ({})
      }
    },
    emits: ['update', 'clear'],
    setup(props, { emit }) {
      // Store
      const promptElementsStore = usePromptElementsStore();
      
      // State
      const selectedElements = ref<Record<string, number[]>>(props.filterValue || {});
      const elementTypes = computed(() => promptElementsStore.elementTypes);
      const elementValuesByType = ref<Record<number, any[]>>({});
      
      // Load element values for each type
      onMounted(async () => {
        for (const type of elementTypes.value) {
          const values = await promptElementsStore.getElementValuesByType(type.id);
          elementValuesByType.value[type.id] = values;
        }
      });
      
      // Watch for changes in the filter value
      watch(() => props.filterValue, (newValue) => {
        if (newValue && Object.keys(newValue).length > 0) {
          selectedElements.value = { ...newValue };
        } else {
          selectedElements.value = {};
        }
      });
      
      // Update the filter when selections change
      function updateFilter(typeId: number, values: number[]) {
        const type = elementTypes.value.find(t => t.id === typeId);
        if (!type) return;
        
        if (values && values.length > 0) {
          selectedElements.value[`element_${type.name}`] = values;
        } else {
          delete selectedElements.value[`element_${type.name}`];
        }
        
        emit('update', { ...selectedElements.value });
      }
      
      // Clear all element filters
      function clearAll() {
        selectedElements.value = {};
        emit('update', {});
      }
      
      // Clear a specific element type filter
      function clearType(typeId: number) {
        const type = elementTypes.value.find(t => t.id === typeId);
        if (!type) return;
        
        delete selectedElements.value[`element_${type.name}`];
        emit('update', { ...selectedElements.value });
      }
      
      // Format element type name for display
      function formatTypeName(name: string) {
        return name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
      }
      
      // Check if a filter is active for a type
      function isFilterActive(typeId: number) {
        const type = elementTypes.value.find(t => t.id === typeId);
        if (!type) return false;
        
        return !!selectedElements.value[`element_${type.name}`]?.length;
      }
      
      // Get elements for a specific type
      function getElementsForType(typeId: number) {
        return elementValuesByType.value[typeId] || [];
      }
      
      // Get the filter label with selection count
      function getFilterLabel(type: any) {
        const typeName = formatTypeName(type.name);
        const selectedCount = selectedElements.value[`element_${type.name}`]?.length || 0;
        
        if (selectedCount > 0) {
          return `${typeName} (${selectedCount} selected)`;
        }
        
        return typeName;
      }
      
      return {
        elementTypes,
        selectedElements,
        elementValuesByType,
        updateFilter,
        clearAll,
        clearType,
        formatTypeName,
        isFilterActive,
        getElementsForType,
        getFilterLabel
      };
    },
    template: `
      <div class="prompt-elements-filter">
        <div class="filter-header">
          <div class="row items-center q-mb-md">
            <div class="col">
              <div class="text-subtitle1">Prompt Elements</div>
            </div>
            <div class="col-auto">
              <q-btn
                flat
                round
                dense
                icon="clear_all"
                color="grey-7"
                @click="clearAll"
              >
                <q-tooltip>Clear all element filters</q-tooltip>
              </q-btn>
            </div>
          </div>
        </div>
        
        <div class="element-filters">
          <div
            v-for="type in elementTypes"
            :key="type.id"
            class="element-filter q-mb-md"
          >
            <div class="filter-container">
              <q-select
                v-model="selectedElements['element_' + type.name]"
                :options="getElementsForType(type.id)"
                option-label="value"
                option-value="id"
                multiple
                outlined
                dense
                use-chips
                stack-label
                use-input
                hide-selected
                fill-input
                input-debounce="300"
                :label="getFilterLabel(type)"
                :class="isFilterActive(type.id) ? 'active-filter' : ''"
                @update:model-value="updateFilter(type.id, $event)"
              >
                <template v-slot:selected-item="scope">
                  <q-chip
                    removable
                    dense
                    @remove="scope.removeAtIndex(scope.index)"
                    :tabindex="scope.tabindex"
                    color="primary"
                    text-color="white"
                  >
                    {{ scope.opt.value }}
                  </q-chip>
                </template>
              </q-select>
              
              <q-btn
                v-if="isFilterActive(type.id)"
                flat
                round
                dense
                icon="clear"
                color="grey-7"
                class="clear-type-btn"
                @click="clearType(type.id)"
              >
                <q-tooltip>Clear {{ formatTypeName(type.name) }} filters</q-tooltip>
              </q-btn>
            </div>
          </div>
        </div>
      </div>
    `
  }),
  tableTypes: ['templates'],
  stateExtractor: (state: any) => {
    const elements: Record<string, number[]> = {};
    
    // Extract element filters from state
    for (const key in state) {
      if (key.startsWith('element_')) {
        elements[key] = state[key];
      }
    }
    
    return elements;
  },
  stateApplier: (item: any, filterValue: Record<string, number[]>) => {
    // If no filter values, return true
    if (!filterValue || Object.keys(filterValue).length === 0) {
      return true;
    }
    
    // Check if the item has template_elements
    if (!item.template_elements || !Array.isArray(item.template_elements)) {
      return false;
    }
    
    // For each element type filter
    for (const [elementType, selectedIds] of Object.entries(filterValue)) {
      if (!selectedIds || selectedIds.length === 0) continue;
      
      // Extract the type name from the key (e.g., 'element_angle' -> 'angle')
      const typeName = elementType.replace('element_', '');
      
      // Find elements of this type in the template
      const matchingElements = item.template_elements.filter((el: any) => {
        return el.element_type === typeName && 
               selectedIds.includes(el.element_value_id);
      });
      
      // If no matching elements found for this type, return false
      if (matchingElements.length === 0) {
        return false;
      }
    }
    
    // All filters passed
    return true;
  },
  clearHandler: (state: any) => {
    // Remove all element filters
    for (const key in state) {
      if (key.startsWith('element_')) {
        delete state[key];
      }
    }
    return state;
  },
  serializeValue: (value: Record<string, number[]>) => {
    return value;
  },
  deserializeValue: (value: Record<string, number[]>) => {
    return value;
  }
};

// Register the filter type
export function registerPromptElementsFilter() {
  filterRegistry.registerFilterType(promptElementsFilterType);
}
```

### 3.2 Create the RelatedItemsFilter

This specialized filter will allow filtering templates based on related items like collections, products, etc.

```typescript
// src/lib/filters/specialized/RelatedItemsFilter.ts
import { defineComponent, ref, computed, watch, onMounted } from 'vue';
import { useCollectionsStore } from '@/stores/collectionsStore';
import { useProductsStore } from '@/stores/productsStore';
import type { FilterTypeDefinition } from '@/lib/filters/FilterRegistry';
import { filterRegistry } from '@/lib/filters/FilterRegistry';

// Define the filter type
export const relatedItemsFilterType: FilterTypeDefinition = {
  id: 'relatedItems',
  name: 'Related Items',
  section: 'specialized',
  component: defineComponent({
    name: 'RelatedItemsFilter',
    props: {
      filterValue: {
        type: Object,
        default: () => ({})
      }
    },
    emits: ['update', 'clear'],
    setup(props, { emit }) {
      // Stores
      const collectionsStore = useCollectionsStore();
      const productsStore = useProductsStore();
      
      // State
      const selectedCollections = ref<number[]>(props.filterValue?.collections || []);
      const selectedProducts = ref<number[]>(props.filterValue?.products || []);
      const collections = ref<any[]>([]);
      const products = ref<any[]>([]);
      
      // Load collections and products
      onMounted(async () => {
        collections.value = await collectionsStore.getAllCollections();
        products.value = await productsStore.getAllProducts();
      });
      
      // Watch for changes in the filter value
      watch(() => props.filterValue, (newValue) => {
        if (newValue) {
          selectedCollections.value = newValue.collections || [];
          selectedProducts.value = newValue.products || [];
        } else {
          selectedCollections.value = [];
          selectedProducts.value = [];
        }
      });
      
      // Computed
      const hasActiveFilters = computed(() => {
        return selectedCollections.value.length > 0 || selectedProducts.value.length > 0;
      });
      
      // Update collection filter
      function updateCollections(collectionIds: number[]) {
        selectedCollections.value = collectionIds || [];
        updateFilter();
      }
      
      // Update product filter
      function updateProducts(productIds: number[]) {
        selectedProducts.value = productIds || [];
        updateFilter();
      }
      
      // Update the filter
      function updateFilter() {
        const filterValue: Record<string, number[]> = {};
        
        if (selectedCollections.value.length > 0) {
          filterValue.collections = selectedCollections.value;
        }
        
        if (selectedProducts.value.length > 0) {
          filterValue.products = selectedProducts.value;
        }
        
        emit('update', filterValue);
      }
      
      // Clear all filters
      function clearAll() {
        selectedCollections.value = [];
        selectedProducts.value = [];
        emit('update', {});
      }
      
      // Clear collections filter
      function clearCollections() {
        selectedCollections.value = [];
        updateFilter();
      }
      
      // Clear products filter
      function clearProducts() {
        selectedProducts.value = [];
        updateFilter();
      }
      
      return {
        selectedCollections,
        selectedProducts,
        collections,
        products,
        hasActiveFilters,
        updateCollections,
        updateProducts,
        clearAll,
        clearCollections,
        clearProducts
      };
    },
    template: `
      <div class="related-items-filter">
        <div class="filter-header">
          <div class="row items-center q-mb-md">
            <div class="col">
              <div class="text-subtitle1">Related Items</div>
            </div>
            <div class="col-auto">
              <q-btn
                flat
                round
dense
                icon="clear_all"
                color="grey-7"
                @click="clearAll"
                :disable="!hasActiveFilters"
              >
                <q-tooltip>Clear all related item filters</q-tooltip>
              </q-btn>
            </div>
          </div>
        </div>
        
        <div class="related-items-filters">
          <!-- Collections Filter -->
          <div class="q-mb-md">
            <div class="row items-center q-mb-sm">
              <div class="col">
                <div class="text-subtitle2">Collections</div>
              </div>
              <div class="col-auto">
                <q-btn
                  flat
                  round
                  dense
                  icon="clear"
                  color="grey-7"
                  @click="clearCollections"
                  :disable="selectedCollections.length === 0"
                >
                  <q-tooltip>Clear collections filter</q-tooltip>
                </q-btn>
              </div>
            </div>
            
            <q-select
              v-model="selectedCollections"
              :options="collections"
              option-label="name"
              option-value="id"
              multiple
              outlined
              dense
              use-chips
              stack-label
              use-input
              hide-selected
              fill-input
              input-debounce="300"
              label="Filter by collections"
              :class="selectedCollections.length > 0 ? 'active-filter' : ''"
              @update:model-value="updateCollections"
            >
              <template v-slot:selected-item="scope">
                <q-chip
                  removable
                  dense
                  @remove="scope.removeAtIndex(scope.index)"
                  :tabindex="scope.tabindex"
                  color="primary"
                  text-color="white"
                >
                  {{ scope.opt.name }}
                </q-chip>
              </template>
            </q-select>
          </div>
          
          <!-- Products Filter -->
          <div class="q-mb-md">
            <div class="row items-center q-mb-sm">
              <div class="col">
                <div class="text-subtitle2">Products</div>
              </div>
              <div class="col-auto">
                <q-btn
                  flat
                  round
                  dense
                  icon="clear"
                  color="grey-7"
                  @click="clearProducts"
                  :disable="selectedProducts.length === 0"
                >
                  <q-tooltip>Clear products filter</q-tooltip>
                </q-btn>
              </div>
            </div>
            
            <q-select
              v-model="selectedProducts"
              :options="products"
              option-label="name"
              option-value="id"
              multiple
              outlined
              dense
              use-chips
              stack-label
              use-input
              hide-selected
              fill-input
              input-debounce="300"
              label="Filter by products"
              :class="selectedProducts.length > 0 ? 'active-filter' : ''"
              @update:model-value="updateProducts"
            >
              <template v-slot:selected-item="scope">
                <q-chip
                  removable
                  dense
                  @remove="scope.removeAtIndex(scope.index)"
                  :tabindex="scope.tabindex"
                  color="primary"
                  text-color="white"
                >
                  {{ scope.opt.name }}
                </q-chip>
              </template>
            </q-select>
          </div>
        </div>
      </div>
    `
  }),
  tableTypes: ['templates'],
  stateExtractor: (state: any) => {
    const relatedItems: Record<string, number[]> = {};
    
    if (state.collections && state.collections.length > 0) {
      relatedItems.collections = state.collections;
    }
    
    if (state.products && state.products.length > 0) {
      relatedItems.products = state.products;
    }
    
    return relatedItems;
  },
  stateApplier: (item: any, filterValue: Record<string, number[]>) => {
    // If no filter values, return true
    if (!filterValue || Object.keys(filterValue).length === 0) {
      return true;
    }
    
    // Check collections filter
    if (filterValue.collections && filterValue.collections.length > 0) {
      // If the item doesn't have template_collections, return false
      if (!item.template_collections || !Array.isArray(item.template_collections)) {
        return false;
      }
      
      // Check if any of the selected collections are in the template's collections
      const collectionIds = item.template_collections.map((tc: any) => tc.collection_id);
      const hasMatchingCollection = filterValue.collections.some(id => collectionIds.includes(id));
      
      if (!hasMatchingCollection) {
        return false;
      }
    }
    
    // Check products filter
    if (filterValue.products && filterValue.products.length > 0) {
      // If the item doesn't have template_products, return false
      if (!item.template_products || !Array.isArray(item.template_products)) {
        return false;
      }
      
      // Check if any of the selected products are in the template's products
      const productIds = item.template_products.map((tp: any) => tp.product_id);
      const hasMatchingProduct = filterValue.products.some(id => productIds.includes(id));
      
      if (!hasMatchingProduct) {
        return false;
      }
    }
    
    // All filters passed
    return true;
  },
  clearHandler: (state: any) => {
    delete state.collections;
    delete state.products;
    return state;
  },
  serializeValue: (value: Record<string, number[]>) => {
    return value;
  },
  deserializeValue: (value: Record<string, number[]>) => {
    return value;
  }
};

// Register the filter type
export function registerRelatedItemsFilter() {
  filterRegistry.registerFilterType(relatedItemsFilterType);
}
```

### 3.3 Create a Filter Registration Module

Create a module to register all filter types in one place.

```typescript
// src/lib/filters/registerFilters.ts
import { registerPromptElementsFilter } from './specialized/PromptElementsFilter';
import { registerRelatedItemsFilter } from './specialized/RelatedItemsFilter';
import { filterRegistry } from './FilterRegistry';

// Register basic filter types
function registerBasicFilterTypes() {
  // Text filter
  filterRegistry.registerFilterType({
    id: 'text',
    name: 'Text',
    section: 'main',
    component: 'FilterControl', // This will be resolved by the FilterPanel
    tableTypes: ['*'], // All tables
    stateExtractor: (state: any) => state,
    stateApplier: (item: any, value: string) => {
      if (!value) return true;
      const itemValue = String(item).toLowerCase();
      return itemValue.includes(value.toLowerCase());
    },
    clearHandler: (state: any) => null
  });
  
  // Number filter
  filterRegistry.registerFilterType({
    id: 'number',
    name: 'Number',
    section: 'main',
    component: 'FilterControl',
    tableTypes: ['*'],
    stateExtractor: (state: any) => state,
    stateApplier: (item: any, value: number) => {
      if (value === null || value === undefined) return true;
      return Number(item) === value;
    },
    clearHandler: (state: any) => null
  });
  
  // Date filter
  filterRegistry.registerFilterType({
    id: 'date',
    name: 'Date',
    section: 'main',
    component: 'FilterControl',
    tableTypes: ['*'],
    stateExtractor: (state: any) => state,
    stateApplier: (item: any, value: any) => {
      if (!value) return true;
      
      try {
        const itemDate = new Date(item);
        if (isNaN(itemDate.getTime())) return false;
        
        if (value.from) {
          const fromDate = new Date(value.from);
          fromDate.setHours(0, 0, 0, 0);
          if (itemDate < fromDate) return false;
        }
        
        if (value.to) {
          const toDate = new Date(value.to);
          toDate.setHours(23, 59, 59, 999);
          if (itemDate > toDate) return false;
        }
        
        return true;
      } catch (err) {
        console.error('Error applying date filter:', err);
        return false;
      }
    },
    clearHandler: (state: any) => null
  });
  
  // Select filter
  filterRegistry.registerFilterType({
    id: 'select',
    name: 'Select',
    section: 'main',
    component: 'FilterControl',
    tableTypes: ['*'],
    stateExtractor: (state: any) => state,
    stateApplier: (item: any, value: any) => {
      if (!value) return true;
      return item === value;
    },
    clearHandler: (state: any) => null
  });
  
  // Multi-select filter
  filterRegistry.registerFilterType({
    id: 'multiselect',
    name: 'Multi-select',
    section: 'main',
    component: 'FilterControl',
    tableTypes: ['*'],
    stateExtractor: (state: any) => state,
    stateApplier: (item: any, values: any[]) => {
      if (!values || values.length === 0) return true;
      return values.includes(item);
    },
    clearHandler: (state: any) => null
  });
  
  // Boolean filter
  filterRegistry.registerFilterType({
    id: 'boolean',
    name: 'Boolean',
    section: 'main',
    component: 'FilterControl',
    tableTypes: ['*'],
    stateExtractor: (state: any) => state,
    stateApplier: (item: any, value: boolean) => {
      if (value === null || value === undefined) return true;
      return Boolean(item) === value;
    },
    clearHandler: (state: any) => null
  });
}

// Register all filter types
export function registerAllFilters() {
  // Register basic filter types
  registerBasicFilterTypes();
  
  // Register specialized filter types
  registerPromptElementsFilter();
  registerRelatedItemsFilter();
  
  console.log('All filter types registered:', filterRegistry.getAllFilterTypes());
}
```

### 3.4 Create a Filter Plugin

Create a Vue plugin to initialize the filter system.

```typescript
// src/plugins/filter.ts
import { App } from 'vue';
import { registerAllFilters } from '@/lib/filters/registerFilters';
import FilterPanel from '@/components/filters/FilterPanel.vue';
import FilterSection from '@/components/filters/FilterSection.vue';
import FilterControl from '@/components/filters/FilterControl.vue';
import SavedFilterSelector from '@/components/filters/SavedFilterSelector.vue';
import FilterActionBar from '@/components/filters/FilterActionBar.vue';

export default {
  install(app: App) {
    // Register components
    app.component('FilterPanel', FilterPanel);
    app.component('FilterSection', FilterSection);
    app.component('FilterControl', FilterControl);
    app.component('SavedFilterSelector', SavedFilterSelector);
    app.component('FilterActionBar', FilterActionBar);
    
    // Register all filter types
    registerAllFilters();
    
    console.log('Filter plugin installed');
  }
};
```

## Expected Outcome

After completing this step, we will have:

1. Specialized filter types for specific use cases
2. A registration system for all filter types
3. A Vue plugin to initialize the filter system
4. Custom UI components for specialized filters

These specialized filter types will allow for more complex filtering scenarios beyond simple column filters.

## Next Steps

In the next step, we'll integrate the filter system with the existing tables in the application.
