import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import chalk from 'chalk';

// Get the directory name of the current module
const __dirname = path.dirname(fileURLToPath(import.meta.url));

/**
 * Creates necessary directories
 */
function createDirectories() {
  const dirs = [
    path.join(__dirname, 'exports'),
    path.join(__dirname, 'queries')
  ];

  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(chalk.green(`Created directory: ${dir}`));
    }
  });
}

/**
 * Creates RPC functions in Supabase if they don't exist
 */
function createRpcFunctions() {
  console.log(chalk.blue('\nTo create necessary RPC functions in Supabase:'));
  console.log(chalk.white('1. Go to your Supabase dashboard'));
  console.log(chalk.white('2. Navigate to SQL Editor'));
  console.log(chalk.white('3. Create the following functions:'));

  console.log(chalk.yellow('\n-- Function to get constraints'));
  console.log(`
CREATE OR REPLACE FUNCTION get_constraints()
RETURNS TABLE (
  constraint_name text,
  constraint_type text,
  table_name text,
  column_name text,
  foreign_table_name text,
  foreign_column_name text
) LANGUAGE plpgsql AS $$
BEGIN
  RETURN QUERY
  SELECT
    tc.constraint_name::text,
    tc.constraint_type::text,
    tc.table_name::text,
    kcu.column_name::text,
    ccu.table_name::text AS foreign_table_name,
    ccu.column_name::text AS foreign_column_name
  FROM
    information_schema.table_constraints tc
    LEFT JOIN information_schema.key_column_usage kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    LEFT JOIN information_schema.constraint_column_usage ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
  WHERE
    tc.table_schema = 'public'
  ORDER BY
    tc.table_name,
    tc.constraint_name;
END;
$$;
  `);

  console.log(chalk.yellow('\n-- Function to get tables'));
  console.log(`
CREATE OR REPLACE FUNCTION get_tables()
RETURNS TABLE (
  table_name text,
  table_schema text
) LANGUAGE plpgsql AS $$
BEGIN
  RETURN QUERY
  SELECT
    tablename::text AS table_name,
    schemaname::text AS table_schema
  FROM
    pg_tables
  WHERE
    schemaname = 'public'
  ORDER BY
    tablename;
END;
$$;
  `);

  console.log(chalk.yellow('\n-- Function to get views'));
  console.log(`
CREATE OR REPLACE FUNCTION get_views()
RETURNS TABLE (
  table_name text,
  table_schema text,
  view_definition text
) LANGUAGE plpgsql AS $$
BEGIN
  RETURN QUERY
  SELECT
    viewname::text AS table_name,
    schemaname::text AS table_schema,
    definition::text AS view_definition
  FROM
    pg_views
  WHERE
    schemaname = 'public'
  ORDER BY
    viewname;
END;
$$;
  `);

  console.log(chalk.yellow('\n-- Function to get columns'));
  console.log(`
CREATE OR REPLACE FUNCTION get_columns()
RETURNS TABLE (
  table_name text,
  column_name text,
  data_type text,
  is_nullable text,
  column_default text,
  ordinal_position integer
) LANGUAGE plpgsql AS $$
BEGIN
  RETURN QUERY
  SELECT
    c.table_name::text,
    c.column_name::text,
    c.data_type::text,
    c.is_nullable::text,
    c.column_default::text,
    c.ordinal_position
  FROM
    information_schema.columns c
  WHERE
    c.table_schema = 'public'
  ORDER BY
    c.table_name,
    c.ordinal_position;
END;
$$;
  `);

  console.log(chalk.yellow('\n-- Function to get functions'));
  console.log(`
CREATE OR REPLACE FUNCTION get_functions()
RETURNS TABLE (
  routine_name text,
  routine_type text,
  data_type text,
  external_language text
) LANGUAGE plpgsql AS $$
BEGIN
  RETURN QUERY
  SELECT
    r.routine_name::text,
    r.routine_type::text,
    r.data_type::text,
    r.external_language::text
  FROM
    information_schema.routines r
  WHERE
    r.routine_schema = 'public'
  ORDER BY
    r.routine_name;
END;
$$;
  `);

  console.log(chalk.yellow('\n-- Function to get RLS policies'));
  console.log(`
CREATE OR REPLACE FUNCTION get_policies()
RETURNS TABLE (
  policyname text,
  tablename text,
  cmd text,
  roles text[],
  qual text,
  with_check text
) LANGUAGE plpgsql AS $$
BEGIN
  RETURN QUERY
  SELECT
    p.policyname::text,
    p.tablename::text,
    p.cmd::text,
    p.roles::text[],
    p.qual::text,
    p.with_check::text
  FROM
    pg_policies p
  WHERE
    p.schemaname = 'public'
  ORDER BY
    p.tablename,
    p.policyname;
END;
$$;
  `);
}

/**
 * Main function
 */
function main() {
  try {
    console.log(chalk.yellow('Setting up Enhanced Database Inspector...'));

    // Install dependencies
    console.log(chalk.blue('\nInstalling dependencies...'));
    execSync('bun install', { stdio: 'inherit', cwd: __dirname });

    // Create necessary directories
    console.log(chalk.blue('\nCreating directories...'));
    createDirectories();

    // Check if .env file exists in the parent directory
    const envPath = path.join(__dirname, '..', '.env');
    if (!fs.existsSync(envPath)) {
      console.error(chalk.red('\nError: .env file not found in the parent directory.'));
      console.error(chalk.red('Please create a .env file with your Supabase credentials:'));
      console.error(chalk.white('VITE_SUPABASE_URL=your_supabase_url'));
      console.error(chalk.white('VITE_SUPABASE_ANON_KEY=your_supabase_key'));
      process.exit(1);
    } else {
      console.log(chalk.green('\nFound .env file with Supabase credentials'));
    }

    // Create RPC functions
    createRpcFunctions();

    console.log(chalk.green('\nSetup completed successfully!'));
    console.log(chalk.blue('\nTo fetch the database structure:'));
    console.log(chalk.white('  bun start'));
    console.log(chalk.blue('\nTo generate documentation:'));
    console.log(chalk.white('  bun generate'));
    console.log(chalk.blue('\nTo browse table data:'));
    console.log(chalk.white('  bun browse'));
    console.log(chalk.blue('\nTo run queries:'));
    console.log(chalk.white('  bun query'));
  } catch (error) {
    console.error(chalk.red(`\nSetup failed: ${error.message}`));
    process.exit(1);
  }
}

// Run the main function
main();
