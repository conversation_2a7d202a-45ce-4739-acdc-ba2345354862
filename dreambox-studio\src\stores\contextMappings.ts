/**
 * Context Mappings
 *
 * Defines which components appear in which areas based on context, role, and device.
 */

/**
 * Component mapping interface
 */
export interface ComponentMapping {
  component: string;        // Component name (registered in componentRegistry)
  area: string;             // UI area (left_drawer, right_drawer, top_bar, bottom_bar, main_content)
  context: string;          // Context pattern (supports wildcards and placeholders)
  role: string;             // User role (super-admin, admin, designer, or ALL)
  device: string;           // Device type (desktop_browser, mobile_browser, desktop_electron, or ALL)
  properties: Record<string, unknown>; // Properties to pass to the component
}

/**
 * Component mappings
 */
export const componentMappings: ComponentMapping[] = [
  // Navigation components - always visible in left drawer
  {
    component: 'Navigation',
    area: 'left_drawer',
    context: 'ALL',
    role: 'super-admin',
    device: 'ALL',
    properties: {
      items: 'super_admin_navigation_items',
      expanded: true,
      initiallySelected: true
    }
  },
  {
    component: 'Navigation',
    area: 'left_drawer',
    context: 'ALL',
    role: 'admin',
    device: 'ALL',
    properties: {
      items: 'admin_navigation_items',
      expanded: true,
      initiallySelected: true
    }
  },
  {
    component: 'Navigation',
    area: 'left_drawer',
    context: 'ALL',
    role: 'designer',
    device: 'ALL',
    properties: {
      items: 'designer_navigation_items',
      expanded: true,
      initiallySelected: true
    }
  },

  // Filter panel - visible in left drawer for table and grid views
  {
    component: 'FilterPanel',
    area: 'left_drawer',
    context: '*_table.table_view',
    role: 'ALL',
    device: 'ALL',
    properties: {
      targetCollection: '[wildcard0]_table',
      initiallySelected: false
    }
  },
  {
    component: 'FilterPanel',
    area: 'left_drawer',
    context: '*_table.grid_view',
    role: 'ALL',
    device: 'ALL',
    properties: {
      targetCollection: '[wildcard0]_table',
      initiallySelected: false
    }
  },

  // Prompt elements panel - disabled for now as editing is done directly in the builder
  // {
  //   component: 'PromptElementsPanel',
  //   area: 'left_drawer',
  //   context: 'templates_table.*.elements',
  //   role: 'ALL',
  //   device: 'ALL',
  //   properties: {
  //     targetCollection: 'prompt_elements',
  //     initiallySelected: true
  //   }
  // },

  // Element values editor - visible in right drawer for template builder
  {
    component: 'ElementValuesEditor',
    area: 'right_drawer',
    context: 'templates_table.*.values',
    role: 'ALL',
    device: 'ALL',
    properties: {
      templateId: '[templateId]',
      elementId: '[elementId]'
    }
  },

  // Template preview - visible in right drawer for template builder
  {
    component: 'TemplatePreview',
    area: 'right_drawer',
    context: 'templates_table.*',
    role: 'ALL',
    device: 'ALL',
    properties: {
      templateId: '[templateId]'
    }
  },

  // Chat assistant - visible in right drawer for all contexts
  {
    component: 'ChatAssistant',
    area: 'right_drawer',
    context: 'ALL',
    role: 'ALL',
    device: 'ALL',
    properties: {
      contextAware: true
    }
  },

  // Search bar - visible in top bar for table and grid views
  {
    component: 'SearchBar',
    area: 'top_bar',
    context: '*_table.table_view',
    role: 'ALL',
    device: 'ALL',
    properties: {
      targetCollection: '[wildcard0]_table',
      placeholder: 'Search [wildcard0]...'
    }
  },
  {
    component: 'SearchBar',
    area: 'top_bar',
    context: '*_table.grid_view',
    role: 'ALL',
    device: 'ALL',
    properties: {
      targetCollection: '[wildcard0]_table',
      placeholder: 'Search [wildcard0]...'
    }
  },

  // View toggle has been removed from top bar and is now used directly in the Templates table view

  // Action buttons container has been removed from bottom bar
  // and is now only in the expanded area

  // Action buttons expanded - visible in expanded area for all devices
  {
    component: 'ActionButtonsExpanded',
    area: 'expanded',
    context: 'ALL',
    role: 'ALL',
    device: 'ALL',
    properties: {}
  },

  // Individual action buttons - these will be used by the ActionButtonsContainer
  {
    component: 'ActionButton',
    area: 'bottom_bar',
    context: 'templates_table.table_view',
    role: 'ALL',
    device: 'ALL',
    properties: {
      id: 'new-template',
      label: 'New Template',
      icon: 'add',
      color: 'primary',
      action: () => {
        document.dispatchEvent(new CustomEvent('template-new-click'));
      }
    }
  },
  {
    component: 'ActionButton',
    area: 'bottom_bar',
    context: 'templates_table.grid_view',
    role: 'ALL',
    device: 'ALL',
    properties: {
      id: 'new-template',
      label: 'New Template',
      icon: 'add',
      color: 'primary',
      action: () => {
        document.dispatchEvent(new CustomEvent('template-new-click'));
      }
    }
  },
  {
    component: 'ActionButton',
    area: 'bottom_bar',
    context: 'templates_table.builder',
    role: 'ALL',
    device: 'ALL',
    properties: {
      id: 'save-template',
      label: 'Save',
      icon: 'save',
      color: 'primary',
      action: 'saveTemplate'
    }
  },
  {
    component: 'ActionButton',
    area: 'bottom_bar',
    context: 'templates_table.builder',
    role: 'ALL',
    device: 'ALL',
    properties: {
      id: 'preview-template',
      label: 'Preview',
      icon: 'visibility',
      color: 'secondary',
      action: 'previewTemplate'
    }
  }
];
