# Technology Stack Reference

## Frontend

### Core Framework
- **Quasar Framework** v2.x
  - Based on Vue 3
  - Component library
  - Build system for PWA and Electron

### JavaScript/TypeScript
- **Vue.js** v3.x
  - Composition API
  - Script setup syntax
- **TypeScript** v4.x
  - Type safety
  - Interface definitions
- **Pinia** v2.x
  - State management
  - Modular stores

### UI/UX
- **Quasar UI Components**
  - Material Design implementation
  - Responsive layouts
- **SCSS/Sass**
  - Custom theming
  - Component styling
- **Tailwind CSS**
  - Utility-first CSS

### Build Tools
- **Vite**
  - Fast development server
  - Optimized builds
- **Electron Builder**
  - Desktop application packaging
  - Auto-updates

## Backend

### Database
- **PostgreSQL** v14.x
  - Relational database
  - JSON support
  - Row-Level Security

### Backend as a Service
- **Supabase**
  - Auto-generated APIs
  - Authentication
  - Storage
  - Realtime subscriptions
  - Edge Functions (Deno)

### Workflow Automation
- **n8n**
  - Workflow editor
  - API integrations
  - Scheduled tasks
  - Webhook processing

### Image Generation
- **SwarmUI**
  - AI image generation interface
  - Model management
- **Stable Diffusion**
  - AI image generation models
  - ControlNet extensions

## Infrastructure

### Development
- **Git**
  - Version control
  - Feature branching

### Deployment
- **Netlify**
  - PWA hosting
  - Continuous deployment
- **Servers**
  - Self-hosted Supabase
  - n8n deployment
  - Image generation server

### Monitoring (future)
- **Prometheus**
  - Metrics collection
  - Performance monitoring
- **Grafana**
  - Visualization
  - Dashboards

## Testing

### Frontend Testing
- **Vitest**
  - Unit testing
  - Component testing
- **Cypress**
  - End-to-end testing
  - Integration testing

### Backend Testing
- **pgTAP**
  - Database testing
  - SQL unit tests
- **Postman/Newman**
  - API testing
  - Automated test runs

## DevOps

### CI/CD
- **GitHub Actions**
  - Continuous integration
  - Automated testing
  - Deployment pipelines

### Infrastructure as Code
- **Terraform**
  - Infrastructure provisioning
  - Environment configuration
