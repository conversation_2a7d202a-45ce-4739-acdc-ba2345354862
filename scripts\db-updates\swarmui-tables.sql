-- SwarmUI Server Management Tables

-- Server instances table
CREATE TABLE IF NOT EXISTS swarmui_servers (
  id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  pod_id TEXT NOT NULL,
  api_endpoint TEXT NOT NULL,
  api_key TEXT NOT NULL,
  status TEXT NOT NULL,
  gpu_type TEXT NOT NULL,
  started_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  stopped_at TIMESTAMP WITH TIME ZONE,
  last_activity TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Add RLS policies for swarmui_servers
ALTER TABLE swarmui_servers ENABLE ROW LEVEL SECURITY;

-- Policy for users to view only their own servers
CREATE POLICY "Users can view their own servers"
  ON swarmui_servers
  FOR SELECT
  USING (auth.uid() = user_id);

-- Policy for users to insert their own servers
CREATE POLICY "Users can insert their own servers"
  ON swarmui_servers
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Policy for users to update their own servers
CREATE POLICY "Users can update their own servers"
  ON swarmui_servers
  FOR UPDATE
  USING (auth.uid() = user_id);

-- Policy for users to delete their own servers
CREATE POLICY "Users can delete their own servers"
  ON swarmui_servers
  FOR DELETE
  USING (auth.uid() = user_id);

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update the updated_at timestamp
CREATE TRIGGER update_swarmui_servers_updated_at
BEFORE UPDATE ON swarmui_servers
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Function to auto-shutdown inactive servers
CREATE OR REPLACE FUNCTION auto_shutdown_inactive_servers()
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
  v_shutdown_count INTEGER := 0;
  v_inactive_threshold INTERVAL := INTERVAL '30 minutes';
  v_server RECORD;
BEGIN
  FOR v_server IN
    SELECT * FROM swarmui_servers
    WHERE status = 'running'
    AND NOW() - last_activity > v_inactive_threshold
  LOOP
    -- Update the status to stopping
    UPDATE swarmui_servers
    SET status = 'stopping', stopped_at = NOW(), updated_at = NOW()
    WHERE id = v_server.id;
    
    v_shutdown_count := v_shutdown_count + 1;
  END LOOP;
  
  RETURN v_shutdown_count;
END;
$$;

-- Create a cron job to run the auto-shutdown function
-- Note: This requires the pg_cron extension to be enabled
-- If pg_cron is not available, you'll need to set up an external scheduler
-- or implement this in your application logic
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_extension WHERE extname = 'pg_cron'
  ) THEN
    PERFORM cron.schedule(
      'auto-shutdown-inactive-servers',
      '*/5 * * * *',  -- Run every 5 minutes
      $$SELECT auto_shutdown_inactive_servers()$$
    );
  END IF;
END
$$;
