# Step 10: API Documentation

## Overview

This document provides detailed API documentation for the new filter engine components. It serves as a reference for developers implementing and using the filter engine.

## Core Components

### FilterRegistry

The FilterRegistry is a singleton class that manages filter type definitions.

#### Interfaces

```typescript
interface FilterTypeDefinition {
  id: string;                                      // Unique identifier for the filter type
  name: string;                                    // Display name
  section: 'main' | 'expanded' | 'specialized';    // Section where the filter appears
  component: any;                                  // Vue component for rendering the filter
  tableTypes: string[];                            // Tables this filter type applies to
  stateExtractor: (state: any) => any;            // Extract filter state from global state
  stateApplier: (item: any, filterValue: any) => boolean; // Apply filter to an item
  clearHandler: (state: any) => any;              // Clear the filter state
  serializeValue?: (value: any) => any;           // Optional: Serialize filter value for storage
  deserializeValue?: (value: any) => any;         // Optional: Deserialize filter value from storage
}
```

#### Methods

```typescript
// Get the singleton instance
static getInstance(): FilterRegistry

// Register a new filter type
registerFilterType(definition: FilterTypeDefinition): void

// Get filter types applicable to a specific table
getFilterTypesForTable(tableName: string): FilterTypeDefinition[]

// Get a specific filter type by ID
getFilterType(id: string): FilterTypeDefinition | undefined

// Get all registered filter types
getAllFilterTypes(): FilterTypeDefinition[]
```

#### Usage Example

```typescript
import { filterRegistry } from '@/lib/filters/FilterRegistry';

// Register a text filter type
filterRegistry.registerFilterType({
  id: 'text',
  name: 'Text',
  section: 'main',
  component: TextFilterComponent,
  tableTypes: ['*'], // All tables
  stateExtractor: (state) => state,
  stateApplier: (item, value) => {
    if (!value) return true;
    return String(item).toLowerCase().includes(String(value).toLowerCase());
  },
  clearHandler: () => null
});

// Get filter types for a table
const templateFilters = filterRegistry.getFilterTypesForTable('templates');
```

### FilterEventBus

The FilterEventBus is a singleton class that manages filter-related events.

#### Constants

```typescript
// Event types
static readonly EVENTS = {
  FILTER_CHANGED: 'filter:changed',
  FILTER_CLEARED: 'filter:cleared',
  FILTER_SAVED: 'filter:saved',
  FILTER_LOADED: 'filter:loaded',
  FILTER_DELETED: 'filter:deleted',
  SPECIALIZED_FILTER_CHANGED: 'specialized-filter:changed',
  SPECIALIZED_FILTER_CLEARED: 'specialized-filter:cleared'
};
```

#### Methods

```typescript
// Get the singleton instance
static getInstance(): FilterEventBus

// Subscribe to an event
on(event: string, callback: (payload: any) => void): void

// Unsubscribe from an event
off(event: string, callback: (payload: any) => void): void

// Emit an event
emit(event: string, payload: any): void
```

#### Usage Example

```typescript
import { filterEventBus } from '@/lib/filters/FilterEventBus';

// Subscribe to filter changed event
filterEventBus.on(FilterEventBus.EVENTS.FILTER_CHANGED, (payload) => {
  console.log('Filter changed:', payload);
  // Update UI or data based on the filter change
});

// Emit a filter changed event
filterEventBus.emit(FilterEventBus.EVENTS.FILTER_CHANGED, {
  filterId: 'name',
  value: 'search term',
  tableName: 'templates'
});
```

### FilterStore

The FilterStore is a Pinia store that manages filter state.

#### State Interface

```typescript
interface FilterDefinition {
  id: string;
  name: string;
  field: string;
  filterType: string;
  section: 'main' | 'expanded';
  order: number;
  visible: boolean;
  filterValue: any;
  filterOptions?: Array<{ label: string; value: any }>;
}

interface SavedFilter {
  id: number;
  user_id: number;
  name: string;
  description: string | null;
  table_name: string;
  context: string;
  configuration: {
    columns: Array<{
      id: string;
      visible?: boolean;
      section?: 'main' | 'expanded';
      order?: number;
      filterValue?: any;
    }>;
    specializedFilters?: Record<string, any>;
  };
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

interface FilterState {
  currentTable: string | null;
  currentFilterId: number | null;
  savedFilters: SavedFilter[];
  columns: FilterDefinition[];
  activeFilters: Record<string, any>;
  specializedFilters: Record<string, any>;
  isLoading: boolean;
  hasUnsavedChanges: boolean;
}
```

#### Getters

```typescript
// Get filters for the current table
filtersByTable: (state) => SavedFilter[]

// Get the current filter
currentFilter: (state) => SavedFilter | undefined

// Get main table columns
mainTableColumns: (state) => FilterDefinition[]

// Get expanded view columns
expandedViewColumns: (state) => FilterDefinition[]

// Get specialized filter types for the current table
specializedFilterTypes: (state) => FilterTypeDefinition[]
```

#### Actions

```typescript
// Set the current table
async setTable(tableName: string): Promise<void>

// Load saved filters for the current user
async loadSavedFilters(): Promise<void>

// Save the current filter
async saveCurrentFilter(name: string, description?: string, makeDefault?: boolean): Promise<boolean>

// Load a saved filter
loadFilter(filterId: number): boolean

// Delete a saved filter
async deleteFilter(filterId: number): Promise<boolean>

// Update the default filter
async updateDefaultFilter(filterId: number): Promise<boolean>

// Set a column filter
setColumnFilter(columnId: string, value: any): void

// Set a specialized filter
setSpecializedFilter(filterId: string, value: any): void

// Clear a column filter
clearFilter(columnId: string): void

// Clear a specialized filter
clearSpecializedFilter(filterId: string): void

// Clear all filters
clearAllFilters(): void

// Toggle column visibility
toggleColumnVisibility(columnId: string, visible: boolean): void

// Update column order
updateColumnOrder(columnId: string, section: 'main' | 'expanded', order: number): void

// Move column to a different section
moveColumnToSection(columnId: string, section: 'main' | 'expanded'): void

// Mark that there are unsaved changes
markUnsavedChanges(): void

// Dismiss unsaved changes warning
dismissUnsavedChanges(): void
```

#### Usage Example

```typescript
import { useFilterStore } from '@/stores/filterStore';

// In a Vue component
const filterStore = useFilterStore();

// Set the current table
await filterStore.setTable('templates');

// Apply a filter
filterStore.setColumnFilter('name', 'search term');

// Apply a specialized filter
filterStore.setSpecializedFilter('promptElements', {
  element_angle: [1, 2, 3]
});

// Save the current filter
await filterStore.saveCurrentFilter('My Filter', 'A description of my filter', true);

// Load a saved filter
filterStore.loadFilter(123);

// Clear all filters
filterStore.clearAllFilters();
```

### FilterEngine

The FilterEngine is a singleton class that applies filters to data.

#### Methods

```typescript
// Get the singleton instance
static getInstance(): FilterEngine

// Apply filters to data
applyFilters(
  data: any[],
  tableName: string,
  activeFilters: Record<string, any>,
  specializedFilters: Record<string, any> = {}
): any[]
```

#### Usage Example

```typescript
import { filterEngine } from '@/lib/filters/FilterEngine';
import { useFilterStore } from '@/stores/filterStore';

// In a Vue component
const filterStore = useFilterStore();
const data = [/* array of items */];

// Apply filters to data
const filteredData = filterEngine.applyFilters(
  data,
  'templates',
  filterStore.activeFilters,
  filterStore.specializedFilters
);
```

## Component APIs

### FilterPanel

The FilterPanel is the main container for all filter-related UI elements.

#### Props

None

#### Events

None (uses FilterEventBus for communication)

#### Slots

None

#### Usage Example

```vue
<template>
  <div class="left-drawer">
    <filter-panel />
  </div>
</template>

<script setup>
import FilterPanel from '@/components/filters/FilterPanel.vue';
</script>
```

### SavedFilterSelector

The SavedFilterSelector allows users to select, save, and manage their filters.

#### Props

```typescript
interface SavedFilterSelectorProps {
  filters: SavedFilter[];
  currentFilterId: number | null;
  hasUnsavedChanges: boolean;
  loading?: boolean;
}
```

#### Events

```typescript
interface SavedFilterSelectorEvents {
  (e: 'select', filterId: number): void;
  (e: 'save'): void;
  (e: 'delete', filterId: number): void;
  (e: 'clear'): void;
}
```

#### Slots

None

#### Usage Example

```vue
<template>
  <saved-filter-selector
    :filters="filterStore.filtersByTable"
    :current-filter-id="filterStore.currentFilterId"
    :has-unsaved-changes="filterStore.hasUnsavedChanges"
    @select="loadFilter"
    @save="showSaveDialog"
    @delete="deleteFilter"
    @clear="clearAllFilters"
  />
</template>

<script setup>
import SavedFilterSelector from '@/components/filters/SavedFilterSelector.vue';
import { useFilterStore } from '@/stores/filterStore';

const filterStore = useFilterStore();

function loadFilter(filterId) {
  filterStore.loadFilter(filterId);
}

function showSaveDialog() {
  // Show save dialog
}

function deleteFilter(filterId) {
  filterStore.deleteFilter(filterId);
}

function clearAllFilters() {
  filterStore.clearAllFilters();
}
</script>
```

### FilterSection

The FilterSection contains a group of related filters.

#### Props

```typescript
interface FilterSectionProps {
  title: string;
  columns: FilterDefinition[];
  collapsible?: boolean;
  defaultCollapsed?: boolean;
}
```

#### Events

```typescript
interface FilterSectionEvents {
  (e: 'update-filter', columnId: string, value: any): void;
  (e: 'clear-filter', columnId: string): void;
  (e: 'toggle-visibility', columnId: string, visible: boolean): void;
}
```

#### Slots

None

#### Usage Example

```vue
<template>
  <filter-section
    title="Main Filters"
    :columns="filterStore.mainTableColumns"
    @update-filter="updateFilter"
    @clear-filter="clearFilter"
    @toggle-visibility="toggleVisibility"
  />
</template>

<script setup>
import FilterSection from '@/components/filters/FilterSection.vue';
import { useFilterStore } from '@/stores/filterStore';

const filterStore = useFilterStore();

function updateFilter(columnId, value) {
  filterStore.setColumnFilter(columnId, value);
}

function clearFilter(columnId) {
  filterStore.clearFilter(columnId);
}

function toggleVisibility(columnId, visible) {
  filterStore.toggleColumnVisibility(columnId, visible);
}
</script>
```

### FilterControl

The FilterControl handles individual filter controls based on their type.

#### Props

```typescript
interface FilterControlProps {
  column: FilterDefinition;
}
```

#### Events

```typescript
interface FilterControlEvents {
  (e: 'update', value: any): void;
  (e: 'clear'): void;
  (e: 'toggle-visibility', visible: boolean): void;
}
```

#### Slots

None

#### Usage Example

```vue
<template>
  <filter-control
    :column="column"
    @update="updateFilter"
    @clear="clearFilter"
    @toggle-visibility="toggleVisibility"
  />
</template>

<script setup>
import FilterControl from '@/components/filters/FilterControl.vue';

const props = defineProps({
  column: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update', 'clear', 'toggle-visibility']);

function updateFilter(value) {
  emit('update', value);
}

function clearFilter() {
  emit('clear');
}

function toggleVisibility(visible) {
  emit('toggle-visibility', visible);
}
</script>
```

### FilterActionBar

The FilterActionBar provides actions for saving and clearing filters.

#### Props

```typescript
interface FilterActionBarProps {
  hasUnsavedChanges: boolean;
  hasActiveFilters: boolean;
}
```

#### Events

```typescript
interface FilterActionBarEvents {
  (e: 'save'): void;
  (e: 'clear'): void;
}
```

#### Slots

None

#### Usage Example

```vue
<template>
  <filter-action-bar
    :has-unsaved-changes="filterStore.hasUnsavedChanges"
    :has-active-filters="hasActiveFilters"
    @save="showSaveDialog"
    @clear="clearAllFilters"
  />
</template>

<script setup>
import FilterActionBar from '@/components/filters/FilterActionBar.vue';
import { useFilterStore } from '@/stores/filterStore';
import { computed } from 'vue';

const filterStore = useFilterStore();

const hasActiveFilters = computed(() => {
  return Object.keys(filterStore.activeFilters).length > 0 || 
         Object.keys(filterStore.specializedFilters).length > 0;
});

function showSaveDialog() {
  // Show save dialog
}

function clearAllFilters() {
  filterStore.clearAllFilters();
}
</script>
```

## Composables

### useFilter

The useFilter composable provides a convenient way to use the filter system in components.

#### Parameters

```typescript
function useFilter<T>(
  tableName: string,
  data: T[] | Ref<T[]>
): {
  filteredData: ComputedRef<T[]>;
  visibleColumns: ComputedRef<FilterDefinition[]>;
  expandedColumns: ComputedRef<FilterDefinition[]>;
  specializedFilterTypes: ComputedRef<FilterTypeDefinition[]>;
  isInitialized: Ref<boolean>;
  activeFilters: ComputedRef<Record<string, any>>;
  specializedFilters: ComputedRef<Record<string, any>>;
  currentFilterId: ComputedRef<number | null>;
  hasUnsavedChanges: ComputedRef<boolean>;
}
```

#### Usage Example

```vue
<template>
  <div>
    <q-table
      :rows="filteredData"
      :columns="visibleColumns"
      row-key="id"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useFilter } from '@/composables/useFilter';

// Raw data
const templates = ref([/* array of templates */]);

// Use the filter composable
const {
  filteredData,
  visibleColumns,
  expandedColumns,
  isInitialized
} = useFilter('templates', templates);
</script>
```

## Specialized Filter Types

### PromptElementsFilter

The PromptElementsFilter allows filtering templates based on their prompt elements.

#### Filter Type Definition

```typescript
const promptElementsFilterType: FilterTypeDefinition = {
  id: 'promptElements',
  name: 'Prompt Elements',
  section: 'specialized',
  component: PromptElementsFilterComponent,
  tableTypes: ['templates'],
  stateExtractor: (state) => {
    // Extract prompt element filters from state
    const elements = {};
    for (const key in state) {
      if (key.startsWith('element_')) {
        elements[key] = state[key];
      }
    }
    return elements;
  },
  stateApplier: (item, filterValue) => {
    // Apply prompt element filters to an item
    // Return true if the item matches the filter, false otherwise
  },
  clearHandler: (state) => {
    // Clear prompt element filters from state
    for (const key in state) {
      if (key.startsWith('element_')) {
        delete state[key];
      }
    }
    return state;
  },
  serializeValue: (value) => value,
  deserializeValue: (value) => value
};
```

#### Component Props

```typescript
interface PromptElementsFilterProps {
  filterValue: Record<string, number[]>;
}
```

#### Component Events

```typescript
interface PromptElementsFilterEvents {
  (e: 'update', value: Record<string, number[]>): void;
  (e: 'clear'): void;
}
```

### RelatedItemsFilter

The RelatedItemsFilter allows filtering templates based on related items like collections, products, etc.

#### Filter Type Definition

```typescript
const relatedItemsFilterType: FilterTypeDefinition = {
  id: 'relatedItems',
  name: 'Related Items',
  section: 'specialized',
  component: RelatedItemsFilterComponent,
  tableTypes: ['templates'],
  stateExtractor: (state) => {
    // Extract related items filters from state
    const relatedItems = {};
    if (state.collections) {
      relatedItems.collections = state.collections;
    }
    if (state.products) {
      relatedItems.products = state.products;
    }
    return relatedItems;
  },
  stateApplier: (item, filterValue) => {
    // Apply related items filters to an item
    // Return true if the item matches the filter, false otherwise
  },
  clearHandler: (state) => {
    // Clear related items filters from state
    delete state.collections;
    delete state.products;
    return state;
  },
  serializeValue: (value) => value,
  deserializeValue: (value) => value
};
```

#### Component Props

```typescript
interface RelatedItemsFilterProps {
  filterValue: {
    collections?: number[];
    products?: number[];
  };
}
```

#### Component Events

```typescript
interface RelatedItemsFilterEvents {
  (e: 'update', value: { collections?: number[]; products?: number[] }): void;
  (e: 'clear'): void;
}
```

## Integration APIs

### Feature Flag System

The Feature Flag System controls feature availability.

#### Constants

```typescript
const FEATURES = {
  NEW_FILTER_ENGINE: 'new-filter-engine'
};
```

#### Methods

```typescript
// Get the singleton instance
static getInstance(): FeatureFlags

// Check if a feature is enabled
isEnabled(feature: string): boolean

// Enable a feature
enable(feature: string): void

// Disable a feature
disable(feature: string): void
```

#### Usage Example

```typescript
import { featureFlags, FEATURES } from '@/config/features';

// Check if the new filter engine is enabled
if (featureFlags.isEnabled(FEATURES.NEW_FILTER_ENGINE)) {
  // Use new filter engine
} else {
  // Use old filter engine
}

// Enable the new filter engine
featureFlags.enable(FEATURES.NEW_FILTER_ENGINE);

// Disable the new filter engine
featureFlags.disable(FEATURES.NEW_FILTER_ENGINE);
```

### Filter Format Converter

The Filter Format Converter converts between old and new filter formats.

#### Methods

```typescript
// Convert from old filter format to new filter format
static oldToNew(oldFilter: OldSavedFilter): NewSavedFilter

// Convert from new filter format to old filter format
static newToOld(newFilter: NewSavedFilter): OldSavedFilter

// Convert active filters from old format to new format
static convertActiveFilters(
  oldActiveFilters: Record<string, any>,
  oldCombinedFilters: Record<string, any>
): {
  activeFilters: Record<string, any>;
  specializedFilters: Record<string, any>;
}
```

#### Usage Example

```typescript
import { FilterFormatConverter } from '@/lib/filters/migration/FilterFormatConverter';

// Convert an old filter to the new format
const oldFilter = {/* old filter format */};
const newFilter = FilterFormatConverter.oldToNew(oldFilter);

// Convert active filters
const oldActiveFilters = {/* old active filters */};
const oldCombinedFilters = {/* old combined filters */};
const { activeFilters, specializedFilters } = FilterFormatConverter.convertActiveFilters(
  oldActiveFilters,
  oldCombinedFilters
);
```

### Server-Side Filtering

#### SQL Function Parameters

```sql
-- filter_templates function parameters
filter_templates(
  search TEXT DEFAULT NULL,
  status TEXT[] DEFAULT NULL,
  created_from TIMESTAMP DEFAULT NULL,
  created_to TIMESTAMP DEFAULT NULL,
  -- Other standard filters
  prompt_elements JSONB DEFAULT NULL, -- For specialized filters
  related_items JSONB DEFAULT NULL,   -- For specialized filters
  sort_by TEXT DEFAULT 'updated_at',
  sort_desc BOOLEAN DEFAULT TRUE,
  limit_val INTEGER DEFAULT 10,
  offset_val INTEGER DEFAULT 0
)
```

#### Client-Side Adapter Methods

```typescript
// Convert filter store state to API parameters
convertToApiParams(
  tableName: string,
  activeFilters: Record<string, any>,
  specializedFilters: Record<string, any>,
  pagination: { page: number; rowsPerPage: number },
  sorting: { sortBy: string; descending: boolean }
): Record<string, any>
```

#### Usage Example

```typescript
import { ServerFilterAdapter } from '@/lib/filters/ServerFilterAdapter';
import { useFilterStore } from '@/stores/filterStore';
import { supabase } from '@/boot/supabase';

// In a Vue component
const filterStore = useFilterStore();
const adapter = new ServerFilterAdapter();

// Convert filter state to API parameters
const params = adapter.convertToApiParams(
  'templates',
  filterStore.activeFilters,
  filterStore.specializedFilters,
  { page: 1, rowsPerPage: 10 },
  { sortBy: 'updated_at', descending: true }
);

// Use with Supabase
const { data, error } = await supabase.rpc('filter_templates', params);
```

## Conclusion

This API documentation provides a comprehensive reference for the new filter engine components. It covers the core components, UI components, composables, specialized filter types, and integration APIs.

By following this documentation, developers can implement and use the filter engine consistently across the application. The detailed interface definitions and usage examples should make it easier to understand how the components work together and how to use them effectively.
