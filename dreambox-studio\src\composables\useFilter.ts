/**
 * useFilter.ts
 *
 * This composable provides an easy way to integrate the filter engine with components.
 * It handles filter initialization, application, and state management.
 */

import { ref, computed } from 'vue';
import type { Ref } from 'vue';
import { useFilterStore } from '../stores/filterStore';
import { filterEngine } from '../lib/filters/FilterEngine';
import type { ColumnDefinition } from '../stores/filterStore';

/**
 * Hook for using filters in components
 *
 * @param tableName Name of the table to filter
 * @param data Data to filter (array or ref)
 * @param columns Optional column definitions
 * @returns Filter-related state and methods
 */
export function useFilter<T>(
  tableName: string,
  data: T[] | Ref<T[]>,
  columns?: ColumnDefinition[]
) {
  const filterStore = useFilterStore();
  const isInitialized = ref(false);
  const isLoading = ref(false);

  /**
   * Initialize the filter store with the table name and columns
   */
  async function initialize() {
    if (isInitialized.value) return;

    isLoading.value = true;

    try {
      // Set the current table
      await filterStore.setTable(tableName);

      // Set columns if provided
      if (columns) {
        filterStore.setColumns(columns);
      }

      isInitialized.value = true;
    } catch (err) {
      console.error('Error initializing filters:', err);
    } finally {
      isLoading.value = false;
    }
  }

  // Call initialize immediately
  void initialize();

  /**
   * Filtered data computed property
   */
  const filteredData = computed(() => {
    if (!isInitialized.value) {
      return Array.isArray(data) ? data : data.value;
    }

    const dataValue = Array.isArray(data) ? data : data.value;

    return filterEngine.applyFilters(
      dataValue,
      tableName,
      filterStore.activeFilters,
      filterStore.specializedFilters
    );
  });

  /**
   * Visible columns computed property
   */
  const visibleColumns = computed(() => {
    return filterStore.columns
      .filter(col => col.visible && col.section === 'main')
      .sort((a, b) => a.order - b.order);
  });

  /**
   * Expanded columns computed property
   */
  const expandedColumns = computed(() => {
    return filterStore.columns
      .filter(col => col.visible && col.section === 'expanded')
      .sort((a, b) => a.order - b.order);
  });

  /**
   * Load a saved filter
   */
  function loadFilter(filterId: number) {
    isLoading.value = true;

    try {
      // This is not an async function, so don't await it
      filterStore.loadFilter(filterId);
    } catch (err) {
      console.error('Error loading filter:', err);
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * Save the current filter
   */
  async function saveFilter(name: string, description?: string, makeDefault?: boolean) {
    isLoading.value = true;

    try {
      return await filterStore.saveCurrentFilter(name, description || null, makeDefault || false);
    } catch (err) {
      console.error('Error saving filter:', err);
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * Clear all filters
   */
  function clearFilters() {
    filterStore.clearAllFilters();
  }

  /**
   * Update a filter value
   */
  function updateFilter(columnId: string, value: unknown) {
    filterStore.updateFilter(columnId, value);
  }

  /**
   * Update a specialized filter
   */
  function updateSpecializedFilter(filterId: string, value: unknown) {
    filterStore.updateSpecializedFilter(filterId, value);
  }

  return {
    // State
    isInitialized,
    isLoading,

    // Computed
    filteredData,
    visibleColumns,
    expandedColumns,

    // Store access
    store: filterStore,

    // Methods
    initialize,
    loadFilter,
    saveFilter,
    clearFilters,
    updateFilter,
    updateSpecializedFilter
  };
}
