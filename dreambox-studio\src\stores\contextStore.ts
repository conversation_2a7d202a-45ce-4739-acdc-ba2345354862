/**
 * contextStore.ts
 *
 * This store manages the application context.
 * It tracks the current context and provides methods for context-related operations.
 */

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { contextManager } from './contextManager';
import type { DeviceTypeValue } from 'src/types/deviceTypes';

/**
 * Context store definition
 */
export const useContextStore = defineStore('context', () => {
  // State
  const currentContext = ref<string | null>(null);
  const contextHistory = ref<string[]>([]);

  // Getters
  const hasContext = computed(() => currentContext.value !== null);

  // Actions
  /**
   * Set the current context
   */
  function setContext(context: string, subContext: string | null = null, data: Record<string, unknown> = {}) {
    // Add to history if different
    if (currentContext.value !== context) {
      // Add to history
      if (currentContext.value) {
        contextHistory.value.push(currentContext.value);
      }

      // Limit history size
      if (contextHistory.value.length > 10) {
        contextHistory.value.shift();
      }
    }

    currentContext.value = context;

    // Update context manager
    contextManager.setContext(context, subContext, data);
  }

  /**
   * Clear the current context
   */
  function clearContext() {
    currentContext.value = null;
    contextManager.setContext('default');
  }

  /**
   * Go back to the previous context
   */
  function goBack() {
    if (contextHistory.value.length > 0) {
      const previousContext = contextHistory.value.pop();
      currentContext.value = previousContext || null;

      if (previousContext) {
        contextManager.setContext(previousContext);
      }

      return true;
    }

    return false;
  }

  /**
   * Associate a filter with a context
   */
  function associateFilterWithContext(filterId: number, context: string, isDefault: boolean = false) {
    // TODO: Implement this when we have the context-filter mapping table
    console.log(`Associating filter ${filterId} with context ${context}, default: ${isDefault}`);
    return Promise.resolve(true);
  }

  /**
   * Get the current device type
   */
  function getCurrentDeviceType(): DeviceTypeValue {
    return contextManager.getCurrentDeviceType();
  }

  /**
   * Set the device type
   */
  function setDeviceType(type: DeviceTypeValue) {
    contextManager.setDeviceType(type);
  }

  return {
    // State
    currentContext,
    contextHistory,

    // Getters
    hasContext,

    // Context manager properties
    leftDrawerComponents: contextManager.leftDrawerComponents,
    rightDrawerComponents: contextManager.rightDrawerComponents,
    topBarComponents: contextManager.topBarComponents,
    expandedComponents: contextManager.expandedComponents,
    leftDrawerTabs: contextManager.leftDrawerTabs,
    rightDrawerTabs: contextManager.rightDrawerTabs,
    contextData: contextManager.contextData,
    currentSubContext: contextManager.currentSubContext,

    // Actions
    setContext,
    clearContext,
    goBack,
    associateFilterWithContext,
    getCurrentDeviceType,
    setDeviceType
  };
});
