<template>
  <q-page class="q-pa-md">
    <div class="column q-gutter-y-md">
      <div class="text-center">
        <h1 class="text-h4">Administrator Dashboard</h1>
        <p class="text-subtitle1">
          Welcome to the Administrator interface
        </p>
        <p v-if="authStore.state.user?.currentCompany">
          Company: {{ authStore.state.user.currentCompany.name }}
        </p>
        <div class="row justify-center q-gutter-md q-mb-xl">
          <q-btn color="primary" label="Designer Mode" @click="switchToDesigner" v-if="hasDesignerRole" />
          <q-btn color="negative" label="Logout" @click="handleLogout" />
        </div>
      </div>

      <!-- ComfyUI Server Controls Section -->
      <div class="q-mt-xl">
        <server-controls />
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { useAuthStore } from 'src/stores/auth'
import { useRouter } from 'vue-router'
import { computed } from 'vue'
import ServerControls from 'src/components/comfyUI/ServerControls.vue'

const authStore = useAuthStore()
const router = useRouter()

const hasDesignerRole = computed(() =>
  authStore.state.user?.roles.includes('designer')
)

async function switchToDesigner() {
  if (authStore.state.user) {
    authStore.setCurrentRole('designer')
    try {
      await router.push('/designer')
    } catch (error) {
      console.error('Navigation error:', error)
    }
  }
}

async function handleLogout() {
  await authStore.logout()
  try {
    await router.push('/auth/login')
  } catch (error) {
    console.error('Navigation error:', error)
  }
}
</script>
