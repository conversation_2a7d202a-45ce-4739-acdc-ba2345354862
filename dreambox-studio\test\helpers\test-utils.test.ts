import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { createFresh<PERSON><PERSON>, mockLocalStorage, setupMockLocalStorage, mockConsole } from './test-utils';
import { getActivePinia } from 'pinia';

describe('Test Utilities', () => {
  describe('createFreshPinia', () => {
    it('creates and activates a new Pinia instance', () => {
      // Make sure there's no active Pinia before the test
      const beforePinia = getActivePinia();
      
      // Create a fresh Pinia
      const pinia = createFreshPinia();
      
      // Get the active Pinia after creation
      const afterPinia = getActivePinia();
      
      // Verify that a Pinia instance was created and activated
      expect(pinia).toBeDefined();
      expect(afterPinia).toBeDefined();
      expect(afterPinia).toBe(pinia);
      
      // If there was no active Pinia before, verify it's different now
      if (beforePinia) {
        expect(afterPinia).not.toBe(beforePinia);
      }
    });
  });
  
  describe('mockLocalStorage', () => {
    beforeEach(() => {
      // Clear the mock localStorage before each test
      mockLocalStorage.clear();
    });
    
    it('stores and retrieves values', () => {
      // Set a value
      mockLocalStorage.setItem('test-key', 'test-value');
      
      // Verify it can be retrieved
      expect(mockLocalStorage.getItem('test-key')).toBe('test-value');
    });
    
    it('returns null for non-existent keys', () => {
      expect(mockLocalStorage.getItem('non-existent')).toBeNull();
    });
    
    it('removes items', () => {
      // Set a value
      mockLocalStorage.setItem('test-key', 'test-value');
      
      // Remove it
      mockLocalStorage.removeItem('test-key');
      
      // Verify it's gone
      expect(mockLocalStorage.getItem('test-key')).toBeNull();
    });
    
    it('clears all items', () => {
      // Set multiple values
      mockLocalStorage.setItem('key1', 'value1');
      mockLocalStorage.setItem('key2', 'value2');
      
      // Clear all
      mockLocalStorage.clear();
      
      // Verify all are gone
      expect(mockLocalStorage.getItem('key1')).toBeNull();
      expect(mockLocalStorage.getItem('key2')).toBeNull();
      expect(mockLocalStorage.store).toEqual({});
    });
  });
  
  describe('setupMockLocalStorage', () => {
    let restoreLocalStorage: () => void;
    
    // Save original localStorage
    const originalLocalStorage = globalThis.localStorage;
    
    afterEach(() => {
      // Restore original localStorage after each test
      if (restoreLocalStorage) {
        restoreLocalStorage();
      } else {
        globalThis.localStorage = originalLocalStorage;
      }
    });
    
    it('replaces global localStorage with mock', () => {
      // Setup mock localStorage
      restoreLocalStorage = setupMockLocalStorage();
      
      // Verify localStorage is now the mock
      expect(globalThis.localStorage).toBe(mockLocalStorage);
      
      // Test functionality
      localStorage.setItem('test-key', 'test-value');
      expect(localStorage.getItem('test-key')).toBe('test-value');
    });
    
    it('returns a function that restores the original localStorage', () => {
      // Setup mock localStorage
      restoreLocalStorage = setupMockLocalStorage();
      
      // Verify localStorage is now the mock
      expect(globalThis.localStorage).toBe(mockLocalStorage);
      
      // Restore original
      restoreLocalStorage();
      
      // Verify localStorage is restored
      expect(globalThis.localStorage).toBe(originalLocalStorage);
    });
  });
  
  describe('mockConsole', () => {
    let restoreConsole: () => void;
    
    // Save original console methods
    const originalLog = console.log;
    const originalWarn = console.warn;
    const originalError = console.error;
    
    afterEach(() => {
      // Restore original console methods after each test
      if (restoreConsole) {
        restoreConsole();
      } else {
        console.log = originalLog;
        console.warn = originalWarn;
        console.error = originalError;
      }
    });
    
    it('replaces console methods with no-op functions', () => {
      // Setup mock console
      restoreConsole = mockConsole();
      
      // Create spies to verify the mocked methods are called
      const logSpy = vi.spyOn(console, 'log');
      const warnSpy = vi.spyOn(console, 'warn');
      const errorSpy = vi.spyOn(console, 'error');
      
      // Call console methods
      console.log('test log');
      console.warn('test warn');
      console.error('test error');
      
      // Verify they were called but didn't do anything
      expect(logSpy).toHaveBeenCalledWith('test log');
      expect(warnSpy).toHaveBeenCalledWith('test warn');
      expect(errorSpy).toHaveBeenCalledWith('test error');
    });
    
    it('returns a function that restores the original console methods', () => {
      // Setup mock console
      restoreConsole = mockConsole();
      
      // Verify console methods are mocked
      expect(console.log).not.toBe(originalLog);
      expect(console.warn).not.toBe(originalWarn);
      expect(console.error).not.toBe(originalError);
      
      // Restore original
      restoreConsole();
      
      // Verify console methods are restored
      expect(console.log).toBe(originalLog);
      expect(console.warn).toBe(originalWarn);
      expect(console.error).toBe(originalError);
    });
  });
});
