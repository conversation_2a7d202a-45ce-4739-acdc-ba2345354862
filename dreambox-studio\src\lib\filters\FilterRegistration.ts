/**
 * FilterRegistration.ts
 *
 * This module handles the registration of all filter types.
 * It registers both standard and specialized filter types.
 */

import { filterRegistry } from './FilterRegistry';
import TextFilterControl from './components/FilterControl.vue';
import { registerSpecializedFilterTypes } from './specialized/SpecializedFilterTypes';

/**
 * Register all standard filter types
 */
export function registerStandardFilterTypes() {
  // Text filter
  filterRegistry.registerFilterType({
    id: 'text',
    name: 'Text Filter',
    section: 'main',
    component: TextFilterControl,
    tableTypes: ['*'], // Available for all tables
    stateExtractor: (state) => state,
    stateApplier: (item, value) => {
      if (!value) return true;

      // Convert item to string and check if it contains the filter value
      const itemStr = String(item).toLowerCase();
      // Convert value to string safely
      let valueStr: string;
      if (typeof value === 'object' && value !== null) {
        valueStr = JSON.stringify(value);
      } else if (typeof value === 'string') {
        valueStr = value;
      } else if (typeof value === 'number' || typeof value === 'boolean') {
        valueStr = String(value);
      } else {
        valueStr = '';
      }
      return itemStr.includes(valueStr.toLowerCase());
    },
    clearHandler: () => null
  });

  // Number filter
  filterRegistry.registerFilterType({
    id: 'number',
    name: 'Number Filter',
    section: 'main',
    component: TextFilterControl,
    tableTypes: ['*'],
    stateExtractor: (state) => state,
    stateApplier: (item, value) => {
      if (!value || typeof value !== 'object' || value === null) return true;

      const rangeValue = value as { min: number | null, max: number | null };
      if (rangeValue.min === null && rangeValue.max === null) return true;

      const num = Number(String(item));
      if (isNaN(num)) return false;

      if (rangeValue.min !== null && num < rangeValue.min) return false;
      if (rangeValue.max !== null && num > rangeValue.max) return false;

      return true;
    },
    clearHandler: () => null
  });

  // Date filter
  filterRegistry.registerFilterType({
    id: 'date',
    name: 'Date Filter',
    section: 'main',
    component: TextFilterControl,
    tableTypes: ['*'],
    stateExtractor: (state) => state,
    stateApplier: (item, value) => {
      if (!value || typeof value !== 'object' || value === null) return true;

      const dateValue = value as { from: string | null, to: string | null };
      if (dateValue.from === null && dateValue.to === null) return true;

      const date = new Date(String(item));
      if (isNaN(date.getTime())) return false;

      if (dateValue.from) {
        const fromDate = new Date(dateValue.from);
        if (date < fromDate) return false;
      }

      if (dateValue.to) {
        const toDate = new Date(dateValue.to);
        if (date > toDate) return false;
      }

      return true;
    },
    clearHandler: () => null
  });

  // Select filter
  filterRegistry.registerFilterType({
    id: 'select',
    name: 'Select Filter',
    section: 'main',
    component: TextFilterControl,
    tableTypes: ['*'],
    stateExtractor: (state) => state,
    stateApplier: (item, value) => {
      if (!value) return true;
      return item === value;
    },
    clearHandler: () => null
  });

  // Multi-select filter
  filterRegistry.registerFilterType({
    id: 'multi-select',
    name: 'Multi-Select Filter',
    section: 'main',
    component: TextFilterControl,
    tableTypes: ['*'],
    stateExtractor: (state) => state,
    stateApplier: (item, value) => {
      if (!value || !Array.isArray(value) || value.length === 0) return true;

      // If item is an array, check for any intersection
      if (Array.isArray(item)) {
        return value.some((v: string) => item.includes(v));
      }

      // Otherwise check if item is in the filter array
      return value.includes(item);
    },
    clearHandler: () => null
  });

  // Boolean filter
  filterRegistry.registerFilterType({
    id: 'boolean',
    name: 'Boolean Filter',
    section: 'main',
    component: TextFilterControl,
    tableTypes: ['*'],
    stateExtractor: (state) => state,
    stateApplier: (item, value) => {
      if (value === null) return true;
      return item === value;
    },
    clearHandler: () => null
  });

  // console.log('Standard filter types registered');
}

/**
 * Register all filter types
 */
export function registerAllFilterTypes() {
  // Register standard filter types
  registerStandardFilterTypes();

  // Register specialized filter types
  registerSpecializedFilterTypes();

  // console.log('All filter types registered');
}

// Export default for module imports
export default { registerAllFilterTypes, registerStandardFilterTypes };
