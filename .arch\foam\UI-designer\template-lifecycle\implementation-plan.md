# Template Lifecycle Implementation Plan

This document outlines the step-by-step implementation plan for the new template lifecycle system.

## Template Lifecycle Overview

| Status   | Description                                       | Actions                                            | Role            | Color  |
| :------- | :------------------------------------------------ | :------------------------------------------------- | :-------------- | :----- |
| Draft    | Template is being created by the designer/admin.  | Designer can send to approval. Admin can activate. | Designer, Admin | Blue   |
| Pending  | Designer has published the template for approval. | Admin can approve (->active) or reject (->draft).  | Designer, Admin | Orange |
| Active   | Template is being approved.                       | Nobody can edit or delete. Admin can archive.      | Designer, Admin | Green  |
| Archived | Template is no longer in use, can be reused       | Designer/Admin can copy (make new draft).          | Designer, Admin | Gray   |

## 1. Database Changes

### 1.1 Update the Templates Table

```sql
-- Update status field to use an enum type for better validation
ALTER TABLE templates 
DROP CONSTRAINT IF EXISTS templates_status_check;

-- Add check constraint to ensure status is one of the allowed values
ALTER TABLE templates 
ADD CONSTRAINT templates_status_check 
CHECK (status IN ('draft', 'pending', 'active', 'archived'));

-- Update any existing 'published' status to 'active'
UPDATE templates 
SET status = 'active' 
WHERE status = 'published';
```

### 1.2 Create a Template History Table

```sql
CREATE TABLE IF NOT EXISTS template_status_history (
    id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    template_id BIGINT REFERENCES templates(id) NOT NULL,
    previous_status VARCHAR NOT NULL,
    new_status VARCHAR NOT NULL,
    changed_by BIGINT REFERENCES app_users(id) NOT NULL,
    change_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    notes TEXT
);
```

### 1.3 Create a Function to Update Template Status

```sql
CREATE OR REPLACE FUNCTION update_template_status(
    p_template_id BIGINT,
    p_new_status VARCHAR,
    p_user_id BIGINT,
    p_notes TEXT DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
    v_current_status VARCHAR;
    v_is_valid BOOLEAN := TRUE;
BEGIN
    -- Get current status
    SELECT status INTO v_current_status FROM templates WHERE id = p_template_id;
    
    -- Validate status transition
    CASE
        WHEN v_current_status = 'draft' AND p_new_status NOT IN ('pending', 'active') THEN
            v_is_valid := FALSE;
        WHEN v_current_status = 'pending' AND p_new_status NOT IN ('draft', 'active') THEN
            v_is_valid := FALSE;
        WHEN v_current_status = 'active' AND p_new_status NOT IN ('archived') THEN
            v_is_valid := FALSE;
        WHEN v_current_status = 'archived' THEN
            v_is_valid := FALSE; -- Archived templates cannot be changed
    END CASE;
    
    -- If valid transition, update status and record history
    IF v_is_valid THEN
        -- Update template status
        UPDATE templates 
        SET 
            status = p_new_status,
            published = (p_new_status = 'active'),
            updated_at = NOW(),
            -- Set approval fields if transitioning to active
            approved_by = CASE WHEN p_new_status = 'active' THEN p_user_id ELSE approved_by END,
            approval_date = CASE WHEN p_new_status = 'active' THEN NOW() ELSE approval_date END
        WHERE id = p_template_id;
        
        -- Record status change in history
        INSERT INTO template_status_history (
            template_id, 
            previous_status, 
            new_status, 
            changed_by, 
            notes
        ) VALUES (
            p_template_id,
            v_current_status,
            p_new_status,
            p_user_id,
            p_notes
        );
        
        RETURN TRUE;
    ELSE
        RETURN FALSE;
    END IF;
END;
$$ LANGUAGE plpgsql;
```

### 1.4 Create a Function for Copying Archived Templates

```sql
CREATE OR REPLACE FUNCTION copy_archived_template(
    p_template_id BIGINT,
    p_user_id BIGINT
) RETURNS BIGINT AS $$
DECLARE
    v_new_template_id BIGINT;
    v_template_record RECORD;
BEGIN
    -- Get the template to copy
    SELECT * INTO v_template_record FROM templates WHERE id = p_template_id;
    
    -- Verify it's archived
    IF v_template_record.status != 'archived' THEN
        RAISE EXCEPTION 'Only archived templates can be copied';
    END IF;
    
    -- Create a new template based on the archived one
    INSERT INTO templates (
        name,
        description,
        company_id,
        designer_id,
        agent_id,
        version,
        status,
        published,
        image_server_data
    ) VALUES (
        v_template_record.name || ' (Copy)',
        v_template_record.description,
        v_template_record.company_id,
        p_user_id, -- New designer is the current user
        v_template_record.agent_id,
        1, -- Reset version to 1
        'draft', -- New template is always draft
        false, -- Not published
        v_template_record.image_server_data
    )
    RETURNING id INTO v_new_template_id;
    
    -- Copy template elements
    INSERT INTO prompt_elements_usage (
        template_id,
        element_id,
        "order"
    )
    SELECT 
        v_new_template_id,
        element_id,
        "order"
    FROM prompt_elements_usage
    WHERE template_id = p_template_id;
    
    -- Return the new template ID
    RETURN v_new_template_id;
END;
$$ LANGUAGE plpgsql;
```

## 2. Backend API Changes

### 2.1 Update Template Service (templateService.ts)

Add new methods to the service:

```typescript
// Update template status
async function updateTemplateStatus(
  templateId: number, 
  newStatus: 'draft' | 'pending' | 'active' | 'archived',
  notes?: string
): Promise<boolean> {
  try {
    const { data, error } = await supabase.rpc(
      'update_template_status',
      {
        p_template_id: templateId,
        p_new_status: newStatus,
        p_user_id: getCurrentUserId(),
        p_notes: notes || null
      }
    );
    
    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating template status:', error);
    return false;
  }
}

// Get template status history
async function getTemplateStatusHistory(templateId: number): Promise<TemplateStatusHistory[]> {
  try {
    const { data, error } = await supabase
      .from('template_status_history')
      .select(`
        id,
        template_id,
        previous_status,
        new_status,
        changed_by,
        change_date,
        notes,
        app_users(first_name, last_name)
      `)
      .eq('template_id', templateId)
      .order('change_date', { ascending: false });
    
    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error fetching template status history:', error);
    return [];
  }
}

// Create a copy of an archived template
async function copyArchivedTemplate(templateId: number): Promise<number | null> {
  try {
    const { data, error } = await supabase.rpc(
      'copy_archived_template',
      {
        p_template_id: templateId,
        p_user_id: getCurrentUserId()
      }
    );
    
    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error copying archived template:', error);
    return null;
  }
}
```

### 2.2 Update Template Interface

Update the Template interface in `src/types/Template.ts`:

```typescript
export interface Template {
  // ... existing fields
  status: 'draft' | 'pending' | 'active' | 'archived';
  // ... other fields
}

// Add new interface for status history
export interface TemplateStatusHistory {
  id: number;
  template_id: number;
  previous_status: string;
  new_status: string;
  changed_by: number;
  change_date: string;
  notes: string | null;
  app_users: {
    first_name: string;
    last_name: string;
  };
}
```

## 3. UI Changes

### 3.1 Update Template Table Component (TemplateTable.vue)

#### 3.1.1 Update Status Badge Colors

```typescript
// Get status color
function getStatusColor(status: string): string {
  switch (status) {
    case 'draft': return 'blue';
    case 'pending': return 'orange';
    case 'active': return 'positive';
    case 'archived': return 'grey';
    default: return 'grey';
  }
}
```

#### 3.1.2 Update More Menu Actions

```html
<!-- Status actions in More menu -->
<q-item 
  v-if="props.row.status === 'draft' && hasRole('designer')"
  clickable 
  v-close-popup 
  @click="updateTemplateStatus(props.row, 'pending')"
>
  <q-item-section avatar>
    <q-icon color="orange" name="send" />
  </q-item-section>
  <q-item-section>Submit for Approval</q-item-section>
</q-item>

<q-item 
  v-if="props.row.status === 'draft' && hasRole('admin')"
  clickable 
  v-close-popup 
  @click="updateTemplateStatus(props.row, 'active')"
>
  <q-item-section avatar>
    <q-icon color="positive" name="check_circle" />
  </q-item-section>
  <q-item-section>Approve</q-item-section>
</q-item>

<q-item 
  v-if="props.row.status === 'pending' && hasRole('admin')"
  clickable 
  v-close-popup 
  @click="updateTemplateStatus(props.row, 'active')"
>
  <q-item-section avatar>
    <q-icon color="positive" name="check_circle" />
  </q-item-section>
  <q-item-section>Approve</q-item-section>
</q-item>

<q-item 
  v-if="props.row.status === 'pending' && hasRole('admin')"
  clickable 
  v-close-popup 
  @click="updateTemplateStatus(props.row, 'draft')"
>
  <q-item-section avatar>
    <q-icon color="negative" name="cancel" />
  </q-item-section>
  <q-item-section>Reject</q-item-section>
</q-item>

<q-item 
  v-if="props.row.status === 'active' && hasRole('admin')"
  clickable 
  v-close-popup 
  @click="updateTemplateStatus(props.row, 'archived')"
>
  <q-item-section avatar>
    <q-icon color="grey" name="archive" />
  </q-item-section>
  <q-item-section>Archive</q-item-section>
</q-item>

<q-item 
  v-if="props.row.status === 'archived'"
  clickable 
  v-close-popup 
  @click="copyTemplate(props.row)"
>
  <q-item-section avatar>
    <q-icon color="blue" name="content_copy" />
  </q-item-section>
  <q-item-section>Copy to New Draft</q-item-section>
</q-item>
```

#### 3.1.3 Add Status Update Function

```typescript
// Update template status
async function updateTemplateStatus(template: Template, newStatus: string) {
  try {
    const success = await templateService.updateTemplateStatus(
      template.id, 
      newStatus as 'draft' | 'pending' | 'active' | 'archived'
    );
    
    if (success) {
      // Show notification based on status change
      const statusMessages = {
        'draft': 'Template returned to draft',
        'pending': 'Template submitted for approval',
        'active': 'Template approved and activated',
        'archived': 'Template archived'
      };
      
      $q.notify({
        type: 'positive',
        message: statusMessages[newStatus] || `Status updated to ${newStatus}`,
        position: 'bottom-right',
        timeout: 2000
      });
      
      // Refresh the templates list
      emit('refresh');
    } else {
      $q.notify({
        type: 'negative',
        message: 'Failed to update template status',
        position: 'bottom-right',
        timeout: 3000
      });
    }
  } catch (error) {
    console.error('Error updating template status:', error);
    $q.notify({
      type: 'negative',
      message: 'Error updating template status',
      position: 'bottom-right',
      timeout: 3000
    });
  }
}

// Copy archived template to create a new draft
async function copyTemplate(template: Template) {
  try {
    const newTemplateId = await templateService.copyArchivedTemplate(template.id);
    
    if (newTemplateId) {
      $q.notify({
        type: 'positive',
        message: 'Template copied successfully',
        position: 'bottom-right',
        timeout: 2000
      });
      
      // Refresh the templates list
      emit('refresh');
    } else {
      $q.notify({
        type: 'negative',
        message: 'Failed to copy template',
        position: 'bottom-right',
        timeout: 3000
      });
    }
  } catch (error) {
    console.error('Error copying template:', error);
    $q.notify({
      type: 'negative',
      message: 'Error copying template',
      position: 'bottom-right',
      timeout: 3000
    });
  }
}
```

### 3.2 Implement Bulk Actions in Template Browser (TemplateBrowser.vue)

#### 3.2.1 Add Bulk Actions Toolbar

```html
<!-- Bulk actions toolbar (visible when templates are selected) -->
<div v-if="selectedTemplates.length > 0" class="bulk-actions-toolbar q-pa-md q-mb-md bg-grey-2 rounded-borders">
  <div class="row items-center justify-between">
    <div>
      <span class="text-subtitle1">{{ selectedTemplates.length }} templates selected</span>
    </div>
    <div class="row q-gutter-sm">
      <!-- Bulk actions based on selected templates status -->
      <q-btn 
        v-if="canSubmitForApproval" 
        color="orange" 
        icon="send" 
        label="Submit for Approval" 
        @click="bulkUpdateStatus('pending')" 
      />
      <q-btn 
        v-if="canApprove" 
        color="positive" 
        icon="check_circle" 
        label="Approve" 
        @click="bulkUpdateStatus('active')" 
      />
      <q-btn 
        v-if="canReject" 
        color="negative" 
        icon="cancel" 
        label="Reject" 
        @click="bulkUpdateStatus('draft')" 
      />
      <q-btn 
        v-if="canArchive" 
        color="grey" 
        icon="archive" 
        label="Archive" 
        @click="bulkUpdateStatus('archived')" 
      />
      <q-btn 
        color="primary" 
        icon="clear" 
        label="Clear Selection" 
        @click="clearSelection" 
      />
    </div>
  </div>
</div>
```

#### 3.2.2 Add Computed Properties and Methods

```typescript
// Track selected templates
const selectedTemplates = ref<Template[]>([]);

// Handle selection changes from the table
function onSelectionChange(templates: Template[]) {
  selectedTemplates.value = templates;
}

// Clear selection
function clearSelection() {
  selectedTemplates.value = [];
  // Also clear selection in the table component
  if (templateTableRef.value) {
    templateTableRef.value.clearSelection();
  }
}

// Computed properties for bulk actions
const canSubmitForApproval = computed(() => {
  return hasRole('designer') && 
    selectedTemplates.value.every(t => t.status === 'draft');
});

const canApprove = computed(() => {
  return hasRole('admin') && 
    selectedTemplates.value.every(t => 
      t.status === 'draft' || t.status === 'pending'
    );
});

const canReject = computed(() => {
  return hasRole('admin') && 
    selectedTemplates.value.every(t => t.status === 'pending');
});

const canArchive = computed(() => {
  return hasRole('admin') && 
    selectedTemplates.value.every(t => t.status === 'active');
});

// Bulk update function
async function bulkUpdateStatus(newStatus: string) {
  try {
    const results = await Promise.all(
      selectedTemplates.value.map(template => 
        templateService.updateTemplateStatus(
          template.id, 
          newStatus as 'draft' | 'pending' | 'active' | 'archived'
        )
      )
    );
    
    const successCount = results.filter(Boolean).length;
    
    $q.notify({
      type: 'positive',
      message: `Updated ${successCount} of ${selectedTemplates.value.length} templates`,
      position: 'bottom-right',
      timeout: 3000
    });
    
    // Refresh the templates list
    await fetchTemplates();
    clearSelection();
  } catch (error) {
    console.error('Error performing bulk update:', error);
    $q.notify({
      type: 'negative',
      message: 'Failed to update templates',
      position: 'bottom-right',
      timeout: 3000
    });
  }
}
```

## 4. Role-Based Access Control

### 4.1 Add Role Check Function

```typescript
// Check if user has a specific role
function hasRole(role: 'designer' | 'admin' | 'super_admin'): boolean {
  const authStore = useAuthStore();
  return authStore.hasRole(role);
}
```

### 4.2 Update Auth Store (authStore.ts)

```typescript
// Add role checking method to auth store
const hasRole = (role: string): boolean => {
  if (!state.user) return false;
  
  // Check if user has the specified role
  return state.userRoles.some(r => r.role_name.toLowerCase() === role.toLowerCase());
};
```

## 5. Testing Plan

### 5.1 Database Testing

1. Test status constraints
2. Test status transition validation
3. Test history recording
4. Test template copying

### 5.2 API Testing

1. Test updateTemplateStatus function
2. Test getTemplateStatusHistory function
3. Test copyArchivedTemplate function

### 5.3 UI Testing

1. Test status badge colors
2. Test More menu actions based on status and role
3. Test bulk actions
4. Test notifications

## 6. Deployment Steps

1. Run database migration scripts
2. Deploy updated backend services
3. Deploy updated frontend components
4. Verify all functionality in production environment

## 7. Rollback Plan

In case of issues:

1. Revert database changes
2. Restore previous version of services
3. Restore previous version of UI components
