# Component Registry

The Component Registry is responsible for registering and retrieving UI components. It ensures that components are only loaded when needed and provides a central place to manage component dependencies.

## File: `boot/componentRegistry.ts`

```typescript
import { boot } from 'quasar/wrappers';
import { markRaw, defineAsyncComponent, Component } from 'vue';

// Component registry
const componentRegistry: Record<string, Component> = {};

// Register a component
export function registerComponent(name: string, component: Component): void {
  componentRegistry[name] = markRaw(component);
}

// Register an async component
export function registerAsyncComponent(name: string, importFn: () => Promise<any>): void {
  componentRegistry[name] = markRaw(defineAsyncComponent(importFn));
}

// Get a component by name
export function getComponent(name: string): Component | undefined {
  return componentRegistry[name];
}

// Register core components
export default boot(({ app }) => {
  // Navigation components
  registerAsyncComponent('Navigation', () => import('src/components/navigation/NavigationTree.vue'));
  
  // Drawer components
  registerAsyncComponent('FilterPanel', () => import('src/components/filters/FilterPanel.vue'));
  registerAsyncComponent('PromptElementsPanel', () => import('src/components/templates/PromptElementsPanel.vue'));
  registerAsyncComponent('ElementValuesEditor', () => import('src/components/templates/ElementValuesEditor.vue'));
  registerAsyncComponent('ChatAssistant', () => import('src/components/chat/ChatAssistant.vue'));
  registerAsyncComponent('TemplatePreview', () => import('src/components/templates/TemplatePreview.vue'));
  
  // Top bar components
  registerAsyncComponent('SearchBar', () => import('src/components/search/SearchBar.vue'));
  registerAsyncComponent('ViewToggle', () => import('src/components/common/ViewToggle.vue'));
  
  // Bottom bar components
  registerAsyncComponent('ActionButton', () => import('src/components/common/ActionButton.vue'));
  
  // Make componentRegistry available to all components
  app.config.globalProperties.$components = {
    get: getComponent,
    register: registerComponent,
    registerAsync: registerAsyncComponent
  };
});
```

## File: `components/common/DynamicComponent.vue`

```vue
<template>
  <component
    v-if="resolvedComponent"
    :is="resolvedComponent"
    v-bind="props"
  />
  <div v-else class="text-grey q-pa-md">
    Component not found: {{ name }}
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { getComponent } from 'src/boot/componentRegistry';

const props = defineProps<{
  name: string;
  [key: string]: any;
}>();

// Resolve the component
const resolvedComponent = computed(() => {
  return getComponent(props.name);
});
</script>
```
