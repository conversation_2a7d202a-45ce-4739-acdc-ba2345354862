<template>
  <div class="create-runpod-button">
    <q-btn
      color="primary"
      :label="$t('swarmUI.createRunPod')"
      :loading="isCreating"
      @click="showCreateDialog"
    />

    <q-dialog v-model="showDialog" persistent>
      <q-card style="min-width: 350px">
        <q-card-section>
          <div class="text-h6">{{ $t('swarmUI.createSwarmUIServer') }}</div>
        </q-card-section>

        <q-card-section>
          <q-select
            v-model="selectedGpu"
            :options="gpuOptions"
            :label="$t('swarmUI.selectGpu')"
            filled
            emit-value
            map-options
          />
        </q-card-section>

        <q-card-section v-if="errorMessage" class="text-negative">
          {{ errorMessage }}
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="$t('swarmUI.cancel')" color="primary" v-close-popup />
          <q-btn
            :label="$t('swarmUI.create')"
            color="primary"
            @click="createRunPod"
            :loading="isCreating"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import { swarmUIService } from 'src/services/swarmUI';

const $q = useQuasar();
const { t } = useI18n();

const showDialog = ref(false);
const isCreating = ref(false);
const errorMessage = ref('');

const selectedGpu = ref('NVIDIA RTX A5000');
const gpuOptions = [
  { label: 'NVIDIA RTX A5000 (24GB VRAM)', value: 'NVIDIA RTX A5000' },
  { label: 'NVIDIA RTX A4000 (16GB VRAM)', value: 'NVIDIA RTX A4000' },
  { label: 'NVIDIA RTX 3090 (24GB VRAM)', value: 'NVIDIA RTX 3090' },
  { label: 'NVIDIA RTX 4090 (24GB VRAM)', value: 'NVIDIA RTX 4090' }
];

function showCreateDialog() {
  errorMessage.value = '';
  showDialog.value = true;
}

async function createRunPod() {
  isCreating.value = true;
  errorMessage.value = '';

  try {
    const result = await swarmUIService.startServer({
      gpuTypeId: selectedGpu.value
    });

    if (result.status === 'error') {
      errorMessage.value = result.message || t('swarmUI.errorCreatingServer');
    } else {
      $q.notify({
        color: 'positive',
        message: t('swarmUI.serverCreating'),
        icon: 'cloud_queue'
      });
      showDialog.value = false;
    }
  } catch (error) {
    errorMessage.value = error instanceof Error ? error.message : String(error);
  } finally {
    isCreating.value = false;
  }
}
</script>


