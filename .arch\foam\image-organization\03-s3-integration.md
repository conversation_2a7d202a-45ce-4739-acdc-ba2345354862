# S3 Integration for Image Storage

This document outlines the integration with Amazon S3 for image storage in the Dreambox Studio application.

## S3 Storage Strategy

### Storage Structure

We'll use a flat storage structure in S3, keeping all organizational logic in the database:

```
/images/{uuid}-{filename}
```

Where:
- `uuid` is a generated unique identifier to prevent collisions
- `filename` is the original filename (sanitized)

This approach provides:
1. Simplicity in storage management
2. Avoidance of path length limitations
3. Prevention of filename collisions
4. Flexibility to reorganize in the database without moving files

### S3 Bucket Configuration

1. **Bucket Name**: `dreambox-studio-images`
2. **Region**: Choose a region close to your primary users
3. **Access Control**: Private (all access through pre-signed URLs)
4. **CORS Configuration**: Allow requests from your application domains

```json
[
  {
    "AllowedHeaders": ["*"],
    "AllowedMethods": ["GET", "PUT", "POST", "DELETE"],
    "AllowedOrigins": ["https://your-app-domain.com"],
    "ExposeHeaders": ["ETag"],
    "MaxAgeSeconds": 3000
  }
]
```

5. **Lifecycle Rules**: Consider setting up lifecycle rules to transition older images to cheaper storage classes

### IAM Configuration

Create an IAM user with limited permissions:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:PutObject",
        "s3:GetObject",
        "s3:DeleteObject",
        "s3:ListBucket"
      ],
      "Resource": [
        "arn:aws:s3:::dreambox-studio-images",
        "arn:aws:s3:::dreambox-studio-images/*"
      ]
    }
  ]
}
```

## Direct Upload Process

The process for direct uploads to S3:

1. Frontend requests a pre-signed URL from an Edge Function
2. Edge Function generates a pre-signed URL and creates a database record
3. Frontend uploads the file directly to S3 using the pre-signed URL
4. Frontend updates the UI with the image information returned from step 2

This approach:
- Minimizes server load
- Reduces bandwidth costs
- Improves upload performance
- Maintains security through pre-signed URLs

## Image URL Structure

Images will be accessible through:

1. **Direct S3 URL**: `https://dreambox-studio-images.s3.amazonaws.com/images/{uuid}-{filename}`
2. **Pre-signed URLs**: For private images or time-limited access

## Security Considerations

1. **Pre-signed URLs**: All uploads and downloads of private images use pre-signed URLs
2. **Expiration**: Pre-signed URLs expire after a short period (typically 1 hour)
3. **Content Type Validation**: Enforce content type validation on uploads
4. **Size Limits**: Implement size limits for uploads
5. **Virus Scanning**: Consider implementing virus scanning for uploaded images

## Cost Optimization

1. **Storage Classes**: Use appropriate storage classes based on access patterns
   - Standard: For frequently accessed images
   - Infrequent Access: For older images
   - Glacier: For archival

2. **Lifecycle Policies**: Automatically transition images between storage classes
   ```json
   {
     "Rules": [
       {
         "Status": "Enabled",
         "Prefix": "images/",
         "Transitions": [
           {
             "Days": 90,
             "StorageClass": "STANDARD_IA"
           },
           {
             "Days": 365,
             "StorageClass": "GLACIER"
           }
         ]
       }
     ]
   }
   ```

3. **Image Compression**: Consider implementing server-side compression for uploaded images

## Next Steps

Once S3 integration is configured, proceed to [Edge Functions Implementation](./04-edge-functions.md) to create the secure interface between your application and S3.
