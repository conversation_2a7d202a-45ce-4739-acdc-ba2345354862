/**
 * Test setup file for Bun tests
 * This file is automatically loaded by <PERSON><PERSON> before running tests
 */

// Import any global test setup here
import { beforeAll, afterAll, afterEach } from 'bun:test';

// Setup global mocks for browser APIs
globalThis.localStorage = {
  getItem: (key: string) => null,
  setItem: (key: string, value: string) => {},
  removeItem: (key: string) => {},
  clear: () => {},
  key: (index: number) => null,
  length: 0
};

// Mock window object for non-browser environments
globalThis.window = {
  ...globalThis.window,
  matchMedia: (query: string) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: () => {},
    removeListener: () => {},
    addEventListener: () => {},
    removeEventListener: () => {},
    dispatchEvent: () => true
  }),
  localStorage: globalThis.localStorage
} as any;

// Mock document object with minimal implementation
globalThis.document = {
  createElement: () => ({
    style: {},
    setAttribute: () => {},
    appendChild: () => {},
    removeChild: () => {},
    addEventListener: () => {},
    removeEventListener: () => {},
    querySelector: () => null,
    querySelectorAll: () => []
  }),
  createElementNS: () => ({
    style: {},
    setAttribute: () => {},
    appendChild: () => {},
    removeChild: () => {}
  }),
  createTextNode: () => ({}),
  body: {
    appendChild: () => {},
    removeChild: () => {},
    classList: {
      add: () => {},
      remove: () => {},
      contains: () => false
    }
  },
  head: {
    appendChild: () => {},
    removeChild: () => {}
  },
  documentElement: {
    style: {}
  }
} as any;

// Mock Vue-related globals
// @ts-ignore - These are just for testing
globalThis.HTMLElement = class HTMLElement {} as any;
// @ts-ignore - These are just for testing
globalThis.SVGElement = class SVGElement {} as any;

// Clean up after all tests
afterAll(() => {
  // Clean up global mocks if needed
});

// Reset mocks after each test
afterEach(() => {
  // Reset localStorage
  globalThis.localStorage.clear();
});
