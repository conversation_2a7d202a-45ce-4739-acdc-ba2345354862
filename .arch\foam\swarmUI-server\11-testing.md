# Step 11: Testing and Troubleshooting

This document outlines the testing and troubleshooting process for the SwarmUI server implementation.

## 11.1 Testing the RunPod Integration

### 11.1.1 Test Server Provisioning

Test that you can provision a RunPod instance programmatically:

```typescript
// Test script for server provisioning
import { serverManagementService } from '../services/serverManagement';

async function testServerProvisioning() {
  try {
    console.log('Starting server...');
    const result = await serverManagementService.startServer();
    console.log('Server started:', result);
    
    // Wait for 5 minutes to allow for testing
    console.log('Waiting for 5 minutes...');
    await new Promise(resolve => setTimeout(resolve, 5 * 60 * 1000));
    
    console.log('Stopping server...');
    const stopResult = await serverManagementService.stopServer(result.serverId);
    console.log('Server stopped:', stopResult);
  } catch (error) {
    console.error('Error in test:', error);
  }
}

testServerProvisioning();
```

### 11.1.2 Test RunPod API

Test the RunPod API directly to ensure your API key and permissions are correct:

```bash
# Test getting pod status
curl -X GET "https://api.runpod.io/v2/me" \
  -H "Authorization: Bearer YOUR_API_KEY"

# Test creating a pod
curl -X POST "https://api.runpod.io/v2/pods" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "templateId": "YOUR_TEMPLATE_ID",
    "gpuCount": 1,
    "volumeInGb": 50,
    "containerDiskInGb": 10,
    "name": "Test Pod",
    "env": {
      "SWARMUI_API_KEY": "test-key"
    }
  }'
```

## 11.2 Testing SwarmUI Integration

### 11.2.1 Test SwarmUI API

Test the SwarmUI API to ensure it's working correctly:

```typescript
// Test script for SwarmUI API
import { swarmUIService } from '../services/swarmUI';
import { createDefaultGenerationParams } from '../services/swarmUI/utils';

async function testSwarmUIAPI() {
  try {
    console.log('Initializing SwarmUI service...');
    const initialized = await swarmUIService.initialize();
    
    if (!initialized) {
      console.error('Failed to initialize SwarmUI service');
      return;
    }
    
    console.log('Getting server info...');
    const serverInfo = await swarmUIService.getServerInfo();
    console.log('Server info:', serverInfo);
    
    console.log('Getting models...');
    const models = await swarmUIService.getModels();
    console.log('Models:', models);
    
    // Check if Flux model is available
    const fluxModel = models.find(model => model.name === 'Flux');
    
    if (!fluxModel) {
      console.warn('Flux model not found!');
    } else {
      console.log('Flux model found:', fluxModel);
    }
    
    // Generate a test image
    console.log('Generating test image...');
    const params = createDefaultGenerationParams();
    params.prompt = 'A beautiful landscape with mountains and a lake, highly detailed, photorealistic';
    
    const response = await swarmUIService.generateImage(params);
    console.log('Generation response:', response);
    
    // Poll for completion
    console.log('Polling for completion...');
    await pollGenerationStatus(response.id);
  } catch (error) {
    console.error('Error in test:', error);
  }
}

async function pollGenerationStatus(generationId) {
  return new Promise((resolve, reject) => {
    const checkStatus = async () => {
      try {
        const status = await swarmUIService.getGenerationStatus(generationId);
        console.log('Generation status:', status);
        
        if (status.status === 'completed') {
          console.log('Generation completed:', status);
          resolve(status);
        } else if (status.status === 'failed') {
          console.error('Generation failed:', status.error);
          reject(new Error(status.error || 'Generation failed'));
        } else {
          // Still processing, check again in 5 seconds
          console.log('Still processing, checking again in 5 seconds...');
          setTimeout(checkStatus, 5000);
        }
      } catch (error) {
        reject(error);
      }
    };
    
    checkStatus();
  });
}

testSwarmUIAPI();
```

### 11.2.2 Test Image Storage

Test the image storage functionality:

```typescript
// Test script for image storage
import { imageStorageService } from '../services/imageStorage';

async function testImageStorage() {
  try {
    // Create a test image
    const canvas = document.createElement('canvas');
    canvas.width = 512;
    canvas.height = 512;
    const ctx = canvas.getContext('2d');
    ctx.fillStyle = 'blue';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Convert to blob
    const blob = await new Promise(resolve => canvas.toBlob(resolve));
    
    // Upload to storage
    console.log('Uploading test image...');
    const storedImage = await imageStorageService.uploadImage({
      userId: 1, // Replace with actual user ID
      generationId: 1, // Replace with actual generation ID
      imageBlob: blob,
      seed: 42,
      width: 512,
      height: 512
    });
    
    console.log('Image uploaded:', storedImage);
    
    // Get images
    console.log('Getting images...');
    const images = await imageStorageService.getImages({
      userId: 1, // Replace with actual user ID
      limit: 10
    });
    
    console.log('Images:', images);
  } catch (error) {
    console.error('Error in test:', error);
  }
}

testImageStorage();
```

## 11.3 Common Issues and Solutions

### 11.3.1 RunPod API Issues

| Issue | Possible Cause | Solution |
|-------|----------------|----------|
| "API key not authorized" | Incorrect API key or insufficient permissions | Verify API key and ensure it has the necessary permissions |
| "Template not found" | Incorrect template ID | Verify template ID in the RunPod console |
| "Insufficient credits" | Not enough credits in RunPod account | Add credits to your RunPod account |
| "No GPUs available" | No available GPUs of the requested type | Try a different GPU type or wait for availability |

### 11.3.2 SwarmUI Issues

| Issue | Possible Cause | Solution |
|-------|----------------|----------|
| "Failed to initialize SwarmUI service" | Server not running or incorrect API endpoint | Verify server is running and API endpoint is correct |
| "Flux model not found" | Flux model not installed or not configured correctly | Verify Flux model installation and configuration |
| "Generation failed" | Issues with the generation parameters or model | Check generation parameters and try with different values |
| "Out of memory" | Model too large for available GPU memory | Use a smaller model or increase GPU memory |

### 11.3.3 Image Storage Issues

| Issue | Possible Cause | Solution |
|-------|----------------|----------|
| "Failed to upload image" | Storage bucket not configured correctly | Verify storage bucket configuration and permissions |
| "Failed to create image record" | Database issues or incorrect schema | Verify database schema and connections |
| "Access denied" | Insufficient permissions | Verify RLS policies and user permissions |

## 11.4 Debugging Tools

### 11.4.1 RunPod Logs

Access RunPod logs to debug server issues:

1. Log in to your RunPod account
2. Navigate to your pod
3. Click on "Logs" to view the pod logs

### 11.4.2 SwarmUI Logs

Access SwarmUI logs to debug generation issues:

```bash
# SSH into the pod
ssh root@YOUR_POD_IP

# View SwarmUI logs
cd ~/StableSwarmUI
cat logs/swarm.log
```

### 11.4.3 Browser Developer Tools

Use browser developer tools to debug API calls and responses:

1. Open your application in Chrome or Firefox
2. Press F12 to open developer tools
3. Navigate to the Network tab
4. Filter for API calls to RunPod and SwarmUI
5. Examine request and response data

## 11.5 Performance Testing

Test the performance of your implementation:

```typescript
// Performance test script
import { swarmUIService } from '../services/swarmUI';
import { createDefaultGenerationParams, createFastGenerationParams } from '../services/swarmUI/utils';

async function testPerformance() {
  try {
    console.log('Initializing SwarmUI service...');
    const initialized = await swarmUIService.initialize();
    
    if (!initialized) {
      console.error('Failed to initialize SwarmUI service');
      return;
    }
    
    // Test high-quality generation
    console.log('Testing high-quality generation...');
    const startHQ = Date.now();
    
    const paramsHQ = createDefaultGenerationParams();
    paramsHQ.prompt = 'A beautiful landscape with mountains and a lake, highly detailed, photorealistic';
    
    const responseHQ = await swarmUIService.generateImage(paramsHQ);
    await pollGenerationStatus(responseHQ.id);
    
    const endHQ = Date.now();
    console.log(`High-quality generation took ${(endHQ - startHQ) / 1000} seconds`);
    
    // Test fast generation
    console.log('Testing fast generation...');
    const startFast = Date.now();
    
    const paramsFast = createFastGenerationParams();
    paramsFast.prompt = 'A beautiful landscape with mountains and a lake, highly detailed, photorealistic';
    
    const responseFast = await swarmUIService.generateImage(paramsFast);
    await pollGenerationStatus(responseFast.id);
    
    const endFast = Date.now();
    console.log(`Fast generation took ${(endFast - startFast) / 1000} seconds`);
  } catch (error) {
    console.error('Error in test:', error);
  }
}

// Same polling function as before
async function pollGenerationStatus(generationId) {
  // ...
}

testPerformance();
```

## Next Steps

Now that you have tested the SwarmUI server implementation, proceed to [Step 12: Cost Optimization Strategies](./12-cost-optimization.md) to optimize costs for your implementation.
