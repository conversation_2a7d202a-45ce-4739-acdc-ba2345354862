# Debugging RunPod Template Issues

This guide provides troubleshooting steps for common issues that may occur when using the SwarmUI RunPod template.

## 1. Template Creation Issues

### 1.1 Template Creation Fails

If you encounter issues when creating the template:

1. Verify that the pod is stopped (but not terminated) before creating the template
2. Check that you have specified the correct container image
3. Ensure that the startup script path is correct: `/workspace/template-startup.sh`
4. Make sure the HTTP port 7801 is exposed

### 1.2 Template Not Showing in RunPod Dashboard

If the template doesn't appear in your RunPod dashboard:

1. Refresh the page
2. Check the "Templates" tab in your RunPod account
3. Verify that the template creation process completed successfully

## 2. Pod Startup Issues

### 2.1 Pod Fails to Start

If a pod created from your template fails to start:

1. Check the pod's logs in the RunPod dashboard
2. Verify that the template has enough disk space (at least 50GB container disk and 100GB volume)
3. Try deploying the pod in a different region

### 2.2 Startup Script Not Running

If the startup script doesn't run:

1. SSH into the pod
2. Check if the script exists: `ls -la /workspace/template-startup.sh`
3. Verify that the script is executable: `chmod +x /workspace/template-startup.sh`
4. Try running the script manually: `/workspace/template-startup.sh`
5. Check the log file: `cat /workspace/template-startup.log`

### 2.3 Issues Creating or Editing the Script

If you have trouble creating or editing the script:

1. Install a text editor if needed:
   ```bash
   apt-get update && apt-get install -y nano
   ```

2. If the script is too large to paste in one go, you can upload it in parts:
   ```bash
   cat > /workspace/template-startup.sh.part1 << 'EOL'
   # First part of the script
   EOL

   cat > /workspace/template-startup.sh.part2 << 'EOL'
   # Second part of the script
   EOL

   cat /workspace/template-startup.sh.part1 /workspace/template-startup.sh.part2 > /workspace/template-startup.sh
   chmod +x /workspace/template-startup.sh
   ```

3. Alternatively, you can download the script directly from a URL:
   ```bash
   apt-get update && apt-get install -y wget
   wget -O /workspace/template-startup.sh https://your-url/template-startup.sh
   chmod +x /workspace/template-startup.sh
   ```

## 3. SwarmUI Installation Issues

### 3.1 SwarmUI Fails to Install

If SwarmUI fails to install:

1. Check the template startup log: `cat /workspace/template-startup.log`
2. Verify that the pod has internet access
3. Check for disk space issues: `df -h`
4. Try installing SwarmUI manually:
   ```bash
   cd /workspace
   git clone https://github.com/mcmonkeyprojects/SwarmUI
   cd SwarmUI/launchtools
   wget https://dot.net/v1/dotnet-install.sh -O dotnet-install.sh
   chmod +x dotnet-install.sh
   ./dotnet-install.sh --channel 8.0 --runtime aspnetcore
   ./dotnet-install.sh --channel 8.0
   ```

### 3.2 Model Download Issues

If models fail to download:

1. Check the template startup log for download errors
2. Verify that the pod has internet access
3. Try downloading the models manually:
   ```bash
   cd /workspace/models/diffusion_models
   python -c "from huggingface_hub import hf_hub_download; hf_hub_download('Kijai/flux-fp8', 'flux1-schnell-fp8-e4m3fn.safetensors', local_dir='.', local_dir_use_symlinks=False)"
   ```

## 4. Backend Configuration Issues

### 4.1 ComfyUI Not Starting

If ComfyUI fails to start:

1. Check the ComfyUI log: `cat /workspace/comfyui.log`
2. Try starting ComfyUI manually:
   ```bash
   cd /workspace/ComfyUI
   python main.py --listen 0.0.0.0 --port 7860
   ```
3. Check for CUDA issues:
   ```bash
   python -c "import torch; print(torch.cuda.is_available())"
   ```

### 4.2 SwarmUI Not Recognizing ComfyUI Backend

If SwarmUI doesn't recognize the ComfyUI backend:

1. Check the SwarmUI config: `cat ~/.swarmui/config.json`
2. Verify that the Backend section is properly configured:
   ```json
   "Backend": {
     "Type": "ComfyUI-SelfStarting",
     "ComfyUIPath": "/workspace/ComfyUI",
     "ComfyUIPort": 7860,
     "EnableWorkflows": true
   }
   ```
3. Check that ComfyUI is running: `ps aux | grep ComfyUI`
4. Restart SwarmUI with the comfy_path parameter:
   ```bash
   cd /workspace/SwarmUI
   ./launch-linux.sh --launch_mode none --host 0.0.0.0 --comfy_path /workspace/ComfyUI
   ```

## 5. API Access Issues

### 5.1 API Not Accessible

If the SwarmUI API is not accessible:

1. Verify that port 7801 is exposed in the template
2. Check if SwarmUI is running: `ps aux | grep SwarmUI`
3. Try accessing the API locally:
   ```bash
   curl -X GET "http://localhost:7801/api/info" \
     -H "Authorization: Bearer YOUR_API_KEY"
   ```
4. Check the RunPod proxy status in the pod dashboard

### 5.2 API Authentication Issues

If you encounter authentication issues with the API:

1. Check the API key in the SwarmUI config: `cat ~/.swarmui/config.json`
2. Verify that you're using the correct API key in your requests
3. Check that the Auth section is properly configured:
   ```json
   "Auth": {
     "Enabled": true,
     "ApiKey": "YOUR_API_KEY"
   }
   ```
4. Try regenerating the API key:
   ```bash
   API_KEY=$(openssl rand -hex 16)
   sed -i "s|\"ApiKey\": \".*\"|\"ApiKey\": \"$API_KEY\"|" ~/.swarmui/config.json
   echo "New API key: $API_KEY"
   ```

## 6. Image Generation Issues

### 6.1 Generation Fails

If image generation fails:

1. Check the SwarmUI log: `cat /workspace/SwarmUI/logs/swarm.log`
2. Check the ComfyUI log: `cat /workspace/comfyui.log`
3. Verify that the models are correctly linked
4. Try a simple generation request directly to the ComfyUI API
5. Check for CUDA memory issues:
   ```bash
   nvidia-smi
   ```

### 6.2 Models Not Found

If models are not found:

1. Check the model paths in the SwarmUI config
2. Verify that the symbolic links are correct:
   ```bash
   ls -la /workspace/SwarmUI/Models/Stable-Diffusion
   ls -la /workspace/ComfyUI/models/checkpoints
   ```
3. Check that the models exist in the expected locations
4. Try recreating the symbolic links:
   ```bash
   ln -sf /workspace/models/diffusion_models/FLUX1-Schnell-FP8.safetensors /workspace/SwarmUI/Models/Stable-Diffusion/flux1-schnell-fp8.safetensors
   ```

## 7. Supabase Integration Issues

### 7.1 Edge Function Not Using Template

If the Supabase Edge Function is not using the template:

1. Verify that the `SWARMUI_TEMPLATE_ID` environment variable is set correctly
2. Check the Edge Function logs in the Supabase dashboard
3. Try invoking the Edge Function manually:
   ```javascript
   const { data, error } = await supabase.functions.invoke('swarmui-server', {
     method: 'POST',
     headers: { 'Content-Type': 'application/json' },
     body: { action: 'start-server' }
   });
   console.log(data, error);
   ```

### 7.2 Edge Function Returning Errors

If the Edge Function returns errors:

1. Check the Edge Function logs in the Supabase dashboard
2. Verify that the RunPod API key is set correctly
3. Check that the template ID is valid
4. Try creating a pod manually using the RunPod API to verify the API key works

## 8. Application Integration Issues

### 8.1 Create Server Button Not Working

If the "Create Server" button in your application doesn't work:

1. Check the browser console for errors
2. Verify that the Supabase client is properly initialized
3. Check that the user has the necessary permissions
4. Try invoking the Edge Function directly from the browser console

### 8.2 Server Status Not Updating

If the server status doesn't update in your application:

1. Check the status check interval in your code
2. Verify that the status check API endpoint is correct
3. Check for CORS issues in the browser console
4. Try implementing a manual refresh button for testing
