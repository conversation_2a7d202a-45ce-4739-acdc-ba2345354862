{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### **Kaggle Notebook za SwarmUI - FLUX - 9.1. 2024**\n", "1. **Choose Accelerator GPU T4 x2**\n", "2. **Check that it is \"Internet on\"**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### STEP 1 ⬇️"]}, {"cell_type": "code", "execution_count": null, "metadata": {"trusted": true}, "outputs": [], "source": ["# STEP 1\n", "\n", "%cd /kaggle/working\n", "!git clone https://github.com/comfyanonymous/ComfyUI.git\n", "%cd ComfyUI\n", "!pip install -r requirements.txt\n", "!pip install ultralytics --upgrade\n", "\n", "%cd /kaggle/working/ComfyUI/custom_nodes\n", "!git clone https://github.com/ltdrdata/ComfyUI-Manager.git\n", "%cd ComfyUI-Manager\n", "!pip install -r requirements.txt\n", "\n", "!mkdir -p /kaggle/temp/models/yolov8\n", "%cd /kaggle/temp/models/yolov8\n", "!wget https://huggingface.co/Bingsu/adetailer/resolve/main/face_yolov9c.pt\n", "\n", "!pip install huggingface_hub\n", "\n", "!pip install ipywidgets\n", "!pip install hf_transfer\n", "!export HF_HUB_ENABLE_HF_TRANSFER=1\n", "!pip install imageio-ffmpeg\n", "\n", "!apt-update -y\n", "\n", "!apt-get install aria2 -y\n", "\n", "!mkdir -p /kaggle/temp/diffusion_models\n", "%cd /kaggle/temp/diffusion_models\n", "!aria2c -x 16 -s 16 -k 1M \"https://huggingface.co/Kijai/flux-fp8/resolve/main/flux1-schnell-fp8-e4m3fn.safetensors\" -o \"FLUX1-<PERSON><PERSON><PERSON>-FP8.safetensors\"\n", "\n", "!mkdir -p /kaggle/temp/clip\n", "%cd /kaggle/temp/clip\n", "!aria2c -x 16 -s 16 -k 1M \"https://huggingface.co/OwlMaster/SD3New/resolve/main/t5xxl_fp8_e4m3fn_scaled.safetensors\" -o \"t5xxl_enconly.safetensors\"\n", "\n", "!mkdir -p /kaggle/temp/VAE\n", "%cd /kaggle/temp/VAE\n", "!aria2c -x 16 -s 16 -k 1M \"https://huggingface.co/black-forest-labs/FLUX.1-schnell/resolve/main/ae.safetensors\" -o \"ae.safetensors\"\n", "\n", "\n", "!pip install flask\n", "!pip install pyngrok"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### STEP 2 ⬇️ (ON THE START AND EVERY TIME AFTER RESTART)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"trusted": true}, "outputs": [], "source": ["# KORAK 2 (HANA)\n", "\n", "import os\n", "import threading\n", "\n", "from flask import Flask\n", "from pyngrok import ngrok, conf\n", "\n", "conf.get_default().auth_token = \"2rPMgi6xr0wDbfZBhdMwRuJOZsR_23HxjAEKUxJ8rqP7XbMnQ\"\n", "\n", "os.environ[\"FLASK_ENV\"] = \"development\"\n", "\n", "app = Flask(__name__)\n", "\n", "public_url = ngrok.connect(7801).public_url\n", "print(\" * ngrok tunnel \\\"{}\\\" -> \\\"http://0.0.0.0:{}/\\\"\".format(public_url, 7801))\n", "\n", "app.config[\"BASE_URL\"] = public_url\n", "\n", "@app.route(\"/\")\n", "def index():\n", "    return \"Hello from <PERSON><PERSON>!\"\n", "\n", "threading.Thread(target=app.run, kwargs={\"use_reloader\": False}).start()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### STEP 3 (ONLY ONCE) ⬇️"]}, {"cell_type": "code", "execution_count": null, "metadata": {"trusted": true}, "outputs": [], "source": ["# KORAK 3\n", "\n", "import os\n", "SCRIPT_DIR = os.path.dirname(os.path.realpath(\"__file__\"))\n", "os.chdir(SCRIPT_DIR)\n", "%cd /kaggle/working\n", "#!rm -r SwarmUI\n", "# Accidental run prevention\n", "if os.path.isdir(\"SwarmasasUI\"):\n", "    print(\"SwarmUI already exists in this directory. Please remove it before installing.\")\n", "else:\n", "    if os.path.isfile(\"SwarmUI.sln\"):\n", "        print(\"SwarmUI already exists in this directory. Please remove it before installing.\")\n", "    else:\n", "        # Download swarm\n", "        !git clone https://github.com/mcmonkeyprojects/SwarmUI\n", "        %cd SwarmUI\n", "        !mkdir -p dlbackend/comfy/ComfyUI/custom_nodes\n", "        %cd dlbackend/comfy/ComfyUI/custom_nodes\n", "        !git clone https://github.com/ltdrdata/ComfyUI-Manager\n", "        %cd /kaggle/working/SwarmUI\n", "        !rm -r /kaggle/working/SwarmUI/dlbackend\n", "        !rm -r /kaggle/working/SwarmUI/Data\n", "        # install dotnet\n", "        os.chdir(\"launchtools\")\n", "        !rm dotnet-install.sh\n", "\n", "        # https://learn.microsoft.com/en-us/dotnet/core/install/linux-scripted-manual#scripted-install\n", "        !wget https://dot.net/v1/dotnet-install.sh -O dotnet-install.sh\n", "        !chmod +x dotnet-install.sh\n", "\n", "        # Note: manual installers that want to avoid home dir, add to both of the below lines: --install-dir $SCRIPT_DIR/.dotnet\n", "        !./dotnet-install.sh --channel 8.0 --runtime aspnetcore\n", "        !./dotnet-install.sh --channel 8.0\n", "        os.chdir(\"..\")\n", "\n", "        # Launch\n", "        !./launch-linux.sh --launch_mode none --data_dir \"/kaggle/temp\" --host 0.0.0.0"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### CHANGE ROOT TO /kaggle/temp \n", "#### <PERSON>AN<PERSON> BACKEND TO /kaggle/working/ComfyUI/main.py  \n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### STEP 4 ⬇️ (EVERY TIME AFTER RESTART)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"trusted": true}, "outputs": [], "source": ["#STEP 4\n", "\n", "%cd /kaggle/working/SwarmUI\n", "!./launch-linux.sh --launch_mode none --host 0.0.0.0"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### ZIP UTILITY"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}, "trusted": true}, "outputs": [], "source": ["# ZIP\n", "\n", "%cd /kaggle/working/SwarmUI\n", "!rm -f ../generated_images.zip\n", "!zip -r ../generated_images.zip Output\n", "%cd /kaggle/working/SwarmUI\n"]}], "metadata": {"kaggle": {"accelerator": "nvidiaTeslaT4", "dataSources": [], "isGpuEnabled": true, "isInternetEnabled": true, "language": "python", "sourceType": "notebook"}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 4}