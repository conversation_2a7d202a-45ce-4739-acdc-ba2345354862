// Type definitions for <PERSON><PERSON>'s test module
// This file is only used during development to prevent TypeScript errors

declare module 'bun:test' {
  export function describe(name: string, fn: () => void): void;
  export function test(name: string, fn: (...args: any[]) => any): void;
  export function beforeEach(fn: () => void): void;
  export function afterEach(fn: () => void): void;
  export function beforeAll(fn: () => void): void;
  export function afterAll(fn: () => void): void;
  
  export function expect<T>(value: T): {
    toBe(expected: any): void;
    toEqual(expected: any): void;
    toBeTruthy(): void;
    toBeFalsy(): void;
    toBeNull(): void;
    toBeUndefined(): void;
    toBeDefined(): void;
    toBeInstanceOf(constructor: any): void;
    toHaveLength(length: number): void;
    toContain(item: any): void;
    toHaveBeenCalled(): void;
    toHaveBeenCalledWith(...args: any[]): void;
    toHaveBeenCalledTimes(count: number): void;
    not: {
      toBe(expected: any): void;
      toEqual(expected: any): void;
      toBeTruthy(): void;
      toBeFalsy(): void;
      toBeNull(): void;
      toBeUndefined(): void;
      toBeDefined(): void;
      toBeInstanceOf(constructor: any): void;
      toHaveLength(length: number): void;
      toContain(item: any): void;
      toHaveBeenCalled(): void;
      toHaveBeenCalledWith(...args: any[]): void;
      toHaveBeenCalledTimes(count: number): void;
    };
  };
  
  export function mock<T extends (...args: any[]) => any>(
    implementation?: T
  ): jest.Mock<ReturnType<T>, Parameters<T>>;
  
  export function spyOn<T extends object, M extends keyof T>(
    object: T,
    method: M
  ): {
    mockImplementation(fn: (...args: any[]) => any): any;
    mockRestore(): void;
    mockClear(): void;
  };
  
  export const jest: {
    fn: typeof mock;
    spyOn: typeof spyOn;
    unstable_mockModule(moduleName: string, factory: () => any): void;
  };
}

// Add Jest namespace for compatibility
declare namespace jest {
  interface Mock<T = any, Y extends any[] = any[]> {
    (...args: Y): T;
    mockImplementation(fn: (...args: Y) => T): this;
    mockReturnValue(value: T): this;
    mockRestore(): void;
    mockClear(): void;
    mockReset(): void;
    getMockName(): string;
    mockReturnThis(): this;
    mockResolvedValue(value: Awaited<T>): this;
    mockRejectedValue(value: any): this;
    mockReturnValueOnce(value: T): this;
    mockResolvedValueOnce(value: Awaited<T>): this;
    mockRejectedValueOnce(value: any): this;
    mockImplementationOnce(fn: (...args: Y) => T): this;
    mockName(name: string): this;
    getMockImplementation(): ((...args: Y) => T) | undefined;
    mock: {
      calls: Y[];
      instances: any[];
      invocationCallOrder: number[];
      results: Array<{ type: string; value: any }>;
    };
  }
}
