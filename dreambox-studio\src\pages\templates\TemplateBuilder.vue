<template>
  <q-page padding>
    <div class="template-builder">
      <!-- Page header -->
      <div class="row items-center justify-between q-mb-md">
        <div class="col-auto">
          <h1 class="text-h4 q-my-none">
            {{ getPageTitle() }}
          </h1>
        </div>
        <div class="col-auto">
          <q-btn
            flat
            icon="arrow_back"
            :label="t('templates.builder.backToTemplates')"
            color="primary"
            @click="navigateBack()"
          />
        </div>
      </div>

      <!-- Loading state -->
      <div v-if="loading" class="row justify-center q-pa-xl">
        <q-spinner color="primary" size="3em" />
        <div class="col-12 text-center q-mt-md">
          {{ t('templates.builder.loadingTemplate') }}
        </div>
      </div>

      <!-- Error state -->
      <div v-else-if="error" class="row justify-center q-pa-xl">
        <div class="col-12 text-center text-negative">
          <q-icon name="error" size="3em" />
          <div class="text-h6 q-mt-md">{{ error }}</div>
          <q-btn
            color="primary"
            label="Try Again"
            class="q-mt-md"
            @click="loadTemplate"
          />
        </div>
      </div>

      <!-- Template form -->
      <div v-else class="template-form">
        <q-form @submit="saveTemplate" class="q-gutter-md" :disable="isViewMode || isTemplateReadOnly">
          <!-- Basic information card -->
          <q-card flat bordered>
            <q-card-section>
              <div class="text-h6">{{ t('templates.builder.basicInformation') }}</div>
            </q-card-section>
            <q-card-section>
              <div class="row q-col-gutter-md">
                <div class="col-12 col-md-6">
                  <q-input
                    v-model="formData.name"
                    :label="t('templates.builder.templateName')"
                    filled
                    :rules="[val => !!val || t('templates.builder.nameRequired')]"
                    :readonly="isViewMode || isTemplateReadOnly"
                    :disable="isViewMode || isTemplateReadOnly"
                  />
                </div>
                <div class="col-12 col-md-6">
                  <q-select
                    v-model="formData.status"
                    :options="availableStatusOptions"
                    :label="t('templates.status')"
                    filled
                    emit-value
                    map-options
                    :readonly="isViewMode || isTemplateReadOnly"
                    :disable="isViewMode || isTemplateReadOnly"
                  >
                    <template v-slot:option="scope">
                      <q-item v-bind="scope.itemProps">
                        <q-item-section avatar>
                          <q-icon :name="getStatusIcon(scope.opt.value)" :color="getStatusColor(scope.opt.value)" />
                        </q-item-section>
                        <q-item-section>
                          <q-item-label>{{ scope.opt.label }}</q-item-label>
                          <q-item-label caption>{{ getStatusDescription(scope.opt.value) }}</q-item-label>
                        </q-item-section>
                      </q-item>
                    </template>
                    <template v-slot:selected>
                      <div class="row items-center">
                        <q-icon :name="getStatusIcon(formData.status)" :color="getStatusColor(formData.status)" class="q-mr-xs" />
                        {{ getStatusLabel(formData.status) }}
                      </div>
                    </template>
                  </q-select>
                </div>
                <div class="col-12">
                  <q-input
                    v-model="formData.description"
                    :label="t('templates.builder.templateDescription')"
                    type="textarea"
                    filled
                    autogrow
                    :readonly="isViewMode || isTemplateReadOnly"
                    :disable="isViewMode || isTemplateReadOnly"
                  />
                </div>
              </div>
            </q-card-section>
          </q-card>

          <!-- Product Types card -->
          <q-card flat bordered>
            <q-card-section>
              <div class="text-h6">Compatible Product Types</div>
              <div class="text-caption">
                <template v-if="isViewMode || isTemplateReadOnly">
                  Physical products this template can be printed on
                </template>
                <template v-else-if="canEditProductTypes">
                  <span v-if="authStore.state.user?.currentRole === 'admin' && !isNewTemplate">
                    As an admin, you can modify which physical products this template can be printed on
                  </span>
                  <span v-else>
                    Select which physical products this template can be printed on
                  </span>
                </template>
              </div>
            </q-card-section>
            <q-card-section>
              <div class="row q-col-gutter-md">
                <div class="col-12">
                  <q-select
                    v-model="formData.productTypes"
                    :options="productTypeOptions"
                    label="Product Types"
                    filled
                    multiple
                    use-chips
                    emit-value
                    map-options
                    :readonly="!canEditProductTypes || isViewMode"
                  >
                    <template v-slot:option="{ opt, selected, toggleOption }">
                      <q-item :clickable="canEditProductTypes && !isViewMode" @click="toggleOption(opt)">
                        <q-item-section avatar>
                          <q-avatar rounded>
                            <img :src="opt.thumbnail_url || 'https://picsum.photos/seed/default/50/50'">
                          </q-avatar>
                        </q-item-section>
                        <q-item-section>
                          <q-item-label>{{ opt.label }}</q-item-label>
                          <q-item-label caption>{{ opt.category }}</q-item-label>
                        </q-item-section>
                        <q-item-section side v-if="canEditProductTypes && !isViewMode">
                          <q-checkbox :model-value="selected" />
                        </q-item-section>
                      </q-item>
                    </template>
                  </q-select>
                </div>
                <div class="col-12" v-if="formData.productTypes.length > 0">
                  <div class="text-subtitle2 q-mb-sm">Recommended Products</div>
                  <div class="text-caption q-mb-sm">
                    <template v-if="isViewMode || isTemplateReadOnly">
                      Products especially recommended for this template
                    </template>
                    <template v-else>
                      Mark which products are especially recommended for this template
                    </template>
                  </div>
                  <div class="row q-col-gutter-sm">
                    <div v-for="productTypeId in formData.productTypes" :key="productTypeId" class="col-6 col-md-4 col-lg-3">
                      <q-item tag="label" v-ripple :disable="!canEditProductTypes || isViewMode">
                        <q-item-section side>
                          <q-checkbox
                            v-model="formData.recommendedProductTypes"
                            :val="productTypeId"
                            color="primary"
                            :disable="!canEditProductTypes || isViewMode"
                          />
                        </q-item-section>
                        <q-item-section>
                          <q-item-label>{{ getProductTypeName(productTypeId) }}</q-item-label>
                        </q-item-section>
                      </q-item>
                    </div>
                  </div>
                </div>
              </div>
            </q-card-section>
          </q-card>

          <!-- Prompt elements card -->
          <q-card flat bordered>
            <q-card-section>
              <div class="text-h6">{{ t('templates.promptElements') }}</div>
              <div class="text-caption">
                {{ t('templates.builder.definePromptElements') }}
              </div>
            </q-card-section>
            <q-card-section>
              <!-- Always show the PromptElementSelector, but with different modes -->
              <prompt-element-selector
                :template-id="isNewTemplate ? 'new' : (templateId || 0)"
                :local-mode="isNewTemplate"
                :initial-element-types="templateBuilderState.selectedElementTypes"
                :initial-element-values="templateBuilderState.selectedElementValues"
                :read-only="isViewMode || isTemplateReadOnly"
                @update="onElementsUpdate"
              />
            </q-card-section>
          </q-card>

          <!-- Server settings card -->
          <q-card flat bordered>
            <q-card-section>
              <div class="text-h6">{{ t('templates.builder.imageSettings') }}</div>
              <div class="text-caption">
                {{ t('templates.builder.configureImageSettings') }}
              </div>
            </q-card-section>
            <q-card-section>
              <div class="row q-col-gutter-md">
                <div class="col-12 col-md-6">
                  <q-select
                    v-model="formData.imageServerData.model"
                    :options="modelOptions"
                    :label="t('templates.builder.model')"
                    filled
                    :readonly="isViewMode || isTemplateReadOnly"
                    :disable="isViewMode || isTemplateReadOnly"
                  />
                </div>
                <div class="col-12 col-md-6">
                  <q-input
                    v-model.number="formData.imageServerData.steps"
                    :label="t('templates.builder.steps')"
                    type="number"
                    filled
                    min="10"
                    max="150"
                    :readonly="isViewMode || isTemplateReadOnly"
                    :disable="isViewMode || isTemplateReadOnly"
                  />
                </div>
                <div class="col-12 col-md-6">
                  <q-select
                    v-model="formData.imageServerData.aspect_ratio"
                    :options="aspectRatioOptions"
                    :label="t('templates.builder.aspectRatio')"
                    filled
                    :readonly="isViewMode || isTemplateReadOnly"
                    :disable="isViewMode || isTemplateReadOnly"
                  />
                </div>
                <div class="col-12 col-md-6">
                  <q-input
                    v-model.number="formData.imageServerData.seed"
                    :label="t('templates.builder.seed') + ' (' + t('common.optional') + ')'"
                    type="number"
                    filled
                    :readonly="isViewMode || isTemplateReadOnly"
                    :disable="isViewMode || isTemplateReadOnly"
                  />
                </div>
              </div>
            </q-card-section>
          </q-card>

          <!-- Form actions -->
          <div class="row justify-end q-gutter-sm">
            <q-btn
              :label="t('templates.builder.back')"
              color="grey"
              flat
              @click="navigateBack()"
              v-if="isViewMode"
            />
            <template v-else>
              <q-btn
                :label="t('templates.builder.cancel')"
                color="grey"
                flat
                @click="navigateBack()"
              />
              <q-btn
                :label="t('templates.builder.save')"
                type="submit"
                color="primary"
                :loading="saving"
                :disable="isTemplateReadOnly"
              />
            </template>
          </div>
        </q-form>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, inject } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import { useTemplateService } from 'src/services/templateService';
import { useProductTypeService } from 'src/services/productTypeService';
import { useAuthStore } from 'src/stores/auth';
import type { Template } from 'src/types/Template';
import type { PromptElement } from 'src/services/promptElementsService';
// No need to import ProductTypeBasic as it's not used directly
import PromptElementSelector from 'src/components/templates/PromptElementSelector.vue';

// Router
const router = useRouter();
const route = useRoute();
const $q = useQuasar();

// i18n
const { t } = useI18n();

// Template service
const {
  loading,
  error,
  getTemplate,
  createTemplate,
  updateTemplate
} = useTemplateService();

// Product type service
const {
  // Only using the functions we need
  getProductTypes,
  getTemplateProductTypes,
  associateTemplateProductTypes
} = useProductTypeService();

// Template data
const template = ref<Template | null>(null);
const saving = ref(false);

// Define the image server data type
interface ImageServerData {
  model: string;
  steps: number;
  aspect_ratio: string;
  seed: number | null;
}

// Form data
const formData = ref({
  name: '',
  description: '',
  status: 'draft',
  published: false,
  productTypes: [] as number[],
  recommendedProductTypes: [] as number[],
  imageServerData: {
    model: 'stable-diffusion-xl-1024-v1-0',
    steps: 30,
    aspect_ratio: '1:1',
    seed: null as number | null
  } as ImageServerData
});

// Product type options
const productTypeOptions = ref<{ label: string; value: number; category: string | null; thumbnail_url: string | null }[]>([]);

// Get product type name by ID
function getProductTypeName(id: number): string {
  const option = productTypeOptions.value.find(opt => opt.value === id);
  return option ? option.label : `Product Type ${id}`;
}

// Get the template builder state from the MainLayout component
const templateBuilderState = inject('templateBuilderState') as {
  selectedElementTypes: number[];
  selectedElementValues: number[];
  imageServerData: ImageServerData;
};

// We're now using the templateBuilderState directly with the PromptElementSelector
// No need for local computed references

// Auth store for role checks
const authStore = useAuthStore();

// Status options
const allStatusOptions = [
  { label: 'Draft', value: 'draft' },
  { label: 'Pending Approval', value: 'pending' },
  { label: 'Active', value: 'active' },
  { label: 'Archived', value: 'archived' }
];

// Computed property to get available status options based on user role and current status
const availableStatusOptions = computed(() => {
  // If it's a new template, only draft is available
  if (isNewTemplate.value) {
    return [{ label: 'Draft', value: 'draft' }];
  }

  const currentStatus = formData.value.status;

  // Super admin can set any status
  if (authStore.state.user?.currentRole === 'super-admin') {
    return allStatusOptions;
  }

  // Admin role permissions
  if (authStore.state.user?.currentRole === 'admin') {
    switch (currentStatus) {
      case 'draft':
        return [
          { label: 'Draft', value: 'draft' },
          { label: 'Active', value: 'active' }
        ];
      case 'pending':
        return [
          { label: 'Draft', value: 'draft' },
          { label: 'Pending Approval', value: 'pending' },
          { label: 'Active', value: 'active' }
        ];
      case 'active':
        return [
          { label: 'Active', value: 'active' },
          { label: 'Archived', value: 'archived' }
        ];
      case 'archived':
        return [{ label: 'Archived', value: 'archived' }];
      default:
        return [{ label: currentStatus, value: currentStatus }];
    }
  }

  // Designer role permissions
  if (authStore.state.user?.currentRole === 'designer') {
    switch (currentStatus) {
      case 'draft':
        return [
          { label: 'Draft', value: 'draft' },
          { label: 'Pending Approval', value: 'pending' }
        ];
      case 'pending':
      case 'active':
      case 'archived':
        // Designers can't change status once it's submitted, active or archived
        return [{ label: getStatusLabel(currentStatus), value: currentStatus }];
      default:
        return [{ label: currentStatus, value: currentStatus }];
    }
  }

  // Default - just show current status
  return [{ label: getStatusLabel(currentStatus), value: currentStatus }];
});

// Helper functions for status display
function getStatusLabel(status: string): string {
  const option = allStatusOptions.find(opt => opt.value === status);
  return option ? option.label : status;
}

function getStatusColor(status: string): string {
  switch (status) {
    case 'draft': return 'blue';
    case 'pending': return 'orange';
    case 'active': return 'positive';
    case 'archived': return 'grey';
    default: return 'grey';
  }
}

function getStatusIcon(status: string): string {
  switch (status) {
    case 'draft': return 'edit';
    case 'pending': return 'hourglass_empty';
    case 'active': return 'check_circle';
    case 'archived': return 'archive';
    default: return 'help';
  }
}

function getStatusDescription(status: string): string {
  switch (status) {
    case 'draft':
      return 'Template is being created and can be edited';
    case 'pending':
      return 'Template is awaiting admin approval';
    case 'active':
      return 'Template is approved and available for use';
    case 'archived':
      return 'Template is no longer in use';
    default:
      return '';
  }
}

const modelOptions = [
  'stable-diffusion-xl-1024-v1-0',
  'stable-diffusion-v1-5',
  'stable-diffusion-v2-1'
];

const aspectRatioOptions = [
  '1:1',
  '4:3',
  '3:4',
  '16:9',
  '9:16'
];

// Computed
const templateId = computed(() => {
  const id = route.params.id;
  return id === 'new' ? null : Number(id);
});

const isNewTemplate = computed(() => {
  console.log('Route params id:', route.params.id);
  return route.params.id === 'new';
});

// Check if we're in view mode (from the route query)
const isViewMode = computed(() => {
  return route.query.mode === 'view';
});

// Check if the template should be read-only based on its status
const isTemplateReadOnly = computed(() => {
  // New templates are always editable
  if (isNewTemplate.value) {
    return false;
  }

  // Active and archived templates are read-only
  return ['active', 'archived'].includes(formData.value.status);
});

// Check if the user can edit product types
const canEditProductTypes = computed(() => {
  // In view mode, nothing is editable
  if (isViewMode.value) {
    return false;
  }

  // New templates are always editable
  if (isNewTemplate.value) {
    return true;
  }

  // Admins can edit product types for any template status
  if (authStore.state.user?.currentRole === 'admin' ||
      authStore.state.user?.currentRole === 'super-admin') {
    return true;
  }

  // Designers can only edit product types for draft templates
  if (authStore.state.user?.currentRole === 'designer') {
    return formData.value.status === 'draft';
  }

  return false;
});

// Get the page title based on the mode and template status
function getPageTitle(): string {
  if (isNewTemplate.value) {
    return t('templates.builder.newTemplate');
  }

  if (isViewMode.value) {
    return t('templates.builder.viewTemplate');
  }

  if (isTemplateReadOnly.value) {
    return `${t('templates.title')}: ${formData.value.name}`;
  }

  return t('templates.builder.editTemplate');
}

// Load template data
async function loadTemplate() {
  // Load product types for the dropdown
  await loadProductTypes();

  if (!isNewTemplate.value && templateId.value) {
    try {
      const data = await getTemplate(templateId.value);
      if (data) {
        template.value = data;

        // Get product types for this template
        const templateProductTypes = await getTemplateProductTypes(templateId.value);

        // Extract product type IDs and recommended IDs
        const productTypeIds = templateProductTypes.map(pt => pt.id);
        const recommendedIds = templateProductTypes
          .filter(pt => pt.recommended)
          .map(pt => pt.id);

        // Populate form data
        formData.value = {
          name: data.name,
          description: data.description || '',
          status: data.status,
          published: data.published,
          productTypes: productTypeIds,
          recommendedProductTypes: recommendedIds,
          imageServerData: (data.image_server_data as unknown as ImageServerData) || {
            model: 'stable-diffusion-xl-1024-v1-0',
            steps: 30,
            aspect_ratio: '1:1',
            seed: null
          }
        };

        // Load element types and values
        if (data.element_types && Array.isArray(data.element_types)) {
          templateBuilderState.selectedElementTypes = data.element_types;
        }

        if (data.element_values && Array.isArray(data.element_values)) {
          templateBuilderState.selectedElementValues = data.element_values;
        }
      }
    } catch (err) {
      console.error('Error loading template:', err);
    }
  }
}

// Load product types
async function loadProductTypes() {
  try {
    const types = await getProductTypes();
    productTypeOptions.value = types.map(type => ({
      label: type.name,
      value: type.id,
      category: type.category,
      thumbnail_url: type.thumbnail_url
    }));
  } catch (err) {
    console.error('Error loading product types:', err);
  }
}

// Save template
async function saveTemplate() {
  try {
    saving.value = true;

    const templateData: Partial<Template> = {
      name: formData.value.name,
      description: formData.value.description,
      status: formData.value.status as 'draft' | 'pending' | 'active' | 'archived',
      // Set published based on status - only active templates are published
      published: formData.value.status === 'active',
      image_server_data: formData.value.imageServerData
    };

    // Add selected element types and values
    if (templateBuilderState.selectedElementTypes.length > 0) {
      templateData.element_types = templateBuilderState.selectedElementTypes;
    }

    if (templateBuilderState.selectedElementValues.length > 0) {
      templateData.element_values = templateBuilderState.selectedElementValues;
    }

    let result: Template | null;

    if (isNewTemplate.value) {
      // Create new template
      result = await createTemplate(templateData);
    } else if (templateId.value) {
      // Update existing template
      result = await updateTemplate(templateId.value, templateData);
    } else {
      throw new Error('Invalid template ID');
    }

    if (result) {
      // Save product types associations
      if (formData.value.productTypes.length > 0) {
        await associateTemplateProductTypes(
          result.id,
          formData.value.productTypes,
          formData.value.recommendedProductTypes
        );
      }

      $q.notify({
        type: 'positive',
        message: t('templates.builder.templateSaved')
      });

      // Navigate back to templates list
      navigateBack();
    } else {
      throw new Error(`Failed to ${isNewTemplate.value ? 'create' : 'update'} template`);
    }
  } catch {
    // Error is handled via the error.value
    $q.notify({
      type: 'negative',
      message: error.value || t('templates.builder.errorSaving')
    });
  } finally {
    saving.value = false;
  }
}

// Navigate back to templates list
function navigateBack() {
  // Get the current role path prefix
  const rolePrefix = route.path.startsWith('/admin') ? '/admin' : '/designer';

  // Set the left drawer tab to filters before navigation
  document.dispatchEvent(new CustomEvent('set-left-drawer-tab', { detail: 'filters' }));

  void router.push(`${rolePrefix}/templates`);
}

// Initialize
onMounted(() => {
  console.log('TemplateBuilder mounted, route params:', route.params);
  console.log('isNewTemplate:', isNewTemplate.value);
  console.log('templateId:', templateId.value);
  console.log('templateBuilderState:', templateBuilderState);

  void loadTemplate();

  // No longer automatically opening the left drawer
});

// Handle elements update from PromptElementSelector
function onElementsUpdate(elements: PromptElement[]) {
  console.log('Elements updated:', elements);

  // Update the templateBuilderState with the new element values
  templateBuilderState.selectedElementValues = elements.map(element => element.id);

  // Also update the selectedElementTypes based on the elements
  const typeIds = [...new Set(elements.map(element => element.type_id))];
  templateBuilderState.selectedElementTypes = typeIds;
}

// Watch for changes in selected element types
watch(() => templateBuilderState.selectedElementTypes, (newTypes, oldTypes) => {
  // If a type is deselected, remove its values from selectedElementValues
  if (newTypes.length < oldTypes.length) {
    // Find types that were removed
    const removedTypes = oldTypes.filter(type => !newTypes.includes(type));

    // Remove values for those types
    // This would require knowing which values belong to which types
    // For now, we'll just clear all values if any type is deselected
    if (removedTypes.length > 0) {
      templateBuilderState.selectedElementValues = [];
    }
  }
});
</script>

<style lang="scss" scoped>
.template-builder {
  // Add any specific styles here
}

.drawer-tabs {
  height: 50px;
}

.drawer-panels {
  height: calc(100% - 50px);
}
</style>
