import { describe, it, expect, vi, beforeEach } from 'vitest';
import { commandRegistry } from '../../../src/services/commands/registry';
import type { CommandDefinition } from '../../../src/services/commands/types';

describe('CommandRegistry', () => {
  // Clear the registry before each test
  beforeEach(() => {
    commandRegistry.clear();
  });

  it('registers a command', () => {
    const testCommand: CommandDefinition = {
      name: 'testCommand',
      description: 'A test command',
      handler: vi.fn().mockReturnValue({ success: true }),
      examples: ['test command']
    };

    commandRegistry.register(testCommand);
    const commands = commandRegistry.getAvailableCommands();
    
    expect(commands).toHaveLength(1);
    expect(commands[0].name).toBe('testCommand');
    expect(commands[0].description).toBe('A test command');
    expect(commands[0].examples).toEqual(['test command']);
  });

  it('updates an existing command', () => {
    // Register initial command
    commandRegistry.register({
      name: 'testCommand',
      description: 'Initial description',
      handler: vi.fn(),
      examples: ['initial example']
    });

    // Update the command
    commandRegistry.register({
      name: 'testCommand',
      description: 'Updated description',
      handler: vi.fn(),
      examples: ['updated example']
    });

    const commands = commandRegistry.getAvailableCommands();
    
    expect(commands).toHaveLength(1);
    expect(commands[0].description).toBe('Updated description');
    expect(commands[0].examples).toEqual(['updated example']);
  });

  it('executes a command successfully', async () => {
    const handler = vi.fn().mockReturnValue({ success: true, data: 'test result' });
    
    commandRegistry.register({
      name: 'testCommand',
      description: 'A test command',
      handler,
      examples: ['test command']
    });

    const result = await commandRegistry.execute('testCommand');
    
    expect(handler).toHaveBeenCalled();
    expect(result).toEqual({ success: true, data: 'test result' });
  });

  it('returns error when executing non-existent command', async () => {
    const result = await commandRegistry.execute('nonExistentCommand');
    
    expect(result).toEqual({
      success: false,
      message: 'Command not found: nonExistentCommand'
    });
  });

  it('validates required parameters', async () => {
    commandRegistry.register({
      name: 'paramCommand',
      description: 'A command with parameters',
      handler: vi.fn(),
      parameters: [
        {
          name: 'requiredParam',
          type: 'string',
          required: true,
          description: 'A required parameter'
        }
      ]
    });

    const result = await commandRegistry.execute('paramCommand', {});
    
    expect(result).toEqual({
      success: false,
      message: 'Missing required parameters: requiredParam'
    });
  });

  it('passes parameters to the handler', async () => {
    const handler = vi.fn().mockReturnValue({ success: true });
    
    commandRegistry.register({
      name: 'paramCommand',
      description: 'A command with parameters',
      handler,
      parameters: [
        {
          name: 'testParam',
          type: 'string',
          required: true,
          description: 'A test parameter'
        }
      ]
    });

    await commandRegistry.execute('paramCommand', { testParam: 'test value' });
    
    expect(handler).toHaveBeenCalledWith({ testParam: 'test value' });
  });

  it('handles errors in command execution', async () => {
    const handler = vi.fn().mockImplementation(() => {
      throw new Error('Test error');
    });
    
    commandRegistry.register({
      name: 'errorCommand',
      description: 'A command that throws an error',
      handler
    });

    const result = await commandRegistry.execute('errorCommand');
    
    expect(result).toEqual({
      success: false,
      message: 'Test error'
    });
  });

  it('wraps non-CommandResult return values', async () => {
    const handler = vi.fn().mockReturnValue('simple string result');
    
    commandRegistry.register({
      name: 'simpleCommand',
      description: 'A command that returns a simple value',
      handler
    });

    const result = await commandRegistry.execute('simpleCommand');
    
    expect(result).toEqual({
      success: true,
      data: 'simple string result'
    });
  });

  it('gets a specific command by name', () => {
    const testCommand: CommandDefinition = {
      name: 'testCommand',
      description: 'A test command',
      handler: vi.fn(),
      examples: ['test command']
    };

    commandRegistry.register(testCommand);
    const command = commandRegistry.getCommand('testCommand');
    
    expect(command).toBeDefined();
    expect(command?.name).toBe('testCommand');
    expect(command?.description).toBe('A test command');
  });

  it('returns undefined for non-existent command', () => {
    const command = commandRegistry.getCommand('nonExistentCommand');
    expect(command).toBeUndefined();
  });

  it('clears all registered commands', () => {
    // Register some commands
    commandRegistry.register({
      name: 'command1',
      description: 'Command 1',
      handler: vi.fn()
    });
    
    commandRegistry.register({
      name: 'command2',
      description: 'Command 2',
      handler: vi.fn()
    });
    
    // Verify commands are registered
    expect(commandRegistry.getAvailableCommands()).toHaveLength(2);
    
    // Clear commands
    commandRegistry.clear();
    
    // Verify commands are cleared
    expect(commandRegistry.getAvailableCommands()).toHaveLength(0);
  });
});
