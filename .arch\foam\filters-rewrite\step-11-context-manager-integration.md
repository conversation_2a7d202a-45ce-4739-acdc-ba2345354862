# Step 11: Context Manager Integration

## Overview

This document outlines how the new filter engine will integrate with the application's context manager. This integration is crucial for ensuring that filters are contextually aware, persistent across navigation, and properly coordinated with other UI components.

## Context Manager Integration

### Goals

1. **Contextual Filter Activation**: Activate appropriate filters based on the current application context
2. **Persistent Filter State**: Save and restore filter states as part of the broader application context
3. **Hierarchical Filter Organization**: Organize filters using the context manager's hierarchical naming convention
4. **Coordinated UI Updates**: Ensure filter UI and table UI update in a coordinated way when context changes
5. **Centralized State Management**: Reduce event-based communication by centralizing state management

### Context-Aware Filter Store

The filter store will be extended to be aware of the current context:

```typescript
// Extended FilterState interface
interface FilterState {
  // Existing properties
  currentTable: string | null;
  currentFilterId: number | null;
  savedFilters: SavedFilter[];
  columns: FilterDefinition[];
  activeFilters: Record<string, any>;
  specializedFilters: Record<string, any>;
  isLoading: boolean;
  hasUnsavedChanges: boolean;
  
  // New properties for context integration
  currentContext: string | null;
  contextFilters: Record<string, number>; // Maps context paths to filter IDs
}

// Extended SavedFilter interface
interface SavedFilter {
  // Existing properties
  id: number;
  user_id: number;
  name: string;
  description: string | null;
  table_name: string;
  context: string;
  configuration: {
    columns: Array<{
      id: string;
      visible?: boolean;
      section?: 'main' | 'expanded';
      order?: number;
      filterValue?: any;
    }>;
    specializedFilters?: Record<string, any>;
  };
  is_default: boolean;
  created_at: string;
  updated_at: string;
  
  // New properties for context integration
  contexts: string[]; // List of contexts this filter applies to
  is_context_default: boolean; // Whether this is the default filter for its contexts
}
```

### Context-Filter Manager

A new class will be created to manage the relationship between contexts and filters:

```typescript
// src/lib/filters/ContextFilterManager.ts
import { useContextStore } from '@/stores/contextStore';
import { useFilterStore } from '@/stores/filterStore';

export class ContextFilterManager {
  private static instance: ContextFilterManager;
  private contextStore = useContextStore();
  private filterStore = useFilterStore();
  
  private constructor() {
    // Initialize and set up context change listeners
    this.setupContextListeners();
  }
  
  public static getInstance(): ContextFilterManager {
    if (!ContextFilterManager.instance) {
      ContextFilterManager.instance = new ContextFilterManager();
    }
    return ContextFilterManager.instance;
  }
  
  private setupContextListeners(): void {
    // Listen for context changes
    this.contextStore.$subscribe((mutation, state) => {
      if (mutation.type === 'setCurrentContext') {
        this.handleContextChanged(state.currentContext);
      }
    });
  }
  
  private handleContextChanged(newContext: string): void {
    // Extract table name from context
    const tableName = this.extractTableFromContext(newContext);
    
    if (tableName) {
      // Update the filter store with the new context and table
      this.filterStore.setContext(newContext, tableName);
      
      // Load the appropriate filter for this context
      this.loadFilterForContext(newContext);
    }
  }
  
  private extractTableFromContext(context: string): string | null {
    // Extract table name from context path
    // e.g., 'templates.list' -> 'templates'
    const parts = context.split('.');
    return parts.length > 0 ? parts[0] : null;
  }
  
  public loadFilterForContext(context: string): void {
    // Check if there's a specific filter for this context
    const filterId = this.filterStore.contextFilters[context];
    
    if (filterId) {
      // Load the specific filter for this context
      this.filterStore.loadFilter(filterId);
    } else {
      // Try to find a filter that applies to this context
      const filter = this.filterStore.savedFilters.find(f => 
        f.contexts && f.contexts.some(c => context.startsWith(c)) && f.is_context_default
      );
      
      if (filter) {
        this.filterStore.loadFilter(filter.id);
      } else {
        // Load the default filter for the table
        const defaultFilter = this.filterStore.savedFilters.find(f => 
          f.table_name === this.extractTableFromContext(context) && f.is_default
        );
        
        if (defaultFilter) {
          this.filterStore.loadFilter(defaultFilter.id);
        } else {
          // No filter found, reset to base configuration
          this.filterStore.clearAllFilters();
        }
      }
    }
  }
  
  public saveFilterForContext(filterId: number, context: string): void {
    // Associate a filter with a specific context
    this.filterStore.associateFilterWithContext(filterId, context);
  }
  
  public setContextDefaultFilter(filterId: number, context: string): void {
    // Set a filter as the default for a context
    this.filterStore.setContextDefaultFilter(filterId, context);
  }
}

// Export a singleton instance
export const contextFilterManager = ContextFilterManager.getInstance();
```

### Extended Filter Store Actions

The filter store will be extended with new actions for context integration:

```typescript
// Additional actions for the filter store
actions: {
  // Existing actions...
  
  // Set the current context and table
  async setContext(context: string, tableName: string) {
    this.currentContext = context;
    
    // Only change the table if it's different
    if (this.currentTable !== tableName) {
      await this.setTable(tableName);
    }
  },
  
  // Associate a filter with a context
  async associateFilterWithContext(filterId: number, context: string) {
    // Update the contextFilters map
    this.contextFilters[context] = filterId;
    
    // Update the filter's contexts array
    const filter = this.savedFilters.find(f => f.id === filterId);
    if (filter) {
      if (!filter.contexts) {
        filter.contexts = [];
      }
      
      if (!filter.contexts.includes(context)) {
        filter.contexts.push(context);
      }
      
      // Update the filter in the database
      const { error } = await supabase
        .from('user_filters')
        .update({
          contexts: filter.contexts
        })
        .eq('id', filterId);
      
      if (error) {
        console.error('Error updating filter contexts:', error);
      }
    }
    
    // Save the context-filter mapping to the database
    await this.saveContextFilterMapping();
  },
  
  // Set a filter as the default for a context
  async setContextDefaultFilter(filterId: number, context: string) {
    // First, unset any existing context default filters
    for (const filter of this.savedFilters) {
      if (filter.contexts && filter.contexts.includes(context) && filter.is_context_default) {
        filter.is_context_default = false;
        
        // Update in database
        const { error } = await supabase
          .from('user_filters')
          .update({
            is_context_default: false
          })
          .eq('id', filter.id);
        
        if (error) {
          console.error('Error updating filter context default:', error);
        }
      }
    }
    
    // Set the new context default filter
    const filter = this.savedFilters.find(f => f.id === filterId);
    if (filter) {
      filter.is_context_default = true;
      
      // Ensure the filter is associated with this context
      await this.associateFilterWithContext(filterId, context);
      
      // Update in database
      const { error } = await supabase
        .from('user_filters')
        .update({
          is_context_default: true
        })
        .eq('id', filterId);
      
      if (error) {
        console.error('Error updating filter context default:', error);
      }
    }
  },
  
  // Save the context-filter mapping to the database
  async saveContextFilterMapping() {
    const authStore = useAuthStore();
    if (!authStore.state.user?.id) {
      console.warn('Cannot save context filter mapping: User not logged in');
      return;
    }
    
    // Get the app_user.id from the user_id
    const { data: userData, error: userError } = await supabase
      .from('app_users')
      .select('id')
      .eq('user_id', authStore.state.user.id)
      .single();
    
    if (userError) {
      console.error('Error fetching app_user id:', userError);
      return;
    }
    
    const appUserId = userData.id;
    
    // Save the context filter mapping
    const { error } = await supabase
      .from('user_context_filters')
      .upsert({
        user_id: appUserId,
        context_filters: this.contextFilters
      })
      .eq('user_id', appUserId);
    
    if (error) {
      console.error('Error saving context filter mapping:', error);
    }
  },
  
  // Load the context-filter mapping from the database
  async loadContextFilterMapping() {
    const authStore = useAuthStore();
    if (!authStore.state.user?.id) {
      console.warn('Cannot load context filter mapping: User not logged in');
      return;
    }
    
    // Get the app_user.id from the user_id
    const { data: userData, error: userError } = await supabase
      .from('app_users')
      .select('id')
      .eq('user_id', authStore.state.user.id)
      .single();
    
    if (userError) {
      console.error('Error fetching app_user id:', userError);
      return;
    }
    
    const appUserId = userData.id;
    
    // Load the context filter mapping
    const { data, error } = await supabase
      .from('user_context_filters')
      .select('context_filters')
      .eq('user_id', appUserId)
      .single();
    
    if (error) {
      if (error.code !== 'PGRST116') { // PGRST116 is "Row not found" error
        console.error('Error loading context filter mapping:', error);
      }
      return;
    }
    
    if (data && data.context_filters) {
      this.contextFilters = data.context_filters;
    }
  }
}
```

### Database Schema Updates

A new table will be needed to store the context-filter mappings:

```sql
-- Create a table for context-filter mappings
CREATE TABLE user_context_filters (
  id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
  user_id BIGINT REFERENCES app_users(id),
  context_filters JSONB NOT NULL DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add unique constraint on user_id
ALTER TABLE user_context_filters ADD CONSTRAINT user_context_filters_user_id_key UNIQUE (user_id);

-- Add contexts and is_context_default columns to user_filters table
ALTER TABLE user_filters ADD COLUMN contexts TEXT[] DEFAULT '{}';
ALTER TABLE user_filters ADD COLUMN is_context_default BOOLEAN DEFAULT FALSE;
```

### Integration with FilterPanel Component

The FilterPanel component will be updated to be context-aware:

```vue
<template>
  <div class="filter-panel">
    <!-- Context information -->
    <div v-if="currentContext" class="context-info q-mb-md">
      <div class="text-subtitle1">{{ contextDisplayName }}</div>
    </div>
    
    <!-- Saved Filter Selector with context awareness -->
    <saved-filter-selector
      :filters="filterStore.filtersByTable"
      :current-filter-id="filterStore.currentFilterId"
      :has-unsaved-changes="filterStore.hasUnsavedChanges"
      :current-context="filterStore.currentContext"
      @select="loadFilter"
      @save="showSaveFilterDialog"
      @delete="deleteFilter"
      @clear="clearAllFilters"
      @set-context-default="setContextDefault"
    />
    
    <!-- Rest of the component remains the same -->
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useFilterStore } from '@/stores/filterStore';
import { useContextStore } from '@/stores/contextStore';
import { contextFilterManager } from '@/lib/filters/ContextFilterManager';

// Stores
const filterStore = useFilterStore();
const contextStore = useContextStore();

// Computed
const currentContext = computed(() => filterStore.currentContext);
const contextDisplayName = computed(() => {
  if (!currentContext.value) return '';
  
  // Convert context path to display name
  // e.g., 'templates.list' -> 'Templates List'
  return currentContext.value
    .split('.')
    .map(part => part.charAt(0).toUpperCase() + part.slice(1))
    .join(' ');
});

// Methods
function loadFilter(filterId) {
  filterStore.loadFilter(filterId);
}

function showSaveFilterDialog() {
  // Show dialog to save filter
  // Include option to associate with current context
}

function deleteFilter(filterId) {
  filterStore.deleteFilter(filterId);
}

function clearAllFilters() {
  filterStore.clearAllFilters();
}

function setContextDefault(filterId) {
  if (currentContext.value) {
    contextFilterManager.setContextDefaultFilter(filterId, currentContext.value);
  }
}
</script>
```

### Save Filter Dialog with Context Options

The save filter dialog will be extended to include context options:

```vue
<template>
  <q-dialog v-model="showDialog" persistent>
    <q-card style="min-width: 350px">
      <q-card-section>
        <div class="text-h6">Save Filter</div>
      </q-card-section>
      
      <q-card-section>
        <q-input
          v-model="filterName"
          label="Filter Name"
          dense
          autofocus
          @keyup.enter="saveFilter"
        />
        
        <q-input
          v-model="filterDescription"
          label="Description"
          dense
          type="textarea"
          class="q-mt-sm"
        />
        
        <q-checkbox
          v-model="makeDefault"
          label="Make default for this table"
          class="q-mt-sm"
        />
        
        <q-checkbox
          v-if="currentContext"
          v-model="associateWithContext"
          label="Associate with current context"
          class="q-mt-sm"
        />
        
        <q-checkbox
          v-if="currentContext && associateWithContext"
          v-model="makeContextDefault"
          label="Make default for this context"
          class="q-mt-sm"
        />
      </q-card-section>
      
      <q-card-actions align="right">
        <q-btn flat label="Cancel" color="primary" v-close-popup />
        <q-btn flat label="Save" color="primary" @click="saveFilter" :disable="!filterName" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useFilterStore } from '@/stores/filterStore';
import { contextFilterManager } from '@/lib/filters/ContextFilterManager';

// Props
const props = defineProps({
  showDialog: Boolean,
  editingFilterId: Number
});

// Emits
const emit = defineEmits(['update:showDialog', 'saved']);

// Stores
const filterStore = useFilterStore();

// State
const filterName = ref('');
const filterDescription = ref('');
const makeDefault = ref(false);
const associateWithContext = ref(false);
const makeContextDefault = ref(false);

// Computed
const currentContext = computed(() => filterStore.currentContext);

// Methods
async function saveFilter() {
  if (!filterName.value) return;
  
  // Save the filter
  const filterId = await filterStore.saveCurrentFilter(
    filterName.value,
    filterDescription.value,
    makeDefault.value
  );
  
  // If successful and we want to associate with context
  if (filterId && associateWithContext.value && currentContext.value) {
    // Associate filter with context
    contextFilterManager.saveFilterForContext(filterId, currentContext.value);
    
    // If we also want to make it the context default
    if (makeContextDefault.value) {
      contextFilterManager.setContextDefaultFilter(filterId, currentContext.value);
    }
  }
  
  // Close dialog and emit saved event
  emit('update:showDialog', false);
  emit('saved', filterId);
}
</script>
```

## Implementation Plan

1. **Database Schema Updates**:
   - Add `contexts` and `is_context_default` columns to `user_filters` table
   - Create `user_context_filters` table

2. **Filter Store Extensions**:
   - Add context-related state properties
   - Implement context-related actions

3. **Context Filter Manager**:
   - Create the ContextFilterManager class
   - Implement context change handling

4. **UI Component Updates**:
   - Update FilterPanel to be context-aware
   - Create/update SaveFilterDialog with context options

5. **Integration Testing**:
   - Test context changes trigger appropriate filter changes
   - Test saving filters with context associations
   - Test loading context-specific filters

## Expected Outcome

After implementing this context manager integration, the filter engine will:

1. Be aware of the current application context
2. Load appropriate filters based on context changes
3. Allow users to associate filters with specific contexts
4. Support default filters for specific contexts
5. Provide a more seamless and contextual filtering experience

This integration will make the filter engine a more integral part of the application, with filters that automatically adapt to the user's current context.
