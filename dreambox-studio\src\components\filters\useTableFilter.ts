import { computed, ref, watch } from 'vue';
import { useTableFilterStore } from 'src/stores/tableFilterStore';

/**
 * Hook to integrate table filter functionality with a table component
 * @param tableName The name of the table to filter
 * @param rawData The raw data array to be filtered
 * @returns Object with filtered data and column configuration
 */
import type { ComputedRef, Ref } from 'vue';

export function useTableFilter<T extends Record<string, unknown>>(tableName: string, rawData: { value: T[] } | ComputedRef<{ value: T[] }> | Ref<{ value: T[] }>) {
  const filterStore = useTableFilterStore();
  const isInitialized = ref(false);

  // Initialize the filter store with the table name
  const initialize = async () => {
    if (!isInitialized.value) {
      await filterStore.setTable(tableName);
      isInitialized.value = true;
    }
  };

  // Call initialize immediately
  void initialize();

  // Computed properties for filtered data and columns
  const filteredData = computed(() => {
    if (!isInitialized.value) return [];

    // Handle different types of rawData
    let dataValue: T[] = [];

    // Check if rawData is a plain object with value property
    if ('value' in rawData && !('effect' in rawData) && !('__v_isRef' in rawData)) {
      // Direct object
      dataValue = rawData.value as T[];
    }
    // Check if it's a ComputedRef
    else if ('effect' in rawData) {
      // ComputedRef
      dataValue = (rawData as ComputedRef<{ value: T[] }>).value.value;
    }
    // Must be a Ref
    else {
      // Ref
      dataValue = (rawData as Ref<{ value: T[] }>).value.value;
    }
    if (!dataValue) return [];

    return filterStore.applyFilters(dataValue);
  });

  const visibleColumns = computed(() => {
    return filterStore.visibleMainColumns.map(col => ({
      name: col.id,
      label: col.name,
      field: col.field,
      sortable: true,
      align: 'left'
    }));
  });

  const expandedColumns = computed(() => {
    return filterStore.visibleExpandedColumns.map(col => ({
      name: col.id,
      label: col.name,
      field: col.field
    }));
  });

  // Watch for changes in the raw data
  // Use { deep: false } to avoid unnecessary updates when only the content changes
  // This is important when adding new items to the table without reloading
  watch(() => rawData.value, () => {
    // No need to do anything here, the computed properties will update automatically
  }, { deep: false });

  return {
    filteredData,
    visibleColumns,
    expandedColumns,
    isInitialized
  };
}
