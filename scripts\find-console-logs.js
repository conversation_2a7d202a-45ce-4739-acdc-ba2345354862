const fs = require('fs');
const path = require('path');

// Object to store file paths and their console.log count
const fileConsoleLogCount = {};

// Function to count console.log statements in a file
function countConsoleLogs(filePath) {
  try {
    // Read the file
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Count console.log occurrences
    const logMatches = content.match(/console\.log\(/g) || [];
    const errorMatches = content.match(/console\.error\(/g) || [];
    
    const totalCount = logMatches.length + errorMatches.length;
    
    if (totalCount > 0) {
      fileConsoleLogCount[filePath] = totalCount;
    }
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error);
  }
}

// Function to process a directory recursively
function processDirectory(directory) {
  const files = fs.readdirSync(directory);
  
  for (const file of files) {
    const filePath = path.join(directory, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isDirectory()) {
      // Recursively process subdirectories
      processDirectory(filePath);
    } else if (stats.isFile() && (file.endsWith('.js') || file.endsWith('.ts') || file.endsWith('.vue'))) {
      // Process JavaScript, TypeScript, and Vue files
      countConsoleLogs(filePath);
    }
  }
}

// Start processing from the src directory
processDirectory('dreambox-studio/src');

// Sort files by console.log count in descending order
const sortedFiles = Object.entries(fileConsoleLogCount)
  .sort((a, b) => b[1] - a[1])
  .map(([filePath, count]) => ({ filePath, count }));

// Print the top 20 files with the most console.log statements
console.log('Top 20 files with the most console.log statements:');
sortedFiles.slice(0, 20).forEach((file, index) => {
  console.log(`${index + 1}. ${file.filePath}: ${file.count} console.log statements`);
});

// Write the results to a file
fs.writeFileSync('console-log-report.json', JSON.stringify(sortedFiles, null, 2), 'utf8');
console.log('Full report written to console-log-report.json');
