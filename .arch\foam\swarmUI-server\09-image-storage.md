# Step 9: Image Storage for SwarmUI

This document outlines the implementation of image storage for images generated by SwarmUI, using the consolidated approach to stay within Supabase's free plan limits.

## 9.1 Consolidated Approach

Instead of creating separate storage mechanisms for SwarmUI-generated images, we'll use the same S3-based storage system described in the [Image Organization](../image-organization/02-database-schema.md) documentation. This approach:

1. Provides a unified storage solution for all images
2. Reduces the number of Edge Functions needed
3. Simplifies the codebase

For details on the consolidated Edge Functions approach, see [Consolidated Edge Functions](./consolidated-edge-functions.md).

## 9.2 S3 Bucket Setup

We'll use the same S3 bucket (`dreambox-studio-images`) for storing SwarmUI-generated images. This bucket should be configured with appropriate CORS settings to allow uploads from both your production domain and development environments:

```json
[
  {
    "AllowedHeaders": ["*"],
    "AllowedMethods": ["GET", "PUT", "POST", "DELETE"],
    "AllowedOrigins": [
      "https://your-app-domain.com",
      "http://localhost:8080",
      "https://localhost:8080",
      "http://localhost:9000",
      "https://localhost:9000",
      "http://localhost:9080",
      "https://localhost:9080",
      "file://*"
    ],
    "ExposeHeaders": ["ETag"],
    "MaxAgeSeconds": 3000
  }
]
```

## 9.3 Database Schema

We'll use the `s3_images` table for storing metadata about all images, including those generated by SwarmUI. The schema includes fields specifically for AI-generated images:

```sql
-- AI generation metadata fields in s3_images table
prompt TEXT,
negative_prompt TEXT,
seed BIGINT,
model_used TEXT,
```

## 9.4 Create Image Service

Instead of creating a separate image storage service for SwarmUI, we'll use the unified `imageService` that works with our S3 storage:

```typescript
// src/services/imageService.ts
import { supabase } from '../boot/supabase'

export const imageService = {
  // ... other methods from 05-frontend-services.md ...

  /**
   * Save a SwarmUI generated image
   */
  async saveSwarmUIImage(imageBlob, metadata) {
    try {
      // Create FormData for the Edge Function
      const formData = new FormData()
      formData.append('image', imageBlob)
      formData.append('metadata', JSON.stringify(metadata))

      // Call the consolidated Edge Function
      const { data, error } = await supabase.functions.invoke('image-management', {
        method: 'POST',
        query: { action: 'save-swarmui-image' },
        body: formData
      })

      if (error) throw error
      return data.image
    } catch (error) {
      console.error('Error saving SwarmUI image:', error)
      throw error
    }
  }
}
```

## 9.5 Integrate with SwarmUI Service

Update the SwarmUI service to use the consolidated image service:

```typescript
// src/services/swarmUIService.ts
import { imageService } from './imageService'

export class SwarmUIService {
  // ... other methods ...

  /**
   * Save generated images from SwarmUI
   */
  async saveGeneratedImages(generationId, userId, swarmUIResponse) {
    const savedImages = []

    if (!swarmUIResponse.images || swarmUIResponse.images.length === 0) {
      return savedImages
    }

    for (const image of swarmUIResponse.images) {
      try {
        // Download the image from SwarmUI
        const imageBlob = await this.downloadImage(image.url)

        // Prepare metadata
        const metadata = {
          generationId,
          userId,
          width: image.width,
          height: image.height,
          seed: image.seed,
          prompt: swarmUIResponse.params.prompt,
          negativePrompt: swarmUIResponse.params.negative_prompt,
          model: swarmUIResponse.params.model,
          steps: swarmUIResponse.params.steps,
          cfgScale: swarmUIResponse.params.cfg_scale,
          sampler: swarmUIResponse.params.sampler,
          filename: `generation_${generationId}_${image.seed}.png`
        }

        // Save the image using the consolidated image service
        const savedImage = await imageService.saveSwarmUIImage(imageBlob, metadata)
        savedImages.push(savedImage)
      } catch (error) {
        console.error('Error saving generated image:', error)
      }
    }

    return savedImages
  }

  /**
   * Generate images using SwarmUI
   */
  async generateImage(params) {
    // Ensure the service is initialized
    await this.ensureInitialized()

    const authStore = useAuthStore()
    const user = authStore.getUser()

    if (!user) {
      throw new Error('User not authenticated')
    }

    try {
      // Create a record in the database
      const { data: generationRecord, error: dbError } = await supabase
        .from('image_generations')
        .insert({
          user_id: user.id,
          server_id: this.serverId,
          prompt: params.prompt,
          negative_prompt: params.negative_prompt || '',
          width: params.width,
          height: params.height,
          num_images: params.batch_size || 1,
          seed: params.seed,
          model: params.model,
          sampler: params.sampler,
          steps: params.steps,
          cfg_scale: params.cfg_scale,
          status: 'pending'
        })
        .select()
        .single()

      if (dbError) {
        throw new Error(`Failed to create generation record: ${dbError.message}`)
      }

      // Send the request to SwarmUI
      const response = await this.callSwarmUIAPI('generate', params)

      // Update the generation status
      await supabase
        .from('image_generations')
        .update({
          status: 'processing',
          updated_at: new Date().toISOString()
        })
        .eq('id', generationRecord.id)

      // If the generation is already complete, save the images
      if (response.status === 'completed' && response.images) {
        const savedImages = await this.saveGeneratedImages(
          generationRecord.id,
          user.id,
          response
        )

        // Update the generation status
        await supabase
          .from('image_generations')
          .update({
            status: 'completed',
            updated_at: new Date().toISOString()
          })
          .eq('id', generationRecord.id)

        // Add the saved images to the response
        response.savedImages = savedImages
      }

      return response
    } catch (error) {
      console.error('Error generating image:', error)
      throw new Error(`Failed to generate image: ${error.message}`)
    }
  }
}
```

## 9.6 Create Image Gallery Component

Create a component to display generated images using the unified image service. This component will use the same `imageService.getImages()` method that we defined in the frontend services documentation, but with filters specific to SwarmUI-generated images.

## 9.7 Next Steps

Now that you have implemented the consolidated image storage approach for SwarmUI, proceed to [Step 10: Integration with Template Creation](./10-template-integration.md) to integrate the SwarmUI image generation with the template creation process.