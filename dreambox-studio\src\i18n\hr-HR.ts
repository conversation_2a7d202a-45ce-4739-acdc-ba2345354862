export default {
  // General
  app: {
    name: 'Dreambox Studio',
    loading: 'Učitavanje...',
    error: '<PERSON><PERSON><PERSON> je do pogreške',
    success: 'Uspjeh!',
    confirm: 'Jeste li sigurni?',
    cancel: '<PERSON><PERSON><PERSON>',
    save: '<PERSON><PERSON><PERSON><PERSON>',
    delete: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    edit: '<PERSON><PERSON>i',
    create: '<PERSON><PERSON><PERSON><PERSON>',
    search: 'Pretraži',
    filter: '<PERSON><PERSON><PERSON><PERSON>',
    sort: 'Sortiraj',
    language: 'Jezi<PERSON>',
    theme: {
      light: '<PERSON>vi<PERSON><PERSON> način',
      dark: '<PERSON><PERSON> način',
      toggle: 'Promijeni tamni način'
    },
    new: 'Novi',
    view: 'Pregled',
    clear: 'Očist<PERSON>',
    clearAll: 'Očisti sve',
    hideAll: 'Sakrij sve',
    showAll: 'Prikaži sve'
  },

  // Column Names
  columns: {
    name: 'Naziv',
    description: 'Opis',
    status: 'Status',
    createdDate: 'Da<PERSON>',
    updatedDate: '<PERSON><PERSON>',
    designer: '<PERSON><PERSON><PERSON><PERSON>',
    type: 'Tip',
    category: 'Kategorija',
    value: 'Vrijednost',
    actions: '<PERSON><PERSON><PERSON><PERSON>',
    // User Management Table
    user: '<PERSON><PERSON><PERSON>',
    email: 'Email',
    role: '<PERSON><PERSON><PERSON>',
    company: 'Tvrtka'
  },

  // Common
  common: {
    cancel: 'Odustani',
    confirm: 'Potvrdi',
    save: 'Spremi',
    delete: 'Izbriši',
    edit: 'Uredi',
    create: 'Kreiraj',
    apply: 'Primijeni',
    reset: 'Resetiraj',
    close: 'Zatvori',
    hideColumn: 'Sakrij stupac',
    showColumn: 'Prikaži stupac',
    clearFilter: 'Očisti filter',
    dismissNotification: 'Odbaci obavijest',
    saveCurrentFilter: 'Spremi trenutni filter',
    createNewFilter: 'Kreiraj novi filter',
    noSavedFilters: 'Nema spremljenih filtera',
    setAsDefault: 'Postavi kao zadano',
    default: 'Zadano',
    loading: 'Učitavanje...',
    optional: 'opcionalno',
    matchAny: 'Odgovara bilo kojem',
    matchAll: 'Odgovara svima',
    on: 'Uključeno',
    off: 'Isključeno'
  },

  // Auth
  auth: {
    login: 'Prijava',
    logout: 'Odjava',
    email: 'Email',
    password: 'Lozinka',
    forgotPassword: 'Zaboravili ste lozinku?',
    resetPassword: 'Resetiraj lozinku',
    register: 'Registracija',
    loginWith: 'Prijavi se putem {provider}',
    loginSuccess: 'Prijava uspješna',
    loginError: 'Prijava neuspješna',
    logoutSuccess: 'Odjava uspješna',
    invalidCredentials: 'Nevažeći email ili lozinka',
    passwordReset: 'Email za resetiranje lozinke je poslan',
    passwordResetError: 'Neuspjelo slanje emaila za resetiranje lozinke',
    selectRole: 'Odaberi ulogu',
    selectCompany: 'Odaberi tvrtku'
  },

  // Navigation
  nav: {
    title: 'Navigacija',
    dashboard: 'Nadzorna ploča',
    settings: 'Postavke',
    profile: 'Profil',
    designs: 'Dizajni',
    templates: 'Predlošci',
    projects: 'Projekti',
    users: 'Korisnici',
    admin: 'Admin',
    home: 'Početna',
    browse: 'Pregled',
    builder: 'Graditelj',
    products: 'Proizvodi',
    images: 'Slike',
    marketing: 'Marketing',
    sales: 'Prodaja',
    customers: 'Kupci',
    statistics: 'Statistika',
    utilities: 'Alati',
    events: 'Događaji',
    collections: 'Kolekcije',
    company: 'Tvrtka',
    branding: 'Branding',
    app: 'Aplikacija',
    companies: 'Tvrtke',
    servers: 'Serveri',
    database: 'Baza podataka',
    deviceType: 'Tip uređaja',
    logout: 'Odjava'
  },

  // Settings
  settings: {
    title: 'Postavke',
    appearance: 'Izgled',
    account: 'Račun',
    notifications: 'Obavijesti',
    security: 'Sigurnost',
    language: 'Jezik',
    theme: 'Tema',
    uiTheme: 'UI Tema',
    darkMode: 'Tamni način',
    lightMode: 'Svijetli način',
    systemDefault: 'Zadano sustavom',
    tableTextWrapMode: 'Prikaz teksta u tablici',
    wrap: 'Prelomi',
    ellipsis: 'Elipsa',
    tableTextWrapModeChanged: 'Prikaz teksta u tablici promijenjen u {mode}',
    toastNotifications: 'Omogući toast obavijesti',
    toastNotificationsEnabled: 'Toast obavijesti omogućene',
    toastNotificationsDisabled: 'Toast obavijesti onemogućene',
    imageServerConfiguration: 'Konfiguracija servera za slike',
    terminateServerOnReload: 'Ugasi server pri ponovnom učitavanju stranice',
    keepRunning: 'Nastavi rad',
    terminate: 'Ugasi',
    serverWillTerminateOnReload: 'Server će biti ugašen kada ponovno učitate ili napustite stranicu',
    serverWillKeepRunningOnReload: 'Server će nastaviti raditi kada ponovno učitate ili napustite stranicu',
    errorLoadingSettings: 'Greška pri učitavanju postavki',
    errorSavingSettings: 'Greška pri spremanju postavki',
    themeSaved: 'Tema uspješno spremljena',
    languageSaved: 'Jezik uspješno spremljen',
    tabs: {
      general: 'Općenito',
      appearance: 'Izgled',
      shortcuts: 'Tipkovnički prečaci'
    },
    shortcuts: {
      title: 'Tipkovnički prečaci',
      description: 'Prilagodite tipkovničke prečace za često korištene naredbe.',
      resetDefaults: 'Vrati na zadano',
      resetConfirm: 'Jeste li sigurni da želite vratiti sve prečace na njihove zadane vrijednosti?',
      pressKeys: 'Pritisnite tipke...',
      needsModifier: 'Prečac mora uključivati modifikacijsku tipku (Ctrl, Alt, Shift ili Meta)',
      alreadyUsed: 'Ovaj prečac je već dodijeljen drugoj naredbi',
      searchPlaceholder: 'Pretraži prečace',
      showUnassignedOnly: 'Prikaži samo nedodijeljene',
      noShortcutsFound: 'Nisu pronađeni prečaci',
      clearShortcut: 'Obriši prečac',
      commands: {
        toggleDarkMode: 'Promijeni tamni način',
        toggleLeftDrawer: 'Promijeni lijevi izbornik',
        toggleRightDrawer: 'Promijeni desni izbornik',
        openDashboard: 'Idi na nadzornu ploču',
        logout: 'Odjava',
        openSettings: 'Otvori postavke',
        help: 'Prikaži dostupne naredbe',
        openProjects: 'Idi na stranicu projekata',
        openChat: 'Otvori panel za razgovor'
      },
      notifications: {
        updated: 'Prečac ažuriran',
        cleared: 'Prečac obrisan',
        resetToDefaults: 'Prečaci vraćeni na zadane vrijednosti',
        failedToReset: 'Neuspjelo vraćanje prečaca',
        refreshed: 'Prečaci osvježeni iz baze podataka',
        failedToRefresh: 'Neuspjelo osvježavanje prečaca',
        synced: 'Prečaci sinkronizirani s dostupnim naredbama',
        failedToSync: 'Neuspjela sinkronizacija prečaca'
      }
    }
  },

  // Voice Commands
  voiceCommands: {
    title: 'Glasovne naredbe',
    listening: 'Slušam...',
    commandExecuted: 'Naredba izvršena',
    commandFailed: 'Naredba nije uspjela',
    noCommandDetected: 'Naredba nije prepoznata',
    availableCommands: 'Dostupne naredbe',
    enterCommand: 'Upišite ili odaberite',
    execute: 'Izvrši',
    lastResult: 'Zadnji rezultat',
    help: 'Pomoć',
    examples: 'Primjeri',
    selectCommand: 'Odaberi naredbu',
    toggleDarkMode: {
      name: 'Promijeni tamni način',
      description: 'Prebaci između svijetlog i tamnog načina',
      examples: ['Promijeni tamni način', 'Promijeni temu', 'Prebaci na tamni način', 'Prebaci na svijetli način']
    },
    openDashboard: {
      name: 'Otvori nadzornu ploču',
      description: 'Navigiraj na nadzornu ploču',
      examples: ['Otvori nadzornu ploču', 'Idi na nadzornu ploču', 'Prikaži nadzornu ploču']
    },
    openSettings: {
      name: 'Otvori postavke',
      description: 'Navigiraj na postavke',
      examples: ['Otvori postavke', 'Idi na postavke', 'Prikaži postavke']
    },
    toggleLeftDrawer: {
      name: 'Promijeni lijevi izbornik',
      description: 'Otvori ili zatvori navigacijski izbornik',
      examples: ['Otvori izbornik', 'Zatvori izbornik', 'Promijeni navigaciju', 'Prikaži navigaciju']
    },
    toggleRightDrawer: {
      name: 'Promijeni desni izbornik',
      description: 'Otvori ili zatvori izbornik za pregled',
      examples: ['Otvori pregled', 'Zatvori pregled', 'Promijeni pregled', 'Prikaži pregled']
    },
    logout: {
      name: 'Odjava',
      description: 'Odjavi se iz aplikacije',
      examples: ['Odjavi se', 'Izađi', 'Odjava']
    },
    helpCommand: {
      name: 'Pomoć',
      description: 'Prikaži dostupne naredbe',
      examples: ['Pomoć', 'Što mogu reći?', 'Prikaži naredbe', 'Dostupne naredbe']
    }
  },

  // Roles
  roles: {
    designer: 'Dizajner',
    admin: 'Administrator',
    superAdmin: 'Super Administrator'
  },

  // Errors
  errors: {
    notFound: 'Stranica nije pronađena',
    unauthorized: 'Neautorizirano',
    forbidden: 'Zabranjeno',
    serverError: 'Greška servera',
    networkError: 'Greška mreže',
    unknown: 'Nepoznata greška'
  },

  // SwarmUI (legacy)
  swarmUI: {
    createRunPod: 'Kreiraj SwarmUI server',
    createSwarmUIServer: 'Kreiraj SwarmUI server',
    selectGpu: 'Odaberi GPU',
    cancel: 'Odustani',
    create: 'Kreiraj',
    serverCreating: 'Server se kreira. Ovo može potrajati nekoliko minuta.',
    errorCreatingServer: 'Greška pri kreiranju servera',
    serverStatus: 'Status servera',
    running: 'Server radi',
    starting: 'Server se pokreće',
    stopping: 'Server se zaustavlja',
    stopped: 'Server zaustavljen',
    'no-server': 'Nema servera',
    error: 'Greška servera',
    stopServer: 'Zaustavi server',
    testConnection: 'Testiraj vezu',
    generateImage: 'Generiraj sliku',
    prompt: 'Prompt',
    negativePrompt: 'Negativni prompt',
    model: 'Model',
    width: 'Širina',
    height: 'Visina',
    steps: 'Koraci',
    cfgScale: 'CFG skala',
    sampler: 'Sampler',
    seed: 'Sjeme',
    randomSeed: 'Nasumično sjeme',
    scheduler: 'Scheduler',
    generating: 'Generiranje...',
    generationError: 'Greška pri generiranju',
    saveToS3: 'Spremi u S3',
    saving: 'Spremanje...',
    imageSaved: 'Slika spremljena u S3',
    failedToSave: 'Neuspjelo spremanje slike',
    reset: 'Resetiraj'
  },

  // ComfyUI
  comfyUI: {
    createRunPod: 'Kreiraj ComfyUI server',
    createComfyUIServer: 'Kreiraj ComfyUI server',
    selectGpu: 'Odaberi GPU',
    cancel: 'Odustani',
    create: 'Kreiraj',
    serverCreating: 'Server se kreira. Ovo može potrajati nekoliko minuta.',
    errorCreatingServer: 'Greška pri kreiranju servera',
    serverStatus: 'Status servera',
    running: 'Server radi',
    starting: 'Server se pokreće',
    stopping: 'Server se zaustavlja',
    stopped: 'Server zaustavljen',
    'no-server': 'Nema servera',
    error: 'Greška servera',
    success: 'Uspjeh',
    stopServer: 'Zaustavi server',
    terminateServer: 'Ugasi server',
    testConnection: 'Testiraj vezu',
    generateImage: 'Generiraj sliku',
    imageGenerated: 'Slika uspješno generirana',
    imageGenerationFailed: 'Neuspjelo generiranje slike',
    connectionSuccessful: 'Veza uspješna',
    connectionFailed: 'Veza neuspješna',
    serverStopping: 'Server se zaustavlja',
    serverTerminating: 'Server se gasi',
    errorStoppingServer: 'Greška pri zaustavljanju servera',
    errorTerminatingServer: 'Greška pri gašenju servera',
    terminateServerConfirm: 'Jeste li sigurni da želite ugasiti server? Ova radnja se ne može poništiti.',
    serverNotRunning: 'Server nije pokrenut',
    serverNotConnected: 'Server još nije povezan',
    serverReady: 'Server je spreman za generiranje slika',
    prompt: 'Prompt',
    negativePrompt: 'Negativni prompt',
    model: 'Model',
    width: 'Širina',
    height: 'Visina',
    steps: 'Koraci',
    cfgScale: 'CFG skala',
    sampler: 'Sampler',
    seed: 'Sjeme',
    randomSeed: 'Nasumično sjeme',
    scheduler: 'Scheduler',
    generating: 'Generiranje...',
    generationError: 'Greška pri generiranju',
    saveToS3: 'Spremi u S3',
    saving: 'Spremanje...',
    imageSaved: 'Slika spremljena u S3',
    failedToSave: 'Neuspjelo spremanje slike',
    reset: 'Resetiraj'
  },

  // Filters
  filters: {
    savedFilters: 'Spremljeni filteri',
    visibleColumns: 'Vidljivo',
    detailsColumns: 'Detaljno',
    expanded: 'Prošireno',
    main: 'Glavno',
    promptElements: 'Prompt',
    relatedItems: 'Povezano',
    noFiltersAvailable: 'Nema dostupnih filtera',
    filterName: 'Naziv filtera',
    description: 'Opis (opcionalno)',
    setAsDefault: 'Postavi kao zadani filter za ovaj prikaz',
    setAsDefaultFilter: 'Postavi kao zadani filter',
    editFilter: 'Uredi filter',
    saveFilter: 'Spremi filter',
    updateFilter: 'Ažuriraj filter',
    deleteFilter: 'Izbriši filter',
    deleteFilterConfirm: 'Jeste li sigurni da želite izbrisati ovaj filter?',
    clearAllPromptElementFilters: 'Očisti sve filtere elemenata upita',
    clearFilters: 'Očisti filtere',
    filtersWillAppear: 'Filteri će se pojaviti ovdje ovisno o trenutnom prikazu.',
    search: 'Pretraži...',
    min: 'Min',
    max: 'Max',
    selectDateOrRange: 'Odaberi datum ili raspon',
    nameRequired: 'Naziv je obavezan',
    filterApplied: 'Filter primijenjen',
    filterCleared: 'Filter očišćen',
    filterSaved: 'Filter uspješno spremljen',
    filterUpdated: 'Filter uspješno ažuriran',
    filterDeleted: 'Filter uspješno izbrisan',
    defaultFilterUpdated: 'Zadani filter ažuriran',
    failedToInitialize: 'Neuspjelo inicijaliziranje filtera. Molimo pokušajte kasnije.',
    failedToUpdate: 'Neuspjelo ažuriranje filtera za novi kontekst.',
    allPromptElementFiltersCleared: 'Svi filteri elemenata upita očišćeni',
    holdAltForDragSelect: 'Držite Alt za odabir povlačenjem'
  },

  // Templates
  templates: {
    elements: 'Elementi',
    related: 'Povezano',
    details: 'Detalji',
    statusHistory: 'Povijest statusa',
    promptElements: 'Prompt Elementi',
    collections: 'Kolekcije',
    products: 'Proizvodi',
    noCollections: 'Nema kolekcija povezanih s ovim predloškom.',
    noProducts: 'Nema proizvoda',
    noElements: 'Nema elemenata',
    templateDetails: 'Detalji predloška',
    selectElementTypes: 'Odaberi Tipove Elemenata',
    selectValues: 'Odaberi Vrijednosti',
    noMatchingElementTypes: 'Nema odgovarajućih tipova elemenata',
    noMatchingValues: 'Nema odgovarajućih vrijednosti',
    selectedTypes: 'Odabrani tipovi: {count}',
    typesShown: 'Prikazani tipovi: {count}',
    selectedValues: 'Odabrane vrijednosti: {count}',
    selectElementTypesPrompt: 'Odaberite tipove elemenata za dodavanje u ovaj predložak',
    loadStatusHistory: 'Učitaj povijest statusa',
    loadingStatusHistory: 'Učitavanje povijesti statusa...',
    noTemplatesFound: 'Nije pronađen nijedan predložak',
    templatesSelected: '{count} predložaka odabrano',
    status: 'Status',
    mixedStatuses: 'Miješani statusi',
    submitForApproval: 'Pošalji na odobrenje',
    approve: 'Odobri',
    reject: 'Odbij',
    archive: 'Arhiviraj',
    edit: 'Uredi',
    view: 'Pregledaj',
    restore: 'Vrati u skicu',
    copyToNewDraft: 'Kopiraj u novu skicu',
    clearSelection: 'Očisti odabir',
    batchActions: 'Grupne akcije',
    updating: 'Ažuriranje {count} predložaka...',
    updated: 'Ažurirano {count} od {total} predložaka',
    copying: 'Kopiranje {count} predložaka...',
    copied: 'Kopirano {count} od {total} predložaka',
    failedToUpdate: 'Neuspjelo ažuriranje predložaka',
    failedToCopy: 'Neuspjelo kopiranje predložaka',
    templateCopied: 'Predložak uspješno kopiran',
    statusUpdated: 'Status ažuriran na {status}',
    statusUpdateFailed: 'Neuspjelo ažuriranje statusa predloška',
    notes: 'Bilješke',
    newTemplate: '+ NOVI PREDLOŽAK',
    title: 'Predlošci',
    templatesFound: '{count} predložaka pronađeno',
    recordsSelected: '{count} zapisa odabrano',
    recordsPerPage: 'Zapisa po stranici:',
    records: 'Zapisi',
    by: 'Po',

    // Template Builder
    builder: {
      title: 'Uređivač predložaka',
      newTemplate: 'Novi predložak',
      editTemplate: 'Uredi predložak',
      viewTemplate: 'Pregledaj predložak',
      backToTemplates: 'Natrag na predloške',
      loadingTemplate: 'Učitavanje predloška...',
      basicInformation: 'Osnovne informacije',
      templateName: 'Naziv predloška',
      templateDescription: 'Opis predloška',
      nameRequired: 'Naziv je obavezan',
      definePromptElements: 'Definirajte prompt elemente koji čine ovaj predložak',
      productTypes: 'Tipovi proizvoda',
      recommendedProductTypes: 'Preporučeni tipovi proizvoda',
      selectProductTypes: 'Odaberite tipove proizvoda za ovaj predložak',
      selectRecommendedTypes: 'Odaberite preporučene tipove proizvoda',
      imageSettings: 'Postavke slike',
      configureImageSettings: 'Konfigurirajte postavke za generiranje slika',
      model: 'Model',
      steps: 'Koraci',
      aspectRatio: 'Omjer stranica',
      seed: 'Sjeme',
      randomSeed: 'Nasumično sjeme',
      save: 'Spremi',
      cancel: 'Odustani',
      back: 'Natrag',
      saving: 'Spremanje...',
      templateSaved: 'Predložak uspješno spremljen',
      errorSaving: 'Greška pri spremanju predloška',
      selectAll: 'Odaberi sve',
      addCustomValue: 'Dodaj prilagođenu vrijednost',
      customValue: 'Prilagođena vrijednost',
      addValue: 'Dodaj vrijednost',
      preview: 'Pregled',
      generatePreview: 'Generiraj pregled',
      generatingPreview: 'Generiranje pregleda...',
      previewError: 'Greška pri generiranju pregleda',
      tryAgain: 'Pokušaj ponovno',
      elementValues: 'Vrijednosti elemenata',
      selectValuesForEachType: 'Odaberite vrijednosti za svaki tip elementa'
    }
  }
};
