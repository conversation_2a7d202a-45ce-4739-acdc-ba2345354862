# UI Components for Image Management

This document outlines the UI components needed for image management in the Dreambox Studio application.

## Image Upload Component

A reusable component for uploading images:

```vue
<!-- src/components/ImageUploader.vue -->
<template>
  <div class="image-uploader">
    <div 
      class="upload-area"
      :class="{ 'is-dragging': isDragging, 'has-image': !!previewUrl }"
      @dragover.prevent="isDragging = true"
      @dragleave.prevent="isDragging = false"
      @drop.prevent="handleDrop"
      @click="triggerFileInput"
    >
      <div v-if="!previewUrl && !isUploading" class="upload-placeholder">
        <q-icon name="cloud_upload" size="48px" />
        <div class="text-subtitle1 q-mt-sm">
          {{ placeholder || 'Drop image here or click to upload' }}
        </div>
        <div class="text-caption q-mt-xs" v-if="acceptedFormats">
          Accepted formats: {{ acceptedFormats }}
        </div>
      </div>
      
      <div v-else-if="isUploading" class="upload-progress">
        <q-circular-progress
          indeterminate
          size="50px"
          color="primary"
        />
        <div class="text-subtitle1 q-mt-sm">Uploading...</div>
      </div>
      
      <div v-else class="preview-container">
        <img :src="previewUrl" class="preview-image" />
        <div class="preview-overlay">
          <q-btn
            round
            flat
            color="white"
            icon="edit"
            @click.stop="triggerFileInput"
          />
          <q-btn
            round
            flat
            color="white"
            icon="delete"
            @click.stop="clearImage"
          />
        </div>
      </div>
    </div>
    
    <input
      ref="fileInput"
      type="file"
      :accept="accept"
      class="hidden-input"
      @change="handleFileChange"
    />
    
    <div v-if="error" class="text-negative q-mt-sm">
      {{ error }}
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { useImageUpload } from '../composables/useImageUpload'

export default {
  name: 'ImageUploader',
  
  props: {
    value: Object,
    placeholder: String,
    accept: {
      type: String,
      default: 'image/*'
    },
    maxSize: {
      type: Number,
      default: 10 * 1024 * 1024 // 10MB
    },
    templateId: Number,
    productId: Number,
    tags: Array,
    isPublic: Boolean,
    metadata: Object
  },
  
  emits: ['update:value', 'upload-success', 'upload-error'],
  
  setup(props, { emit }) {
    const fileInput = ref(null)
    const isDragging = ref(false)
    const previewUrl = ref('')
    const localError = ref('')
    
    const acceptedFormats = computed(() => {
      if (!props.accept || props.accept === 'image/*') return 'All image formats'
      return props.accept.split(',').join(', ')
    })
    
    const {
      isUploading,
      uploadProgress,
      uploadedImage,
      error: uploadError,
      uploadImage,
      reset
    } = useImageUpload()
    
    const error = computed(() => localError.value || uploadError.value)
    
    // Create preview when value changes
    watch(() => props.value, (newValue) => {
      if (newValue && newValue.s3_url) {
        previewUrl.value = newValue.s3_url
      } else {
        previewUrl.value = ''
      }
    }, { immediate: true })
    
    // Update value when upload completes
    watch(uploadedImage, (newImage) => {
      if (newImage) {
        previewUrl.value = newImage.s3_url
        emit('update:value', newImage)
        emit('upload-success', newImage)
      }
    })
    
    function triggerFileInput() {
      fileInput.value.click()
    }
    
    function handleFileChange(event) {
      const file = event.target.files[0]
      if (file) {
        processFile(file)
      }
      // Reset the input so the same file can be selected again
      event.target.value = ''
    }
    
    function handleDrop(event) {
      isDragging.value = false
      const file = event.dataTransfer.files[0]
      if (file) {
        processFile(file)
      }
    }
    
    async function processFile(file) {
      localError.value = ''
      
      // Validate file type
      if (props.accept && props.accept !== 'image/*') {
        const acceptedTypes = props.accept.split(',')
        const fileType = file.type
        
        if (!acceptedTypes.some(type => fileType.match(type.trim()))) {
          localError.value = `File type not accepted. Please upload ${acceptedFormats.value}`
          return
        }
      }
      
      // Validate file size
      if (file.size > props.maxSize) {
        localError.value = `File too large. Maximum size is ${props.maxSize / (1024 * 1024)}MB`
        return
      }
      
      // Create a preview
      previewUrl.value = URL.createObjectURL(file)
      
      try {
        // Upload the file
        await uploadImage(file, {
          templateId: props.templateId,
          productId: props.productId,
          tags: props.tags,
          isPublic: props.isPublic,
          metadata: props.metadata
        })
      } catch (err) {
        emit('upload-error', err)
      }
    }
    
    function clearImage() {
      previewUrl.value = ''
      reset()
      emit('update:value', null)
    }
    
    return {
      fileInput,
      isDragging,
      previewUrl,
      isUploading,
      uploadProgress,
      error,
      acceptedFormats,
      triggerFileInput,
      handleFileChange,
      handleDrop,
      clearImage
    }
  }
}
</script>

<style scoped>
.image-uploader {
  width: 100%;
}

.upload-area {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  min-height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-area:hover {
  border-color: #1976D2;
}

.is-dragging {
  border-color: #1976D2;
  background-color: rgba(25, 118, 210, 0.05);
}

.has-image {
  border-style: solid;
  padding: 0;
  overflow: hidden;
}

.preview-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.preview-container:hover .preview-overlay {
  opacity: 1;
}

.hidden-input {
  display: none;
}
</style>
```

## Image Gallery Component

A component for displaying and managing images:

```vue
<!-- src/components/ImageGallery.vue -->
<template>
  <div class="image-gallery">
    <div class="gallery-header q-mb-md">
      <div class="row items-center justify-between">
        <div class="col-12 col-sm-6">
          <h5 class="q-my-none">{{ title || 'Image Gallery' }}</h5>
        </div>
        <div class="col-12 col-sm-6 text-right">
          <q-input
            v-model="searchQuery"
            dense
            outlined
            placeholder="Search images"
            class="q-mr-sm"
            style="max-width: 200px; display: inline-block"
          >
            <template v-slot:append>
              <q-icon name="search" />
            </template>
          </q-input>
          
          <q-btn
            color="primary"
            icon="refresh"
            flat
            round
            :loading="isLoading"
            @click="refreshGallery"
          />
        </div>
      </div>
      
      <div class="filter-section q-mt-sm" v-if="showFilters">
        <q-chip
          v-for="tag in selectedTags"
          :key="tag"
          removable
          @remove="removeTag(tag)"
        >
          {{ tag }}
        </q-chip>
        
        <q-select
          v-model="tagInput"
          use-input
          hide-selected
          fill-input
          input-debounce="0"
          label="Add tags"
          dense
          outlined
          class="q-ml-sm"
          style="width: 150px; display: inline-block"
          :options="filteredTagOptions"
          @filter="filterTags"
          @input="addTag"
        >
          <template v-slot:no-option>
            <q-item>
              <q-item-section class="text-grey">
                No results
              </q-item-section>
            </q-item>
          </template>
        </q-select>
      </div>
    </div>
    
    <div v-if="isLoading && !images.length" class="text-center q-pa-lg">
      <q-spinner color="primary" size="48px" />
      <div class="q-mt-sm">Loading images...</div>
    </div>
    
    <div v-else-if="!images.length" class="text-center q-pa-lg">
      <q-icon name="image_not_supported" size="48px" color="grey-7" />
      <div class="q-mt-sm">No images found</div>
    </div>
    
    <div v-else class="row q-col-gutter-md">
      <div 
        v-for="image in images" 
        :key="image.id"
        class="col-6 col-sm-4 col-md-3"
      >
        <q-card class="image-card">
          <q-img
            :src="image.s3_url"
            :ratio="1"
            spinner-color="primary"
            spinner-size="42px"
            @click="selectImage(image)"
          >
            <div class="image-overlay">
              <q-btn
                round
                flat
                color="white"
                icon="fullscreen"
                @click.stop="openImageDetail(image)"
              />
              <q-btn
                v-if="allowDelete"
                round
                flat
                color="white"
                icon="delete"
                @click.stop="confirmDelete(image)"
              />
            </div>
          </q-img>
          
          <q-card-section class="q-py-sm">
            <div class="text-subtitle2 ellipsis">{{ image.title || image.filename }}</div>
            <div class="text-caption text-grey ellipsis">{{ formatDate(image.created_at) }}</div>
          </q-card-section>
        </q-card>
      </div>
    </div>
    
    <div class="pagination-controls q-mt-md text-center" v-if="pageCount > 1">
      <q-pagination
        v-model="currentPage"
        :max="pageCount"
        :max-pages="6"
        boundary-links
        direction-links
        @update:model-value="changePage"
      />
    </div>
    
    <!-- Image Detail Dialog -->
    <q-dialog v-model="showImageDetail" maximized>
      <q-card>
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">{{ selectedImage?.title || selectedImage?.filename }}</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>
        
        <q-card-section class="q-pt-none">
          <div class="row">
            <div class="col-12 col-md-8">
              <q-img
                :src="selectedImage?.s3_url"
                spinner-color="primary"
                spinner-size="42px"
                style="max-height: 70vh; width: 100%"
                fit="contain"
              />
            </div>
            
            <div class="col-12 col-md-4">
              <q-list>
                <q-item>
                  <q-item-section>
                    <q-item-label caption>Filename</q-item-label>
                    <q-item-label>{{ selectedImage?.filename }}</q-item-label>
                  </q-item-section>
                </q-item>
                
                <q-item>
                  <q-item-section>
                    <q-item-label caption>Dimensions</q-item-label>
                    <q-item-label>{{ selectedImage?.width || 'N/A' }} × {{ selectedImage?.height || 'N/A' }}</q-item-label>
                  </q-item-section>
                </q-item>
                
                <q-item>
                  <q-item-section>
                    <q-item-label caption>Size</q-item-label>
                    <q-item-label>{{ formatFileSize(selectedImage?.file_size) }}</q-item-label>
                  </q-item-section>
                </q-item>
                
                <q-item>
                  <q-item-section>
                    <q-item-label caption>Created</q-item-label>
                    <q-item-label>{{ formatDate(selectedImage?.created_at, true) }}</q-item-label>
                  </q-item-section>
                </q-item>
                
                <q-item v-if="selectedImage?.tags && selectedImage.tags.length">
                  <q-item-section>
                    <q-item-label caption>Tags</q-item-label>
                    <div>
                      <q-chip
                        v-for="tag in selectedImage.tags"
                        :key="tag"
                        size="sm"
                        class="q-ma-xs"
                      >
                        {{ tag }}
                      </q-chip>
                    </div>
                  </q-item-section>
                </q-item>
                
                <q-item v-if="selectedImage?.description">
                  <q-item-section>
                    <q-item-label caption>Description</q-item-label>
                    <q-item-label>{{ selectedImage.description }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </div>
          </div>
        </q-card-section>
      </q-card>
    </q-dialog>
    
    <!-- Delete Confirmation Dialog -->
    <q-dialog v-model="showDeleteConfirm">
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar icon="warning" color="negative" text-color="white" />
          <span class="q-ml-sm">Delete this image?</span>
        </q-card-section>
        
        <q-card-section>
          This action cannot be undone. The image will be permanently deleted from storage.
        </q-card-section>
        
        <q-card-actions align="right">
          <q-btn flat label="Cancel" color="primary" v-close-popup />
          <q-btn flat label="Delete" color="negative" @click="deleteImage" :loading="isDeleting" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue'
import { date } from 'quasar'
import { useImageGallery } from '../composables/useImageGallery'
import { imageService } from '../services/imageService'

export default {
  name: 'ImageGallery',
  
  props: {
    title: String,
    templateId: Number,
    productId: Number,
    showFilters: {
      type: Boolean,
      default: true
    },
    allowDelete: {
      type: Boolean,
      default: true
    },
    initialFilters: {
      type: Object,
      default: () => ({})
    }
  },
  
  emits: ['select-image', 'delete-image'],
  
  setup(props, { emit }) {
    // Prepare initial filters
    const initialFilters = {
      ...props.initialFilters
    }
    
    if (props.templateId) {
      initialFilters.templateId = props.templateId
    }
    
    if (props.productId) {
      initialFilters.productId = props.productId
    }
    
    // Set up image gallery
    const {
      images,
      isLoading,
      error,
      totalCount,
      currentPage,
      pageSize,
      pageCount,
      filters,
      loadImages
    } = useImageGallery({
      filters: initialFilters,
      autoLoad: true
    })
    
    // Search and filtering
    const searchQuery = ref('')
    const selectedTags = ref([])
    const tagOptions = ref([])
    const tagInput = ref('')
    const filteredTagOptions = ref([])
    
    // Image detail dialog
    const showImageDetail = ref(false)
    const selectedImage = ref(null)
    
    // Delete confirmation
    const showDeleteConfirm = ref(false)
    const imageToDelete = ref(null)
    const isDeleting = ref(false)
    
    // Load tag options
    onMounted(async () => {
      try {
        tagOptions.value = await imageService.getImageTags()
      } catch (err) {
        console.error('Failed to load tags:', err)
      }
    })
    
    // Watch for search query changes
    watch(searchQuery, (newQuery) => {
      if (newQuery) {
        filters.value = {
          ...filters.value,
          search: newQuery
        }
      } else {
        const { search, ...restFilters } = filters.value
        filters.value = restFilters
      }
    })
    
    // Watch for tag changes
    watch(selectedTags, (newTags) => {
      if (newTags.length) {
        filters.value = {
          ...filters.value,
          tags: newTags
        }
      } else {
        const { tags, ...restFilters } = filters.value
        filters.value = restFilters
      }
    })
    
    function filterTags(val, update) {
      if (val === '') {
        update(() => {
          filteredTagOptions.value = tagOptions.value
        })
        return
      }
      
      const needle = val.toLowerCase()
      update(() => {
        filteredTagOptions.value = tagOptions.value.filter(
          v => v.toLowerCase().indexOf(needle) > -1
        )
      })
    }
    
    function addTag(tag) {
      if (tag && !selectedTags.value.includes(tag)) {
        selectedTags.value = [...selectedTags.value, tag]
      }
      tagInput.value = ''
    }
    
    function removeTag(tag) {
      selectedTags.value = selectedTags.value.filter(t => t !== tag)
    }
    
    function changePage(page) {
      loadImages(page)
    }
    
    function refreshGallery() {
      loadImages(currentPage.value)
    }
    
    function selectImage(image) {
      emit('select-image', image)
    }
    
    function openImageDetail(image) {
      selectedImage.value = image
      showImageDetail.value = true
    }
    
    function confirmDelete(image) {
      imageToDelete.value = image
      showDeleteConfirm.value = true
    }
    
    async function deleteImage() {
      if (!imageToDelete.value) return
      
      try {
        isDeleting.value = true
        await imageService.deleteImage(imageToDelete.value.id)
        emit('delete-image', imageToDelete.value)
        showDeleteConfirm.value = false
        
        // Refresh the gallery
        refreshGallery()
      } catch (err) {
        console.error('Failed to delete image:', err)
      } finally {
        isDeleting.value = false
      }
    }
    
    function formatDate(dateStr, includeTime = false) {
      if (!dateStr) return 'N/A'
      
      const format = includeTime ? 'YYYY-MM-DD HH:mm' : 'YYYY-MM-DD'
      return date.formatDate(dateStr, format)
    }
    
    function formatFileSize(bytes) {
      if (!bytes) return 'N/A'
      
      const units = ['B', 'KB', 'MB', 'GB']
      let size = bytes
      let unitIndex = 0
      
      while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024
        unitIndex++
      }
      
      return `${size.toFixed(1)} ${units[unitIndex]}`
    }
    
    return {
      // Gallery data
      images,
      isLoading,
      error,
      totalCount,
      currentPage,
      pageCount,
      
      // Search and filtering
      searchQuery,
      selectedTags,
      tagOptions,
      tagInput,
      filteredTagOptions,
      filterTags,
      addTag,
      removeTag,
      
      // Actions
      changePage,
      refreshGallery,
      selectImage,
      openImageDetail,
      confirmDelete,
      deleteImage,
      
      // Image detail
      showImageDetail,
      selectedImage,
      
      // Delete confirmation
      showDeleteConfirm,
      isDeleting,
      
      // Formatting
      formatDate,
      formatFileSize
    }
  }
}
</script>

<style scoped>
.image-card {
  transition: transform 0.2s;
  cursor: pointer;
}

.image-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.q-img:hover .image-overlay {
  opacity: 1;
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
```

## Next Steps

Once the UI components are implemented, proceed to [Testing and Optimization](./08-testing-optimization.md) to ensure the image organization system works correctly and efficiently.
