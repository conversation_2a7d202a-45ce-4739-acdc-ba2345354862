# Change to the script directory
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $scriptDir

# Check if bun is installed
try {
    $null = Get-Command bun -ErrorAction Stop
} catch {
    Write-Error "Error: bun is not installed. Please install bun first."
    Write-Host "Visit https://bun.sh/ for installation instructions."
    exit 1
}

# Run setup if node_modules doesn't exist
if (-not (Test-Path "node_modules")) {
    Write-Host "Running setup..."
    bun setup
}

# Parse command line arguments
$command = $args[0]
$restArgs = $args[1..($args.Length-1)]

switch ($command) {
    "setup" {
        bun setup
    }
    "start" {
        bun start
    }
    "generate" {
        bun generate
    }
    "query" {
        bun query
    }
    "browse" {
        bun browse $restArgs
    }
    "export" {
        bun export $restArgs
    }
    default {
        Write-Host "Enhanced Database Inspector"
        Write-Host ""
        Write-Host "Usage: .\run.ps1 [command] [options]"
        Write-Host ""
        Write-Host "Commands:"
        Write-Host "  setup                 Run setup"
        Write-Host "  start                 Fetch database structure"
        Write-Host "  generate              Generate documentation"
        Write-Host "  query                 Run interactive SQL queries"
        Write-Host "  browse [options]      Browse table data"
        Write-Host "  export [options]      Export data"
        Write-Host ""
        Write-Host "For more information on a command, run:"
        Write-Host "  .\run.ps1 [command] --help"
    }
}
