/**
 * features.ts
 * 
 * This module provides a feature flag system for controlling the rollout of new features.
 * It allows enabling/disabling features for specific users or environments.
 */

// Feature flag definitions
export const FEATURES = {
  NEW_FILTER_ENGINE: 'new-filter-engine'
};

// Feature flag storage key
const FEATURE_FLAGS_STORAGE_KEY = 'app_feature_flags';

/**
 * Feature flag manager class
 */
class FeatureFlags {
  private flags: Record<string, boolean> = {};
  
  constructor() {
    this.loadFromStorage();
  }
  
  /**
   * Load feature flags from local storage
   */
  private loadFromStorage(): void {
    try {
      const storedFlags = localStorage.getItem(FEATURE_FLAGS_STORAGE_KEY);
      
      if (storedFlags) {
        this.flags = JSON.parse(storedFlags);
      }
    } catch (err) {
      console.error('Error loading feature flags from storage:', err);
      this.flags = {};
    }
  }
  
  /**
   * Save feature flags to local storage
   */
  private saveToStorage(): void {
    try {
      localStorage.setItem(FEATURE_FLAGS_STORAGE_KEY, JSON.stringify(this.flags));
    } catch (err) {
      console.error('Error saving feature flags to storage:', err);
    }
  }
  
  /**
   * Check if a feature is enabled
   */
  isEnabled(featureKey: string): boolean {
    return !!this.flags[featureKey];
  }
  
  /**
   * Enable a feature
   */
  enable(featureKey: string): void {
    this.flags[featureKey] = true;
    this.saveToStorage();
  }
  
  /**
   * Disable a feature
   */
  disable(featureKey: string): void {
    this.flags[featureKey] = false;
    this.saveToStorage();
  }
  
  /**
   * Toggle a feature
   */
  toggle(featureKey: string): boolean {
    const newValue = !this.isEnabled(featureKey);
    this.flags[featureKey] = newValue;
    this.saveToStorage();
    return newValue;
  }
  
  /**
   * Get all feature flags
   */
  getAllFlags(): Record<string, boolean> {
    return { ...this.flags };
  }
  
  /**
   * Reset all feature flags
   */
  resetAll(): void {
    this.flags = {};
    this.saveToStorage();
  }
}

// Create and export a singleton instance
export const featureFlags = new FeatureFlags();

// Export default for module imports
export default featureFlags;
