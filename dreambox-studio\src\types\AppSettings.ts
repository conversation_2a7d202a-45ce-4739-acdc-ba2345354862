/**
 * App settings interface
 */
export interface AppSettings {
  id: number;
  setting_key: string;
  setting_value: string | null;
  setting_type: string;
  description: string | null;
  is_global: boolean;
  as_company_id: number | null; // Changed from company_id
  created_at: string | null;
  updated_at: string | null;
}

/**
 * App settings filter interface
 */
export interface AppSettingsFilter {
  as_company_id?: number | null; // Changed from company_id
  is_global?: boolean;
  setting_type?: string;
  search?: string;
}

/**
 * App settings pagination interface
 */
export interface AppSettingsPagination {
  page: number;
  rowsPerPage: number;
  rowsNumber: number;
  sortBy?: string;
  descending?: boolean;
}
