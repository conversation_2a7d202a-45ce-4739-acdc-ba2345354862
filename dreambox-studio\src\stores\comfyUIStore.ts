import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { comfyUIService } from 'src/services/comfyUI/ComfyUIService';
import type { ServerStatus } from 'src/services/comfyUI/ComfyUIService';

export interface ComfyUIState {
  serverStatus: ServerStatus;
  isCreating: boolean;
  isTerminating: boolean;
  isConnected: boolean;
  selectedGpu: string;
  errorMessage: string;
}

export const useComfyUIStore = defineStore('comfyUI', () => {
  // State
  const serverStatus = ref<ServerStatus>({ status: 'no-server' });
  const isCreating = ref(false);
  const isTerminating = ref(false);
  const isConnected = ref(false);
  const selectedGpu = ref('NVIDIA RTX A5000');
  const errorMessage = ref('');

  // GPU options
  const gpuOptions = [
    { label: 'NVIDIA RTX A5000 (24GB VRAM)', value: 'NVIDIA RTX A5000' },
    { label: 'NVIDIA RTX A4000 (16GB VRAM)', value: 'NVIDIA RTX A4000' },
    { label: 'NVIDIA RTX 3090 (24GB VRAM)', value: 'NVIDIA RTX 3090' },
    { label: 'NVIDIA RTX 4090 (24GB VRAM)', value: 'NVIDIA RTX 4090' }
  ];

  // Computed properties
  const serverExists = computed(() =>
    ['running', 'starting', 'stopping'].includes(serverStatus.value.status)
  );

  const isServerRunning = computed(() => serverStatus.value.status === 'running');

  const statusColor = computed(() => {
    switch (serverStatus.value.status) {
      case 'running':
        return 'positive';
      case 'starting':
        return 'info';
      case 'stopping':
        return 'warning';
      case 'error':
        return 'negative';
      default:
        return 'grey';
    }
  });

  const toggleState = computed(() => {
    if (!serverExists.value) return 'off';
    
    switch (serverStatus.value.status) {
      case 'starting':
        return 'creating';
      case 'running':
        return isConnected.value ? 'ready' : 'starting-comfyui';
      case 'stopping':
        return 'stopping';
      default:
        return 'off';
    }
  });

  // Actions
  async function updateServerStatus() {
    const status = await comfyUIService.checkServerStatus();
    serverStatus.value = status;
  }

  async function startServer(options: { gpuTypeId?: string } = {}) {
    isCreating.value = true;
    errorMessage.value = '';

    try {
      const result = await comfyUIService.startServer({
        gpuTypeId: options.gpuTypeId || selectedGpu.value
      });

      if (result.status === 'error') {
        errorMessage.value = result.message || 'Error creating server';
        return false;
      } else {
        // Update status immediately
        await updateServerStatus();
        return true;
      }
    } catch (error) {
      errorMessage.value = error instanceof Error ? error.message : String(error);
      return false;
    } finally {
      isCreating.value = false;
    }
  }

  async function terminateServer() {
    isTerminating.value = true;

    try {
      const success = await comfyUIService.terminateServer();
      
      if (success) {
        // Update status immediately
        await updateServerStatus();
        return true;
      } else {
        return false;
      }
    } catch (error) {
      console.error('Error terminating server:', error);
      return false;
    } finally {
      isTerminating.value = false;
    }
  }

  function setConnected(connected: boolean) {
    isConnected.value = connected;
  }

  function setSelectedGpu(gpu: string) {
    selectedGpu.value = gpu;
  }

  function clearError() {
    errorMessage.value = '';
  }

  // Initialize store
  function initialize() {
    // Start periodic status checks
    void updateServerStatus();
  }

  return {
    // State
    serverStatus,
    isCreating,
    isTerminating,
    isConnected,
    selectedGpu,
    errorMessage,
    gpuOptions,

    // Computed
    serverExists,
    isServerRunning,
    statusColor,
    toggleState,

    // Actions
    updateServerStatus,
    startServer,
    terminateServer,
    setConnected,
    setSelectedGpu,
    clearError,
    initialize
  };
});
