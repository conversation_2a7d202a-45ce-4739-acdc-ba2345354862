import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { comfyUIService } from 'src/services/comfyUI/ComfyUIService';
import { userSettingsService } from 'src/services/userSettingsService';
import type { ServerStatus } from 'src/services/comfyUI/types';

export interface ComfyUIState {
  serverStatus: ServerStatus;
  isCreating: boolean;
  isTerminating: boolean;
  isConnected: boolean;
  selectedGpu: string;
  errorMessage: string;
}

export const useComfyUIStore = defineStore('comfyUI', () => {
  // State
  const serverStatus = ref<ServerStatus>({ status: 'no-server' });
  const isCreating = ref(false);
  const isTerminating = ref(false);
  const isConnected = ref(false);
  const isCheckingHealth = ref(false);
  const selectedGpu = ref('NVIDIA RTX A5000');
  const errorMessage = ref('');
  const terminateOnReload = ref(true); // Default to terminate (safer option)

  // GPU options
  const gpuOptions = [
    { label: 'NVIDIA RTX A5000 (24GB VRAM)', value: 'NVIDIA RTX A5000' },
    { label: 'NVIDIA RTX A4000 (16GB VRAM)', value: 'NVIDIA RTX A4000' },
    { label: 'NVIDIA RTX 3090 (24GB VRAM)', value: 'NVIDIA RTX 3090' },
    { label: 'NVIDIA RTX 4090 (24GB VRAM)', value: 'NVIDIA RTX 4090' }
  ];

  // Computed properties
  const serverExists = computed(() =>
    ['running', 'starting', 'stopping'].includes(serverStatus.value.status)
  );

  const isServerRunning = computed(() => serverStatus.value.status === 'running');

  const statusColor = computed(() => {
    switch (serverStatus.value.status) {
      case 'running':
        return 'positive';
      case 'starting':
        return 'info';
      case 'stopping':
        return 'warning';
      case 'error':
        return 'negative';
      default:
        return 'grey';
    }
  });

  const toggleState = computed(() => {
    if (!serverExists.value) return 'off';

    switch (serverStatus.value.status) {
      case 'starting':
        return 'creating';
      case 'running':
        return isConnected.value ? 'ready' : 'starting-comfyui';
      case 'stopping':
        return 'stopping';
      default:
        return 'off';
    }
  });

  const reloadBehavior = computed(() => {
    // Provide a default value while loading
    return terminateOnReload.value ? 'terminate' : 'keep';
  });

  // Actions
  async function updateServerStatus() {
    const status = await comfyUIService.checkServerStatus();
    serverStatus.value = status;

    // If server is running and not connected, check ComfyUI readiness
    if (status.status === 'running' && !isConnected.value && !isCheckingHealth.value) {
      void waitForComfyUIReadiness();
    }
  }

  async function startServer(options: { gpuTypeId?: string } = {}) {
    isCreating.value = true;
    errorMessage.value = '';

    try {
      const result = await comfyUIService.startServer({
        gpuTypeId: options.gpuTypeId || selectedGpu.value
      });

      if (result.status === 'error') {
        errorMessage.value = result.message || 'Error creating server';
        return false;
      } else {
        // Update status immediately
        await updateServerStatus();
        return true;
      }
    } catch (error) {
      errorMessage.value = error instanceof Error ? error.message : String(error);
      return false;
    } finally {
      isCreating.value = false;
    }
  }

  async function terminateServer() {
    isTerminating.value = true;

    try {
      const success = await comfyUIService.terminateServer();
      
      if (success) {
        // Update status immediately
        await updateServerStatus();
        return true;
      } else {
        return false;
      }
    } catch (error) {
      console.error('Error terminating server:', error);
      return false;
    } finally {
      isTerminating.value = false;
    }
  }

  function setConnected(connected: boolean) {
    isConnected.value = connected;
  }

  function setSelectedGpu(gpu: string) {
    selectedGpu.value = gpu;
  }

  function clearError() {
    errorMessage.value = '';
  }

  async function waitForComfyUIReadiness() {
    if (!isServerRunning.value || !serverStatus.value.apiEndpoint) return;

    // If already connected, don't check again
    if (isConnected.value) {
      console.log('ComfyUI already connected, skipping health check');
      return;
    }

    isCheckingHealth.value = true;

    try {
      console.log('Waiting for ComfyUI to be ready...');

      // Wait for ComfyUI to be ready (up to 5 minutes)
      const isReady = await comfyUIService.waitForComfyUIReady(serverStatus.value.apiEndpoint, 300000);

      if (isReady) {
        isConnected.value = true;
        console.log('ComfyUI is ready!');
      } else {
        console.log('ComfyUI readiness check timed out');
      }
    } catch (error) {
      console.error('Error checking ComfyUI readiness:', error);
    } finally {
      isCheckingHealth.value = false;
    }
  }

  async function checkExistingConnection() {
    if (!isServerRunning.value || !serverStatus.value.apiEndpoint) return;

    // Check if ComfyUI is already ready
    const isHealthy = await comfyUIService.checkComfyUIHealth(serverStatus.value.apiEndpoint);
    if (isHealthy) {
      isConnected.value = true;
      console.log('ComfyUI was already ready');
    }
  }

  async function loadTerminateOnReloadSetting() {
    try {
      const setting = await userSettingsService.getSetting('comfyui-terminate-on-reload');
      console.log('Loaded terminate on reload setting:', setting);
      if (setting !== null) {
        terminateOnReload.value = setting === 'true';
      }
      console.log('terminateOnReload.value is now:', terminateOnReload.value);
    } catch (error) {
      console.error('Error loading terminate on reload setting:', error);
    }
  }

  // Initialize store
  async function initialize() {
    // Load settings first
    await loadTerminateOnReloadSetting();
    // Start periodic status checks
    void updateServerStatus();
  }

  return {
    // State
    serverStatus,
    isCreating,
    isTerminating,
    isConnected,
    isCheckingHealth,
    selectedGpu,
    errorMessage,
    gpuOptions,

    // Computed
    serverExists,
    isServerRunning,
    statusColor,
    toggleState,
    reloadBehavior,

    // Actions
    updateServerStatus,
    startServer,
    terminateServer,
    setConnected,
    setSelectedGpu,
    clearError,
    waitForComfyUIReadiness,
    checkExistingConnection,
    initialize
  };
});
