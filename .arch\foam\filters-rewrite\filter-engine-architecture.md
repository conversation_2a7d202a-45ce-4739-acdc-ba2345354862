# Generic Filter Engine Architecture

## Overview

This document outlines the architecture for a generic, table-agnostic filter engine that can be used across the entire application. The goal is to create a robust, consistent, and extensible filtering system that works reliably for all tables (templates, products, users, etc.) and supports various filter types.

## Current Issues

1. **Inconsistent Filter Selection**:
   - Newly created filters aren't properly selected in the dropdown
   - UI state doesn't always reflect the actual filter state

2. **Unreliable Filter Application**:
   - Filters sometimes need to be reloaded multiple times to be applied
   - Inconsistent behavior between different filter types (main fields, expanded fields, prompt elements)

3. **Architectural Problems**:
   - Filter logic is spread across multiple components with different communication patterns
   - Mix of local state, store state, and localStorage
   - Inconsistent event handling for different filter types
   - No clear ownership of filter state

4. **Extensibility Issues**:
   - Adding new filter types requires modifying multiple components
   - No standardized way to add new filter types

## Core Architecture

### 1. Filter Registry

A central registry for all filter types across all tables. This registry will:

- Store metadata about each filter type
- Provide a standardized registration API
- Enable discovery of available filters for each table

```typescript
interface FilterTypeDefinition {
  id: string;
  name: string;
  section: 'main' | 'expanded' | 'specialized';
  component: Component;
  tableTypes: string[]; // Which tables this filter type applies to
  stateExtractor: (state: any) => any;
  stateApplier: (state: any, filterValue: any) => any;
  clearHandler: (state: any) => any;
}

class FilterRegistry {
  private filterTypes: Map<string, FilterTypeDefinition> = new Map();

  registerFilterType(definition: FilterTypeDefinition): void {
    this.filterTypes.set(definition.id, definition);
  }

  getFilterTypesForTable(tableName: string): FilterTypeDefinition[] {
    return Array.from(this.filterTypes.values())
      .filter(def => def.tableTypes.includes(tableName));
  }
}
```

### 2. Filter State Store

A Pinia store that serves as the single source of truth for all filter-related state:

```typescript
interface FilterState {
  currentTable: string | null;
  currentFilterId: number | null;
  savedFilters: SavedFilter[];
  activeFilters: Record<string, unknown>;
  filterDefinitions: Record<string, FilterDefinition>;
  isLoading: boolean;
  hasUnsavedChanges: boolean;
}

export const useFilterStore = defineStore('filter', {
  state: (): FilterState => ({
    currentTable: null,
    currentFilterId: null,
    savedFilters: [],
    activeFilters: {},
    filterDefinitions: {},
    isLoading: false,
    hasUnsavedChanges: false
  }),
  
  getters: {
    filtersByTable: (state) => state.savedFilters.filter(f => f.table_name === state.currentTable),
    currentFilter: (state) => state.savedFilters.find(f => f.id === state.currentFilterId),
    // Other getters
  },
  
  actions: {
    async setTable(tableName: string) { /* ... */ },
    async loadSavedFilters() { /* ... */ },
    async saveCurrentFilter(name: string, description: string, makeDefault: boolean) { /* ... */ },
    async loadFilter(filterId: number) { /* ... */ },
    async deleteFilter(filterId: number) { /* ... */ },
    setFilterValue(filterId: string, value: any) { /* ... */ },
    clearFilter(filterId: string) { /* ... */ },
    clearAllFilters() { /* ... */ },
    // Other actions
  }
});
```

### 3. Filter Engine

Responsible for applying filters to data:

```typescript
class FilterEngine {
  constructor(private filterRegistry: FilterRegistry, private filterStore: any) {}

  applyFilters(data: any[], tableName: string): any[] {
    const activeFilters = this.filterStore.activeFilters;
    if (!activeFilters || Object.keys(activeFilters).length === 0) {
      return data;
    }

    const filterTypes = this.filterRegistry.getFilterTypesForTable(tableName);
    
    return data.filter(item => {
      // Apply each filter type
      for (const filterType of filterTypes) {
        const filterValue = activeFilters[filterType.id];
        if (!filterValue) continue;
        
        const result = filterType.stateApplier(item, filterValue);
        if (!result) return false;
      }
      
      return true;
    });
  }
}
```

### 4. Filter Components

A set of standardized UI components for different filter types:

- **FilterPanel**: Main container for all filters
- **FilterSection**: Container for a group of related filters
- **FilterControl**: Individual filter control (text input, select, etc.)
- **SavedFilterSelector**: Dropdown for selecting saved filters
- **FilterActionBar**: Buttons for saving, clearing, etc.

### 5. Filter Events

A standardized set of events for filter-related communication:

```typescript
// Event types
const FILTER_EVENTS = {
  FILTER_CHANGED: 'filter:changed',
  FILTER_CLEARED: 'filter:cleared',
  FILTER_SAVED: 'filter:saved',
  FILTER_LOADED: 'filter:loaded',
  FILTER_DELETED: 'filter:deleted'
};

// Event bus
const filterEventBus = new EventEmitter();

// Example usage
filterEventBus.emit(FILTER_EVENTS.FILTER_CHANGED, {
  filterId: 'status',
  value: ['active', 'pending'],
  table: 'templates'
});
```

## Filter Lifecycle

1. **Initialization**:
   - Load filter registry
   - Register built-in filter types
   - Load saved filters for the current user

2. **Table Selection**:
   - Set the current table
   - Load filter definitions for the table
   - Load default filter or last used filter

3. **Filter Interaction**:
   - User interacts with filter controls
   - Filter state is updated
   - Filter events are emitted
   - Data is filtered in real-time

4. **Filter Persistence**:
   - User saves the current filter
   - Filter is stored in the database
   - Filter appears in the saved filters dropdown

5. **Filter Selection**:
   - User selects a saved filter
   - Filter state is loaded
   - Filter controls are updated
   - Data is filtered according to the selected filter

## Implementation Plan

### Phase 1: Core Architecture (1-2 weeks)

1. Create the filter registry
   - Define the registry interface
   - Implement the registration mechanism
   - Create utility functions for filter discovery

2. Implement the filter state store
   - Define the state structure
   - Implement basic actions and getters
   - Add support for loading and saving filters

3. Develop the filter engine
   - Implement the filter application logic
   - Create adapters for different data sources
   - Add support for complex filter combinations

### Phase 2: Base Components (1-2 weeks)

1. Create the FilterPanel component
   - Implement the basic layout
   - Add support for different sections
   - Create the saved filter selector

2. Implement the FilterSection component
   - Add support for collapsible sections
   - Implement section headers
   - Create the section content container

3. Develop base FilterControl components
   - Text filter
   - Number filter
   - Date filter
   - Select filter
   - Multi-select filter

### Phase 3: Specialized Filter Types (2-3 weeks)

1. Implement the PromptElementsFilter
   - Create the specialized component
   - Implement the state extraction and application logic
   - Register the filter type

2. Develop the RelatedItemsFilter
   - Create the specialized component
   - Implement the state extraction and application logic
   - Register the filter type

3. Add support for custom filter types
   - Create a plugin system for custom filters
   - Implement the registration mechanism
   - Add documentation for creating custom filters

### Phase 4: Integration and Testing (1-2 weeks)

1. Integrate with existing tables
   - Templates table
   - Products table
   - Users table
   - Other tables

2. Implement migration from old filters
   - Create a migration utility
   - Convert existing filters to the new format
   - Ensure backward compatibility

3. Comprehensive testing
   - Unit tests for core components
   - Integration tests for filter application
   - End-to-end tests for the complete filter lifecycle

### Phase 5: Refinement and Optimization (1 week)

1. Performance optimization
   - Optimize filter application for large datasets
   - Implement caching for frequently used filters
   - Add support for server-side filtering

2. User experience improvements
   - Add loading indicators
   - Improve error handling
   - Enhance accessibility

3. Documentation and examples
   - Create comprehensive documentation
   - Add examples for common use cases
   - Create a filter type creation guide

## Database Schema

The existing `user_filters` table can be used with minimal modifications:

```sql
CREATE TABLE user_filters (
  id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
  user_id BIGINT REFERENCES app_users(id),
  name TEXT NOT NULL,
  description TEXT,
  table_name TEXT NOT NULL,
  context TEXT NOT NULL,
  configuration JSONB NOT NULL,
  is_default BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

The `configuration` field will store the filter state in a structured format:

```json
{
  "columns": [
    {
      "id": "name",
      "visible": true,
      "section": "main",
      "order": 0,
      "filterValue": "search term"
    },
    // Other columns
  ],
  "specializedFilters": {
    "promptElements": {
      "element_type_1": [1, 2, 3],
      "element_type_2": [4, 5, 6]
    },
    "relatedItems": {
      "collections": [1, 2, 3]
    }
  }
}
```

## Benefits

1. **Consistency**: All filter types will behave the same way across all tables
2. **Reliability**: Filters will be applied consistently and predictably
3. **Extensibility**: Adding new filter types or supporting new tables will be straightforward
4. **Maintainability**: Clear architecture makes the code easier to understand and modify
5. **Performance**: Optimized filter application with better caching and state management
6. **User Experience**: Consistent UI and behavior across the application

## Conclusion

This architecture provides a solid foundation for a generic, table-agnostic filter engine that can be used across the entire application. By implementing this design, we will address the current issues with the filter system and create a more robust, consistent, and extensible solution.

The implementation plan outlines a step-by-step approach to building this system, with clear milestones and deliverables for each phase. This will ensure a smooth transition from the current filter system to the new architecture.
