# Step 2: SwarmUI Installation and Configuration

This document outlines the process for installing and configuring SwarmUI on a RunPod instance. This installation will serve as the basis for our custom template.

## 2.1 Launch a Temporary RunPod Instance

First, we'll launch a temporary RunPod instance to set up SwarmUI:

1. Log in to your RunPod account
2. Navigate to [RunPod Secure Cloud](https://www.runpod.io/console/gpu-cloud)
3. Select a GPU type (recommended: NVIDIA RTX A4000 or better)
4. Choose the following configuration:
   - Container Disk: 50GB
   - Volume Size: 100GB (for storing models and generated images)
   - Container Image: `runpod/pytorch:2.0.1-py3.10-cuda11.8.0-devel-ubuntu22.04`
5. Click "Deploy"

## 2.2 Connect to the Pod via SSH

Once the pod is running:

1. Click on the pod to open its details
2. Click "Connect" and select "SSH"
3. Follow the instructions to connect via SSH

## 2.3 Install System Dependencies

Once connected via SSH, install the necessary system dependencies:

```bash
# Update package lists
apt-get update

# Install system dependencies
apt-get install -y git wget curl libgl1-mesa-glx libglib2.0-0 python3-pip python3-dev build-essential

# Install Node.js and npm (required for SwarmUI frontend)
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt-get install -y nodejs

# Verify installations
python3 --version
node --version
npm --version
```

## 2.4 Clone SwarmUI Repository

Clone the SwarmUI repository:

```bash
# Navigate to the home directory
cd ~

# Clone the repository
git clone https://github.com/LykosAI/StableSwarmUI.git

# Navigate to the repository
cd StableSwarmUI
```

## 2.5 Install Python Dependencies

Install the required Python packages:

```bash
# Create and activate a virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Install additional dependencies for image processing
pip install pillow opencv-python-headless
```

## 2.6 Configure SwarmUI

Create a configuration file for SwarmUI:

```bash
# Create a configuration directory
mkdir -p ~/.swarmui

# Create a configuration file
cat > ~/.swarmui/config.json << EOL
{
  "BackendUrl": "http://0.0.0.0:7801",
  "FrontendUrl": "http://0.0.0.0:7800",
  "ModelRoot": "/workspace/models",
  "OutputsPath": "/workspace/outputs",
  "DisableUpdateCheck": true,
  "AutoInstall": {
    "Torch": false,
    "Comfy": true,
    "Models": true
  },
  "Auth": {
    "Enabled": true,
    "ApiKey": "REPLACE_WITH_GENERATED_API_KEY"
  }
}
EOL

# Generate a secure API key
API_KEY=$(openssl rand -hex 16)
sed -i "s/REPLACE_WITH_GENERATED_API_KEY/$API_KEY/" ~/.swarmui/config.json

# Remember this API key as we'll need it later
echo "Your SwarmUI API key is: $API_KEY"
echo "Please save this key securely as you'll need it to authenticate with SwarmUI"
```

## 2.7 Create Startup Script

Create a script to start SwarmUI automatically:

```bash
cat > ~/start-swarmui.sh << EOL
#!/bin/bash
cd ~/StableSwarmUI
source venv/bin/activate
python3 -m swarm.main
EOL

chmod +x ~/start-swarmui.sh
```

## 2.8 Install Models (Optional)

If you want to pre-install some models:

```bash
# Create models directory
mkdir -p /workspace/models/checkpoints

# Download a model (example: Stable Diffusion 1.5)
wget -O /workspace/models/checkpoints/v1-5-pruned-emaonly.safetensors https://huggingface.co/runwayml/stable-diffusion-v1-5/resolve/main/v1-5-pruned-emaonly.safetensors
```

## 2.9 Start SwarmUI

Start SwarmUI to verify the installation:

```bash
~/start-swarmui.sh
```

This will start SwarmUI. The first run will take some time as it downloads and sets up additional components.

## 2.10 Test SwarmUI API

In a new terminal, test the SwarmUI API:

```bash
# Replace API_KEY with your actual API key
curl -X GET "http://localhost:7801/api/models" \
  -H "Authorization: Bearer $API_KEY"
```

You should receive a JSON response with the available models.

## 2.11 Create a Systemd Service (Optional)

For automatic startup, create a systemd service:

```bash
cat > /etc/systemd/system/swarmui.service << EOL
[Unit]
Description=SwarmUI Service
After=network.target

[Service]
User=root
WorkingDirectory=/root/StableSwarmUI
ExecStart=/root/start-swarmui.sh
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
EOL

# Enable and start the service
systemctl enable swarmui
systemctl start swarmui
```

## 2.12 Configure Nginx for Secure Access (Optional)

For production environments, set up Nginx as a reverse proxy:

```bash
# Install Nginx
apt-get install -y nginx

# Create Nginx configuration
cat > /etc/nginx/sites-available/swarmui << EOL
server {
    listen 80;
    server_name _;

    location / {
        proxy_pass http://localhost:7800;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    location /api/ {
        proxy_pass http://localhost:7801;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOL

# Enable the site
ln -s /etc/nginx/sites-available/swarmui /etc/nginx/sites-enabled/

# Test Nginx configuration
nginx -t

# Restart Nginx
systemctl restart nginx
```

## Next Steps

Once SwarmUI is installed and configured, proceed to [Step 2.1: Flux Model Integration with SwarmUI](./02.1-flux-model-integration.md) to add the Flux model to your SwarmUI installation.
