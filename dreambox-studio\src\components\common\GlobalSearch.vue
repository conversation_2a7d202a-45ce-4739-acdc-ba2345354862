<template>
  <div class="global-search" :class="{ 'expanded': searchStore.expanded }">
    <div class="row items-center q-col-gutter-md search-container">
      <!-- Search input -->
      <div class="col-12 col-md-auto">
        <q-input
          v-model="searchStore.searchText"
          dense
          outlined
          filled
          stack-label
          :label="searchPlaceholder"
          :placeholder="searchPlaceholder"
          clearable
          debounce="300"
          @update:model-value="(value) => value !== null && onSearch(String(value))"
          @focus="onSearchFocus"
        >
          <template v-slot:append>
            <q-icon name="search" />
          </template>
        </q-input>
      </div>

      <!-- Dynamic filters and action button removed as requested -->
      <!-- Will be implemented in a different location -->
      <!-- Only search input is kept -->
    </div>
  </div>
</template>

<script setup lang="ts">
// Removed unused imports: computed, Filter
import { useSearchStore } from 'src/stores/searchStore';

// Props
const props = defineProps<{
  searchPlaceholder?: string;
}>();

// Emits
const emit = defineEmits<{
  (e: 'search', text: string): void;
  // filter-change event removed as it's no longer used
}>();

// Store
const searchStore = useSearchStore();

// Computed - filtersList and hasFilters removed as they're no longer used

// Default props
const searchPlaceholder = props.searchPlaceholder || 'Search...';

// Methods
function onSearch(text: string) {
  emit('search', text);
}

function onSearchFocus() {
  searchStore.setExpanded(true);
}

// onFilterChange function removed as it's no longer used
</script>

<style lang="scss" scoped>
.global-search {
  width: 100%;

  // Ensure proper spacing between elements
  .filter-item, .col-12 {
    margin-bottom: 8px;
  }

  // Style for desktop filter dropdowns
  @media (min-width: 600px) {
    .q-select {
      min-width: 150px;
    }

    .q-input {
      min-width: 250px;
    }
  }

  // Style for all form controls
  :deep(.q-field__control) {
    background-color: rgba(33, 150, 243, 0.15) !important; // Primary blue with higher opacity
    border-color: rgba(33, 150, 243, 0.3) !important;
  }

  :deep(.q-field:hover .q-field__control) {
    background-color: rgba(33, 150, 243, 0.2) !important;
  }

  :deep(.q-field--focused .q-field__control) {
    background-color: rgba(33, 150, 243, 0.25) !important;
    border-color: rgba(33, 150, 243, 0.5) !important;
  }

  // Dark mode support
  :deep(.body--dark .q-field__control) {
    background-color: rgba(33, 150, 243, 0.2) !important;
  }

  :deep(.body--dark .q-field:hover .q-field__control) {
    background-color: rgba(33, 150, 243, 0.25) !important;
  }

  :deep(.body--dark .q-field--focused .q-field__control) {
    background-color: rgba(33, 150, 243, 0.3) !important;
  }

  // Make the placeholder more visible
  .filter-placeholder {
    font-weight: 500;
    opacity: 0.9;
  }

  // Direct override for Quasar's placeholder styling
  :deep(.q-field__native) {
    &::placeholder {
      color: var(--q-primary) !important;
      opacity: 1 !important;
      font-weight: 600 !important;
    }
  }

  // Force placeholder to be visible
  :deep(.q-field--float .q-field__label) {
    color: var(--q-primary) !important;
    opacity: 1 !important;
    font-weight: 600 !important;
    transform: translateY(-40%) scale(0.75) !important;
  }

  // Style for the input placeholder
  :deep(.q-field__label) {
    color: var(--q-primary) !important;
    opacity: 1 !important;
    font-weight: 600 !important;
  }

  // Helper class to show elements only on xs screens
  .xs-only {
    @media (min-width: 600px) {
      display: none;
    }
  }

  @media (max-width: 599px) {
    .q-input {
      width: 100%;
    }

    .search-container {
      flex-direction: column;
      align-items: stretch;
    }

    // Make the layout more intuitive on mobile
    .filter-item {
      margin-top: 8px;
    }
  }
}
</style>
