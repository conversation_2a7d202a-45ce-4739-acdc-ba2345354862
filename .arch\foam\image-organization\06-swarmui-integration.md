# SwarmUI Integration for AI-Generated Images

This document outlines the integration between SwarmUI (running on RunPod) and the image organization system.

## Overview

SwarmUI generates images based on prompts and other parameters. These images need to be:
1. Saved to S3 storage
2. Recorded in the database with appropriate metadata
3. Associated with templates, products, or other entities

## Edge Function for SwarmUI Image Upload

Create an Edge Function to handle images generated by Swarm<PERSON>:

```typescript
// supabase/functions/save-swarmui-image/index.ts
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { S3Client, PutObjectCommand } from 'https://esm.sh/@aws-sdk/client-s3'
import { v4 as uuidv4 } from 'https://esm.sh/uuid@9.0.0'

serve(async (req) => {
  // CORS headers
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Authorization, Content-Type',
      }
    })
  }

  // Create Supabase client
  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
  )

  // Get user from request
  const { data: { user } } = await supabaseClient.auth.getUser()
  if (!user) {
    return new Response(JSON.stringify({ error: 'Unauthorized' }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    })
  }

  try {
    // Parse the request body
    const formData = await req.formData()
    const imageFile = formData.get('image')
    const metadataStr = formData.get('metadata')

    if (!imageFile || !(imageFile instanceof File)) {
      throw new Error('No image file provided')
    }

    if (!metadataStr) {
      throw new Error('No metadata provided')
    }

    const metadata = JSON.parse(metadataStr)

    // Create an S3 client
    const s3Client = new S3Client({
      region: 'us-east-1', // Your S3 bucket region
      credentials: {
        accessKeyId: Deno.env.get('AWS_ACCESS_KEY_ID') ?? '',
        secretAccessKey: Deno.env.get('AWS_SECRET_ACCESS_KEY') ?? ''
      }
    })

    // Read the file as an array buffer
    const arrayBuffer = await imageFile.arrayBuffer()
    const buffer = new Uint8Array(arrayBuffer)

    // Create a unique file key with UUID
    const uuid = uuidv4()
    const filename = metadata.filename || 'generated-image.png'
    const sanitizedFilename = filename.replace(/[^a-zA-Z0-9.-]/g, '_')
    const key = `images/${uuid}-${sanitizedFilename}`

    // Upload to S3
    const command = new PutObjectCommand({
      Bucket: 'dreambox-studio-images',
      Key: key,
      Body: buffer,
      ContentType: imageFile.type || 'image/png'
    })

    await s3Client.send(command)

    // Extract tags from prompt
    const promptTags = metadata.prompt
      ? extractTagsFromPrompt(metadata.prompt)
      : []

    // Combine with any explicit tags
    const tags = [...new Set([...(metadata.tags || []), ...promptTags])]

    // Create a record in the s3_images table
    const { data: imageData, error } = await supabaseClient.rpc(
      'create_s3_image',
      {
        p_filename: sanitizedFilename,
        p_s3_key: key,
        p_s3_url: `https://dreambox-studio-images.s3.amazonaws.com/${key}`,
        p_mime_type: imageFile.type || 'image/png',
        p_file_size: buffer.length,
        p_width: metadata.width,
        p_height: metadata.height,
        p_title: metadata.title || 'Generated Image',
        p_description: metadata.description || metadata.prompt,
        p_tags: tags,
        p_template_id: metadata.templateId,
        p_resolution: metadata.resolution || 'standard',
        p_is_public: metadata.isPublic || false,
        p_metadata: {
          ...metadata,
          steps: metadata.steps,
          cfgScale: metadata.cfgScale,
          sampler: metadata.sampler
        },
        p_prompt: metadata.prompt,
        p_negative_prompt: metadata.negativePrompt,
        p_seed: metadata.seed,
        p_model_used: metadata.model,
        p_parent_image_id: metadata.parentImageId
      }
    )

    // If product ID is provided, associate the image with the product
    if (metadata.productId) {
      await supabaseClient.rpc(
        'associate_image_with_product',
        {
          p_image_id: imageData.id,
          p_product_id: metadata.productId,
          p_position: metadata.position,
          p_is_primary: metadata.isPrimary || false
        }
      )
    }

    if (error) throw new Error(`Failed to create image record: ${error.message}`)

    return new Response(JSON.stringify({
      success: true,
      image: imageData
    }), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    })
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    })
  }
})

// Helper function to extract potential tags from a prompt
function extractTagsFromPrompt(prompt) {
  // Simple extraction of comma-separated terms
  return prompt
    .split(',')
    .map(term => term.trim())
    .filter(term => term.length > 3 && term.length < 20) // Filter out very short or long terms
    .slice(0, 10) // Limit to 10 tags
}
```

## SwarmUI API Client

Create a service to interact with the SwarmUI API:

```typescript
// src/services/swarmUIService.ts
import { supabase } from '../boot/supabase'
import { imageService } from './imageService'

export const swarmUIService = {
  /**
   * Generate an image using SwarmUI
   */
  async generateImage(params) {
    // Get the active server
    const { data: server, error: serverError } = await supabase.rpc('get_active_server')

    if (serverError) throw serverError
    if (!server) throw new Error('No active SwarmUI server found')

    // Prepare the generation parameters
    const generationParams = {
      prompt: params.prompt,
      negative_prompt: params.negativePrompt || '',
      width: params.width || 512,
      height: params.height || 512,
      steps: params.steps || 30,
      cfg_scale: params.cfgScale || 7,
      sampler: params.sampler || 'euler_a',
      seed: params.seed || -1,
      batch_size: 1
    }

    // Call the SwarmUI API to generate the image
    const response = await fetch(`${server.api_endpoint}/api/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${server.api_key}`
      },
      body: JSON.stringify(generationParams)
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`SwarmUI API error: ${errorText}`)
    }

    const result = await response.json()

    // Get the image data
    const imageResponse = await fetch(result.images[0])
    const imageBlob = await imageResponse.blob()

    // Create a file from the blob
    const file = new File([imageBlob], `generated-${Date.now()}.png`, { type: 'image/png' })

    // Save the image to S3 and create a database record
    const formData = new FormData()
    formData.append('image', file)
    formData.append('metadata', JSON.stringify({
      filename: file.name,
      width: generationParams.width,
      height: generationParams.height,
      title: params.title || 'Generated Image',
      description: params.description || params.prompt,
      templateId: params.templateId,
      productId: params.productId,
      isPublic: params.isPublic || false,
      prompt: params.prompt,
      negativePrompt: params.negativePrompt,
      model: result.model || 'unknown',
      seed: result.seed || generationParams.seed,
      steps: generationParams.steps,
      cfgScale: generationParams.cfg_scale,
      sampler: generationParams.sampler,
      generationParams
    }))

    const { data: savedImage, error } = await supabase.functions.invoke('save-swarmui-image', {
      body: formData
    })

    if (error) throw error

    // Update server activity
    await supabase.rpc('update_server_activity', {
      p_server_id: server.id
    })

    return savedImage.image
  },

  /**
   * Get available models from SwarmUI
   */
  async getModels() {
    // Get the active server
    const { data: server, error: serverError } = await supabase.rpc('get_active_server')

    if (serverError) throw serverError
    if (!server) throw new Error('No active SwarmUI server found')

    // Call the SwarmUI API to get models
    const response = await fetch(`${server.api_endpoint}/api/models`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${server.api_key}`
      }
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`SwarmUI API error: ${errorText}`)
    }

    const result = await response.json()

    // Update server activity
    await supabase.rpc('update_server_activity', {
      p_server_id: server.id
    })

    return result.models
  }
}
```

## Next Steps

Once the SwarmUI integration is implemented, proceed to [UI Components](./07-ui-components.md) to create the user interface for image management.
