import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/auth/login'
  },
  {
    path: '/auth',
    component: () => import('layouts/AuthLayout.vue'),
    children: [
      {
        path: 'login',
        name: 'login',
        component: () => import('pages/LoginPage.vue'),
      }
    ]
  },
  {
    path: '/dashboard',
    component: () => import('layouts/MainLayout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'dashboard',
        component: () => import('pages/DashboardPage.vue'),
      },
      {
        path: 'settings',
        name: 'settings',
        component: () => import('pages/settings/SettingsPage.vue'),
      },
    ],
  },
  {
    path: '/designer',
    component: () => import('layouts/MainLayout.vue'),
    meta: { requiresAuth: true, role: 'designer' },
    children: [
      {
        path: '',
        name: 'designer-dashboard',
        component: () => import('pages/designer/DesignerDashboard.vue')
      },
      {
        path: 'templates',
        name: 'designer-templates',
        component: () => import('pages/templates/TemplateBrowser.vue')
      },

      {
        path: 'templates/builder/:id',
        name: 'designer-template-builder',
        component: () => import('pages/templates/TemplateBuilder.vue')
      },
      {
        path: 'templates/builder/new',
        name: 'designer-template-builder-new',
        component: () => import('pages/templates/TemplateBuilder.vue')
      },
      {
        path: 'settings',
        name: 'designer-settings',
        component: () => import('pages/settings/SettingsPage.vue')
      },
    ],
  },
  {
    path: '/admin',
    component: () => import('layouts/MainLayout.vue'),
    meta: { requiresAuth: true, role: 'admin' },
    children: [
      {
        path: '',
        name: 'admin-dashboard',
        component: () => import('pages/admin/AdminDashboard.vue')
      },
      {
        path: 'templates',
        name: 'admin-templates',
        component: () => import('pages/templates/TemplateBrowser.vue')
      },

      {
        path: 'templates/builder/:id',
        name: 'admin-template-builder',
        component: () => import('pages/templates/TemplateBuilder.vue')
      },
      {
        path: 'templates/builder/new',
        name: 'admin-template-builder-new',
        component: () => import('pages/templates/TemplateBuilder.vue')
      },
      {
        path: 'settings',
        name: 'admin-settings',
        component: () => import('pages/settings/SettingsPage.vue')
      },
    ],
  },
  {
    path: '/super-admin',
    component: () => import('layouts/MainLayout.vue'),
    meta: { requiresAuth: true, role: 'super-admin' },
    children: [
      {
        path: '',
        name: 'super-admin-dashboard',
        component: () => import('pages/super-admin/SuperAdminDashboard.vue')
      },
      {
        path: 'user-management',
        name: 'user-management',
        component: () => import('pages/super-admin/UserManagement.vue')
      },

      {
        path: 'settings',
        name: 'super-admin-settings',
        component: () => import('pages/settings/SettingsPage.vue')
      },
    ],
  },

  // Always leave this as last one,
  // but you can also remove it
  {
    path: '/:catchAll(.*)*',
    component: () => import('pages/ErrorNotFound.vue'),
  },
];

export default routes;
