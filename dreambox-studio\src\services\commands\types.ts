/**
 * Type definitions for the command system
 */

export interface CommandParameter {
  name: string;
  type: string;
  required: boolean;
  description: string;
}

export interface CommandDefinition {
  name: string;
  handler: (...args: unknown[]) => unknown;
  description: string;
  parameters?: CommandParameter[];
  examples?: string[];
}

export interface CommandResult {
  success: boolean;
  message?: string;
  data?: unknown;
  silent?: boolean; // If true, don't show a notification for this result
}
