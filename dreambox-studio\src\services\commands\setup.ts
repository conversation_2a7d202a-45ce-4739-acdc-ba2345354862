/**
 * Command Setup
 *
 * This module registers all available commands with the command registry.
 * It should be imported and executed during application initialization.
 */

import { commandRegistry } from './registry';
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { useAuthStore } from 'src/stores/auth';
import type { CommandResult } from './types';
import { notify } from 'src/boot/notifications';

/**
 * Setup all application commands
 * Note: This function uses Vue composables which must be called inside setup()
 * So we return a function that should be called within a component's setup
 */
export function createCommandSetup() {
  return function setupCommands() {
    const router = useRouter();
    const $q = useQuasar();
    const authStore = useAuthStore();

    // Navigation commands
    commandRegistry.register({
      name: 'openDashboard',
      handler: () => {
        void router.push('/');
        return { success: true, message: 'Navigated to dashboard' };
      },
      description: 'Navigate to the dashboard',
      examples: ['Open dashboard', 'Go to dashboard', 'Show dashboard']
    });

    commandRegistry.register({
      name: 'openSettings',
      handler: () => {
        // Get the current role to determine the correct settings path
        const role = authStore.state.user?.currentRole;
        let settingsPath = '/';

        // Set the path based on the user's role
        if (role === 'super-admin') {
          settingsPath = '/super-admin/settings';
        } else if (role === 'admin') {
          settingsPath = '/admin/settings';
        } else if (role === 'designer') {
          settingsPath = '/designer/settings';
        }

        void router.push(settingsPath);
        return { success: true, message: 'Navigated to settings' };
      },
      description: 'Navigate to settings',
      examples: ['Open settings', 'Go to settings', 'Show settings']
    });

    // UI commands
    // Track the last toggle time to prevent multiple rapid toggles
    let lastToggleTime = 0;

    commandRegistry.register({
      name: 'toggleDarkMode',
      handler: (): CommandResult => {
        // Prevent multiple toggles within 500ms
        const now = Date.now();
        if (now - lastToggleTime < 500) {
          // Silent return - no notification
          return { success: true, silent: true };
        }

        // Update last toggle time
        lastToggleTime = now;

        // Toggle dark mode
        $q.dark.toggle();
        const mode = $q.dark.isActive ? 'dark' : 'light';

        // Show a single notification using our notification service
        void notify({
          message: `Switched to ${mode} mode`,
          color: 'primary',
          position: 'top',
          timeout: 2000
        });

        // Silent return - we already showed the notification
        return { success: true, silent: true };
      },
      description: 'Toggle between light and dark mode',
      examples: ['Toggle dark mode', 'Switch theme', 'Change to dark mode', 'Change to light mode']
    });

    // Track the last left drawer toggle time
    let lastLeftDrawerToggleTime = 0;

    commandRegistry.register({
      name: 'toggleLeftDrawer',
      handler: (): CommandResult => {
        // Prevent multiple toggles within 500ms
        const now = Date.now();
        if (now - lastLeftDrawerToggleTime < 500) {
          // Silent return - no notification
          return { success: true, silent: true };
        }

        // Update last toggle time
        lastLeftDrawerToggleTime = now;

        const event = new CustomEvent('toggle-left-drawer');
        window.dispatchEvent(event);

        // Silent return - let the drawer function handle the notification
        return { success: true, silent: true };
      },
      description: 'Open or close the navigation drawer',
      examples: ['Open menu', 'Close menu', 'Toggle navigation', 'Show navigation']
    });

    // Track the last right drawer toggle time
    let lastRightDrawerToggleTime = 0;

    commandRegistry.register({
      name: 'toggleRightDrawer',
      handler: (): CommandResult => {
        // Prevent multiple toggles within 500ms
        const now = Date.now();
        if (now - lastRightDrawerToggleTime < 500) {
          // Silent return - no notification
          return { success: true, silent: true };
        }

        // Update last toggle time
        lastRightDrawerToggleTime = now;

        const event = new CustomEvent('toggle-right-drawer');
        window.dispatchEvent(event);

        // Silent return - let the drawer function handle the notification
        return { success: true, silent: true };
      },
      description: 'Open or close the preview drawer',
      examples: ['Open preview', 'Close preview', 'Toggle preview', 'Show preview']
    });

    // Track the last chat open time
    let lastChatOpenTime = 0;

    commandRegistry.register({
      name: 'openChat',
      handler: (): CommandResult => {
        // Prevent multiple opens within 500ms
        const now = Date.now();
        if (now - lastChatOpenTime < 500) {
          // Silent return - no notification
          return { success: true, silent: true };
        }

        // Update last open time
        lastChatOpenTime = now;

        const event = new CustomEvent('open-chat');
        window.dispatchEvent(event);

        // Silent return - let the drawer function handle the notification
        return { success: true, silent: true };
      },
      description: 'Open the chat panel',
      examples: ['Open chat', 'Show chat', 'Open voice commands', 'Show voice commands']
    });

    // Auth commands
    commandRegistry.register({
      name: 'logout',
      handler: async (): Promise<CommandResult> => {
        try {
          await authStore.logout();
          void router.push('/auth/login');
          return { success: true, message: 'Logged out successfully' };
        } catch (error) {
          return {
            success: false,
            message: error instanceof Error ? error.message : 'Logout failed'
          };
        }
      },
      description: 'Log out of the application',
      examples: ['Log out', 'Sign out', 'Logout']
    });

    // Role-specific commands
    if (authStore.state.user?.currentRole === 'designer') {
      commandRegistry.register({
        name: 'openDesigns',
        handler: (): CommandResult => {
          void router.push('/designer/designs');
          return { success: true, message: 'Navigated to designs' };
        },
        description: 'Navigate to the designs page',
        examples: ['Open designs', 'Show my designs', 'Go to designs']
      });

      commandRegistry.register({
        name: 'openTemplates',
        handler: (): CommandResult => {
          void router.push('/designer/templates');
          return { success: true, message: 'Navigated to templates' };
        },
        description: 'Navigate to the templates page',
        examples: ['Open templates', 'Show templates', 'Go to templates']
      });
    }

    if (authStore.state.user?.currentRole === 'admin') {
      commandRegistry.register({
        name: 'openProjects',
        handler: (): CommandResult => {
          void router.push('/admin/projects');
          return { success: true, message: 'Navigated to projects' };
        },
        description: 'Navigate to the projects page',
        examples: ['Open projects', 'Show projects', 'Go to projects']
      });
    }

    if (authStore.state.user?.currentRole === 'super-admin') {
      commandRegistry.register({
        name: 'openUserManagement',
        handler: (): CommandResult => {
          void router.push('/super-admin/user-management');
          return { success: true, message: 'Navigated to user management' };
        },
        description: 'Navigate to the user management page',
        examples: ['Open user management', 'Show users', 'Manage users']
      });
    }

    // System commands
    commandRegistry.register({
      name: 'help',
      handler: (): CommandResult => {
        // In a real implementation, this would show a help dialog
        // For now, we'll just return the available commands
        const commands = commandRegistry.getAvailableCommands();
        return {
          success: true,
          message: 'Available commands',
          data: { commands }
        };
      },
      description: 'Show available commands',
      examples: ['Help', 'What can I say?', 'Show commands', 'Available commands']
    });

    console.log('Voice commands initialized');
  };
}
