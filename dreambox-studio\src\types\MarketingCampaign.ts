/**
 * Marketing campaign interface
 */
export interface MarketingCampaign {
  id: number;
  mc_company_id: number; // Changed from company_id
  agent_id: number | null;
  initiated_by: number | null;
  name: string;
  description: string | null;
  start_date: string | null;
  end_date: string | null;
  status: string;
  target_audience: Record<string, unknown> | null;
  channels: Record<string, unknown> | null;
  metrics: Record<string, unknown> | null;
  created_at: string;
  updated_at: string;
}

/**
 * Marketing campaign filter interface
 */
export interface MarketingCampaignFilter {
  mc_company_id?: number; // Changed from company_id
  status?: string;
  search?: string;
}

/**
 * Marketing campaign pagination interface
 */
export interface MarketingCampaignPagination {
  page: number;
  rowsPerPage: number;
  rowsNumber: number;
  sortBy?: string;
  descending?: boolean;
}
