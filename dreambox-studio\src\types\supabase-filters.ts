import type { Database } from './supabase';

// Export SavedFilter type for use in components
export type SavedFilter = DatabaseWithFilters['public']['Tables']['user_filters']['Row'];

// Extend the Database interface to include user_filters table
export interface DatabaseWithFilters extends Database {
  public: {
    Tables: {
      user_filters: {
        Row: {
          id: number;
          user_id: number;
          name: string;
          description: string | null;
          table_name: string;
          context: string;
          configuration: {
            columns: Array<{
              id: string;
              visible?: boolean;
              section?: 'main' | 'expanded';
              order?: number;
              filterValue?: string | null;
            }>;
            additionalFilters?: Record<string, unknown>;
          };
          is_default: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: number;
          user_id: number;
          name: string;
          description?: string | null;
          table_name: string;
          context: string;
          configuration: {
            columns: Array<{
              id: string;
              visible?: boolean;
              section?: 'main' | 'expanded';
              order?: number;
              filterValue?: string | null;
            }>;
            additionalFilters?: Record<string, unknown>;
          };
          is_default?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: number;
          user_id?: number;
          name?: string;
          description?: string | null;
          table_name?: string;
          context?: string;
          configuration?: {
            columns: Array<{
              id: string;
              visible?: boolean;
              section?: 'main' | 'expanded';
              order?: number;
              filterValue?: string | null;
            }>;
            additionalFilters?: Record<string, unknown>;
          };
          is_default?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "user_filters_user_id_fkey";
            columns: ["user_id"];
            referencedRelation: "app_users";
            referencedColumns: ["id"];
          }
        ];
      };
    } & Database['public']['Tables'];
    Views: Database['public']['Views'];
    Functions: Database['public']['Functions'];
    Enums: Database['public']['Enums'];
    CompositeTypes: Database['public']['CompositeTypes'];
  };
}
