import { ref } from 'vue';
import { supabase } from 'src/boot/supabase';

// Type for Supabase RPC functions
// eslint-disable-next-line @typescript-eslint/no-explicit-any
type CustomRpcFunction = any;

export interface ElementTypeOrder {
  elementTypeId: number;
  order: number;
}

export interface OrderedElementType {
  element_type_id: number;
  name: string;
  description: string | null;
  is_array: boolean;
  is_required: boolean;
  order_index: number;
}

/**
 * Service for managing element type ordering
 */
export const useElementTypeOrderService = () => {
  const loading = ref(false);
  const error = ref<string | null>(null);

  /**
   * Reorder element types in a template
   */
  const reorderElementTypes = async (templateId: number, typeOrders: ElementTypeOrder[]): Promise<boolean> => {
    try {
      loading.value = true;
      error.value = null;

      console.log('Reordering element types:', { templateId, typeOrders });

      // Use our SQL function to update all orders at once
      // Cast to CustomRpcFunction to avoid TypeScript errors
      const { error: err } = await (supabase.rpc as CustomRpcFunction)('admin_reorder_element_types', {
        p_template_id: templateId,
        p_type_orders: typeOrders
      });

      if (err) {
        console.error('Error reordering element types:', err);
        throw err;
      }

      return true;
    } catch (err) {
      console.error('Error reordering element types:', err);
      error.value = err instanceof Error ? err.message : 'Failed to reorder element types';
      return false;
    } finally {
      loading.value = false;
    }
  };

  /**
   * Get ordered element types for a template
   */
  const getOrderedElementTypes = async (templateId: number): Promise<OrderedElementType[]> => {
    try {
      loading.value = true;
      error.value = null;

      // Use the get_ordered_element_types function
      // Cast to CustomRpcFunction to avoid TypeScript errors
      const { data, error: err } = await (supabase.rpc as CustomRpcFunction)('get_ordered_element_types', {
        p_template_id: templateId
      });

      if (err) {
        console.error('Error getting ordered element types:', err);
        throw err;
      }

      // Cast the data to the expected type
      return (data || []) as OrderedElementType[];
    } catch (err) {
      console.error('Error getting ordered element types:', err);
      error.value = err instanceof Error ? err.message : 'Failed to get ordered element types';
      return [];
    } finally {
      loading.value = false;
    }
  };

  return {
    loading,
    error,
    reorderElementTypes,
    getOrderedElementTypes
  };
};
