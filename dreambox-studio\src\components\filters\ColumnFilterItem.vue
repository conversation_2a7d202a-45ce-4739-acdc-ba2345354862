<template>
  <q-item
    v-bind="$attrs"
    class="column-filter-item q-py-sm"
    :data-column-id="column.id"
    style="padding-left: 4px !important; padding-right: 12px !important;"
  >
    <div class="filter-table-layout">
      <div class="filter-label-column">
        <div class="row items-center no-wrap">
          <q-btn
            flat
            round
            dense
            :icon="isVisible ? 'visibility' : 'visibility_off'"
            :color="isVisible ? 'primary' : 'grey'"
            @click="toggleVisibility"
            class="visibility-toggle"
            style="margin: 0 4px 0 0 !important; padding: 4px !important;"
          >
            <q-tooltip>{{ isVisible ? t('common.hideColumn') : t('common.showColumn') }}</q-tooltip>
          </q-btn>
          <div class="column-name">{{ column.name }}</div>
        </div>
      </div>

      <div class="filter-control-column">
        <!-- Text filter -->
        <q-input
          v-if="column.filterType === 'text' || !column.filterType"
          v-model="filterValue"
          dense
          outlined
          :placeholder="t('app.filter') + '...'"
          @update:model-value="updateFilter"
          class="filter-input"
          @click.stop
        />

      <!-- Number filter -->
      <q-input
        v-else-if="column.filterType === 'number'"
        v-model="filterValue"
        dense
        outlined
        :placeholder="t('app.filter') + '...'"
        type="number"
        @update:model-value="updateFilter"
        class="filter-input"
        @click.stop
      />

      <!-- Date filter with range support -->
      <q-input
        v-else-if="column.filterType === 'date'"
        v-model="dateDisplay"
        dense
        outlined
        :placeholder="t('app.filter') + '...'"
        readonly
        class="filter-input"
        @click.stop
      >
        <template v-slot:append>
          <q-icon name="event" class="cursor-pointer">
            <q-popup-proxy cover transition-show="scale" transition-hide="scale">
              <div class="column">
                <q-date
                  v-model="dateRange"
                  range
                  multiple
                  mask="YYYY-MM-DD"
                  @update:model-value="onDateRangeChanged"
                />
                <div class="row justify-end q-mt-sm q-mr-sm">
                  <q-btn
                    color="primary"
                    label="Clear"
                    dense
                    flat
                    @click="clearDateFilterInPopup"
                  />
                </div>
              </div>
            </q-popup-proxy>
          </q-icon>
          <q-icon
            v-if="hasDateRangeValues()"
            name="close"
            class="cursor-pointer q-ml-xs"
            @click.stop="clearDateFilter"
          />
        </template>
      </q-input>

      <!-- Boolean filter -->
      <q-select
        v-else-if="column.filterType === 'boolean'"
        v-model="filterValue"
        dense
        outlined
        placeholder="Filter..."
        :options="[
          { label: 'Yes', value: 'true' },
          { label: 'No', value: 'false' }
        ]"
        emit-value
        map-options
        clearable
        @update:model-value="updateFilter"
        class="filter-input"
        @click.stop
      />

      <!-- Select filter -->
      <q-select
        v-else-if="column.filterType === 'select' && column.filterOptions"
        v-model="filterValue"
        dense
        outlined
        placeholder="Filter..."
        :options="column.filterOptions"
        emit-value
        map-options
        clearable
        @update:model-value="updateFilter"
        class="filter-input"
        @click.stop
      />

      <!-- Multiselect filter -->
      <q-select
        v-else-if="column.filterType === 'multiselect' && column.filterOptions"
        v-model="filterValue"
        dense
        outlined
        placeholder="Filter..."
        :options="column.filterOptions"
        emit-value
        map-options
        clearable
        multiple
        display-value-sanitize
        :display-value="getMultiselectDisplayValue()"
        @update:model-value="updateMultiselectFilter"
        class="filter-input"
        @click.stop
        behavior="menu"
      >
        <!-- Custom selected slot to show only count or placeholder -->
        <template v-slot:selected>
          <div v-if="filterValue && filterValue.length > 0" class="ellipsis">
            {{ getMultiselectDisplayValue() }}
          </div>
          <div v-else class="text-grey">
            Filter...
          </div>
        </template>
      </q-select>
      </div>
    </div>
  </q-item>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, computed } from 'vue';
import type { ColumnDefinition } from 'src/stores/tableFilterStore';
import { notify } from 'src/boot/notifications';
import { useI18n } from 'vue-i18n';

const props = defineProps<{
  column: ColumnDefinition;
}>();

const emit = defineEmits<{
  (e: 'toggle-visibility', columnId: string, visible: boolean): void;
  (e: 'update-filter', columnId: string, value: string | number | null): void;
}>();

// Initialize i18n
const { t } = useI18n();

// Local state with two-way binding
const isVisible = ref(props.column.visible);
const filterValue = ref(props.column.filterValue || null);

// Parse JSON for multiselect values if needed
if (props.column.filterType === 'multiselect' && props.column.filterValue) {
  try {
    filterValue.value = JSON.parse(props.column.filterValue);
  } catch (e) {
    console.error('Error parsing multiselect values:', e);
    filterValue.value = null;
  }
}

// Date range state with Quasar's expected format for range and multiple
type DateRange = { from: string | null; to: string | null };
type DateRangeValue = DateRange | DateRange[];

const dateRange = ref<DateRangeValue>({
  from: null,
  to: null
});

// Computed property for displaying the date range in the input field
const dateDisplay = computed(() => {
  // For multiple ranges, we'll just show a summary
  if (Array.isArray(dateRange.value)) {
    const count = dateRange.value.length;
    return count > 0 ? `${count} date range${count > 1 ? 's' : ''} selected` : '';
  }

  // Handle single range
  if (!dateRange.value.from && !dateRange.value.to) {
    return '';
  }

  if (dateRange.value.from && dateRange.value.to) {
    return `${dateRange.value.from} to ${dateRange.value.to}`;
  }

  if (dateRange.value.from) {
    return `From ${dateRange.value.from}`;
  }

  if (dateRange.value.to) {
    return `Until ${dateRange.value.to}`;
  }

  return '';
});

// Helper function to check if there are any date range values
function hasDateRangeValues(): boolean {
  if (Array.isArray(dateRange.value)) {
    return dateRange.value.length > 0;
  } else {
    return !!dateRange.value.from || !!dateRange.value.to;
  }
}

// Helper function to set a single range value safely
function setSingleRangeValue(from: string | null, to: string | null) {
  // Ensure we're working with a single range object
  if (Array.isArray(dateRange.value)) {
    dateRange.value = { from, to };
  } else {
    dateRange.value.from = from;
    dateRange.value.to = to;
  }
}

// Initialize date range if there's an existing filter value
onMounted(() => {
  if (props.column.filterType === 'date' && props.column.filterValue) {
    try {
      const rangeValue = JSON.parse(props.column.filterValue as unknown as string);

      // Handle array of ranges (multiple ranges)
      if (Array.isArray(rangeValue)) {
        dateRange.value = rangeValue as DateRange[];
      }
      // Handle single range object
      else if (rangeValue && typeof rangeValue === 'object') {
        setSingleRangeValue(rangeValue.from || null, rangeValue.to || null);
      }
      // Handle legacy single date string
      else if (typeof props.column.filterValue === 'string') {
        setSingleRangeValue(props.column.filterValue, null);
      }
    } catch {
      // If it's not JSON, it might be a single date string
      if (typeof props.column.filterValue === 'string') {
        setSingleRangeValue(props.column.filterValue, null);
      }
    }
  }
});

// Watch for external changes to column
watch(() => props.column.visible, (newValue) => {
  isVisible.value = newValue;
});

watch(() => props.column.filterValue, (newValue) => {
  if (props.column.filterType === 'multiselect' && newValue) {
    try {
      filterValue.value = JSON.parse(newValue);
    } catch (e) {
      console.error('Error parsing multiselect values in watcher:', e);
      filterValue.value = null;
    }
  } else {
    filterValue.value = newValue || null;
  }
});

// Emit events when values change
function toggleVisibility() {
  isVisible.value = !isVisible.value;
  emit('toggle-visibility', props.column.id, isVisible.value);
}

function updateFilter(value: string | number | null) {
  emit('update-filter', props.column.id, value);
}

// Helper function for multiselect display

function getMultiselectDisplayValue(): string {
  if (!filterValue.value || !Array.isArray(filterValue.value) || filterValue.value.length === 0) {
    return '';
  }

  // For status filter specifically
  if (props.column.id === 'status') {
    // If only one item is selected, show its label
    if (filterValue.value.length === 1) {
      // Find the option with this value to get its label
      const selectedValue = filterValue.value[0];
      const selectedOption = props.column.filterOptions?.find(opt => opt.value === selectedValue);
      return selectedOption?.label || String(selectedValue);
    }
    // If multiple items are selected, show the count
    return `${filterValue.value.length} selected`;
  }

  // For other multiselect filters, you can decide whether to show the values or just the count
  // For now, we'll show the count for all multiselect filters for consistency
  if (filterValue.value.length === 1) {
    const selectedValue = filterValue.value[0];
    const selectedOption = props.column.filterOptions?.find(opt => opt.value === selectedValue);
    return selectedOption?.label || String(selectedValue);
  }
  return `${filterValue.value.length} selected`;
}

// Handle multiselect filter updates
function updateMultiselectFilter(values: (string | number)[] | null) {
  console.log(`Multiselect filter updated for column ${props.column.id} with values:`, values);

  if (!values || values.length === 0) {
    // If no values selected, clear the filter
    console.log(`No values selected for ${props.column.id}, clearing filter`);

    // Special handling for status filter
    if (props.column.id === 'status') {
      console.log('STATUS FILTER CLEARED - setting to null');

      // DIRECT FIX: When status filter is cleared, also clear it from localStorage
      // This ensures it doesn't persist after page reload
      const lastFilterJson = localStorage.getItem('lastTemplateFilter');
      if (lastFilterJson) {
        try {
          const lastFilter = JSON.parse(lastFilterJson);
          if ('status' in lastFilter) {
            delete lastFilter.status;
            localStorage.setItem('lastTemplateFilter', JSON.stringify(lastFilter));
            console.log('STATUS FILTER removed from localStorage');
          }
        } catch (err) {
          console.error('Error updating localStorage:', err);
        }
      }

      // Dispatch a special event just for status filter clearing
      // This ensures the template browser knows to completely remove the status filter
      document.dispatchEvent(new CustomEvent('status-filter-cleared', {
        detail: { columnId: props.column.id }
      }));
    }

    emit('update-filter', props.column.id, null);
  } else {
    // Convert array to JSON string for storage
    const jsonValue = JSON.stringify(values);
    console.log(`Storing multiselect values for ${props.column.id} as JSON:`, jsonValue);

    // For status filter, add extra logging
    if (props.column.id === 'status') {
      console.log('STATUS FILTER SET TO:', jsonValue);
    }

    emit('update-filter', props.column.id, jsonValue);
  }
}

// Date range handlers
function onDateRangeChanged() {
  // Create a JSON string with the date range(s)
  console.log('Date range changed:', dateRange.value);
  const rangeValue = JSON.stringify(dateRange.value);
  console.log('Stringified range value:', rangeValue);

  // Update the filter value
  filterValue.value = rangeValue;
  emit('update-filter', props.column.id, rangeValue);
}

// Clear date filter (from the close icon)
function clearDateFilter() {
  console.log('Clearing date filter');

  // Reset to a single empty range
  dateRange.value = {
    from: null,
    to: null
  };

  // Clear the filter value and emit the change
  filterValue.value = null;
  emit('update-filter', props.column.id, null);

  console.log('Date range after clear:', dateRange.value);
}

// Clear date filter from within the popup
function clearDateFilterInPopup() {
  console.log('Clearing date filter from popup');

  // Reset to a single empty range
  dateRange.value = {
    from: null,
    to: null
  };

  // Clear the filter value and emit the change
  filterValue.value = null;
  emit('update-filter', props.column.id, null);

  // Show a notification
  void notify({
    color: 'primary',
    message: 'Date filter cleared',
    icon: 'event_busy',
    timeout: 1500
  });
}
</script>

<style lang="scss" scoped>
.column-filter-item {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  width: 100%;

  &:last-child {
    border-bottom: none;
  }

  .filter-table-layout {
    display: flex;
    width: 100%;
    align-items: center;
    padding-right: 12px !important; /* Add right padding from filter to border */
    padding-left: 4px !important; /* Reduce left padding from border to eye icon */
  }

  .filter-label-column {
    width: 140px;
    min-width: 140px;
    padding-right: 8px;

    .visibility-toggle {
      min-width: 24px;
      padding: 4px;
      margin-left: 0 !important; /* Reduce space before the eye icon */
      margin-right: 4px !important; /* Consistent spacing after the icon */
    }

    .column-name {
      font-weight: 500;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .filter-control-column {
    flex: 1;
    min-width: 0; /* Important for flex items to shrink properly */
  }

  .filter-input {
    width: 100%;
    cursor: pointer;

    :deep(.q-field__control) {
      cursor: pointer;
    }

    :deep(.q-field__native) {
      cursor: pointer;
    }
  }

  /* Removed date-range-filter class */

  // Mobile adjustments
  @media (max-width: 599px) {
    .filter-label-column {
      width: 110px;
      min-width: 110px;
    }

    :deep(.q-field__control) {
      padding: 0 4px;
    }

    :deep(.q-field__marginal) {
      height: 32px;
      width: 28px;
    }

    :deep(.q-field__native, .q-field__input) {
      padding: 0;
      font-size: 13px;
    }

    :deep(.q-field__label) {
      font-size: 13px;
      top: 10px;
    }
  }
}

.q-dark .column-filter-item {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}
</style>
