# Database Setup for Dreambox Studio

This document provides instructions on how to set up the database for the Dreambox Studio application.

## Prerequisites

1. Supabase project with the following tables:
   - `allowed_emails`
   - `app_users`
   - `companies`
   - `roles`
   - `companies_users`
   - `companies_users_roles`

2. Environment variables:
   - `VITE_SUPABASE_URL`
   - `VITE_SUPABASE_ANON_KEY`
   - `VITE_SUPABASE_SERVICE_KEY` (for admin operations)

## Setup Steps

### 1. Run the Database Setup Scripts

You can run the database setup scripts using the provided JavaScript scripts:

```bash
# Navigate to the scripts directory
cd scripts

# Run the setup-database script
bun setup-database.js
```

This will run the following SQL scripts in order:
1. `populate-allowed-emails.sql` - Populates the allowed_emails table with test users
2. `populate-app-users.sql` - Populates the app_users table with users from auth.users
3. `setup-multiple-roles.sql` - Sets up multiple roles for users
4. `create-user-permissions-view.sql` - Creates the user_permissions_view
5. `enable-rls-on-allowed-emails.sql` - Enables RLS on the allowed_emails table
6. `check-user-setup.sql` - Checks the user setup

### 2. Check User Setup

You can check if a user is properly set up using the check-user script:

```bash
# Check a specific user
bun check-user.js <EMAIL>
```

This will check if the user exists in:
- `allowed_emails` table
- `auth.users` table
- `app_users` table
- `companies_users` table
- `companies_users_roles` table
- `user_permissions_view` view

### 3. Create App User Manually

If a user exists in `auth.users` but not in `app_users`, you can create the app_user record manually:

```bash
# Create app_user for a specific user
bun create-app-user.js <EMAIL>
```

This will:
1. Check if the user exists in `allowed_emails`
2. Check if the user exists in `auth.users`
3. Create an `app_users` record
4. Create a `companies_users` record
5. Create a `companies_users_roles` record

## Troubleshooting

### User Can't Log In

If a user can't log in, check the following:

1. Make sure the user's email is in the `allowed_emails` table:
   ```sql
   SELECT * FROM allowed_emails WHERE email = '<EMAIL>';
   ```

2. Make sure the user exists in `auth.users`:
   ```sql
   SELECT * FROM auth.users WHERE email = '<EMAIL>';
   ```

3. Make sure the user exists in `app_users`:
   ```sql
   SELECT * FROM app_users WHERE user_id = 'auth_user_id';
   ```

4. Make sure the user has company and role associations:
   ```sql
   SELECT * FROM companies_users WHERE app_user_id = 'app_user_id';
   SELECT * FROM companies_users_roles WHERE app_user_id = 'app_user_id';
   ```

5. Check the user_permissions_view:
   ```sql
   SELECT * FROM user_permissions_view WHERE user_id = 'auth_user_id';
   ```

### RLS Policies

Make sure the RLS policies are properly set up:

```sql
-- Check RLS policies on allowed_emails
SELECT * FROM pg_policies WHERE tablename = 'allowed_emails';
```

### Database Structure

Make sure the database structure is correct:

```sql
-- Check tables
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';

-- Check views
SELECT table_name FROM information_schema.views WHERE table_schema = 'public';

-- Check columns in a table
SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'app_users';
```

## Additional Resources

- [Supabase Documentation](https://supabase.io/docs)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Row Level Security (RLS) Documentation](https://www.postgresql.org/docs/current/ddl-rowsecurity.html)
