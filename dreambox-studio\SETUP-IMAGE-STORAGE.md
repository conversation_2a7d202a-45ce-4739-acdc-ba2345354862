# Setting Up Image Storage

This document provides instructions for setting up image storage using Supabase Storage.

## 1. Create the Storage Bucket

Run the setup script to create the storage bucket:

```bash
cd dreambox-studio/scripts
bun setup-storage-bucket.js
```

This script will:
- Create an 'images' bucket if it doesn't exist
- Set up appropriate permissions
- Test the bucket by uploading and deleting a sample image

## 2. Using the Image Storage Service

The `ImageStorageService` provides methods for working with images:

```typescript
import { imageStorage } from '../services/image-storage';

// Upload an image
const file = event.target.files[0];
const imageUrl = await imageStorage.uploadImage(file, 'templates');

// Save the image record in the database
const imageId = await imageStorage.saveImageRecord(
  imageUrl,
  templateId,
  companyId,
  'My Image Title',
  'Optional description',
  eventId
);

// Get images for a company
const images = await imageStorage.getCompanyImages(companyId);

// Update image status
await imageStorage.updateImageStatus(imageId, 'approved', userId);

// Delete an image
await imageStorage.deleteImage(imageUrl);
```

## 3. Image Workflow

The typical workflow for images is:

1. **Upload**: Designer uploads an image
2. **Draft**: Image is saved with 'draft' status
3. **Review**: Admin reviews the image
4. **Approval**: Admin approves the image, changing status to 'approved'
5. **Use**: Approved images can be used in products

## 4. Migrating to S3 for Production

For production, you can migrate to Amazon S3:

1. Create an S3 bucket
2. Update the `ImageStorageService` to use S3 instead of Supabase Storage
3. Migrate existing images from Supabase Storage to S3

The `ImageStorageService` is designed with an abstraction layer that makes it easy to switch storage providers without changing the rest of the application.

## 5. Storage Policies

The Supabase Storage bucket has the following policies:

- **Public Access**: Anyone can view images (but not list or modify them)
- **Upload**: Only authenticated users can upload images
- **Delete**: Only the user who uploaded an image or an admin can delete it

## 6. Image Sizes and Optimization

Consider implementing:

- Image resizing for different use cases (thumbnails, previews, full-size)
- Image optimization to reduce file sizes
- Caching strategies for frequently accessed images

## 7. Troubleshooting

If you encounter issues:

1. Check the browser console for errors
2. Verify that the storage bucket exists
3. Check the permissions on the storage bucket
4. Try uploading a small test image manually through the Supabase dashboard
