import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import chalk from 'chalk';
// Test comment

// Get the directory name of the current module
const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Path to the database structure JSON file
const dbStructurePath = path.join(__dirname, 'db-structure.json');
// Path to the output markdown file
const outputMarkdownPath = path.join(__dirname, 'database-structure.md');

/**
 * Generates a Mermaid entity-relationship diagram from the database structure
 * @param {Object} dbStructure - The database structure
 * @returns {string} The Mermaid diagram code
 */
function generateERDiagram(dbStructure) {
  const { tables, columns, constraints } = dbStructure;

  // Start the ER diagram
  let mermaidCode = 'erDiagram\n';

  // Track processed tables to avoid duplicates
  const processedTables = new Set();

  // Process each table
  tables.forEach(table => {
    const tableName = table.table_name;

    // Skip if already processed
    if (processedTables.has(tableName)) return;
    processedTables.add(tableName);

    // Add table to the diagram (just the table name for ER diagram)
    mermaidCode += `    ${tableName} {\n`;

    // Get columns for this table
    const tableColumns = columns.filter(col => col.table_name === tableName);

    tableColumns.forEach(column => {
      // Format column type
      let columnType = column.data_type;
      if (columnType.includes('character varying')) {
        columnType = 'varchar';
      } else if (columnType.includes('timestamp')) {
        columnType = 'timestamp';
      }

      // Check if column is a primary key
      // We'll mark it as PK if it's part of a primary key constraint
      const isPrimaryKey = constraints.some(con =>
        con.constraint_type === 'PRIMARY KEY' &&
        con.table_name === tableName &&
        con.column_name === column.column_name
      );
      const pkMarker = isPrimaryKey ? ' PK' : '';

      // Add column to the diagram
      mermaidCode += `        ${columnType} ${column.column_name}${pkMarker}\n`;
    });

    mermaidCode += '    }\n';
  });

  // Process relationships (foreign keys)
  const processedRelationships = new Set();

  constraints.forEach(constraint => {
    if (constraint.constraint_type === 'FOREIGN KEY') {
      const sourceTable = constraint.table_name;
      const targetTable = constraint.foreign_table_name;

      // Skip if target table is null or undefined
      if (!targetTable) return;

      const relationshipKey = `${sourceTable}:${targetTable}:${constraint.column_name}`;

      // Skip if already processed
      if (processedRelationships.has(relationshipKey)) return;
      processedRelationships.add(relationshipKey);

      // Add relationship to the diagram with correct Mermaid syntax
      // In ER diagrams, the relationship direction is from the table with the foreign key
      // to the table being referenced (the one with the primary key)
      // Format: TableWithForeignKey }|..|| TableWithPrimaryKey : relationship_label
      mermaidCode += `    ${sourceTable} }o--|| ${targetTable} : "${constraint.column_name}"\n`;
    }
  });

  return mermaidCode;
}

/**
 * Generates a Mermaid class diagram showing table structures
 * @param {Object} dbStructure - The database structure
 * @returns {string} The Mermaid diagram code
 */
function generateClassDiagram(dbStructure) {
  const { tables, columns, constraints } = dbStructure;

  // Start the class diagram
  let mermaidCode = 'classDiagram\n';

  // Process each table
  tables.forEach(table => {
    const tableName = table.table_name;

    // Get columns for this table
    const tableColumns = columns.filter(col => col.table_name === tableName);

    // Add table and its columns to the diagram
    mermaidCode += `    class ${tableName} {\n`;

    tableColumns.forEach(column => {
      // Format column type
      let columnType = column.data_type;
      if (columnType.includes('character varying')) {
        columnType = 'varchar';
      } else if (columnType.includes('timestamp')) {
        columnType = 'timestamp';
      }

      // Check if column is nullable
      const nullable = column.is_nullable === 'YES' ? '' : ' [NOT NULL]';

      // Check if column has a default value
      const defaultValue = column.column_default
        ? ` = ${column.column_default}`
        : '';

      // Add column to the diagram
      mermaidCode += `        ${columnType} ${column.column_name}${nullable}${defaultValue}\n`;
    });

    mermaidCode += '    }\n';
  });

  // Process relationships (foreign keys)
  constraints.forEach(constraint => {
    if (constraint.constraint_type === 'FOREIGN KEY') {
      const sourceTable = constraint.table_name;
      const targetTable = constraint.foreign_table_name;

      // Skip if target table is null or undefined
      if (!targetTable) return;

      // Add relationship to the diagram
      mermaidCode += `    ${sourceTable} --> ${targetTable} : ${constraint.column_name}\n`;
    }
  });

  return mermaidCode;
}

/**
 * Generates a section for views
 * @param {Object} dbStructure - The database structure
 * @returns {string} The markdown for views
 */
function generateViewsSection(dbStructure) {
  const { views, columns } = dbStructure;

  if (!views || views.length === 0) {
    return '';
  }

  let markdown = `## Views\n\n`;

  views.forEach(view => {
    const viewName = view.table_name;
    markdown += `### ${viewName}\n\n`;

    // Add view definition if available
    if (view.view_definition) {
      markdown += `#### Definition\n\n`;
      markdown += '```sql\n';
      markdown += view.view_definition;
      markdown += '\n```\n\n';
    }

    // Get columns for this view
    const viewColumns = columns.filter(col => col.table_name === viewName);

    // Create table of columns
    markdown += `#### Columns\n\n`;
    markdown += `| Column | Type | Nullable | Default |\n`;
    markdown += `|--------|------|----------|--------|\n`;

    viewColumns.forEach(column => {
      markdown += `| ${column.column_name} | ${column.data_type} | ${column.is_nullable} | ${column.column_default || ''} |\n`;
    });

    markdown += '\n';
  });

  return markdown;
}

/**
 * Generates a section for functions
 * @param {Object} dbStructure - The database structure
 * @returns {string} The markdown for functions
 */
function generateFunctionsSection(dbStructure) {
  const { functions } = dbStructure;

  if (!functions || functions.length === 0) {
    return '';
  }

  let markdown = `## Functions\n\n`;

  markdown += `| Name | Type | Return Type | Language |\n`;
  markdown += `|------|------|-------------|----------|\n`;

  functions.forEach(func => {
    markdown += `| ${func.routine_name} | ${func.routine_type} | ${func.data_type || 'void'} | ${func.external_language} |\n`;
  });

  markdown += '\n';

  return markdown;
}

/**
 * Generates a detailed markdown documentation of the database structure
 * @param {Object} dbStructure - The database structure
 * @returns {string} The markdown documentation
 */
function generateMarkdownDocumentation(dbStructure) {
  const { tables, columns, constraints, policies, metadata } = dbStructure;

  let markdown = `# Database Structure Documentation\n\n`;

  // Add timestamp and database info
  markdown += `*Generated on: ${new Date().toLocaleString()}*\n\n`;
  if (metadata) {
    markdown += `*Database: ${metadata.supabase_url}*\n\n`;
  }

  // Note: Mermaid diagrams removed as they're not needed
  // DBML diagrams are used instead
  markdown += `## Database Diagrams\n\n`;
  markdown += 'Database diagrams are available in DBML format.\n\n';

  // Add detailed table information
  markdown += `## Tables\n\n`;

  tables.forEach(table => {
    const tableName = table.table_name;
    markdown += `### ${tableName}\n\n`;

    // Get columns for this table
    const tableColumns = columns.filter(col => col.table_name === tableName);

    // Create table of columns
    markdown += `#### Columns\n\n`;
    markdown += `| Column | Type | Nullable | Default |\n`;
    markdown += `|--------|------|----------|--------|\n`;

    tableColumns.forEach(column => {
      markdown += `| ${column.column_name} | ${column.data_type} | ${column.is_nullable} | ${column.column_default || ''} |\n`;
    });

    markdown += '\n';

    // Add constraints information
    const tableConstraints = constraints.filter(con => con.table_name === tableName);

    if (tableConstraints.length > 0) {
      markdown += `#### Constraints\n\n`;
      markdown += `| Name | Type | Column | References |\n`;
      markdown += `|------|------|--------|------------|\n`;

      tableConstraints.forEach(constraint => {
        const references = constraint.foreign_table_name
          ? `${constraint.foreign_table_name}(${constraint.foreign_column_name})`
          : '';

        markdown += `| ${constraint.constraint_name} | ${constraint.constraint_type} | ${constraint.column_name} | ${references} |\n`;
      });

      markdown += '\n';
    }

    // Add RLS policies information
    const tablePolicies = policies ? policies.filter(policy => policy.tablename === tableName) : [];

    if (tablePolicies.length > 0) {
      markdown += `#### Row Level Security Policies\n\n`;
      markdown += `| Name | Command | Roles | Using | With Check |\n`;
      markdown += `|------|---------|-------|-------|------------|\n`;

      tablePolicies.forEach(policy => {
        markdown += `| ${policy.policyname} | ${policy.cmd} | ${policy.roles} | ${policy.qual || ''} | ${policy.with_check || ''} |\n`;
      });

      markdown += '\n';
    }
  });

  // Add views section
  markdown += generateViewsSection(dbStructure);

  // Add functions section
  markdown += generateFunctionsSection(dbStructure);

  return markdown;
}

/**
 * Main function
 */
function main() {
  try {
    // Check if the database structure file exists
    if (!fs.existsSync(dbStructurePath)) {
      console.error(chalk.red(`Error: Database structure file not found at ${dbStructurePath}`));
      console.error(chalk.red('Please run "bun start" first to fetch the database structure.'));
      process.exit(1);
    }

    console.log(chalk.blue('Generating documentation...'));

    // Read the database structure
    const dbStructure = JSON.parse(fs.readFileSync(dbStructurePath, 'utf8'));

    // Generate markdown documentation
    const markdown = generateMarkdownDocumentation(dbStructure);

    // Write to file
    fs.writeFileSync(outputMarkdownPath, markdown);

    console.log(chalk.green(`Documentation generated successfully at ${outputMarkdownPath}`));
  } catch (error) {
    console.error(chalk.red(`Error generating documentation: ${error.message}`));
    process.exit(1);
  }
}

// Run the main function
main();
