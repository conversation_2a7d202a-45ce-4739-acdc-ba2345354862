# Step 7: UI Components for Server Management

This document outlines the implementation of UI components for managing SwarmUI servers, including starting and stopping servers, and monitoring server status.

## 7.1 Create Component Directory Structure

First, create the directory structure for the UI components:

```
src/
└── components/
    └── serverManagement/
        ├── ServerControls.vue
        ├── ServerStatus.vue
        ├── ServerUsageStats.vue
        └── ServerConfigSelector.vue
```

## 7.2 Implement Server Controls Component

Create the Server Controls component in `src/components/serverManagement/ServerControls.vue`:

```vue
<template>
  <div class="server-controls q-pa-md">
    <q-card class="server-card">
      <q-card-section>
        <div class="text-h6">Image Generation Server</div>
        <div class="text-subtitle2">
          Start a GPU server to generate images for your templates
        </div>
      </q-card-section>

      <q-card-section v-if="!serverRunning">
        <server-config-selector v-model="selectedConfig" />
      </q-card-section>

      <q-card-section>
        <server-status :status="serverStatus" :server="activeServer" />
      </q-card-section>

      <q-card-actions align="right">
        <q-btn
          v-if="!serverRunning"
          color="primary"
          :loading="starting"
          :disable="starting"
          @click="startServer"
          icon="mdi-server-plus"
          label="Start Server"
        />
        
        <q-btn
          v-else
          color="negative"
          :loading="stopping"
          :disable="stopping"
          @click="stopServer"
          icon="mdi-server-off"
          label="Stop Server"
        />
      </q-card-actions>
    </q-card>

    <server-usage-stats v-if="showUsageStats" class="q-mt-md" />

    <!-- Server Starting Dialog -->
    <q-dialog v-model="showServerStarting" persistent>
      <q-card style="min-width: 350px">
        <q-card-section>
          <div class="text-h6">Starting Server</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <p>The image generation server is starting up. This may take 1-2 minutes.</p>
          <q-linear-progress indeterminate class="q-mt-md" />
        </q-card-section>

        <q-card-section class="text-caption">
          You will be charged for GPU usage while the server is running.
          The server will automatically shut down after 30 minutes of inactivity.
        </q-card-section>
      </q-card>
    </q-dialog>

    <!-- Server Stopping Dialog -->
    <q-dialog v-model="showServerStopping" persistent>
      <q-card style="min-width: 350px">
        <q-card-section>
          <div class="text-h6">Stopping Server</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <p>The image generation server is shutting down. This may take a moment.</p>
          <q-linear-progress indeterminate class="q-mt-md" />
        </q-card-section>
      </q-card>
    </q-dialog>
  </div>
</template>

<script>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { useQuasar } from 'quasar';
import { serverManagementService } from '../../services/serverManagement';
import ServerConfigSelector from './ServerConfigSelector.vue';
import ServerStatus from './ServerStatus.vue';
import ServerUsageStats from './ServerUsageStats.vue';

export default {
  name: 'ServerControls',
  
  components: {
    ServerConfigSelector,
    ServerStatus,
    ServerUsageStats
  },
  
  setup() {
    const $q = useQuasar();
    const activeServer = ref(null);
    const serverStatus = ref('unknown');
    const starting = ref(false);
    const stopping = ref(false);
    const showServerStarting = ref(false);
    const showServerStopping = ref(false);
    const selectedConfig = ref(null);
    const statusCheckInterval = ref(null);
    
    const serverRunning = computed(() => {
      return activeServer.value && 
             (serverStatus.value === 'running' || 
              serverStatus.value === 'starting');
    });
    
    const showUsageStats = computed(() => {
      return activeServer.value !== null;
    });
    
    async function checkServerStatus() {
      try {
        const server = await serverManagementService.getActiveServer();
        activeServer.value = server;
        serverStatus.value = server ? server.status : 'stopped';
        
        // If server was starting and is now running, close the dialog
        if (showServerStarting.value && serverStatus.value === 'running') {
          showServerStarting.value = false;
          starting.value = false;
          $q.notify({
            color: 'positive',
            message: 'Server is now running',
            icon: 'mdi-server-network'
          });
        }
        
        // If server was stopping and is now stopped, close the dialog
        if (showServerStopping.value && serverStatus.value === 'stopped') {
          showServerStopping.value = false;
          stopping.value = false;
          $q.notify({
            color: 'info',
            message: 'Server has been stopped',
            icon: 'mdi-server-off'
          });
        }
      } catch (error) {
        console.error('Error checking server status:', error);
      }
    }
    
    async function startServer() {
      try {
        starting.value = true;
        showServerStarting.value = true;
        
        const options = {
          configurationId: selectedConfig.value ? selectedConfig.value.id : undefined
        };
        
        const result = await serverManagementService.startServer(options);
        
        if (result.status === 'running') {
          // Server started immediately (was already running)
          showServerStarting.value = false;
          starting.value = false;
          await checkServerStatus();
        } else {
          // Server is starting, keep the dialog open
          // Status check interval will close it when ready
        }
      } catch (error) {
        console.error('Error starting server:', error);
        $q.notify({
          color: 'negative',
          message: 'Failed to start server: ' + error.message,
          icon: 'mdi-alert'
        });
        showServerStarting.value = false;
        starting.value = false;
      }
    }
    
    async function stopServer() {
      if (!activeServer.value) return;
      
      try {
        stopping.value = true;
        showServerStopping.value = true;
        
        const result = await serverManagementService.stopServer(activeServer.value.id);
        
        if (result.success) {
          // Server is stopping, keep the dialog open
          // Status check interval will close it when ready
        } else {
          $q.notify({
            color: 'warning',
            message: result.message,
            icon: 'mdi-alert'
          });
          showServerStopping.value = false;
          stopping.value = false;
        }
      } catch (error) {
        console.error('Error stopping server:', error);
        $q.notify({
          color: 'negative',
          message: 'Failed to stop server: ' + error.message,
          icon: 'mdi-alert'
        });
        showServerStopping.value = false;
        stopping.value = false;
      }
    }
    
    onMounted(async () => {
      await checkServerStatus();
      
      // Set up interval to check server status
      statusCheckInterval.value = setInterval(checkServerStatus, 5000);
      
      // Load server configurations
      try {
        const configs = await serverManagementService.getServerConfigurations();
        if (configs.length > 0) {
          // Find the default configuration
          const defaultConfig = configs.find(c => c.isDefault) || configs[0];
          selectedConfig.value = defaultConfig;
        }
      } catch (error) {
        console.error('Error loading server configurations:', error);
      }
    });
    
    onBeforeUnmount(() => {
      // Clear the status check interval
      if (statusCheckInterval.value) {
        clearInterval(statusCheckInterval.value);
      }
    });
    
    return {
      activeServer,
      serverStatus,
      serverRunning,
      starting,
      stopping,
      showServerStarting,
      showServerStopping,
      selectedConfig,
      showUsageStats,
      startServer,
      stopServer
    };
  }
};
</script>

<style scoped>
.server-card {
  max-width: 600px;
}
</style>
```

## 7.3 Implement Server Status Component

Create the Server Status component in `src/components/serverManagement/ServerStatus.vue`:

```vue
<template>
  <div class="server-status">
    <div class="row items-center">
      <div class="col-auto">
        <q-icon
          :name="statusIcon"
          :color="statusColor"
          size="2rem"
        />
      </div>
      <div class="col q-ml-md">
        <div class="text-subtitle1">Status: {{ statusText }}</div>
        <div v-if="server" class="text-caption">
          <div v-if="server.gpuType">GPU: {{ server.gpuType }}</div>
          <div v-if="server.startedAt">
            Started: {{ formatDateTime(server.startedAt) }}
          </div>
          <div v-if="server.lastActivity">
            Last activity: {{ formatDateTime(server.lastActivity) }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue';
import { date } from 'quasar';

export default {
  name: 'ServerStatus',
  
  props: {
    status: {
      type: String,
      required: true
    },
    server: {
      type: Object,
      default: null
    }
  },
  
  setup(props) {
    const statusText = computed(() => {
      switch (props.status) {
        case 'running':
          return 'Running';
        case 'starting':
          return 'Starting';
        case 'stopping':
          return 'Stopping';
        case 'stopped':
          return 'Stopped';
        case 'error':
          return 'Error';
        default:
          return 'Unknown';
      }
    });
    
    const statusColor = computed(() => {
      switch (props.status) {
        case 'running':
          return 'positive';
        case 'starting':
        case 'stopping':
          return 'warning';
        case 'stopped':
          return 'grey';
        case 'error':
          return 'negative';
        default:
          return 'grey';
      }
    });
    
    const statusIcon = computed(() => {
      switch (props.status) {
        case 'running':
          return 'mdi-server-network';
        case 'starting':
          return 'mdi-server-network-off';
        case 'stopping':
          return 'mdi-server-off';
        case 'stopped':
          return 'mdi-server-off';
        case 'error':
          return 'mdi-alert';
        default:
          return 'mdi-help-circle';
      }
    });
    
    function formatDateTime(dateString) {
      return date.formatDate(dateString, 'MMM D, YYYY h:mm A');
    }
    
    return {
      statusText,
      statusColor,
      statusIcon,
      formatDateTime
    };
  }
};
</script>
```

## 7.4 Implement Server Usage Stats Component

Create the Server Usage Stats component in `src/components/serverManagement/ServerUsageStats.vue`:

```vue
<template>
  <q-card class="server-usage-stats">
    <q-card-section>
      <div class="text-h6">Server Usage Statistics</div>
    </q-card-section>

    <q-card-section>
      <div v-if="loading" class="text-center">
        <q-spinner color="primary" size="2rem" />
        <div class="q-mt-sm">Loading usage data...</div>
      </div>
      
      <div v-else-if="error" class="text-negative">
        <q-icon name="mdi-alert" />
        {{ error }}
      </div>
      
      <div v-else>
        <div class="row q-col-gutter-md">
          <div class="col-12 col-md-4">
            <q-card flat bordered>
              <q-card-section>
                <div class="text-subtitle2">Total Usage Time</div>
                <div class="text-h5">{{ formatDuration(totalUsageSeconds) }}</div>
              </q-card-section>
            </q-card>
          </div>
          
          <div class="col-12 col-md-4">
            <q-card flat bordered>
              <q-card-section>
                <div class="text-subtitle2">Estimated Cost</div>
                <div class="text-h5">${{ totalCost.toFixed(2) }}</div>
              </q-card-section>
            </q-card>
          </div>
          
          <div class="col-12 col-md-4">
            <q-card flat bordered>
              <q-card-section>
                <div class="text-subtitle2">Images Generated</div>
                <div class="text-h5">{{ totalImages }}</div>
              </q-card-section>
            </q-card>
          </div>
        </div>
        
        <q-table
          class="q-mt-md"
          :rows="usageSessions"
          :columns="columns"
          row-key="id"
          :pagination="{ rowsPerPage: 5 }"
          dense
        >
          <template v-slot:body-cell-duration="props">
            <q-td :props="props">
              {{ formatDuration(props.row.duration_seconds) }}
            </q-td>
          </template>
          
          <template v-slot:body-cell-cost="props">
            <q-td :props="props">
              ${{ props.row.cost_estimate.toFixed(2) }}
            </q-td>
          </template>
        </q-table>
      </div>
    </q-card-section>
  </q-card>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import { supabase } from '../../boot/supabase';
import { useAuthStore } from '../../stores/auth';
import { formatDuration } from '../../services/serverManagement/utils';

export default {
  name: 'ServerUsageStats',
  
  setup() {
    const authStore = useAuthStore();
    const loading = ref(true);
    const error = ref(null);
    const usageSessions = ref([]);
    const totalImages = ref(0);
    
    const columns = [
      {
        name: 'start_time',
        required: true,
        label: 'Start Time',
        align: 'left',
        field: row => new Date(row.start_time).toLocaleString(),
        sortable: true
      },
      {
        name: 'end_time',
        required: true,
        label: 'End Time',
        align: 'left',
        field: row => row.end_time ? new Date(row.end_time).toLocaleString() : 'Active',
        sortable: true
      },
      {
        name: 'duration',
        required: true,
        label: 'Duration',
        align: 'left',
        sortable: true
      },
      {
        name: 'cost',
        required: true,
        label: 'Cost',
        align: 'right',
        sortable: true
      }
    ];
    
    const totalUsageSeconds = computed(() => {
      return usageSessions.value.reduce((total, session) => {
        return total + (session.duration_seconds || 0);
      }, 0);
    });
    
    const totalCost = computed(() => {
      return usageSessions.value.reduce((total, session) => {
        return total + (session.cost_estimate || 0);
      }, 0);
    });
    
    async function loadUsageData() {
      try {
        loading.value = true;
        error.value = null;
        
        const user = authStore.getUser();
        if (!user) {
          throw new Error('User not authenticated');
        }
        
        // Load usage sessions
        const { data: sessions, error: sessionsError } = await supabase
          .from('gpu_server_usage')
          .select('*')
          .eq('user_id', user.id)
          .order('start_time', { ascending: false });
          
        if (sessionsError) {
          throw new Error('Failed to load usage data');
        }
        
        usageSessions.value = sessions;
        
        // Load image count
        const { count, error: countError } = await supabase
          .from('generated_images')
          .select('*', { count: 'exact', head: true })
          .eq('user_id', user.id);
          
        if (countError) {
          throw new Error('Failed to load image count');
        }
        
        totalImages.value = count || 0;
      } catch (err) {
        console.error('Error loading usage data:', err);
        error.value = err.message;
      } finally {
        loading.value = false;
      }
    }
    
    onMounted(() => {
      loadUsageData();
    });
    
    return {
      loading,
      error,
      usageSessions,
      columns,
      totalUsageSeconds,
      totalCost,
      totalImages,
      formatDuration
    };
  }
};
</script>
```

## 7.5 Implement Server Configuration Selector Component

Create the Server Configuration Selector component in `src/components/serverManagement/ServerConfigSelector.vue`:

```vue
<template>
  <div class="server-config-selector">
    <div class="text-subtitle1 q-mb-sm">Select Server Configuration</div>
    
    <div v-if="loading" class="text-center q-py-md">
      <q-spinner color="primary" size="2rem" />
      <div class="q-mt-sm">Loading configurations...</div>
    </div>
    
    <div v-else-if="error" class="text-negative q-py-md">
      <q-icon name="mdi-alert" />
      {{ error }}
    </div>
    
    <div v-else>
      <q-option-group
        v-model="selectedConfigId"
        :options="configOptions"
        type="radio"
        @update:model-value="updateSelectedConfig"
      />
      
      <div v-if="selectedConfig" class="q-mt-md">
        <q-list bordered separator>
          <q-item>
            <q-item-section>
              <q-item-label caption>GPU Type</q-item-label>
              <q-item-label>{{ selectedConfig.gpuType }}</q-item-label>
            </q-item-section>
          </q-item>
          
          <q-item>
            <q-item-section>
              <q-item-label caption>Memory</q-item-label>
              <q-item-label>{{ selectedConfig.memoryGB }} GB</q-item-label>
            </q-item-section>
          </q-item>
          
          <q-item>
            <q-item-section>
              <q-item-label caption>Storage</q-item-label>
              <q-item-label>{{ selectedConfig.diskGB }} GB</q-item-label>
            </q-item-section>
          </q-item>
          
          <q-item>
            <q-item-section>
              <q-item-label caption>Hourly Cost</q-item-label>
              <q-item-label>${{ selectedConfig.hourlyCost.toFixed(2) }} / hour</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue';
import { serverManagementService } from '../../services/serverManagement';

export default {
  name: 'ServerConfigSelector',
  
  props: {
    modelValue: {
      type: Object,
      default: null
    }
  },
  
  emits: ['update:modelValue'],
  
  setup(props, { emit }) {
    const loading = ref(true);
    const error = ref(null);
    const configurations = ref([]);
    const selectedConfigId = ref(null);
    
    const configOptions = computed(() => {
      return configurations.value.map(config => ({
        label: config.name,
        value: config.id,
        disable: !config.isActive
      }));
    });
    
    const selectedConfig = computed(() => {
      if (!selectedConfigId.value) return null;
      return configurations.value.find(c => c.id === selectedConfigId.value);
    });
    
    function updateSelectedConfig() {
      emit('update:modelValue', selectedConfig.value);
    }
    
    async function loadConfigurations() {
      try {
        loading.value = true;
        error.value = null;
        
        const configs = await serverManagementService.getServerConfigurations();
        configurations.value = configs;
        
        // Select default configuration
        if (configs.length > 0) {
          const defaultConfig = configs.find(c => c.isDefault) || configs[0];
          selectedConfigId.value = defaultConfig.id;
          emit('update:modelValue', defaultConfig);
        }
      } catch (err) {
        console.error('Error loading configurations:', err);
        error.value = err.message;
      } finally {
        loading.value = false;
      }
    }
    
    // Watch for external changes to modelValue
    watch(() => props.modelValue, (newValue) => {
      if (newValue && newValue.id) {
        selectedConfigId.value = newValue.id;
      }
    });
    
    onMounted(() => {
      loadConfigurations();
    });
    
    return {
      loading,
      error,
      configurations,
      selectedConfigId,
      configOptions,
      selectedConfig,
      updateSelectedConfig
    };
  }
};
</script>
```

## 7.6 Integrate Server Controls into Template Editor

Integrate the Server Controls component into your template editor page:

```vue
<template>
  <div class="template-editor">
    <!-- Existing template editor content -->
    
    <div class="q-mt-lg">
      <q-expansion-item
        icon="mdi-server"
        label="Image Generation Server"
        caption="Start a GPU server to generate images"
        header-class="text-primary"
      >
        <server-controls />
      </q-expansion-item>
    </div>
    
    <!-- Rest of template editor -->
  </div>
</template>

<script>
import { ref } from 'vue';
import ServerControls from '../components/serverManagement/ServerControls.vue';

export default {
  name: 'TemplateEditor',
  
  components: {
    ServerControls
  },
  
  // Rest of component script
};
</script>
```

## 7.7 Add Server Status Indicator to App Header

Add a server status indicator to your app header for quick access:

```vue
<template>
  <q-header>
    <q-toolbar>
      <!-- Existing header content -->
      
      <q-space />
      
      <q-btn
        v-if="serverRunning"
        flat
        round
        icon="mdi-server-network"
        color="positive"
      >
        <q-tooltip>GPU Server Running</q-tooltip>
        
        <q-menu>
          <q-list style="min-width: 200px">
            <q-item>
              <q-item-section>
                <q-item-label>GPU Server Status</q-item-label>
                <q-item-label caption>Running since {{ formatTime(serverStartTime) }}</q-item-label>
              </q-item-section>
            </q-item>
            
            <q-separator />
            
            <q-item clickable v-close-popup @click="navigateToServerControls">
              <q-item-section avatar>
                <q-icon name="mdi-cog" />
              </q-item-section>
              <q-item-section>Manage Server</q-item-section>
            </q-item>
            
            <q-item clickable v-close-popup @click="stopServer">
              <q-item-section avatar>
                <q-icon name="mdi-server-off" />
              </q-item-section>
              <q-item-section>Stop Server</q-item-section>
            </q-item>
          </q-list>
        </q-menu>
      </q-btn>
      
      <!-- Rest of header content -->
    </q-toolbar>
  </q-header>
</template>

<script>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { useRouter } from 'vue-router';
import { date } from 'quasar';
import { serverManagementService } from '../services/serverManagement';

export default {
  // Rest of component script
  
  setup() {
    const router = useRouter();
    const activeServer = ref(null);
    const statusCheckInterval = ref(null);
    
    const serverRunning = computed(() => {
      return activeServer.value && activeServer.value.status === 'running';
    });
    
    const serverStartTime = computed(() => {
      return activeServer.value ? activeServer.value.startedAt : null;
    });
    
    async function checkServerStatus() {
      try {
        const server = await serverManagementService.getActiveServer();
        activeServer.value = server;
      } catch (error) {
        console.error('Error checking server status:', error);
      }
    }
    
    function formatTime(timeString) {
      if (!timeString) return '';
      return date.formatDate(timeString, 'h:mm A');
    }
    
    function navigateToServerControls() {
      router.push('/templates/server-management');
    }
    
    async function stopServer() {
      if (!activeServer.value) return;
      
      try {
        await serverManagementService.stopServer(activeServer.value.id);
      } catch (error) {
        console.error('Error stopping server:', error);
      }
    }
    
    onMounted(() => {
      checkServerStatus();
      statusCheckInterval.value = setInterval(checkServerStatus, 30000);
    });
    
    onBeforeUnmount(() => {
      if (statusCheckInterval.value) {
        clearInterval(statusCheckInterval.value);
      }
    });
    
    return {
      serverRunning,
      serverStartTime,
      formatTime,
      navigateToServerControls,
      stopServer
    };
  }
};
</script>
```

## Next Steps

Now that you have implemented the UI components for server management, proceed to [Step 8: Activity Tracking and Auto-Shutdown](./08-activity-tracking.md) to implement the system for tracking user activity and automatically shutting down idle servers.
