<template>
  <q-btn
    :icon="isDark ? 'light_mode' : 'dark_mode'"
    flat
    round
    :color="isDark ? 'yellow' : 'blue-grey-8'"
    @click="toggleDarkMode"
    :aria-label="isDark ? 'Switch to light mode' : 'Switch to dark mode'"
  >
    <q-tooltip>{{ isDark ? 'Switch to light mode' : 'Switch to dark mode' }}</q-tooltip>
  </q-btn>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, onUnmounted } from 'vue';
import { useQuasar } from 'quasar';
import { useLocalStorage } from '@vueuse/core';
import PlatformUtil from 'src/utils/platform';

const $q = useQuasar();
const isDark = ref(false);
const storedTheme = useLocalStorage('dreambox-theme', 'auto');
const isElectron = PlatformUtil.isElectron();
let electronThemeListener: (() => void) | null = null;

// Function to toggle dark mode
async function toggleDarkMode() {
  isDark.value = !isDark.value;
  $q.dark.set(isDark.value);
  const newTheme = isDark.value ? 'dark' : 'light';
  storedTheme.value = newTheme;

  // If running in Electron, sync with native theme
  if (isElectron && typeof window.electronAPI !== 'undefined') {
    await window.electronAPI.setTheme(newTheme);
  }
}

// Function to detect system preference
function detectSystemDarkMode() {
  return window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
}

// Watch for system preference changes
function setupSystemPreferenceWatcher() {
  if (window.matchMedia) {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    // Initial check
    if (storedTheme.value === 'auto') {
      isDark.value = mediaQuery.matches;
      $q.dark.set(isDark.value);
    }

    // Add listener for changes
    mediaQuery.addEventListener('change', (e) => {
      if (storedTheme.value === 'auto') {
        isDark.value = e.matches;
        $q.dark.set(isDark.value);
      }
    });
  }
}

// Initialize theme based on stored preference
onMounted(async () => {
  // If running in Electron, check if we can get the theme from there first
  if (isElectron && typeof window.electronAPI !== 'undefined') {
    try {
      // Set up listener for theme changes from Electron
      electronThemeListener = window.electronAPI.onThemeChanged((theme) => {
        isDark.value = theme === 'dark';
        $q.dark.set(isDark.value);
      });

      // Get initial theme from Electron
      const electronTheme = await window.electronAPI.getTheme();
      isDark.value = electronTheme === 'dark';
      $q.dark.set(isDark.value);

      // Sync Electron with our stored preference
      if (storedTheme.value === 'dark') {
        await window.electronAPI.setTheme('dark');
      } else if (storedTheme.value === 'light') {
        await window.electronAPI.setTheme('light');
      } else {
        await window.electronAPI.setTheme('system');
      }
    } catch (error) {
      console.error('Error syncing with Electron theme:', error);
      // Fall back to web implementation if Electron API fails
      initializeWebTheme();
    }
  } else {
    // Standard web implementation
    initializeWebTheme();
  }
});

// Clean up Electron listeners when component is unmounted
onUnmounted(() => {
  if (electronThemeListener) {
    electronThemeListener();
  }
});

// Initialize theme for web (non-Electron) context
function initializeWebTheme() {
  if (storedTheme.value === 'dark') {
    isDark.value = true;
    $q.dark.set(true);
  } else if (storedTheme.value === 'light') {
    isDark.value = false;
    $q.dark.set(false);
  } else {
    // Auto mode - use system preference
    isDark.value = detectSystemDarkMode();
    $q.dark.set(isDark.value);
  }

  setupSystemPreferenceWatcher();
}

// Watch for dark mode changes from Quasar
watch(() => $q.dark.isActive, (newValue) => {
  isDark.value = newValue;
});
</script>

<style scoped>
.q-btn {
  transition: all 0.3s ease;
}
</style>
