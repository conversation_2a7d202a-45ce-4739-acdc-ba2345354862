import { createI18n } from 'vue-i18n';
import enUS from './en-US';
import hrHR from './hr-HR';
import { userSettingsService } from 'src/services/userSettingsService';

// Type definition for messages
type MessageSchema = typeof enUS;

// Create i18n instance with type safety
const i18n = createI18n<[MessageSchema], 'en-US' | 'hr-HR'>({
  legacy: false, // Use Composition API
  locale: 'en-US', // Default locale
  fallbackLocale: 'en-US',
  messages: {
    'en-US': enUS,
    'hr-HR': hrHR
  }
});

export default i18n;

// Helper function to get the browser language
export function getBrowserLocale(): string {
  // Try to get from localStorage first (user preference)
  const storedLocale = localStorage.getItem('dreambox-locale');
  if (storedLocale) {
    return storedLocale;
  }

  // Try to get from browser
  const navigatorLocale =
    navigator.languages !== undefined
      ? navigator.languages[0]
      : navigator.language;

  if (!navigatorLocale) {
    return 'en-US'; // Default to English
  }

  // Check if the browser locale is supported
  const locale = navigatorLocale.trim().split(/-|_/)[0];
  if (locale === 'hr') {
    return 'hr-HR';
  }

  return 'en-US'; // Default to English
}

// Helper function to set the locale
export function setLocale(locale: 'en-US' | 'hr-HR'): void {
  try {
    // For Composition API with legacy: false
    // We need to directly modify the global property
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const instance = i18n as any;

    // Set the locale directly on the global object
    instance.global.locale.value = locale;

    console.log('i18n locale set to:', locale);
  } catch (error) {
    console.error('Error setting locale:', error);
  }

  // Extract the language code from the locale (e.g., 'en' from 'en-US')
  // This will be 'en' or 'hr'
  const lang = locale.substring(0, 2);

  // Set the lang attribute on the HTML element
  const htmlElement = document.querySelector('html');
  if (htmlElement) {
    htmlElement.setAttribute('lang', lang);
  }

  // Save to localStorage
  localStorage.setItem('dreambox-locale', locale);

  // Save to database - use void to handle the promise
  void (async () => {
    try {
      await userSettingsService.saveSetting('language', locale);
    } catch (error) {
      console.error('Error saving language setting to database:', error);
    }
  })();

  // Log the change for debugging
  console.log(`Language changed to: ${locale}`);
}
