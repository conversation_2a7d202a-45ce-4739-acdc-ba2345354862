# Step 12: Cost Optimization Strategies

This document outlines strategies for optimizing costs when using RunPod for SwarmUI image generation.

## 12.1 Understanding RunPod Costs

RunPod charges based on the following factors:

1. **GPU Type**: Different GPU models have different hourly rates
2. **GPU Count**: Multiple GPUs cost more
3. **Runtime**: You pay for the time your pod is running
4. **Storage**: You pay for persistent storage volumes
5. **Network Egress**: You pay for data transferred out of RunPod

## 12.2 Server Provisioning Strategies

### 12.2.1 On-Demand Provisioning

The most cost-effective approach is to provision servers only when needed:

```typescript
// Only start a server when the user explicitly requests it
async function startServerOnDemand() {
  // Check if user already has a running server
  const activeServer = await serverManagementService.getActiveServer();
  
  if (activeServer) {
    return {
      serverId: activeServer.id,
      status: activeServer.status,
      message: 'Server is already running'
    };
  }
  
  // Start a new server
  return await serverManagementService.startServer();
}
```

### 12.2.2 Batch Processing

For multiple image generations, batch them together to minimize server uptime:

```typescript
// Batch multiple image generations together
async function batchGenerateImages(prompts, params) {
  // Start server if not already running
  const serverResult = await startServerOnDemand();
  
  try {
    const results = [];
    
    // Generate all images in sequence
    for (const prompt of prompts) {
      const generationParams = { ...params, prompt };
      const result = await swarmUIService.generateImage(generationParams);
      results.push(result);
    }
    
    return results;
  } finally {
    // Consider whether to stop the server here
    // or let the auto-shutdown handle it
  }
}
```

### 12.2.3 Scheduled Provisioning

For predictable usage patterns, schedule server provisioning during off-peak hours:

```typescript
// Schedule server provisioning for a specific time
function scheduleServerProvisioning(hour, minute) {
  const now = new Date();
  const scheduledTime = new Date(
    now.getFullYear(),
    now.getMonth(),
    now.getDate(),
    hour,
    minute
  );
  
  // If the scheduled time is in the past, schedule for tomorrow
  if (scheduledTime < now) {
    scheduledTime.setDate(scheduledTime.getDate() + 1);
  }
  
  const timeUntilScheduled = scheduledTime.getTime() - now.getTime();
  
  setTimeout(async () => {
    await serverManagementService.startServer();
    
    // Schedule the next day
    scheduleServerProvisioning(hour, minute);
  }, timeUntilScheduled);
}

// Schedule server provisioning for 9:00 AM
scheduleServerProvisioning(9, 0);
```

## 12.3 Auto-Shutdown Optimization

### 12.3.1 Adaptive Timeout

Adjust the auto-shutdown timeout based on user activity patterns:

```typescript
// Adaptive timeout based on user activity
function getAdaptiveTimeout(userId) {
  return supabase
    .from('user_activity_stats')
    .select('avg_session_duration')
    .eq('user_id', userId)
    .single()
    .then(({ data, error }) => {
      if (error || !data) {
        return 30 * 60 * 1000; // Default: 30 minutes
      }
      
      // Add a buffer to the average session duration
      const timeout = Math.ceil(data.avg_session_duration * 1.2);
      
      // Constrain between 10 minutes and 2 hours
      return Math.max(10 * 60 * 1000, Math.min(timeout, 2 * 60 * 60 * 1000));
    });
}
```

### 12.3.2 Proactive Shutdown

Proactively shut down servers when the user navigates away from the image generation page:

```typescript
// In your image generation component
onBeforeUnmount(async () => {
  // Check if the server was started by this component
  if (serverStartedByComponent.value) {
    // Ask the user if they want to keep the server running
    const keepRunning = await confirmDialog(
      'Keep Server Running?',
      'Do you want to keep the image generation server running?'
    );
    
    if (!keepRunning) {
      await serverManagementService.stopServer(activeServer.value.id);
    }
  }
});
```

### 12.3.3 Batch Completion Shutdown

Shut down the server after completing a batch of generations:

```typescript
// Shut down after batch completion
async function generateAndShutdown(prompts, params) {
  // Start server if not already running
  const serverResult = await startServerOnDemand();
  
  try {
    const results = [];
    
    // Generate all images in sequence
    for (const prompt of prompts) {
      const generationParams = { ...params, prompt };
      const result = await swarmUIService.generateImage(generationParams);
      results.push(result);
    }
    
    return results;
  } finally {
    // Always shut down the server when done
    await serverManagementService.stopServer(serverResult.serverId);
  }
}
```

## 12.4 GPU Selection Strategies

### 12.4.1 Tiered GPU Options

Offer different GPU options based on user needs:

```typescript
// Define GPU tiers
const gpuTiers = [
  {
    id: 'economy',
    name: 'Economy',
    description: 'Good for basic image generation',
    gpuType: 'NVIDIA RTX 3060',
    hourlyRate: 0.2
  },
  {
    id: 'standard',
    name: 'Standard',
    description: 'Balanced performance and cost',
    gpuType: 'NVIDIA RTX A4000',
    hourlyRate: 0.4
  },
  {
    id: 'performance',
    name: 'Performance',
    description: 'Fast generation for complex images',
    gpuType: 'NVIDIA RTX A5000',
    hourlyRate: 0.7
  }
];

// Start server with selected tier
async function startServerWithTier(tierId) {
  const tier = gpuTiers.find(t => t.id === tierId);
  
  if (!tier) {
    throw new Error('Invalid GPU tier');
  }
  
  // Find configuration matching the tier
  const { data: configs } = await supabase
    .from('server_configurations')
    .select('*')
    .eq('gpu_type', tier.gpuType)
    .eq('is_active', true);
    
  if (!configs || configs.length === 0) {
    throw new Error(`No configuration found for GPU tier: ${tier.name}`);
  }
  
  // Start server with the selected configuration
  return await serverManagementService.startServer({
    configurationId: configs[0].id
  });
}
```

### 12.4.2 Time-Based GPU Selection

Use different GPUs based on time of day:

```typescript
// Select GPU based on time of day
function selectGpuByTime() {
  const hour = new Date().getHours();
  
  // Use cheaper GPUs during peak hours
  if (hour >= 9 && hour <= 17) {
    return 'economy';
  }
  
  // Use better GPUs during off-peak hours
  return 'standard';
}
```

### 12.4.3 Task-Based GPU Selection

Select GPU based on the complexity of the task:

```typescript
// Select GPU based on task complexity
function selectGpuByTask(params) {
  // High resolution images need more powerful GPUs
  if (params.width * params.height >= 1024 * 1024) {
    return 'performance';
  }
  
  // Batch generations need more powerful GPUs
  if (params.batch_size > 1) {
    return 'standard';
  }
  
  // Default to economy
  return 'economy';
}
```

## 12.5 Storage Optimization

### 12.5.1 Temporary Storage

Use temporary storage for intermediate results:

```typescript
// Use temporary storage for intermediate results
async function generateWithTempStorage(params) {
  // Generate image
  const result = await swarmUIService.generateImage(params);
  
  // Download the images
  const images = [];
  
  for (const image of result.images) {
    const blob = await swarmUIService.downloadImage(image.url);
    images.push({
      blob,
      seed: image.seed,
      width: image.width,
      height: image.height
    });
  }
  
  return images;
}
```

### 12.5.2 Image Compression

Compress images before storing them:

```typescript
// Compress image before storing
async function compressAndStoreImage(imageBlob, quality = 0.8) {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      const canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;
      
      const ctx = canvas.getContext('2d');
      ctx.drawImage(img, 0, 0);
      
      canvas.toBlob(
        blob => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to compress image'));
          }
        },
        'image/jpeg',
        quality
      );
    };
    
    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(imageBlob);
  });
}
```

### 12.5.3 Cleanup Old Images

Automatically clean up old images to reduce storage costs:

```typescript
// Clean up old images
async function cleanupOldImages(olderThanDays = 30) {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
  
  const { data: oldImages, error } = await supabase
    .from('generated_images')
    .select('id, storage_path')
    .lt('created_at', cutoffDate.toISOString());
    
  if (error || !oldImages) {
    console.error('Error fetching old images:', error);
    return;
  }
  
  // Delete images from storage
  for (const image of oldImages) {
    await supabase.storage
      .from('generated_images')
      .remove([image.storage_path]);
      
    // Delete record from database
    await supabase
      .from('generated_images')
      .delete()
      .eq('id', image.id);
  }
  
  console.log(`Cleaned up ${oldImages.length} old images`);
}

// Schedule cleanup to run daily
setInterval(cleanupOldImages, 24 * 60 * 60 * 1000);
```

## 12.6 Usage Monitoring and Alerting

### 12.6.1 Usage Dashboard

Create a dashboard to monitor usage and costs:

```vue
<template>
  <div class="usage-dashboard">
    <div class="row q-col-gutter-md">
      <div class="col-12 col-md-4">
        <q-card>
          <q-card-section>
            <div class="text-h6">Current Month Usage</div>
            <div class="text-h4">${{ currentMonthCost.toFixed(2) }}</div>
          </q-card-section>
        </q-card>
      </div>
      
      <div class="col-12 col-md-4">
        <q-card>
          <q-card-section>
            <div class="text-h6">Server Uptime</div>
            <div class="text-h4">{{ formatDuration(totalUptimeSeconds) }}</div>
          </q-card-section>
        </q-card>
      </div>
      
      <div class="col-12 col-md-4">
        <q-card>
          <q-card-section>
            <div class="text-h6">Images Generated</div>
            <div class="text-h4">{{ totalImages }}</div>
          </q-card-section>
        </q-card>
      </div>
    </div>
    
    <!-- Usage chart -->
    <q-card class="q-mt-md">
      <q-card-section>
        <div class="text-h6">Daily Usage</div>
        <canvas ref="usageChart"></canvas>
      </q-card-section>
    </q-card>
  </div>
</template>

<script>
// Implementation details...
</script>
```

### 12.6.2 Cost Alerts

Set up alerts for unusual usage patterns:

```typescript
// Check for unusual usage
async function checkUnusualUsage() {
  // Get average daily usage for the past 30 days
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  
  const { data: usageStats } = await supabase
    .from('gpu_server_usage')
    .select('date, SUM(duration_seconds) as total_duration')
    .gte('start_time', thirtyDaysAgo.toISOString())
    .group('date')
    .order('date');
    
  if (!usageStats || usageStats.length === 0) {
    return;
  }
  
  // Calculate average daily usage
  const totalDuration = usageStats.reduce((sum, day) => sum + day.total_duration, 0);
  const avgDailyDuration = totalDuration / usageStats.length;
  
  // Get today's usage
  const today = new Date().toISOString().split('T')[0];
  const todayStats = usageStats.find(day => day.date === today);
  
  if (!todayStats) {
    return;
  }
  
  // Check if today's usage is significantly higher than average
  if (todayStats.total_duration > avgDailyDuration * 2) {
    // Send alert
    sendAlert(`Unusual GPU usage detected: ${formatDuration(todayStats.total_duration)} today vs. ${formatDuration(avgDailyDuration)} average`);
  }
}

// Schedule to run every hour
setInterval(checkUnusualUsage, 60 * 60 * 1000);
```

### 12.6.3 Budget Enforcement

Enforce budget limits to prevent unexpected costs:

```typescript
// Check if user has exceeded their budget
async function checkBudget(userId) {
  // Get user's monthly budget
  const { data: userPrefs } = await supabase
    .from('user_preferences')
    .select('monthly_budget')
    .eq('user_id', userId)
    .single();
    
  if (!userPrefs || !userPrefs.monthly_budget) {
    return true; // No budget set
  }
  
  // Get current month's usage
  const startOfMonth = new Date();
  startOfMonth.setDate(1);
  startOfMonth.setHours(0, 0, 0, 0);
  
  const { data: usageStats } = await supabase
    .from('gpu_server_usage')
    .select('SUM(cost_estimate) as total_cost')
    .eq('user_id', userId)
    .gte('start_time', startOfMonth.toISOString())
    .single();
    
  if (!usageStats) {
    return true; // No usage data
  }
  
  // Check if user has exceeded their budget
  return usageStats.total_cost < userPrefs.monthly_budget;
}

// Before starting a server, check budget
async function startServerWithBudgetCheck() {
  const user = authStore.getUser();
  
  if (!user) {
    throw new Error('User not authenticated');
  }
  
  // Check if user has exceeded their budget
  const budgetOk = await checkBudget(user.id);
  
  if (!budgetOk) {
    throw new Error('Monthly budget exceeded');
  }
  
  // Start server
  return await serverManagementService.startServer();
}
```

## 12.7 Conclusion

By implementing these cost optimization strategies, you can significantly reduce the cost of using RunPod for SwarmUI image generation while still providing a great user experience.

Remember to regularly review your usage patterns and adjust your strategies accordingly to ensure you're getting the most value from your RunPod investment.

## Next Steps

Congratulations! You have completed all the steps for implementing SwarmUI on RunPod with on-demand provisioning. You now have a cost-effective solution for generating high-quality images with the Flux model.
