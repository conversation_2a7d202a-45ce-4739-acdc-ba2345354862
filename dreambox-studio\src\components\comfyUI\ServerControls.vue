<template>
  <div class="server-controls">
    <q-card class="q-mb-md">
      <q-card-section>
        <div class="text-h6">ComfyUI Server</div>
        <div class="text-subtitle2">Manage your image generation server</div>
      </q-card-section>

      <q-card-section>
        <div class="row items-center q-mb-md">
          <div class="col">{{ t('comfyUI.serverStatus') }}:</div>
          <div class="col-auto">
            <q-badge
              :color="statusColor"
              :label="t('comfyUI.' + serverStatus.status)"
              class="q-px-sm"
            />
          </div>
        </div>

        <div v-if="serverStatus.message" class="q-mb-md">
          {{ serverStatus.message }}
        </div>

        <div v-if="serverStatus.apiEndpoint" class="q-mb-md text-caption">
          API Endpoint: {{ serverStatus.apiEndpoint }}
          <q-badge v-if="isServerRunning" :color="getConnectionBadgeColor()" class="q-ml-sm">
            {{ getConnectionBadgeText() }}
            <q-spinner v-if="isCheckingHealth || isWarmingUp || (!isConnected && !isCheckingHealth)" size="1em" class="q-ml-xs" />
          </q-badge>
        </div>

        <div class="row q-gutter-sm">
          <q-btn
            v-if="!isServerRunning && serverStatus.status !== 'starting'"
            color="primary"
            :label="getCreateButtonLabel()"
            :loading="isCreating || isCheckingHealth || isWarmingUp"
            @click="startServer"
          />
          <q-btn
            v-if="isServerRunning || serverStatus.status === 'starting' || serverStatus.status === 'stopping'"
            color="negative"
            :label="t('comfyUI.terminateServer')"
            :loading="isTerminating"
            @click="confirmTerminate"
          />

          <q-btn
            v-if="isServerRunning"
            :color="shouldTerminateOnLeave ? 'positive' : 'orange'"
            :label="shouldTerminateOnLeave ? 'Will Terminate on Leave' : 'Manage Server'"
            :icon="shouldTerminateOnLeave ? 'schedule_send' : 'settings'"
            @click="handleServerBeforeNavigation"
            class="q-ml-sm"
          >
            <q-tooltip>
              {{ shouldTerminateOnLeave ? 'Server will be terminated when you leave the page (click to change)' : 'Decide what to do with your server when leaving the page' }}
            </q-tooltip>
          </q-btn>
        </div>
      </q-card-section>
    </q-card>

    <q-card v-if="serverStatus.status === 'running'">
      <q-card-section>
        <div class="text-h6">Generate Test Image</div>
        <div class="text-subtitle2">Test image generatyUI</div>
      </q-card-section>

      <q-card-section>
        <q-input
          v-model="testPrompt"
          :label="t('comfyUI.prompt')"
          filled
          type="textarea"
          rows="3"
        />

        <q-input
          v-model="testNegativePrompt"
          :label="t('comfyUI.negativePrompt')"
          filled
          type="textarea"
          rows="2"
          class="q-mt-md"
        />

        <div class="row q-col-gutter-md q-mt-md">
          <div class="col-6 col-md-3">
            <q-input
              v-model.number="testWidth"
              :label="t('comfyUI.width')"
              filled
              type="number"
              :min="512"
              :max="2048"
              :step="64"
            />
          </div>
          <div class="col-6 col-md-3">
            <q-input
              v-model.number="testHeight"
              :label="t('comfyUI.height')"
              filled
              type="number"
              :min="512"
              :max="2048"
              :step="64"
            />
          </div>
          <div class="col-6 col-md-3">
            <q-input
              v-model.number="testSteps"
              :label="t('comfyUI.steps')"
              filled
              type="number"
              :min="10"
              :max="100"
              :step="1"
            />
          </div>
          <div class="col-6 col-md-3">
            <q-input
              v-model.number="testCfgScale"
              :label="t('comfyUI.cfgScale')"
              filled
              type="number"
              :min="1"
              :max="20"
              :step="0.5"
            />
          </div>
        </div>

        <div class="row justify-end q-mt-md">
          <q-btn
            color="primary"
            :label="t('comfyUI.generateImage')"
            :loading="isGenerating"
            :disable="!isServerRunning || !isConnected || isCheckingHealth || isWarmingUp"
            @click="generateTestImage"
          />
        </div>
      </q-card-section>

      <q-card-section v-if="generatedImageUrl">
        <div class="text-h6">Generated Image</div>
        <div class="text-center">
          <img :src="generatedImageUrl" class="generated-image q-mt-md" />
          <div class="q-mt-md">
            <q-btn
              color="primary"
              icon="save"
              label="Save Image"
              :loading="isSavingImage"
              @click="saveGeneratedImage"
              class="q-mr-sm"
            />
            <q-btn
              color="secondary"
              icon="download"
              label="Download"
              @click="downloadImage"
              flat
            />
          </div>
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import { comfyUIService, createBasicFluxWorkflow } from 'src/services/comfyUI';
import type { ServerStatus } from 'src/services/comfyUI';
import { notificationService } from 'src/services/notificationService';

const $q = useQuasar();
const { t } = useI18n();

// Server status
const serverStatus = ref<ServerStatus>({ status: 'no-server' });
const isTerminating = ref(false);
const isCreating = ref(false);
const isConnected = ref(false);
const isCheckingHealth = ref(false);
const isWarmingUp = ref(false);

// Auto-termination cleanup function
let autoTerminationCleanup: (() => void) | null = null;

// Flag to track if server should be terminated on page leave
let shouldTerminateOnLeave = false;

// Test image generation
const testPrompt = ref('A beautiful landscape with mountains and a lake');
const testNegativePrompt = ref('ugly, blurry, low quality');
const testWidth = ref(1024);
const testHeight = ref(1024);
const testSteps = ref(4);
const testCfgScale = ref(1);
const isGenerating = ref(false);
const generatedImageUrl = ref('');
const isSavingImage = ref(false);
const lastGeneratedImageMetadata = ref<Record<string, unknown> | null>(null);

// Computed properties
const isServerRunning = computed(() => serverStatus.value.status === 'running');
const statusColor = computed(() => {
  switch (serverStatus.value.status) {
    case 'running':
      return 'positive';
    case 'starting':
      return 'info';
    case 'stopping':
      return 'warning';
    case 'error':
      return 'negative';
    default:
      return 'grey';
  }
});

// Methods
function getCreateButtonLabel(): string {
  if (isCreating.value) {
    return 'Creating Server...';
  } else if (isCheckingHealth.value) {
    return 'Waiting for ComfyUI...';
  } else if (isWarmingUp.value) {
    return 'Warming up...';
  } else {
    return t('comfyUI.createRunPod');
  }
}

function getConnectionBadgeColor(): string {
  if (isCheckingHealth.value) {
    return 'orange';
  } else if (isWarmingUp.value) {
    return 'purple';
  } else if (isConnected.value) {
    return 'positive';
  } else {
    return 'warning';
  }
}

function getConnectionBadgeText(): string {
  if (isCheckingHealth.value) {
    return 'Checking ComfyUI...';
  } else if (isWarmingUp.value) {
    return 'Warming up...';
  } else if (isConnected.value) {
    return 'ComfyUI Ready';
  } else {
    return 'Connecting...';
  }
}

async function updateServerStatus() {
  const status = await comfyUIService.checkServerStatus();
  serverStatus.value = status;
}

async function startServer() {
  isCreating.value = true;
  try {
    const result = await comfyUIService.startServer({
      gpuTypeId: 'NVIDIA RTX A4000' // Default GPU type
    });

    if (result.status === 'error') {
      // Show a detailed error message
      $q.dialog({
        title: 'Error Creating Server',
        message: result.message || t('comfyUI.errorCreatingServer'),
        color: 'negative',
        persistent: true,
        ok: {
          label: 'OK',
          color: 'primary'
        }
      });
    } else {
      // Use the notification service to respect user settings
      await notificationService.notify({
        color: 'positive',
        message: t('comfyUI.serverCreating'),
        icon: 'cloud_queue'
      });

      // Start the enhanced server readiness check
      if (serverStatus.value.status === 'running' || serverStatus.value.status === 'starting') {
        // Start checking for ComfyUI readiness
        setTimeout(() => { void waitForComfyUIReadiness(); }, 5000);
      }
    }
  } catch (error) {
    // Show a detailed error message
    $q.dialog({
      title: 'Error Creating Server',
      message: error instanceof Error ? error.message : String(error),
      color: 'negative',
      persistent: true,
      ok: {
        label: 'OK',
        color: 'primary'
      }
    });
  } finally {
    isCreating.value = false;
  }
}

async function waitForComfyUIReadiness() {
  if (!isServerRunning.value || !serverStatus.value.apiEndpoint) return;

  // If already connected, don't check again
  if (isConnected.value) {
    console.log('ComfyUI already connected, skipping health check');
    return;
  }

  isCheckingHealth.value = true;

  try {
    console.log('Waiting for ComfyUI to be ready...');

    // Wait for ComfyUI to be ready (up to 5 minutes)
    const isReady = await comfyUIService.waitForComfyUIReady(serverStatus.value.apiEndpoint, 300000);

    if (isReady) {
      isConnected.value = true;

      // Notify that ComfyUI is ready
      await notificationService.notify({
        color: 'positive',
        message: 'ComfyUI is ready!',
        icon: 'check_circle'
      });

      // TODO: Re-enable warmup once CORS issues are resolved
      // For now, skip warmup to avoid CORS errors
      console.log('Skipping warmup process to avoid CORS issues');

      await notificationService.notify({
        color: 'positive',
        message: 'ComfyUI is ready for image generation!',
        icon: 'check_circle'
      });

      /* Warmup code - disabled due to CORS issues
      // Start warmup process
      isWarmingUp.value = true;

      try {
        console.log('Starting warmup process...');
        const warmupSuccess = await comfyUIService.generateWarmupImage(serverStatus.value.apiEndpoint);

        if (warmupSuccess) {
          await notificationService.notify({
            color: 'positive',
            message: 'ComfyUI warmed up and ready for fast image generation!',
            icon: 'flash_on'
          });
        }
      } catch (warmupError) {
        console.error('Warmup failed:', warmupError);
        // Don't show error to user - warmup is optional
      } finally {
        isWarmingUp.value = false;
      }
      */
    } else {
      // Timeout waiting for ComfyUI
      $q.notify({
        color: 'warning',
        message: 'ComfyUI is taking longer than expected to start. You can try generating an image manually.',
        icon: 'warning'
      });
    }
  } catch (error) {
    console.error('Error waiting for ComfyUI readiness:', error);
    $q.notify({
      color: 'negative',
      message: 'Error checking ComfyUI readiness. You can try generating an image manually.',
      icon: 'error'
    });
  } finally {
    isCheckingHealth.value = false;
  }
}

// checkConnection function removed - replaced by waitForComfyUIReadiness

function confirmTerminate() {
  $q.dialog({
    title: 'Terminate Server & Save Costs?',
    message: 'This will stop the ComfyUI server and save costs. You can create a new server anytime.',
    color: 'orange',
    persistent: true,
    ok: {
      label: 'Yes, Save Costs 💰',
      color: 'positive'
    },
    cancel: {
      label: 'Keep Running',
      color: 'grey'
    }
  }).onOk(() => {
    void terminateServer();
  });
}

async function terminateServer() {
  isTerminating.value = true;
  try {
    const success = await comfyUIService.terminateServer();

    if (success) {
      // Use notification service to respect user settings
      await notificationService.notify({
        color: 'positive',
        message: 'Server terminated successfully. Costs saved! 💰',
        icon: 'savings'
      });

      // Update status immediately
      await updateServerStatus();
    } else {
      // Always show error notifications
      $q.notify({
        color: 'negative',
        message: t('comfyUI.errorTerminatingServer'),
        icon: 'error'
      });
    }
  } catch (error) {
    console.error('Error terminating server:', error);
    $q.notify({
      color: 'negative',
      message: t('comfyUI.errorTerminatingServer'),
      icon: 'error'
    });
  } finally {
    isTerminating.value = false;
  }
}

// Function to handle server management before navigation
async function handleServerBeforeNavigation(): Promise<void> {
  if (!isServerRunning.value) {
    await notificationService.notify({
      color: 'info',
      message: 'No server is currently running.',
      icon: 'info'
    });
    return;
  }

  try {
    const choice = await showServerManagementDialog();

    switch (choice) {
      case 'keep':
        console.log('User chose to keep server running');
        shouldTerminateOnLeave = false;
        await notificationService.notify({
          color: 'info',
          message: 'Server will keep running when you leave the page. Remember to terminate it later to save costs.',
          icon: 'info'
        });
        break;

      case 'terminate':
        console.log('User chose to schedule server termination on page leave');
        shouldTerminateOnLeave = true;
        await notificationService.notify({
          color: 'warning',
          message: 'Server will be terminated when you leave the page. You can change this decision anytime.',
          icon: 'schedule'
        });
        break;

      case 'cancel':
        console.log('User cancelled server management');
        break;

      default:
        break;
    }
  } catch (error) {
    console.error('Error in server management dialog:', error);
  }
}

async function generateTestImage() {
  // Check if server is running
  if (!isServerRunning.value) {
    $q.notify({
      color: 'negative',
      message: t('comfyUI.serverNotRunning'),
      icon: 'error'
    });
    return;
  }

  // Check if connected to server (skip in development mode for testing)
  if (!isConnected.value && process.env.NODE_ENV !== 'development') {
    $q.notify({
      color: 'warning',
      message: t('comfyUI.serverNotConnected'),
      icon: 'warning'
    });
    return;
  }

  isGenerating.value = true;
  generatedImageUrl.value = '';
  lastGeneratedImageMetadata.value = null;

  try {
    // Create a workflow for FLUX model
    const workflow = createBasicFluxWorkflow({
      prompt: testPrompt.value,
      negative_prompt: testNegativePrompt.value,
      width: testWidth.value,
      height: testHeight.value,
      steps: testSteps.value,
      cfg_scale: testCfgScale.value
    });

    // Generate the image
    const result = await comfyUIService.generateImage({ prompt: workflow });

    if (result && result.images && result.images.length > 0) {
      // Construct the image URL
      if (result?.images?.[0]) {
        const image = result.images[0];

        // Use the URL from the response if available, otherwise construct it
        if (image.url) {
          generatedImageUrl.value = image.url;
        } else {
          // Fallback: construct the URL from the server endpoint
          generatedImageUrl.value = `${serverStatus.value.apiEndpoint}/view?filename=${image.filename}&subfolder=${image.subfolder || ''}&type=${image.type || 'output'}`;
        }

        // Store metadata for saving later
        lastGeneratedImageMetadata.value = {
          prompt: testPrompt.value,
          negative_prompt: testNegativePrompt.value,
          width: testWidth.value,
          height: testHeight.value,
          steps: testSteps.value,
          cfg_scale: testCfgScale.value,
          model: 'FLUX',
          source: 'comfyui'
        };
      }

      // Use notification service to respect user settings
      await notificationService.notify({
        color: 'positive',
        message: t('comfyUI.imageGenerated'),
        icon: 'check_circle'
      });
    } else {
      $q.notify({
        color: 'negative',
        message: t('comfyUI.imageGenerationFailed'),
        icon: 'error'
      });
    }
  } catch (err) {
    console.error('Error generating image:', err);
    $q.notify({
      color: 'negative',
      message: t('comfyUI.imageGenerationFailed'),
      icon: 'error'
    });
  } finally {
    isGenerating.value = false;
  }
}

async function saveGeneratedImage() {
  if (!generatedImageUrl.value || !lastGeneratedImageMetadata.value) {
    $q.notify({
      color: 'negative',
      message: 'No image to save',
      icon: 'error'
    });
    return;
  }

  isSavingImage.value = true;

  try {
    const imageId = await comfyUIService.saveGeneratedImage(
      generatedImageUrl.value,
      lastGeneratedImageMetadata.value
    );

    // Use notification service to respect user settings
    await notificationService.notify({
      color: 'positive',
      message: `Image saved successfully! ID: ${imageId}`,
      icon: 'check_circle'
    });
  } catch (error) {
    console.error('Error saving image:', error);
    $q.notify({
      color: 'negative',
      message: `Error saving image: ${error instanceof Error ? error.message : 'Unknown error'}`,
      icon: 'error'
    });
  } finally {
    isSavingImage.value = false;
  }
}

function downloadImage() {
  if (!generatedImageUrl.value) {
    $q.notify({
      color: 'negative',
      message: 'No image to download',
      icon: 'error'
    });
    return;
  }

  // Create a temporary link to download the image
  const link = document.createElement('a');
  link.href = generatedImageUrl.value;
  link.download = `comfyui-generated-${Date.now()}.png`;
  link.target = '_blank';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

// Variable to store interval ID
let statusCheckIntervalId: ReturnType<typeof setInterval> | null = null;

// Function to show server management dialog
function showServerManagementDialog(): Promise<'keep' | 'terminate' | 'cancel'> {
  return new Promise((resolve) => {
    $q.dialog({
      title: 'Manage ComfyUI Server',
      message: 'What should happen to your ComfyUI server when you leave this page?',
      persistent: true,
      options: {
        type: 'radio',
        model: shouldTerminateOnLeave ? 'terminate' : 'keep',
        items: [
          { label: 'Keep server running when I leave (continue paying)', value: 'keep', color: 'primary' },
          { label: 'Terminate server when I leave (save costs)', value: 'terminate', color: 'positive' },
        ]
      },
      ok: {
        label: 'Set Preference',
        color: 'primary'
      },
      cancel: {
        label: 'Cancel',
        color: 'grey'
      }
    }).onOk((choice: 'keep' | 'terminate') => {
      resolve(choice);
    }).onCancel(() => {
      resolve('cancel');
    });
  });
}

// Smart server management when app is closed
function setupAutoTermination() {
  const handleBeforeUnload = (event: BeforeUnloadEvent) => {
    if (isServerRunning.value && serverStatus.value.podId) {
      console.log('Page unloading with running server...');

      if (shouldTerminateOnLeave) {
        console.log('Terminating server as requested by user...');
        // Fire and forget - terminate the server
        void comfyUIService.terminateServer().then(() => {
          console.log('Server terminated successfully on page leave');
        }).catch((error) => {
          console.error('Failed to terminate server on page leave:', error);
        });

        // Show confirmation that server will be terminated
        event.preventDefault();
        event.returnValue = 'ComfyUI server will be terminated to save costs as requested.';
      } else {
        // Show warning that server will keep running
        event.preventDefault();
        event.returnValue = 'ComfyUI server is running and will continue running (and charging). Use "Manage Server" to change this.';
      }

      return event.returnValue;
    }
  };

  let inactivityTimeoutId: ReturnType<typeof setTimeout> | null = null;

  const handleVisibilityChange = () => {
    if (document.visibilityState === 'hidden' && isServerRunning.value) {
      console.log('App hidden, scheduling server termination...');

      // Clear any existing timeout
      if (inactivityTimeoutId) {
        clearTimeout(inactivityTimeoutId);
      }

      // Set a timeout to terminate server if app stays hidden
      inactivityTimeoutId = setTimeout(() => {
        void (async () => {
          if (document.visibilityState === 'hidden' && isServerRunning.value) {
            console.log('App has been hidden for 30 seconds, terminating server...');
            try {
              await comfyUIService.terminateServer();
              await notificationService.notify({
                color: 'info',
                message: 'ComfyUI server terminated to save costs (app was inactive)',
                icon: 'power_off'
              });
            } catch (error) {
              console.error('Failed to auto-terminate server:', error);
            }
          }
        })();
      }, 30000); // 30 seconds delay
    } else if (document.visibilityState === 'visible' && inactivityTimeoutId) {
      // App became visible again, cancel the termination
      clearTimeout(inactivityTimeoutId);
      inactivityTimeoutId = null;
    }
  };

  // Add event listeners
  window.addEventListener('beforeunload', handleBeforeUnload);
  document.addEventListener('visibilitychange', handleVisibilityChange);

  // Return cleanup function
  return () => {
    window.removeEventListener('beforeunload', handleBeforeUnload);
    document.removeEventListener('visibilitychange', handleVisibilityChange);

    // Clear any pending timeout
    if (inactivityTimeoutId) {
      clearTimeout(inactivityTimeoutId);
    }
  };
}

// Register cleanup function before any async operations
onBeforeUnmount(() => {
  if (statusCheckIntervalId) {
    clearInterval(statusCheckIntervalId);
    statusCheckIntervalId = null;
  }

  // Also stop the service's status check
  if (comfyUIService.statusCheckInterval) {
    comfyUIService.stopStatusCheck();
  }

  // Clean up auto-termination listeners
  if (autoTerminationCleanup) {
    autoTerminationCleanup();
  }
});

// Lifecycle hooks
onMounted(async () => {
  console.log('ServerControls mounted, checking for existing server...');

  // Set up auto-termination
  autoTerminationCleanup = setupAutoTermination();

  // Initial status check with retry logic
  console.log('Performing initial server status check...');
  await updateServerStatus();

  // If no server found, try again after a short delay (sometimes takes a moment)
  if (serverStatus.value.status === 'no-server') {
    console.log('No server found on first check, retrying in 2 seconds...');
    setTimeout(() => {
      void (async () => {
        await updateServerStatus();
        await handleServerDetection();
      })();
    }, 2000);
  } else {
    await handleServerDetection();
  }

  // Set up interval to check server status
  statusCheckIntervalId = setInterval(() => {
    void (async () => {
      await updateServerStatus();

      // If server is running and not connected, check ComfyUI readiness (but not too frequently)
      if (serverStatus.value.status === 'running' && !isConnected.value && !isCheckingHealth.value && !isWarmingUp.value) {
        void waitForComfyUIReadiness();
      }
    })();
  }, 10000);
});

// Separate function to handle server detection logic
async function handleServerDetection() {
  console.log('Handling server detection, status:', serverStatus.value.status);

  // Enhanced server detection and notification
  if (serverStatus.value.status === 'running' && serverStatus.value.apiEndpoint) {
    console.log('Found existing running server:', serverStatus.value.podId);

    // Notify user about existing server
    await notificationService.notify({
      color: 'info',
      message: `Found existing ComfyUI server (${serverStatus.value.podId}). Checking readiness...`,
      icon: 'info'
    });

    // Check if ComfyUI is already ready
    const isHealthy = await comfyUIService.checkComfyUIHealth(serverStatus.value.apiEndpoint);
    if (isHealthy) {
      isConnected.value = true;
      console.log('ComfyUI was already ready on mount');

      await notificationService.notify({
        color: 'positive',
        message: 'Existing ComfyUI server is ready for use!',
        icon: 'check_circle'
      });
    } else {
      console.log('ComfyUI not ready yet, starting health check...');
      void waitForComfyUIReadiness();
    }
  } else if (serverStatus.value.status === 'starting') {
    console.log('Found server in starting state:', serverStatus.value.podId);

    await notificationService.notify({
      color: 'warning',
      message: `Server ${serverStatus.value.podId} is starting up. Please wait...`,
      icon: 'hourglass_empty'
    });

    // Continue checking until it's running
    void waitForComfyUIReadiness();
  } else {
    console.log('No existing server found, status:', serverStatus.value.status);
  }
}
</script>

<style scoped>
.server-controls {
  max-width: 800px;
  margin: 0 auto;
}

.generated-image {
  max-width: 100%;
  max-height: 500px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}
</style>
