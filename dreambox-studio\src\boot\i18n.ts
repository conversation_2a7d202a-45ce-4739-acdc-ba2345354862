import { boot } from 'quasar/wrappers';
import i18n, { getBrowserLocale, setLocale } from 'src/i18n';
import { userSettingsService } from 'src/services/userSettingsService';

export default boot(async ({ app }) => {
  // Set i18n instance on app
  app.use(i18n);

  // Try to get language setting from database first
  let locale = 'en-US';
  try {
    const savedLanguage = await userSettingsService.getSetting('language');
    if (savedLanguage && (savedLanguage === 'en-US' || savedLanguage === 'hr-HR')) {
      locale = savedLanguage;
    } else {
      // Fall back to browser or stored preference
      locale = getBrowserLocale();
    }
  } catch (error) {
    console.error('Error loading language setting from database:', error);
    // Fall back to browser or stored preference
    locale = getBrowserLocale();
  }

  // Set the locale
  setLocale(locale as 'en-US' | 'hr-HR');

  // Log the current locale for debugging
  console.log('Boot: i18n initialized with locale:', locale);

  // Make i18n available globally for debugging
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  (window as any).$i18n = i18n;
});
