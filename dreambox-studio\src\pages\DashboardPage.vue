<template>
  <q-page class="flex flex-center">
    <div class="column items-center q-gutter-y-md">
      <h1 class="text-h4">Welcome to Dreambox Studio</h1>
      <p class="text-subtitle1">
        You are logged in as {{ authStore.state.user?.email }}
      </p>
      <p v-if="authStore.state.user?.currentCompany">
        Company: {{ authStore.state.user.currentCompany.name }}
      </p>
      <p v-if="authStore.state.user?.currentRole">
        Role: {{ authStore.state.user.currentRole }}
      </p>
      <q-btn color="primary" label="Logout" @click="handleLogout" />
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { useAuthStore } from 'src/stores/auth'
import { useRouter } from 'vue-router'

const authStore = useAuthStore()
const router = useRouter()

async function handleLogout() {
  await authStore.logout()
  try {
    await router.push('/auth/login')
  } catch (error) {
    console.error('Navigation error:', error)
  }
}
</script>
