/**
 * Context Matcher Utility
 *
 * Provides functions for matching context patterns and extracting variables from context strings.
 * Supports wildcards (*) and placeholders ([name]).
 */

/**
 * Match a context pattern against the current context
 * Supports wildcards (*) and placeholders ([name])
 *
 * @param pattern The pattern to match against (e.g., "templates_table.*")
 * @param context The actual context string (e.g., "templates_table.table_view")
 * @returns True if the pattern matches the context
 */
export function matchContextPattern(pattern: string, context: string): boolean {
  // Special case for ALL pattern
  if (pattern === 'ALL') return true;

  // Convert pattern to regex
  // - Replace . with \. (escape dots)
  // - Replace * with .* (wildcard)
  // - Replace placeholders like [table_type] with capturing groups
  const regexPattern = pattern
    .replace(/\./g, '\\.')
    .replace(/\*/g, '.*')
    .replace(/\[(\w+)\]/g, '([\\w-]+)');

  const regex = new RegExp(`^${regexPattern}$`);
  return regex.test(context);
}

/**
 * Extract variables from a context match
 * Returns an object with variable names as keys and matched values as values
 *
 * @param pattern The pattern with placeholders (e.g., "templates_table.[view]")
 * @param context The actual context string (e.g., "templates_table.table_view")
 * @returns Object with extracted variables (e.g., { view: "table_view" })
 */
export function extractContextVariables(pattern: string, context: string): Record<string, string> {
  const variables: Record<string, string> = {};

  // Special case for ALL pattern
  if (pattern === 'ALL') return variables;

  // Extract named placeholders
  const placeholderRegex = /\[(\w+)\]/g;
  let placeholderMatch;
  const placeholders: string[] = [];

  while ((placeholderMatch = placeholderRegex.exec(pattern)) !== null) {
    if (placeholderMatch[1]) {
      placeholders.push(placeholderMatch[1]);
    }
  }

  // Convert pattern to regex with capturing groups
  const regexPattern = pattern
    .replace(/\./g, '\\.')
    .replace(/\*/g, '(.*)')
    .replace(/\[(\w+)\]/g, '([\\w-]+)');

  const regex = new RegExp(`^${regexPattern}$`);
  const matches = regex.exec(context);

  if (matches) {
    // First match is the full string, skip it
    for (let i = 1; i < matches.length; i++) {
      if (i <= placeholders.length && placeholders[i-1]) {
        const placeholder = placeholders[i-1];
        if (placeholder) {
          variables[placeholder] = matches[i] || '';
        }
      } else {
        // For anonymous wildcards (*), use index as key
        variables[`wildcard${i-placeholders.length-1}`] = matches[i] || '';
      }
    }
  }

  return variables;
}
