import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { supabase } from '../boot/supabase'
import type { AuthState, Company, UserRole } from '../types/auth'

// Local storage keys
const AUTH_USER_KEY = 'dreambox-auth-user';

export const useAuthStore = defineStore('auth', () => {
  const state = ref<AuthState>({
    user: null,
    loading: false,
    error: null
  })

  // Save user state to localStorage
  function saveUserToLocalStorage() {
    if (state.value.user) {
      try {
        const userData = JSON.stringify(state.value.user);
        localStorage.setItem(AUTH_USER_KEY, userData);
        console.log('User data saved to localStorage:', { key: AUTH_USER_KEY, size: userData.length });
      } catch (error) {
        console.error('Failed to save user data to localStorage:', error);
      }
    } else {
      console.warn('Attempted to save null user to localStorage');
    }
  }

  // Load user state from localStorage
  function loadUserFromLocalStorage() {
    console.log('Attempting to load user from localStorage');
    const savedUser = localStorage.getItem(AUTH_USER_KEY);

    if (savedUser) {
      console.log('Found saved user data in localStorage');
      try {
        state.value.user = JSON.parse(savedUser);
        console.log('Successfully loaded user from localStorage:', {
          email: state.value.user?.email,
          role: state.value.user?.currentRole,
          company: state.value.user?.currentCompany?.name
        });
        return true;
      } catch (e) {
        console.error('Failed to parse saved user data:', e);
        localStorage.removeItem(AUTH_USER_KEY);
      }
    } else {
      console.log('No saved user found in localStorage');
    }
    return false;
  }

  // Clear user state from localStorage
  function clearUserFromLocalStorage() {
    console.log('Clearing user data from localStorage');
    localStorage.removeItem(AUTH_USER_KEY);
  }

  const isAuthenticated = computed(() => !!state.value.user)
  const hasMultipleCompanies = computed(() =>
    state.value.user?.companies && state.value.user.companies.length > 1 || false
  )
  const hasMultipleRoles = computed(() =>
    state.value.user?.roles && state.value.user.roles.length > 1 || false
  )

  async function login(email: string, password: string) {
    try {
      state.value.loading = true
      state.value.error = null

      // Proceed with authentication first
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (error) throw new Error(`Authentication failed: ${error.message}`)

      // Now check if the email is in the allowed_emails table
      const { data: allowedEmail, error: allowedEmailError } = await supabase
        .from('allowed_emails')
        .select('email')
        .eq('email', email)
        .limit(1);

      if (allowedEmailError) throw allowedEmailError;

      if (!allowedEmail || allowedEmail.length === 0) {
        // Sign out if not authorized
        await supabase.auth.signOut();
        throw new Error('Access denied. Your email is not authorized to use this application.');
      }

      // Fetch user data from auth.users
      const { data: authUser, error: authUserError } = await supabase.auth.getUser();

      if (authUserError) throw new Error(`Failed to get user data: ${authUserError.message}`);

      // Since we don't have access to the users table in the new database,
      // we'll create a minimal user object with the data from auth.users
      const userData = {
        id: authUser.user.id,
        email: authUser.user.email,
        first_name: authUser.user.user_metadata?.first_name || '',
        last_name: authUser.user.user_metadata?.last_name || ''
      };

      // No need to check for userError since we're creating userData manually

      // Fetch user's app_user_id from the app_users table
      const { data: appUserData, error: appUserError } = await supabase
        .from('app_users')
        .select('id, first_name, last_name, is_active')
        .eq('user_id', authUser.user.id)
        .single();

      if (appUserError) {
        console.error('Error fetching app_user data:', appUserError);
        throw new Error('Failed to fetch user data');
      }

      if (!appUserData) {
        throw new Error('User not found in app_users table');
      }

      // Fetch user's roles and companies from the database
      const { data: userPermissions, error: userPermissionsError } = await supabase
        .from('user_roles_view')
        .select('*')
        .eq('user_id', authUser.user.id);

      if (userPermissionsError) {
        console.error('Error fetching user permissions:', userPermissionsError);
        throw new Error('Failed to fetch user permissions');
      }

      // Process the permissions data into the format we need
      const userRoles = userPermissions
        .filter((permission: { role_id: number | null, cur_company_id: number | null }) =>
          permission.role_id !== null && permission.cur_company_id !== null)
        .map((permission: {
          role_id: number,
          role_name: string | null,
          cur_company_id: number,
          company_name: string | null
        }) => ({
          role: {
            id: permission.role_id.toString(),
            name: permission.role_name || ''
          },
          company: {
            id: permission.cur_company_id.toString(),
            name: permission.company_name || ''
          }
        }));

      // Update userData with app_user data
      userData.first_name = appUserData.first_name || '';
      userData.last_name = appUserData.last_name || '';

      console.log('User roles from database:', userRoles);

      // Process companies and roles
      const companies: Company[] = [];
      const roles: UserRole[] = [];
      const companyMap = new Map<string, boolean>();
      const roleMap = new Map<string, boolean>();

      // console.log('User roles from database:', userRoles);

      userRoles.forEach((userRole: {
        role: { id: string, name: string },
        company: { id: string, name: string }
      }) => {
        // Add company if not already added
        if (userRole.company && !companyMap.has(userRole.company.id)) {
          companyMap.set(userRole.company.id, true);
          companies.push({
            id: userRole.company.id,
            name: userRole.company.name
          });
          // console.log('Added company:', userRole.company);
        }

        // Add role if not already added and it's a valid role
        if (userRole.role && userRole.role.name && !roleMap.has(userRole.role.name)) {
          roleMap.set(userRole.role.name, true);
          if (
            userRole.role.name === 'designer' ||
            userRole.role.name === 'admin' ||
            userRole.role.name === 'super-admin'
          ) {
            roles.push(userRole.role.name as UserRole);
            // console.log('Added role:', userRole.role.name);
          }
        }
      });

      // console.log('Processed companies:', companies);
      // console.log('Processed roles:', roles);

      // Create user object
      if (state.value.user === null) {
        state.value.user = {
          id: userData.id,
          email: email, // Use the email from login since it's not in app_users
          firstName: userData.first_name,
          lastName: userData.last_name,
          companies: companies,
          roles: roles,
          appUserId: appUserData.id // Store the app_user.id (integer)
        }

        console.log('Created user object with appUserId:', appUserData.id);

        // If user has only one company and role, set them automatically
        if (companies.length === 1 && companies[0]) {
          state.value.user.currentCompany = companies[0]
        }
        if (roles.length === 1 && roles[0]) {
          state.value.user.currentRole = roles[0]
        }

        // Save user data to localStorage
        saveUserToLocalStorage();
      }
    } catch (error) {
      state.value.error = error instanceof Error ? error.message : 'An error occurred during login'
    } finally {
      state.value.loading = false
    }
  }

  async function logout() {
    const { error } = await supabase.auth.signOut()
    if (!error) {
      state.value.user = null
      // Clear user data from localStorage
      clearUserFromLocalStorage();
    }
    return error
  }

  function setCurrentCompany(company: Company) {
    // console.log('Setting current company:', company);
    if (state.value.user) {
      state.value.user.currentCompany = company;
      // Save updated user data to localStorage
      saveUserToLocalStorage();
      // console.log('User state after setting company:', state.value.user);
    }
  }

  function setCurrentRole(role: UserRole) {
    // console.log('Setting current role:', role);
    if (state.value.user) {
      state.value.user.currentRole = role;
      // Save updated user data to localStorage
      saveUserToLocalStorage();
      // console.log('User state after setting role:', state.value.user);
    }
  }

  // Check Supabase session status
  async function checkSession() {
    console.log('Checking Supabase session status...');
    try {
      const { data, error } = await supabase.auth.getSession();

      if (error) {
        console.error('Error checking session:', error);
        return null;
      }

      console.log('Session check result:', data?.session ? 'Active session' : 'No session');
      return data?.session;
    } catch (error) {
      console.error('Exception checking session:', error);
      return null;
    }
  }

  // Fetch user data from database
  async function fetchUserData(userId: string) {
    console.log('Fetching user data for ID:', userId);
    try {
      // Fetch user data from auth.users
      const { data: authUser, error: authUserError } = await supabase.auth.getUser();

      if (authUserError) {
        console.error('Error fetching auth user:', authUserError);
        return false;
      }

      // Since we don't have access to the users table in the new database,
      // we'll create a minimal user object with the data from auth.users
      const userData = {
        id: authUser.user.id,
        email: authUser.user.email,
        first_name: authUser.user.user_metadata?.first_name || '',
        last_name: authUser.user.user_metadata?.last_name || ''
      };

      // No need to check for userError since we're creating userData manually

      // For testing, let's create user roles based on the auth user
      // In a real implementation, you would fetch this from the database
      let userRoles = [];

      // Get the email from the auth user
      const email = authUser.user.email || '';

      // <EMAIL> - super-admin for Dreambox
      if (email === '<EMAIL>') {
        userRoles = [
          {
            role: { id: '1', name: 'super-admin' },
            company: { id: '1', name: 'Dreambox' }
          }
        ];

        // Super-admins can access all companies
        const allCompanies = [
          { id: '1', name: 'Dreambox' },
          { id: '2', name: 'Lidija' },
          { id: '3', name: 'Mirjan' },
          { id: '4', name: 'Karmen' },
          { id: '5', name: 'Laura' },
          { id: '6', name: 'Karla' },
          { id: '7', name: 'Hana' }
        ];

        // Add access to all companies for super-admin
        allCompanies.forEach(company => {
          if (company.id !== '1') { // Skip Dreambox as it's already added
            userRoles.push({
              role: { id: '1', name: 'super-admin' },
              company: company
            });
          }
        });
      }
      // <EMAIL> - admin and designer for Dreambox and Lidija
      else if (email === '<EMAIL>') {
        userRoles = [
          {
            role: { id: '2', name: 'admin' },
            company: { id: '1', name: 'Dreambox' }
          },
          {
            role: { id: '3', name: 'designer' },
            company: { id: '1', name: 'Dreambox' }
          },
          {
            role: { id: '2', name: 'admin' },
            company: { id: '2', name: 'Lidija' }
          },
          {
            role: { id: '3', name: 'designer' },
            company: { id: '2', name: 'Lidija' }
          }
        ];
      }
      // Default case - just give them a designer role for testing
      else {
        userRoles = [
          {
            role: { id: '3', name: 'designer' },
            company: { id: '1', name: 'Dreambox' }
          }
        ];
      }

      // No need to check for userRolesError since we're creating userRoles manually

      // Process companies and roles
      const companies: Company[] = [];
      const roles: UserRole[] = [];
      const companyMap = new Map<string, boolean>();
      const roleMap = new Map<string, boolean>();

      userRoles.forEach(userRole => {
        // Add company if not already added
        if (userRole.company && !companyMap.has(userRole.company.id)) {
          companyMap.set(userRole.company.id, true);
          companies.push({
            id: userRole.company.id,
            name: userRole.company.name
          });
        }

        // Add role if not already added and it's a valid role
        if (userRole.role && !roleMap.has(userRole.role.name)) {
          roleMap.set(userRole.role.name, true);
          if (
            userRole.role.name === 'designer' ||
            userRole.role.name === 'admin' ||
            userRole.role.name === 'super-admin'
          ) {
            roles.push(userRole.role.name as UserRole);
          }
        }
      });

      // Fetch user's app_user_id from the app_users table
      const { data: appUserData, error: appUserError } = await supabase
        .from('app_users')
        .select('id, first_name, last_name, is_active')
        .eq('user_id', userData.id)
        .single();

      if (appUserError) {
        console.error('Error fetching app_user data:', appUserError);
        return false;
      }

      if (!appUserData) {
        console.error('User not found in app_users table');
        return false;
      }

      // Create user object
      state.value.user = {
        id: userData.id,
        email: userData.email || '',  // Ensure email is never undefined
        firstName: userData.first_name,
        lastName: userData.last_name,
        companies: companies,
        roles: roles,
        appUserId: appUserData.id // Store the app_user.id (integer)
      }

      console.log('fetchUserData: Created user object with appUserId:', appUserData.id);

      // If user has only one company and role, set them automatically
      if (state.value.user && companies.length === 1 && companies[0]) {
        state.value.user.currentCompany = companies[0]
      }
      if (state.value.user && roles.length === 1 && roles[0]) {
        state.value.user.currentRole = roles[0]
      }

      // Save user data to localStorage
      saveUserToLocalStorage();
      return true;
    } catch (error) {
      console.error('Exception fetching user data:', error);
      return false;
    }
  }

  // Initialize auth state from localStorage and session
  async function initAuth() {
    console.log('Initializing auth state...');
    try {
      state.value.loading = true;

      // First try to load from localStorage for immediate UI response
      const loadedFromStorage = loadUserFromLocalStorage();

      // Log the user object to see its structure
      console.log('User object from localStorage:', state.value.user);

      // Then check if we have a valid session
      const session = await checkSession();

      if (session) {
        console.log('Active session found with user ID:', session.user.id);

        // If we couldn't load from localStorage or user data doesn't match session user
        if (!loadedFromStorage || state.value.user?.id !== session.user.id) {
          console.log('Need to fetch fresh user data from database');
          await fetchUserData(session.user.id);
        } else {
          console.log('Using user data from localStorage');
        }
      } else {
        console.log('No active session found, clearing auth state');
        state.value.user = null;
        clearUserFromLocalStorage();
      }
    } catch (error) {
      console.error('Error initializing auth:', error);
    } finally {
      state.value.loading = false;
    }
  }

  // Call initAuth when the store is created
  void initAuth(); // void operator to explicitly mark promise as intentionally not awaited

  // Helper function to safely get the user
  function getUser() {
    return state.value.user;
  }

  return {
    state,
    isAuthenticated,
    hasMultipleCompanies,
    hasMultipleRoles,
    login,
    logout,
    setCurrentCompany,
    setCurrentRole,
    initAuth, // Export this so it can be called manually if needed
    checkSession,
    fetchUserData,
    getUser // Add a safe way to get the user
  }
})
