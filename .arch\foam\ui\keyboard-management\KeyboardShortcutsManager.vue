<template>
  <div class="keyboard-shortcuts-manager">
    <div class="q-mb-md">
      <div class="text-h6">{{ $t('settings.shortcuts.title') }}</div>
      <p>{{ $t('settings.shortcuts.description') }}</p>
      
      <q-btn 
        color="primary" 
        :label="$t('settings.shortcuts.resetDefaults')" 
        icon="restore"
        @click="confirmReset"
        class="q-mb-md"
      />
    </div>
    
    <q-list bordered separator>
      <q-item v-for="shortcut in shortcuts" :key="shortcut.id">
        <q-item-section>
          <q-item-label>{{ shortcut.description }}</q-item-label>
          <q-item-label caption>{{ shortcut.command }}</q-item-label>
        </q-item-section>
        
        <q-item-section side>
          <shortcut-recorder 
            v-model="shortcut.keys" 
            :exclude-id="shortcut.id"
            @update:model-value="updateShortcut(shortcut.id, $event)"
          />
        </q-item-section>
      </q-item>
    </q-list>
    
    <q-dialog v-model="resetConfirmDialog">
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar icon="warning" color="warning" text-color="white" />
          <span class="q-ml-sm">{{ $t('settings.shortcuts.resetConfirm') }}</span>
        </q-card-section>
        
        <q-card-actions align="right">
          <q-btn flat :label="$t('common.cancel')" color="primary" v-close-popup />
          <q-btn flat :label="$t('common.confirm')" color="negative" @click="resetToDefaults" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useKeyboardShortcutsStore } from 'src/stores/keyboardShortcutsStore';
import ShortcutRecorder from './ShortcutRecorder.vue';

const shortcutsStore = useKeyboardShortcutsStore();
const shortcuts = computed(() => shortcutsStore.shortcuts);
const resetConfirmDialog = ref(false);

// Update a shortcut
function updateShortcut(id: string, keys: string) {
  shortcutsStore.updateShortcut(id, { keys });
}

// Show reset confirmation dialog
function confirmReset() {
  resetConfirmDialog.value = true;
}

// Reset shortcuts to defaults
function resetToDefaults() {
  shortcutsStore.resetToDefaults();
}
</script>

<style lang="scss" scoped>
.keyboard-shortcuts-manager {
  max-width: 800px;
  margin: 0 auto;
}
</style>
