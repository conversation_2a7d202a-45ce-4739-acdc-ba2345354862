#!/bin/bash
# SwarmUI Backend Verification Script
# This script verifies that SwarmUI is properly configured with ComfyUI as a backend

# Check if API key is provided
if [ -z "$1" ]; then
  echo "Usage: $0 <api_key>"
  echo "Please provide the SwarmUI API key as an argument"
  exit 1
fi

API_KEY=$1
API_URL="http://localhost:7801/api"

# Function to check if a process is running
check_process() {
  pgrep -f "$1" > /dev/null
  return $?
}

# Check if ComfyUI is running
echo "Checking if ComfyUI is running..."
if check_process "python.*ComfyUI/main.py"; then
  echo "✅ ComfyUI is running"
else
  echo "❌ ComfyUI is not running"
  echo "Check the ComfyUI log: cat /workspace/comfyui.log"
  exit 1
fi

# Check if SwarmUI is running
echo "Checking if SwarmUI is running..."
if check_process "SwarmUI"; then
  echo "✅ SwarmUI is running"
else
  echo "❌ SwarmUI is not running"
  echo "Check the SwarmUI log: cat /workspace/SwarmUI/logs/swarm.log"
  exit 1
fi

# Check if SwarmUI API is accessible
echo "Checking if SwarmUI API is accessible..."
response=$(curl -s -o /dev/null -w "%{http_code}" -H "Authorization: Bearer $API_KEY" "$API_URL/info")
if [ "$response" -eq 200 ]; then
  echo "✅ SwarmUI API is accessible"
else
  echo "❌ SwarmUI API returned status code: $response"
  echo "Check the API key and make sure SwarmUI is properly configured"
  exit 1
fi

# Check if models are available
echo "Checking if models are available..."
models_response=$(curl -s -H "Authorization: Bearer $API_KEY" "$API_URL/models")
if [[ $models_response == *"models"* ]]; then
  echo "✅ Models are available"
  # Extract and display model names
  echo "Available models:"
  echo "$models_response" | grep -o '"name":"[^"]*"' | sed 's/"name":"//g' | sed 's/"//g' | sed 's/^/  - /g'
else
  echo "❌ No models found"
  echo "Check the models directory and SwarmUI configuration"
  exit 1
fi

# Check if backend is configured
echo "Checking if backend is configured..."
backend_response=$(curl -s -H "Authorization: Bearer $API_KEY" "$API_URL/backend/status")
if [[ $backend_response == *"ComfyUI"* ]]; then
  echo "✅ Backend is configured (ComfyUI)"
else
  echo "❌ Backend is not properly configured"
  echo "Response: $backend_response"
  echo "Check the SwarmUI configuration"
  exit 1
fi

# Test a simple generation
echo "Testing a simple image generation..."
generation_request='{
  "prompt": "test image",
  "negative_prompt": "ugly, deformed",
  "model": "Flux Schnell",
  "width": 512,
  "height": 512,
  "steps": 10,
  "cfg_scale": 7.0,
  "sampler": "dpmpp_2m",
  "batch_size": 1
}'

generation_response=$(curl -s -X POST -H "Authorization: Bearer $API_KEY" -H "Content-Type: application/json" -d "$generation_request" "$API_URL/generations")
if [[ $generation_response == *"id"* ]]; then
  generation_id=$(echo "$generation_response" | grep -o '"id":"[^"]*"' | head -1 | sed 's/"id":"//g' | sed 's/"//g')
  echo "✅ Generation started with ID: $generation_id"
  echo "You can check the status with:"
  echo "curl -H \"Authorization: Bearer $API_KEY\" \"$API_URL/generations/$generation_id\""
else
  echo "❌ Failed to start generation"
  echo "Response: $generation_response"
  echo "Check the SwarmUI and ComfyUI logs"
  exit 1
fi

echo ""
echo "✅ All checks passed! SwarmUI is properly configured with ComfyUI as a backend."
echo "You can now use the SwarmUI API for image generation."
