<template>
  <component
    v-if="resolvedComponent"
    :is="resolvedComponent"
    v-bind="props"
  />
  <div v-else class="text-grey q-pa-md">
    <q-icon name="warning" color="warning" />
    Component not found: {{ props.name }}
  </div>
</template>

<script setup lang="ts">
import { computed, defineAsyncComponent, type Component } from 'vue';
import { getComponent } from 'src/boot/componentRegistry';

// Direct component imports as fallback
const directComponents: Record<string, Component> = {
  'FilterPanel': defineAsyncComponent(() => import('src/lib/filters/components/FilterPanel.vue')),
  'TableFilterPanel': defineAsyncComponent(() => import('src/components/filters/TableFilterPanel.vue')),
  'SearchBar': defineAsyncComponent(() => import('src/components/search/SearchBar.vue')),
  'ViewToggle': defineAsyncComponent(() => import('src/components/common/ViewToggle.vue')),
  'ActionButton': defineAsyncComponent(() => import('src/components/common/ActionButton.vue')),
  'ActionButtonsContainer': defineAsyncComponent(() => import('src/components/common/ActionButtonsContainer.vue')),
  'ActionButtonsExpanded': defineAsyncComponent(() => import('src/components/common/ActionButtonsExpanded.vue')),
  'PromptElementsPanel': defineAsyncComponent(() => import('src/components/templates/TemplateBuilderTools.vue')),
  'ElementValuesEditor': defineAsyncComponent(() => import('src/components/templates/TemplateBuilderSettings.vue')),
  'TemplatePreview': defineAsyncComponent(() => import('src/components/templates/TemplateBuilderPreview.vue')),
  'ChatAssistant': defineAsyncComponent(() => import('src/components/chat/ChatAssistant.vue')),
};

const props = defineProps<{
  name: string;
  [key: string]: unknown;
}>();

// Resolve the component
const resolvedComponent = computed(() => {
  console.log(`Resolving component: ${props.name}`, props);

  // First try to get from registry
  const registryComponent = getComponent(props.name);
  if (registryComponent) {
    console.log(`Component resolved from registry: ${props.name}`);
    return registryComponent;
  }

  // If not found in registry, try direct import
  if (props.name in directComponents) {
    console.log(`Component resolved from direct import: ${props.name}`);
    return directComponents[props.name];
  }

  console.log(`Component not found: ${props.name}`);
  return undefined;
});
</script>
