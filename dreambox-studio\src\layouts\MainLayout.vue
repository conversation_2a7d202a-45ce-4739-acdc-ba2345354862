<template>
  <q-layout view="hHh lpR fFf" :class="platformClass">
    <q-header elevated class="bg-primary text-white" :style="{ height: 'auto' }">
      <q-toolbar>
        <!-- Left side of toolbar -->
        <q-btn
          flat
          round
          dense
          icon="menu"
          aria-label="Menu"
          @click="toggleLeftDrawer"
        />

        <!-- App icon and title on the left (hidden on mobile) -->
        <q-toolbar-title :style="`padding: 0; min-height: 0; margin-left: ${$q.screen.lt.sm ? '12px' : '50px'};`" class="gt-xs">
          <q-avatar size="24px" class="q-mr-sm">
            <img src="https://cdn.quasar.dev/logo-v2/svg/logo-mono-white.svg">
          </q-avatar>
          <span>{{ $t('app.name') }}</span>
        </q-toolbar-title>

        <q-space />

        <!-- Right side of toolbar -->
        <q-space />

        <div class="row no-wrap items-center">
          <!-- Dynamic top bar components -->
          <!-- Top bar components removed for now -->
          <!-- <template v-for="comp in contextStore.topBarComponents" :key="comp.id">
            <dynamic-component :name="comp.component ? comp.component : ''" v-bind="comp.props || {}" />
          </template> -->

          <!-- Account button removed from top bar and moved to left drawer -->

          <!-- Right drawer toggle button -->
          <q-btn
            flat
            round
            dense
            icon="menu"
            color="white"
            aria-label="Toggle Right Drawer"
            @click="toggleRightDrawer"
            :style="$q.screen.lt.sm ? 'margin-left: 4px;' : 'margin-left: 50px;'"
          >
            <q-tooltip>{{ rightDrawerOpen ? 'Hide Drawer' : 'Show Drawer' }}</q-tooltip>
          </q-btn>


        </div>
      </q-toolbar>

      <!-- Tabs removed to save screen space - will be added to central area if needed -->
      <!-- Expanded header content removed - search is now directly in the toolbar -->
    </q-header>

    <q-drawer
      v-model="leftDrawerOpen"
      :behavior="drawerBehavior"
      show-if-above
      bordered
      :width="leftDrawerWidth"
      :breakpoint="768"
      side="left"
      class="drawer-with-tabs"
      content-class="no-scroll"
      :mini="false"
    >
      <!-- Drawer tabs - icon only for space efficiency -->
      <q-tabs
        v-model="activeLeftDrawerTab"
        dense
        class="bg-primary text-white drawer-tabs drawer-tabs-height"
        indicator-color="secondary"
        narrow-indicator
      >
        <!-- Main navigation tab - always present -->
        <q-tab name="nav" icon="home" @click="setLeftDrawerTab('nav')" />

        <!-- Context-specific tabs - shown based on current content -->
        <q-tab v-if="contextStore.leftDrawerTabs.filters" name="filters" icon="filter_list" @click="setLeftDrawerTab('filters')" />
        <q-tab v-if="contextStore.leftDrawerTabs.tools" name="tools" icon="build" @click="setLeftDrawerTab('tools')" />
        <q-tab v-if="contextStore.leftDrawerTabs.elements" name="elements" icon="widgets" @click="setLeftDrawerTab('elements')" />

        <!-- Dynamic custom tabs -->
        <!-- Custom tabs are now handled by the context manager -->
      </q-tabs>

      <!-- Tab panels with QScrollArea for independent scrolling -->
      <q-tab-panels
        v-model="activeLeftDrawerTab"
        animated
        transition-prev="slide-right"
        transition-next="slide-left"
        class="drawer-panels"
        style="height: calc(100% - 50px); overflow: hidden;"
      >
        <!-- Navigation Panel -->
        <q-tab-panel name="nav" class="q-pa-none">
          <q-scroll-area class="fit">
            <!-- Navigation tree first -->
            <tree-navigation
              :title="$t('nav.title')"
              :items="navigationItems"
              @navigate="navigateTo"
            />

            <q-separator spaced class="q-my-md" />

            <!-- User account section below navigation -->
            <q-list padding>
              <q-item-label header>{{ $t('settings.account') }}</q-item-label>

              <q-item clickable @click="navigateToProfile">
                <q-item-section avatar>
                  <q-avatar>
                    <img :src="milanAvatar" />
                  </q-avatar>
                </q-item-section>
                <q-item-section>
                  <q-item-label class="user-email">{{ authStore.state.user?.email || 'User' }}</q-item-label>
                  <q-item-label caption>{{ $t('nav.profile') }}</q-item-label>
                </q-item-section>
              </q-item>

              <!-- Settings Link -->
              <q-item clickable @click="navigateToSettings">
                <q-item-section avatar>
                  <q-icon name="settings" />
                </q-item-section>
                <q-item-section>{{ $t('nav.settings') }}</q-item-section>
              </q-item>

              <!-- Device Type Selector -->
              <q-item>
                <q-item-section avatar>
                  <q-icon :name="getDeviceIcon()" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ $t('nav.deviceType') }}</q-item-label>
                  <q-item-label caption>{{ getDeviceTypeName() }}</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-btn-dropdown flat dense>
                    <template v-slot:label>
                      <q-icon name="swap_horiz" size="xs" />
                    </template>
                    <q-list>
                      <q-item clickable @click="setDeviceType(DeviceType.DESKTOP_BROWSER)">
                        <q-item-section>Desktop Browser</q-item-section>
                      </q-item>
                      <q-item clickable @click="setDeviceType(DeviceType.MOBILE_BROWSER)">
                        <q-item-section>Mobile Browser</q-item-section>
                      </q-item>
                      <q-item clickable @click="setDeviceType(DeviceType.DESKTOP_ELECTRON)">
                        <q-item-section>Electron App</q-item-section>
                      </q-item>
                    </q-list>
                  </q-btn-dropdown>
                </q-item-section>
              </q-item>

              <!-- Logout Button -->
              <q-item clickable @click="logout">
                <q-item-section avatar>
                  <q-icon name="logout" />
                </q-item-section>
                <q-item-section>{{ $t('nav.logout') }}</q-item-section>
              </q-item>
            </q-list>
          </q-scroll-area>
        </q-tab-panel>

        <!-- Filters Panel -->
        <q-tab-panel name="filters" class="q-pa-none">
          <q-scroll-area class="fit">
            <template v-for="comp in getComponentsForTab('filters')" :key="comp.id">
              <dynamic-component :name="comp.component" v-bind="comp.props" />
            </template>
          </q-scroll-area>
        </q-tab-panel>

        <!-- Tools Panel -->
        <q-tab-panel name="tools" class="q-pa-none">
          <q-scroll-area class="fit">
            <template v-for="comp in getComponentsForTab('tools')" :key="comp.id">
              <dynamic-component :name="comp.component" v-bind="comp.props" />
            </template>
          </q-scroll-area>
        </q-tab-panel>

        <!-- Elements Panel -->
        <q-tab-panel name="elements" class="q-pa-none">
          <q-scroll-area class="fit">
            <template v-for="comp in getComponentsForTab('elements')" :key="comp.id">
              <dynamic-component :name="comp.component" v-bind="comp.props" />
            </template>
          </q-scroll-area>
        </q-tab-panel>
      </q-tab-panels>

      <DrawerResizer side="left" :min-width="200" :max-width="leftDrawerMaxWidth" @resize="resizeLeftDrawer" />
    </q-drawer>

    <q-drawer
      v-model="rightDrawerOpen"
      :behavior="drawerBehavior"
      bordered
      :width="rightDrawerWidth"
      side="right"
      content-class="no-scroll"
      class="drawer-with-tabs"
    >
      <!-- Drawer tabs - icon only for consistency with left drawer -->
      <q-tabs
        v-model="activeRightDrawerTab"
        dense
        class="bg-primary text-white drawer-tabs drawer-tabs-height"
        indicator-color="secondary"
        narrow-indicator
      >
        <q-tab v-if="contextStore.rightDrawerTabs.chat" name="chat" icon="chat" @click="activeRightDrawerTab = 'chat'">
          <q-tooltip>Chat</q-tooltip>
        </q-tab>
        <q-tab v-if="contextStore.rightDrawerTabs.preview" name="preview" icon="visibility" @click="activeRightDrawerTab = 'preview'">
          <q-tooltip>Preview</q-tooltip>
        </q-tab>
        <q-tab v-if="contextStore.rightDrawerTabs.settings" name="settings" icon="settings" @click="activeRightDrawerTab = 'settings'">
          <q-tooltip>Settings</q-tooltip>
        </q-tab>
        <q-tab v-if="contextStore.rightDrawerTabs.values" name="values" icon="edit" @click="activeRightDrawerTab = 'values'">
          <q-tooltip>Values</q-tooltip>
        </q-tab>

        <!-- Dynamic custom tabs -->
        <!-- Custom tabs are now handled by the context manager -->
      </q-tabs>

      <!-- Tab panels with QScrollArea for independent scrolling -->
      <q-tab-panels
        v-model="activeRightDrawerTab"
        animated
        transition-prev="slide-right"
        transition-next="slide-left"
        class="drawer-panels"
        style="height: calc(100% - 50px); overflow: hidden;"
      >
        <!-- Chat Panel with Voice Commands -->
        <q-tab-panel name="chat" class="q-pa-none full-height chat-tab-panel">
          <!-- Unified Chat & Voice Commands Section -->
          <div class="chat-container">
            <!-- Messages area with scroll -->
            <div class="chat-messages-scroll">
              <div class="chat-messages" ref="chatMessagesContainer">
                <q-chat-message
                  v-for="(message, index) in chatMessages"
                  :key="index"
                  :name="message.name"
                  :avatar="message.avatar"
                  :text="message.text"
                  :sent="message.sent"
                />
              </div>
            </div>

            <!-- Fixed input at the bottom -->
            <div class="chat-input">
              <q-select
                    ref="chatInput"
                    v-model="commandText"
                    :options="filteredCommands"
                    :placeholder="$t('voiceCommands.enterCommand')"
                    outlined
                    dense
                    use-input
                    hide-selected
                    fill-input
                    input-debounce="0"
                    @filter="filterCommands"
                    @keyup.enter="executeCommand"
                    class="command-select"
                    popup-content-class="command-dropdown custom-scrollbar"
                    :bg-color="$q.dark.isActive ? 'grey-9' : 'white'"
                    :color="$q.dark.isActive ? 'white' : 'primary'"
                    :input-style="{ color: $q.dark.isActive ? 'white' : 'black' }"
                  >
                    <template v-slot:no-option>
                      <q-item>
                        <q-item-section class="text-grey">
                          {{ $t('voiceCommands.noCommandDetected') }}
                        </q-item-section>
                      </q-item>
                    </template>

                    <template v-slot:option="scope">
                      <q-item v-bind="scope.itemProps">
                        <q-item-section>
                          <q-item-label>{{ scope.opt }}</q-item-label>
                          <q-item-label caption v-if="getCommandDescription(scope.opt)">
                            {{ getCommandDescription(scope.opt) }}
                          </q-item-label>
                          <q-item-label caption v-if="getCommandExample(scope.opt)">
                            {{ $t('voiceCommands.examples') }}: "{{ getCommandExample(scope.opt) }}"
                          </q-item-label>
                        </q-item-section>
                      </q-item>
                    </template>

                    <template v-slot:after>
                      <q-btn
                        round
                        dense
                        flat
                        :icon="isListening ? 'mic_off' : 'mic'"
                        :color="isListening ? 'negative' : ($q.dark.isActive ? 'white' : 'primary')"
                        @click="simulateVoiceInput"
                      />
                      <q-btn
                        round
                        dense
                        flat
                        :color="$q.dark.isActive ? 'white' : 'primary'"
                        icon="send"
                        @click="executeCommand"
                      />
                    </template>
                  </q-select>
                </div>
              </div>
            </q-tab-panel>

        <!-- Preview Panel -->
        <q-tab-panel name="preview" class="q-pa-none">
          <q-scroll-area class="fit">
            <template v-for="comp in getComponentsForTab('preview')" :key="comp.id">
              <dynamic-component :name="comp.component" v-bind="comp.props" />
            </template>
          </q-scroll-area>
        </q-tab-panel>

        <!-- Settings Panel -->
        <q-tab-panel name="settings" class="q-pa-none">
          <q-scroll-area class="fit">
            <template v-for="comp in getComponentsForTab('settings')" :key="comp.id">
              <dynamic-component :name="comp.component" v-bind="comp.props" />
            </template>
          </q-scroll-area>
        </q-tab-panel>

        <!-- Values Panel -->
        <q-tab-panel name="values" class="q-pa-none">
          <q-scroll-area class="fit">
            <template v-for="comp in getComponentsForTab('values')" :key="comp.id">
              <dynamic-component :name="comp.component" v-bind="comp.props" />
            </template>
          </q-scroll-area>
        </q-tab-panel>
      </q-tab-panels>

      <DrawerResizer side="right" :min-width="200" :max-width="rightDrawerMaxWidth" @resize="resizeRightDrawer" />
    </q-drawer>

    <q-page-container>
      <q-scroll-area :style="{ height: 'calc(100vh - 50px)' }">
        <router-view />
      </q-scroll-area>
    </q-page-container>

    <!-- Bottom bar removed -->

    <!-- Voice Command Panel moved to Chat tab in right drawer -->
  </q-layout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, inject, onUnmounted, watch, reactive, provide, nextTick } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useQuasar, QSelect } from 'quasar';
import { useI18n } from 'vue-i18n';
import { useAuthStore } from 'src/stores/auth';
import { useNavigationStore } from 'src/stores/navigationStore';
// Search store is now used directly in the SearchBar component
// import { useSearchStore } from 'src/stores/searchStore';
import { useContextStore } from 'src/stores/contextStore';
import { notify } from 'src/boot/notifications';
import { DeviceType } from 'src/types/deviceTypes';
import type { DeviceTypeValue } from 'src/types/deviceTypes';
import { useKeyboardShortcuts } from 'src/composables/useKeyboardShortcuts';
import type { CommandResult } from 'src/services/commands/types';
import PlatformUtil from 'src/utils/platform';
import { useDrawerResize } from 'src/composables/useDrawerResize';
import { useLocalStorage } from '@vueuse/core';
import { commandRegistry } from 'src/services/commands/registry';
import { mockWebSocket } from 'src/services/commands/mock-websocket';
import DrawerResizer from 'src/components/common/DrawerResizer.vue';
import TreeNavigation from 'src/components/navigation/TreeNavigation.vue';
import DynamicComponent from 'src/components/common/DynamicComponent.vue';
// GlobalSearch component removed as we're using direct search input
// import { clearAppCache } from '../utils/cache-cleaner'; // No longer needed
// Template components are now loaded dynamically via the component registry

// Import images
import milanAvatar from '../assets/milan.jpeg';
import robotAvatar from '../assets/robot-thumbnail.png';

const $q = useQuasar();
const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();
// Search store is now used directly in the SearchBar component
const contextStore = useContextStore();
const { t } = useI18n();

// Drawer states
const leftDrawerOpen = ref(false);
const rightDrawerOpen = ref(false);

// Use the drawer resize composable with custom constraints
// For the right drawer, allow it to be resized to at least half the screen width
const { leftDrawerWidth, rightDrawerWidth, resizeLeftDrawer, resizeRightDrawer } = useDrawerResize(
  PlatformUtil.isMobile() ? 280 : 300, // default width
  200,  // left min width
  500,  // left max width
  200,  // right min width
  800   // right max width (will be dynamically adjusted to at least half screen width)
);

// Left drawer tabs state
const activeLeftDrawerTab = ref('nav'); // Default to navigation tab
// Filter and tool tabs visibility now controlled by context manager

// Right drawer tabs state
const activeRightDrawerTab = ref('chat'); // Default to chat tab

// Chat messages for voice commands
const chatMessages = ref([
  {
    name: 'Assistant',
    text: ['How can I help you?'],
    sent: true,
    avatar: robotAvatar // Robot avatar
  }
]);

// Reference to chat messages container for scrolling
const chatMessagesContainer = ref<HTMLElement | null>(null);

// Reference to chat input for focusing
const chatInput = ref<QSelect | null>(null);

// Function to scroll chat to bottom
function scrollToBottom() {
  void nextTick(() => {
    const scrollArea = document.querySelector('.chat-messages-scroll');
    if (scrollArea) {
      scrollArea.scrollTop = scrollArea.scrollHeight;
    }
  });
}

// Watch for changes to chat messages and scroll to bottom
watch(chatMessages, () => {
  scrollToBottom();
}, { deep: true });

// Voice command functionality
const commandText = ref('');
const lastResult = ref<null | Record<string, unknown>>(null);
const availableCommands = ref<Array<{
  name: string;
  description: string;
  parameters?: Array<{
    name: string;
    type: string;
    required: boolean;
    description: string;
  }>;
  examples?: string[];
}>>([]);

// Filtered commands for the dropdown
const filteredCommands = ref<string[]>([]);
const isListening = ref(false);
const commandHistory = ref<string[]>([]);
const historyIndex = ref(-1);

// Filter commands based on user input
function filterCommands(val: string, update: (callback: () => void) => void) {
  update(() => {
    if (val === '') {
      // If no search text, show all commands
      filteredCommands.value = availableCommands.value.map(cmd => cmd.name);
    } else {
      // Filter commands based on search text
      const needle = val.toLowerCase();
      filteredCommands.value = availableCommands.value
        .filter(cmd => {
          // Check command name
          if (cmd.name.toLowerCase().includes(needle)) return true;

          // Check normalized name
          if (normalizeCommandName(cmd.name).includes(needle)) return true;

          // Check examples
          if (cmd.examples && cmd.examples.some(ex => ex.toLowerCase().includes(needle))) return true;

          // Check aliases
          const aliasMatch = Object.entries(commandAliases)
            .some(([alias, cmdName]) => alias.includes(needle) && cmdName === cmd.name);
          if (aliasMatch) return true;

          return false;
        })
        .map(cmd => cmd.name);
    }
  });
}

// Refresh available commands
function refreshAvailableCommands() {
  availableCommands.value = commandRegistry.getAvailableCommands() as typeof availableCommands.value;
  filteredCommands.value = availableCommands.value.map(cmd => cmd.name);
  // console.log('Refreshed available commands:', availableCommands.value);
}

// Helper functions to get command details
function getCommandDescription(commandName: string): string {
  const cmd = availableCommands.value.find(c => c.name === commandName);
  return cmd?.description || '';
}

function getCommandExample(commandName: string): string {
  const cmd = availableCommands.value.find(c => c.name === commandName);
  return cmd?.examples?.[0] || '';
}

// Command history navigation
function addToHistory(command: string) {
  if (command.trim() === '') return;

  // Don't add duplicate consecutive commands
  if (commandHistory.value.length > 0 && commandHistory.value[0] === command) {
    return;
  }

  commandHistory.value.unshift(command);
  if (commandHistory.value.length > 10) {
    commandHistory.value.pop();
  }
  historyIndex.value = -1;
}

function navigateHistory(direction: 'up' | 'down') {
  if (commandHistory.value.length === 0) return;

  if (direction === 'up') {
    historyIndex.value = Math.min(historyIndex.value + 1, commandHistory.value.length - 1);
  } else {
    historyIndex.value = Math.max(historyIndex.value - 1, -1);
  }

  if (historyIndex.value >= 0) {
    commandText.value = commandHistory.value[historyIndex.value] || '';
  } else {
    commandText.value = '';
  }
}

// Helper function to normalize command names for matching
function normalizeCommandName(name: string): string {
  // Convert camelCase to space-separated words
  return name.replace(/([a-z])([A-Z])/g, '$1 $2').toLowerCase();
}

// Map of common command variations to actual command names
const commandAliases: Record<string, string> = {
  'toggle dark mode': 'toggleDarkMode',
  'switch theme': 'toggleDarkMode',
  'change theme': 'toggleDarkMode',
  'dark mode': 'toggleDarkMode',
  'light mode': 'toggleDarkMode',
  'toggle theme': 'toggleDarkMode',
  'open dashboard': 'openDashboard',
  'go to dashboard': 'openDashboard',
  'show dashboard': 'openDashboard',
  'open settings': 'openSettings',
  'go to settings': 'openSettings',
  'show settings': 'openSettings',
  'toggle left drawer': 'toggleLeftDrawer',
  'open menu': 'toggleLeftDrawer',
  'close menu': 'toggleLeftDrawer',
  'toggle navigation': 'toggleLeftDrawer',
  'toggle right drawer': 'toggleRightDrawer',
  'open preview': 'toggleRightDrawer',
  'close preview': 'toggleRightDrawer',
  'log out': 'logout',
  'sign out': 'logout'
};

function executeCommand() {
  const trimmedCommand = commandText.value.trim();
  if (!trimmedCommand) return;

  try {
    // Log the command being executed
    console.log('Executing command:', trimmedCommand);
    console.log('Available commands:', availableCommands.value);

    // Add user's command to chat messages
    chatMessages.value.push({
      name: 'You',
      text: [trimmedCommand],
      sent: false,
      avatar: milanAvatar // User avatar
    });

    // Special case for toggleDarkMode
    if (trimmedCommand.toLowerCase() === 'toggledarkmode' ||
        trimmedCommand.toLowerCase() === 'toggle dark mode') {
      console.log('Executing toggleDarkMode command');
      mockWebSocket.simulateCommand('toggleDarkMode');
      addToHistory('toggleDarkMode');
      commandText.value = '';
      return;
    }

    // Check if the command is in the available commands list
    const isExactCommand = availableCommands.value.some(cmd => cmd.name === trimmedCommand);

    if (isExactCommand) {
      // If it's an exact command, execute it directly
      console.log('Executing exact command:', trimmedCommand);
      mockWebSocket.simulateCommand(trimmedCommand);
      addToHistory(trimmedCommand);
      commandText.value = '';
    } else {
      // If it's not an exact command, try to find a matching command
      const matchingCommand = availableCommands.value.find(cmd =>
        cmd.name.toLowerCase() === trimmedCommand.toLowerCase() ||
        cmd.examples?.some(ex => ex.toLowerCase() === trimmedCommand.toLowerCase())
      );

      if (matchingCommand) {
        console.log('Executing matching command:', matchingCommand.name);
        mockWebSocket.simulateCommand(matchingCommand.name);
        addToHistory(matchingCommand.name);
        commandText.value = '';
      } else {
        // If no matching command is found, execute as is
        console.log('No matching command found, executing as is:', trimmedCommand);
        mockWebSocket.simulateCommand(trimmedCommand);
        addToHistory(trimmedCommand);
        commandText.value = '';
      }
    }

    // Refresh available commands list
    refreshAvailableCommands();
  } catch (error) {
    lastResult.value = {
      error: error instanceof Error ? error.message : 'Unknown error',
      input: trimmedCommand
    };

    // Add error message to chat
    chatMessages.value.push({
      name: 'Assistant',
      text: [`Error: ${error instanceof Error ? error.message : 'Unknown error'}`],
      sent: true,
      avatar: robotAvatar // Robot avatar
    });

    void notify({
      color: 'negative',
      message: error instanceof Error ? error.message : 'Unknown error',
      icon: 'error'
    });
  }
}

function simulateVoiceInput() {
  isListening.value = true;

  // Show notification
  void notify({
    color: 'info',
    message: t('voiceCommands.listening'),
    icon: 'mic'
  });

  // Simulate processing time
  setTimeout(() => {
    isListening.value = false;

    // If there's text, execute the command
    if (commandText.value) {
      void executeCommand();
    } else {
      void notify({
        color: 'warning',
        message: t('voiceCommands.noCommandDetected'),
        icon: 'mic_off'
      });
    }
  }, 1500);
}

// Template builder state
const templateBuilderState = reactive({
  selectedElementTypes: [] as number[],
  selectedElementValues: [] as number[],
  imageServerData: {
    model: 'stable-diffusion-xl-1024-v1-0',
    steps: 30,
    aspect_ratio: '1:1',
    seed: null as number | null
  }
});

// Provide the template builder state to child components
provide('templateBuilderState', templateBuilderState);

// Drawer behavior

// Computed property to determine drawer behavior based on screen size
const drawerBehavior = computed(() => {
  // Use 'mobile' (overlay) behavior on small screens, 'desktop' on larger screens
  return $q.screen.lt.md ? 'mobile' : 'desktop';
});

// Computed properties for drawer max widths
const leftDrawerMaxWidth = computed(() => {
  return Math.floor(window.innerWidth * 0.8);
});

const rightDrawerMaxWidth = computed(() => {
  return Math.floor(window.innerWidth * 0.8);
});

// Current tab for navigation
const currentTab = ref(route.path);

// Platform detection
// const isMobile = computed(() => PlatformUtil.isMobile()); // Commented out as it's not currently used
const platformClass = computed(() => PlatformUtil.getPlatformClass());

// Voice command system
const setupCommands = inject<() => void>('setupCommands');
// const voiceCommandStatus = computed(() => t('voiceCommands.title')); // Commented out as it's no longer used

// Expandable panels state
// headerExpanded and footerExpanded removed as we're not using expansion anymore

// Search visibility is now controlled by context manager

// Import Template type
import type { Template } from 'src/services/templateService';

// Note: Using any for context actions to avoid type conflicts

// Selected templates for actions
const selectedTemplates = ref<Template[]>([]);

// Initialize keyboard shortcuts
useKeyboardShortcuts();

// Device type helper functions
function getDeviceIcon(): string {
  const deviceType = contextStore.getCurrentDeviceType();
  switch (deviceType) {
    case DeviceType.DESKTOP_BROWSER:
      return 'computer';
    case DeviceType.MOBILE_BROWSER:
      return 'smartphone';
    case DeviceType.DESKTOP_ELECTRON:
      return 'desktop_windows';
    default:
      return 'devices';
  }
}

function getDeviceTypeName(): string {
  const deviceType = contextStore.getCurrentDeviceType();
  switch (deviceType) {
    case DeviceType.DESKTOP_BROWSER:
      return 'Desktop Browser';
    case DeviceType.MOBILE_BROWSER:
      return 'Mobile Browser';
    case DeviceType.DESKTOP_ELECTRON:
      return 'Electron App';
    default:
      return 'Unknown';
  }
}

function setDeviceType(deviceType: DeviceTypeValue): void {
  contextStore.setDeviceType(deviceType);
}

/**
 * Get components for a specific tab
 *
 * @param tabName The name of the tab
 * @returns Array of component configurations
 */
function getComponentsForTab(tabName: string) {
  // console.log(`Getting components for tab: ${tabName}`);
  // console.log('Available components:', {
  //   left: contextStore.leftDrawerComponents,
  //   right: contextStore.rightDrawerComponents
  // });
  // console.log('Current context:', contextStore.currentContext);
  // console.log('Current route:', route.path);

  let components = [];

  switch (tabName) {
    case 'filters':
      components = contextStore.leftDrawerComponents.filter(comp =>
        comp.component === 'FilterPanel' || comp.component === 'TableFilterPanel'
      );
      // console.log('Filter components:', components);
      return components;
    case 'tools':
      return contextStore.leftDrawerComponents.filter(comp =>
        comp.component === 'PromptElementsPanel'
      );
    case 'elements':
      return contextStore.leftDrawerComponents.filter(comp =>
        comp.component === 'PromptElementsPanel'
      );
    case 'preview':
      return contextStore.rightDrawerComponents.filter(comp =>
        comp.component === 'TemplatePreview'
      );
    case 'settings':
      return contextStore.rightDrawerComponents.filter(comp =>
        comp.component === 'ElementValuesEditor'
      );
    case 'values':
      return contextStore.rightDrawerComponents.filter(comp =>
        comp.component === 'ElementValuesEditor'
      );
    default:
      return [];
  }
}

// Navigate to template builder - used by context actions
// eslint-disable-next-line @typescript-eslint/no-unused-vars
async function navigateToBuilder(template?: Template) {
  // Get the current role path prefix
  const rolePrefix = route.path.startsWith('/admin') ? '/admin' : '/designer';

  // Set the left drawer tab to tools
  document.dispatchEvent(new CustomEvent('set-left-drawer-tab', { detail: 'tools' }));

  try {
    if (template) {
      await router.push(`${rolePrefix}/templates/builder/${template.id}`);
    } else {
      await router.push(`${rolePrefix}/templates/builder/new`);
    }

    // Bottom bar removed
  } catch (error) {
    console.error('Navigation error:', error);
  }
}

// Note: Row actions are now handled by the context manager

// Search placeholder is now controlled by context manager

// Search is now handled by the SearchBar component
// The onSearch function has been moved to the SearchBar component

// onFilterChange function removed as it's no longer used

// toggleHeaderExpand function removed as we're not using header expansion anymore

// Bottom bar removed, so no need for footer expansion toggle

// Open the right drawer with the Chat tab active
function openChatTab() {
  rightDrawerOpen.value = true;
  activeRightDrawerTab.value = 'chat';

  // Focus the chat input after the drawer is opened
  void nextTick(() => {
    const inputEl = chatInput.value?.$el.querySelector('input');
    if (inputEl) {
      inputEl.focus();
    }
  });
}

// Language management removed as it's handled in the Settings page

// Theme management
const isDark = ref(false);
const storedTheme = useLocalStorage('dreambox-theme', 'auto');
const isElectron = PlatformUtil.isElectron();
let electronThemeListener: (() => void) | null = null;

// Note: toggleDarkMode function removed as it's no longer needed

// Function to detect system preference
function detectSystemDarkMode() {
  return window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
}

// Initialize theme for web (non-Electron) context
function initializeWebTheme() {
  if (storedTheme.value === 'dark') {
    isDark.value = true;
    $q.dark.set(true);
  } else if (storedTheme.value === 'light') {
    isDark.value = false;
    $q.dark.set(false);
  } else {
    // Auto mode - use system preference
    isDark.value = detectSystemDarkMode();
    $q.dark.set(isDark.value);
  }

  // Setup system preference watcher
  if (window.matchMedia) {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    mediaQuery.addEventListener('change', (e) => {
      if (storedTheme.value === 'auto') {
        isDark.value = e.matches;
        $q.dark.set(isDark.value);
      }
    });
  }
}

// Clear cache function removed as it's no longer needed

// Toggle drawer functions
function toggleLeftDrawer() {
  leftDrawerOpen.value = !leftDrawerOpen.value;

  // If opening the drawer in builder route, set active tab to 'nav'
  if (leftDrawerOpen.value && route.path.includes('/templates/builder/')) {
    activeLeftDrawerTab.value = 'nav';
  }

  // Show notification
  void notify({
    message: leftDrawerOpen.value ? 'Left drawer opened' : 'Left drawer closed',
    color: 'info',
    position: 'top',
    timeout: 1000
  });
}

function toggleRightDrawer() {
  rightDrawerOpen.value = !rightDrawerOpen.value;

  // Show notification
  void notify({
    message: rightDrawerOpen.value ? 'Right drawer opened' : 'Right drawer closed',
    color: 'info',
    position: 'top',
    timeout: 1000
  });
}

// Set left drawer tab
function setLeftDrawerTab(tab: string) {
  activeLeftDrawerTab.value = tab;

  // Don't automatically open the drawer for the 'tools' tab when in builder route
  if (tab === 'tools' && route.path.includes('/templates/builder/')) {
    // console.log('Not opening left drawer for tools tab in builder route');
    return;
  }

  // Always open the drawer when clicking on a tab
  leftDrawerOpen.value = true;
}

// Main tabs for the header - commented out as tabs were removed from the header
/*
const mainTabs = computed(() => {
  const tabs = [];

  // Add role-specific tabs
  if (authStore.state.user?.currentRole === 'super-admin') {
    tabs.push(
      { label: 'Dashboard', route: '/' },
      { label: 'User Management', route: '/super-admin/user-management' }
    );
  } else if (authStore.state.user?.currentRole === 'admin') {
    tabs.push(
      { label: 'Dashboard', route: '/' },
      { label: 'Projects', route: '/admin/projects' }
    );
  } else if (authStore.state.user?.currentRole === 'designer') {
    tabs.push(
      { label: 'Dashboard', route: '/' },
      { label: 'Designs', route: '/designer/designs' },
      { label: 'Templates', route: '/designer/templates' }
    );
  }

  return tabs;
});
*/

// Initialize navigation store
const navigationStore = useNavigationStore();

// For development, we can log the current auth state
if (process.env.DEV) {
  // console.log('Auth state:', authStore.state);
}

// Get navigation items based on user role
const navigationItems = computed(() => {
  const role = authStore.state.user?.currentRole || null;
  // console.log('Current role:', role);
  // console.log('Auth state:', authStore.state);
  const items = navigationStore.getNavigationForRole(role);
  // console.log('Navigation items:', items);
  return items;
});

// Mobile menu items (limited for footer tabs) - commented out as mobile tabs were removed
/*
const mobileMenuItems = computed(() => {
  // Create a copy of navigation items
  const items = [];  // Would be populated from navigationItems

  // Add voice command item
  items.push({
    label: t('voiceCommands.title'),
    icon: 'mic',
    route: 'voice-commands' // Special route for voice commands
  });

  // Limit to 5 items for mobile footer
  return items.slice(0, 5);
});
*/

// Navigation functions
async function navigateTo(route: string) {
  // Special handling for voice commands
  if (route === 'voice-commands') {
    openChatTab();
    currentTab.value = route;
    return;
  }

  try {
    await router.push(route);
    currentTab.value = route;

    // Close drawer on mobile after navigation
    if (PlatformUtil.isMobile()) {
      leftDrawerOpen.value = false;
    }
  } catch (error) {
    console.error('Navigation error:', error);
  }
}

async function navigateToProfile() {
  try {
    await router.push('/profile');
  } catch (error) {
    console.error('Navigation error:', error);
  }
}

async function navigateToSettings() {
  // Get the current role path prefix
  const rolePrefix = route.path.startsWith('/admin') ? '/admin' : '/designer';
  try {
    await router.push(`${rolePrefix}/settings`);
  } catch (error) {
    console.error('Navigation error:', error);
  }
}

async function logout() {
  try {
    await authStore.logout();
    await router.push('/auth/login');
  } catch (error) {
    console.error('Logout error:', error);
  }
}

// Event listeners for drawer toggles from voice commands
function setupEventListeners() {
  window.addEventListener('toggle-left-drawer', toggleLeftDrawer);
  window.addEventListener('toggle-right-drawer', toggleRightDrawer);
  window.addEventListener('open-chat', openChatTab);
}

function cleanupEventListeners() {
  window.removeEventListener('toggle-left-drawer', toggleLeftDrawer);
  window.removeEventListener('toggle-right-drawer', toggleRightDrawer);
  window.removeEventListener('open-chat', openChatTab);
}

// Handle language change events function removed as it's no longer needed

// Initialize
onMounted(async () => {
  // Set initial drawer state based on screen size and route
  // Don't open drawer for builder route
  if (route.path.includes('/templates/builder/')) {
    leftDrawerOpen.value = false;
  } else {
    leftDrawerOpen.value = $q.screen.gt.sm;
  }

  // Ensure consistent breakpoints across all platforms
  $q.screen.setSizes({ sm: 768, md: 992, lg: 1200 });

  // Add platform class to body
  document.body.classList.add(PlatformUtil.getPlatformClass());

  // Add window resize event listener to update drawer max widths
  window.addEventListener('resize', handleWindowResize);

  // Initialize theme
  if (isElectron && typeof window.electronAPI !== 'undefined') {
    try {
      // Set up listener for theme changes from Electron
      electronThemeListener = window.electronAPI.onThemeChanged((theme) => {
        // console.log('Theme changed from Electron:', theme);
        isDark.value = theme === 'dark';
        $q.dark.set(isDark.value);
      });

      // Get initial theme from Electron
      const electronTheme = await window.electronAPI.getTheme();
      isDark.value = electronTheme === 'dark';
      $q.dark.set(isDark.value);

      // Sync Electron with our stored preference
      if (storedTheme.value === 'dark') {
        await window.electronAPI.setTheme('dark');
      } else if (storedTheme.value === 'light') {
        await window.electronAPI.setTheme('light');
      } else {
        await window.electronAPI.setTheme('system');
      }
    } catch (error) {
      console.error('Error syncing with Electron theme:', error);
      // Fall back to web implementation if Electron API fails
      initializeWebTheme();
    }
  } else {
    // Standard web implementation
    initializeWebTheme();
  }

  // Initialize voice commands
  if (setupCommands) {
    setupCommands();
  }

  // Initialize voice command functionality
  setTimeout(() => {
    refreshAvailableCommands();
    // console.log('Available commands on mount:', availableCommands.value.map(cmd => ({
    //   name: cmd.name,
    //   normalized: normalizeCommandName(cmd.name),
    //   examples: cmd.examples
    // })));
  }, 500);

  // Set up keyboard shortcuts for command history navigation
  window.addEventListener('keydown', commandHistoryKeyHandler);

  // Listen for WebSocket results
  mockWebSocket.on('result', webSocketResultHandler);

  // Set up event listeners for voice commands
  setupEventListeners();

  // Language change event listener removed as it's no longer needed

  // Listen for custom events
  document.addEventListener('set-left-drawer-tab', (event: Event) => {
    const customEvent = event as CustomEvent;
    // console.log('set-left-drawer-tab event received:', customEvent.detail);
    if (customEvent.detail) {
      setLeftDrawerTab(customEvent.detail);
    }
  });

  // Listen for template selection changes
  document.addEventListener('template-selection-changed', (event: Event) => {
    const customEvent = event as CustomEvent;
    // console.log('template-selection-changed event received:', customEvent.detail);
    if (customEvent.detail) {
      selectedTemplates.value = customEvent.detail.templates || [];
    }
  });

  // Watch for dark mode changes from Quasar
  watch(() => $q.dark.isActive, (newValue) => {
    isDark.value = newValue;
  });

  // Watch for route changes to set the appropriate context and drawer tab
  watch(() => route.path, (newPath) => {
    // console.log('Route changed to:', newPath);

    // Set context based on route
    if (newPath === '/' || newPath.includes('/dashboard')) {
      contextStore.setContext('dashboard');
    } else if (newPath.includes('/templates/builder/')) {
      contextStore.setContext('templates_table', 'builder');
      // Close the left drawer for builder route
      leftDrawerOpen.value = false;
      // Set active tab to 'nav' for when drawer is manually opened
      activeLeftDrawerTab.value = 'nav';
      // console.log('Template builder route detected - closing left drawer and setting active tab to nav');
    } else if (newPath.includes('/filter-test')) {
      contextStore.setContext('filter_test');
      activeLeftDrawerTab.value = 'filters';
    } else if (newPath.includes('/templates')) {
      contextStore.setContext('templates_table', 'table_view', { view: 'table_view' });
      activeLeftDrawerTab.value = 'filters';
    } else if (newPath.includes('/products')) {
      contextStore.setContext('products_table', 'table_view', { view: 'table_view' });
      activeLeftDrawerTab.value = 'filters';
    } else if (newPath.includes('/users')) {
      contextStore.setContext('users_table', 'table_view', { view: 'table_view' });
      activeLeftDrawerTab.value = 'filters';
    } else {
      // Default context for other routes
      contextStore.setContext('default');
    }
  });

  // Check the current route immediately and set initial context
  // console.log('Initial route check:', route.path);

  // Set initial context based on route
  if (route.path === '/' || route.path.includes('/dashboard')) {
    contextStore.setContext('dashboard');
  } else if (route.path.includes('/templates/builder/')) {
    contextStore.setContext('templates_table', 'builder');
    // Close the left drawer for builder route
    leftDrawerOpen.value = false;
    // Set active tab to 'nav' for when drawer is manually opened
    activeLeftDrawerTab.value = 'nav';
    // console.log('Template builder route detected (initial) - closing left drawer and setting active tab to nav');
  } else if (route.path.includes('/filter-test')) {
    contextStore.setContext('filter_test');
    activeLeftDrawerTab.value = 'filters';
  } else if (route.path.includes('/templates')) {
    contextStore.setContext('templates_table', 'table_view', { view: 'table_view' });
    activeLeftDrawerTab.value = 'filters';
  } else if (route.path.includes('/products')) {
    contextStore.setContext('products_table', 'table_view', { view: 'table_view' });
    activeLeftDrawerTab.value = 'filters';
  } else if (route.path.includes('/users')) {
    contextStore.setContext('users_table', 'table_view', { view: 'table_view' });
    activeLeftDrawerTab.value = 'filters';
  } else {
    // Default context for other routes
    contextStore.setContext('default');
  }
});

// Store reference to command history keyboard handler
const commandHistoryKeyHandler = (e: KeyboardEvent) => {
  if (activeRightDrawerTab.value === 'chat' && rightDrawerOpen.value) {
    if (e.key === 'ArrowUp') {
      navigateHistory('up');
      e.preventDefault();
    } else if (e.key === 'ArrowDown') {
      navigateHistory('down');
      e.preventDefault();
    }
  }
};

// Define a type for command result data
type CommandResultData = {
  result?: CommandResult;
  error?: string;
  command?: string;
  params?: Record<string, unknown>;
};

// Store reference to WebSocket result handler
const webSocketResultHandler = (data: unknown) => {
  const resultData = data as CommandResultData;
  lastResult.value = resultData as Record<string, unknown>;

  // Format the result as a natural language message
  let responseMessage = '';

  if (resultData.result?.success) {
    // Check if this is a silent success (no notification needed)
    const isSilent = resultData.result.silent === true;

    if (!isSilent) {
      // Success message
      responseMessage = resultData.result.message || t('voiceCommands.commandExecuted');

      // Add additional data if available
      if (resultData.result.data) {
        const dataEntries = Object.entries(resultData.result.data);
        if (dataEntries.length > 0) {
          responseMessage += '. ' + dataEntries.map(([key, value]) => {
            return `${key.charAt(0).toUpperCase() + key.slice(1)}: ${String(value)}`;
          }).join(', ');
        }
      }

      // Show notification
      void notify({
        color: 'positive',
        message: responseMessage,
        icon: 'check_circle',
        position: 'top'
      });
    }
  } else if (resultData.error) {
    // Error message
    responseMessage = `Error executing command: ${resultData.error}`;

    // Show notification
    void notify({
      color: 'negative',
      message: String(resultData.error),
      icon: 'error'
    });
  }

  // Add the response to chat messages
  if (responseMessage) {
    chatMessages.value.push({
      name: 'Assistant',
      text: [responseMessage],
      sent: true,
      avatar: robotAvatar // Robot avatar
    });
  }
};

// Function to handle window resize
function handleWindowResize() {
  // The computed properties will automatically update
  // This is just a placeholder for the event listener
}

onUnmounted(() => {
  // Clean up event listeners
  cleanupEventListeners();
  // Language change event listener cleanup removed as it's no longer needed
  document.removeEventListener('set-left-drawer-tab', (event: Event) => {
    const customEvent = event as CustomEvent;
    if (customEvent.detail) {
      setLeftDrawerTab(customEvent.detail);
    }
  });

  // Remove command history keyboard handler
  window.removeEventListener('keydown', commandHistoryKeyHandler);

  // Clean up WebSocket listener
  mockWebSocket.off('result', webSocketResultHandler);

  // Clean up Electron theme listener
  if (electronThemeListener) {
    electronThemeListener();
    electronThemeListener = null;
  }

  // Remove window resize event listener
  window.removeEventListener('resize', handleWindowResize);
});
</script>

<style lang="scss">
// Platform-specific styles
.platform-electron {
  .q-header {
    -webkit-app-region: drag; // Makes the header draggable in Electron
    height: 50px !important; // Fix the double height issue in Electron
    padding: 0 !important;
    margin: 0 !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: flex-start !important;

    &.header-expanded {
      height: auto !important; // Dynamic height based on content
      min-height: 50px !important; // Ensure minimum height for toolbar

      .header-expanded-content {
        position: relative !important;
        top: 0 !important; // Adjust position for Electron
        height: auto !important; // Dynamic height based on content
        padding-top: 8px !important;
        padding-bottom: 8px !important;
        margin-top: 0 !important;

        // Fix expanded content elements
        .row, .col-12, .col-md-6 {
          margin-top: 0 !important;
          padding-top: 0 !important;
        }

        // Fix input fields in expanded content
        .q-input, .q-select {
          margin-top: 0 !important;
          margin-bottom: 0 !important;
        }
      }
    }

    // Fix toolbar alignment in Electron
    .q-toolbar {
      height: 50px !important;
      min-height: 50px !important;
      padding: 0 12px !important;
      display: flex !important;
      align-items: center !important;
      position: relative !important;
      top: 0 !important;
      transform: translateY(0) !important; // Prevent any transform

      // Fix all direct children of toolbar
      > * {
        transform: translateY(0) !important;
        margin-top: 0 !important;
        margin-bottom: 0 !important;
        padding-top: 0 !important;
        padding-bottom: 0 !important;
      }

      // Fix columns in toolbar
      .col-4 {
        height: 50px !important;
        display: flex !important;
        align-items: center !important;
      }
    }

    // Fix button alignment in toolbar
    .q-btn {
      margin-top: 0 !important;
      margin-bottom: 0 !important;
      align-self: center !important;
      transform: translateY(0) !important; // Prevent any transform
    }

    // Fix avatar and title alignment
    .q-toolbar-title, .q-avatar {
      align-self: center !important;
      transform: translateY(0) !important; // Prevent any transform
    }
  }

  .q-btn, .q-tabs {
    -webkit-app-region: no-drag; // Make buttons clickable
  }
}

.platform-mobile {
  .q-drawer {
    top: 50px !important; // Adjust drawer position for mobile
  }
}

// Responsive adjustments
@media (max-width: 599px) {
  .q-toolbar-title {
    font-size: 1rem;
  }
}

// Expandable panels styles
.header-expanded-content,
.footer-expanded-content {
  opacity: 1;
  transition: opacity 0.3s ease;
  overflow: hidden;
}

.header-expanded-content {
  background-color: var(--q-primary);
}

.footer-expanded-content {
  background-color: var(--q-grey-8);
}

// Drawer with tabs styles
.drawer-with-tabs {
  display: flex;
  flex-direction: column;
  height: 100%;

  .drawer-tabs {
    flex: 0 0 auto;
  }

  .drawer-tabs-height {
    height: 50px; // Match the header height

    :deep(.q-tab) {
      height: 50px;
      padding: 0 16px;
      min-height: 50px;

      .q-tab__icon {
        font-size: 24px; // Slightly larger icons for better visibility
      }
    }
  }

  .drawer-panels {
    flex: 1 1 auto;
    overflow: hidden;
  }

  .q-tab-panel {
    padding: 0;
    height: 100%;
    overflow: hidden;
  }

  .q-scroll-area {
    height: 100%;
  }
}

// Prevent drawers from scrolling with main content
.no-scroll {
  overflow: hidden !important;
}

// Ensure drawer resizers are always visible
.drawer-resizer {
  position: fixed;
  z-index: 1000;
}

// Left drawer resizer positioning
.q-drawer--left .drawer-resizer {
  right: 0;
  top: 50px; // Adjust based on your header height
  bottom: 0;
}

// Right drawer resizer positioning
.q-drawer--right .drawer-resizer {
  left: 0;
  top: 50px; // Adjust based on your header height
  bottom: 0;
}

// Custom language switcher styling
.language-switcher-container {
  display: flex;
  border-radius: 4px;
  overflow: hidden;

  .lang-btn {
    min-width: 40px;
    border-radius: 0;

    &:first-child {
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
    }

    &:last-child {
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
    }
  }
}

// Footer expanded styling
.q-footer.footer-expanded {
  background-color: var(--q-primary) !important;

  .footer-expanded-content {
    background-color: var(--q-primary-lighten-1, rgba(255, 255, 255, 0.05));

    body.body--dark & {
      background-color: var(--q-dark-page, #121212);
    }

    body:not(.body--dark) & {
      background-color: var(--q-primary-lighten-4, #f5f5f5);
    }
  }
}

// Search input in toolbar styling
.search-input {
  transition: width 0.3s ease;
  display: inline-block !important;

  // Responsive width and spacing
  @media (max-width: 599px) {
    width: calc(100% - 80px); // More width on mobile now that title is hidden
    max-width: none; // No max width on mobile
    margin-left: 12px; // More spacing on the left on mobile
    margin-right: 4px; // Very little spacing on the right on mobile

    // Fix for white line issue
    :deep(.q-field__control) {
      border-radius: 0;
      border: none;
      box-shadow: none;
    }
  }

  @media (min-width: 600px) and (max-width: 1023px) {
    width: 200px; // Medium screens
  }

  @media (min-width: 1024px) {
    width: 300px; // Large screens
  }

  // Make the search input match the top bar styling
  :deep(.q-field__control) {
    background-color: transparent !important;
    color: white;
    height: 36px;
    min-height: 36px;
  }

  // Force pure white color with no transparency for all elements
  :deep(.q-field__label),
  :deep(.q-field__marginal),
  :deep(.q-field__native),
  :deep(.q-field__append .q-icon),
  :deep(.q-field__focusable-action),
  :deep(.q-icon) {
    color: #FFFFFF !important;
    opacity: 1 !important;
  }

  :deep(.q-field__native)::placeholder {
    color: #FFFFFF !important;
    opacity: 1 !important;
  }

  // Dark mode styling - keep everything pure white
  body.body--dark & {
    :deep(.q-field__control) {
      background-color: transparent !important;
    }
  }
}

// Footer action buttons styling
.q-footer {
  .row.items-center {
    // Add a subtle background to make buttons more visible on mobile
    @media (max-width: 599px) {
      background-color: rgba(0, 0, 0, 0.2);
      border-radius: 4px;
      padding: 4px;
      margin-left: 4px;
    }
  }

  // Style for footer toolbar buttons
  .q-toolbar {
    .q-btn.q-btn--round {
      border-radius: 50%;
      overflow: hidden;

      &::before {
        border-radius: 50%;
      }
    }

    .footer-icon-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      cursor: pointer;
      z-index: 1;

      &:hover {
        .q-icon {
          transform: scale(1.1);
        }
      }

      .q-icon {
        transition: transform 0.2s ease;
      }
    }
  }

  // Add padding to the voice button
  .voice-btn {
    margin-right: 10px; // Add space from the right edge for all screen sizes

    @media (max-width: 599px) {
      margin-right: 22px; // Add more space on mobile (12px + 10px)
    }
  }

  // Fix toolbar styling to remove shadow and gap
  .q-footer {
    border-radius: 0 !important;
    overflow: hidden !important;

    &::before,
    &::after,
    & > *,
    & > *::before,
    & > *::after {
      border-radius: 0 !important;
    }

    .q-toolbar {
      padding: 0;
      min-height: 50px;
      width: 100%;
      box-shadow: none;
      border-radius: 0 !important;
    }
  }

  // Remove border radius from footer elements
  .no-border-radius {
    border-radius: 0 !important;
    overflow: hidden !important;

    &::before,
    &::after,
    & > *,
    & > *::before,
    & > *::after {
      border-radius: 0 !important;
    }
  }

  // Style the expanded footer content
  .footer-expanded-content {
    // Ensure content fits properly with proper spacing
    width: 100%;
    padding-left: 0;
    padding-right: 0;
    border-radius: 0 !important;

    @media (max-width: 599px) {
      .q-btn {
        min-height: 40px;
        padding: 4px;

        .q-icon {
          font-size: 1.5rem;
        }
      }

      // Fix for light border in light mode
      .footer-buttons-row {
        margin: -8px; // Counteract the gutter

        // Adjust column padding to maintain spacing between buttons
        > div {
          padding: 4px;
        }
      }
    }
  }
}

// User email styling in left drawer
.user-email {
  font-size: 0.9rem; // Make the email text slightly smaller
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// Chat panel styling
.chat-container {
  display: flex;
  flex-direction: column;
  height: calc(100% - 16px); // Account for padding
  background-color: rgba(0, 0, 0, 0.02);
  padding: 8px;

  .body--dark & {
    background-color: rgba(255, 255, 255, 0.03);
  }
}

.chat-messages-scroll {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 8px;

  // Quasar scrollbar styling
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
  }

  .body--dark &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
  }

  .body--dark &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
  }
}

.chat-input {
  flex-shrink: 0;
  background-color: inherit;
}

.chat-messages {
  padding: 8px;
  display: flex;
  flex-direction: column;

  // Scroll to bottom by default
  .q-message:last-child {
    margin-top: auto;
  }

  // Quasar scrollbar styling
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
  }

  .body--dark &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
  }

  .body--dark &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
  }
}

.chat-input {
  .q-input, .q-select {
    border-radius: 4px;
  }

  .command-select {
    margin-bottom: 0 !important;
  }

  // Style the command dropdown
  .command-dropdown {
    max-height: 300px;
    overflow-y: auto;

    // Quasar scrollbar styling
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 4px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.05);
      border-radius: 4px;
    }

    .body--dark &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.2);
    }

    .body--dark &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.05);
    }
  }
}

// Command select styling
.command-select {
  width: 100%;
  max-width: 100%;

  :deep(.q-field__control) {
    width: 100%;
    max-width: 100%;
  }

  :deep(.q-field__native) {
    width: 100%;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  :deep(.q-menu) {
    max-width: 300px;
  }
}
</style>
