<template>
  <div class="element-type-selector q-pa-md">
    <div class="row items-center q-mb-md">
      <q-checkbox
        v-model="selectAll"
        label="Select All"
        @update:model-value="toggleAllTypes"
      />
    </div>

    <q-list separator>
      <q-item v-for="type in elementTypes" :key="type.id">
        <q-item-section avatar>
          <q-checkbox v-model="selectedTypes" :val="type.id" />
        </q-item-section>
        <q-item-section>
          <q-item-label>{{ formatElementTypeName(type.name) }}</q-item-label>
          <q-item-label caption v-if="type.description">{{ type.description }}</q-item-label>
        </q-item-section>
      </q-item>
    </q-list>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, onMounted } from 'vue';
import { supabase } from 'src/boot/supabase';

const props = defineProps({
  modelValue: {
    type: Array as () => number[],
    default: () => []
  }
});

const emit = defineEmits(['update:modelValue']);

// Element types
const elementTypes = ref<{ id: number, name: string, description: string | null }[]>([]);

// Selected types
const selectedTypes = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
});

// Select all checkbox state
const selectAll = computed({
  get: () => {
    return elementTypes.value.length > 0 && selectedTypes.value.length === elementTypes.value.length;
  },
  set: (val) => {
    toggleAllTypes(val);
  }
});

// Format element type name (convert snake_case to Title Case)
function formatElementTypeName(name: string): string {
  return name
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// Toggle all element types
function toggleAllTypes(selected: boolean) {
  if (selected) {
    selectedTypes.value = elementTypes.value.map(type => type.id);
  } else {
    selectedTypes.value = [];
  }
}

// Fetch element types from the database
async function fetchElementTypes() {
  try {
    const { data, error } = await supabase
      .from('prompt_element_types')
      .select('id, name, description')
      .order('name');

    if (error) throw new Error(error.message || 'Error fetching element types');

    if (data) {
      elementTypes.value = data;
    }
  } catch (error) {
    console.error('Error fetching element types:', error);
  }
}

// Initialize
onMounted(() => {
  void fetchElementTypes();
});
</script>

<style lang="scss" scoped>
.element-type-selector {
  // Add any specific styles here
}
</style>
