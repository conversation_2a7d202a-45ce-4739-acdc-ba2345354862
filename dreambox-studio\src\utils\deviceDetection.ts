/**
 * Device Detection Utility
 * 
 * Provides functions for detecting the current device type.
 * Uses the existing PlatformUtil for consistency.
 */
import PlatformUtil from './platform';
import { DeviceType } from 'src/types/deviceTypes';
import type { DeviceTypeValue } from 'src/types/deviceTypes';

/**
 * Detect the current device type
 * 
 * @returns The detected device type
 */
export function detectDeviceType(): DeviceTypeValue {
  // Check if running in Electron
  if (PlatformUtil.isElectron()) {
    return DeviceType.DESKTOP_ELECTRON;
  }
  
  // Check if mobile browser
  if (PlatformUtil.isMobile()) {
    return DeviceType.MOBILE_BROWSER;
  }
  
  // Default to desktop browser
  return DeviceType.DESKTOP_BROWSER;
}

/**
 * Get a human-readable name for a device type
 * 
 * @param deviceType The device type
 * @returns A human-readable name for the device type
 */
export function getDeviceTypeName(deviceType: DeviceTypeValue): string {
  switch (deviceType) {
    case DeviceType.DESKTOP_BROWSER:
      return 'Desktop Browser';
    case DeviceType.MOBILE_BROWSER:
      return 'Mobile Browser';
    case DeviceType.DESKTOP_ELECTRON:
      return 'Electron App';
    default:
      return 'Unknown';
  }
}

/**
 * Get an icon name for a device type
 * 
 * @param deviceType The device type
 * @returns An icon name for the device type
 */
export function getDeviceTypeIcon(deviceType: DeviceTypeValue): string {
  switch (deviceType) {
    case DeviceType.DESKTOP_BROWSER:
      return 'computer';
    case DeviceType.MOBILE_BROWSER:
      return 'smartphone';
    case DeviceType.DESKTOP_ELECTRON:
      return 'desktop_windows';
    default:
      return 'device_unknown';
  }
}
