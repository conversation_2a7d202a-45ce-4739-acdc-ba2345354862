<template>
  <div class="filter-section q-mb-sm">
    <div
      class="filter-section-header q-py-sm q-px-md cursor-pointer"
      @click="toggleExpanded"
    >
      <div class="row items-center justify-between">
        <div class="filter-section-title">
          {{ title }}
          <span v-if="activeFilterCount && activeFilterCount > 0" class="filter-count q-ml-sm">
            ({{ activeFilterCount }})
          </span>
        </div>
        <q-btn
          flat
          dense
          round
          :icon="expanded ? 'expand_less' : 'expand_more'"
          size="sm"
        />
      </div>
    </div>

    <q-slide-transition>
      <div v-show="expanded" class="filter-section-content q-pa-md">
        <!-- Removed "Clear All" button as it's redundant with individual filter clear buttons and the global Clear button -->

        <slot></slot>

        <div v-if="!hasFilters" class="filter-section-empty q-pa-md text-center text-grey">
          {{ t('filters.noFiltersAvailable') }}
        </div>
      </div>
    </q-slide-transition>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';

// Props
const props = defineProps<{
  title: string;
  initialExpanded?: boolean;
  activeFilterCount: number;
  hasFilters?: boolean;
}>();

// No emits needed anymore since we removed the Clear All button

// Initialize i18n
const { t } = useI18n();

// State
const expanded = ref(props.initialExpanded !== undefined ? props.initialExpanded : true);

// Methods
function toggleExpanded() {
  expanded.value = !expanded.value;
}
</script>

<style scoped>
.filter-section {
  border: 1px solid #e0e0e0;
  overflow: hidden;
}

.filter-section-header {
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

.filter-section-title {
  font-weight: 500;
  font-size: 1rem;
}

.filter-count {
  font-size: 0.8rem;
  color: #666;
}

.filter-section-content {
  background-color: #ffffff;
}

.filter-section-actions {
  display: flex;
  justify-content: flex-end;
}

.filter-section-empty {
  font-style: italic;
}

/* Dark mode styles */
.body--dark .filter-section {
  border-color: #424242;
}

.body--dark .filter-section-header {
  background-color: #333333;
  border-bottom-color: #424242;
}

.body--dark .filter-section-content {
  background-color: #1e1e1e;
}
</style>
