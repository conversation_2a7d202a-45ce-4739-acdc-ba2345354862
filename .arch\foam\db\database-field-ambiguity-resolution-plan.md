# Database Field Ambiguity Resolution Plan

## Background

Similar to the `company_id` ambiguity issue that was previously resolved by renaming the field in 17 tables with table-specific prefixes, several other fields in the database could cause similar ambiguity problems in joins and queries. This document outlines a comprehensive plan to address these potential ambiguities systematically.

## 1. Identified Ambiguous Fields

The following fields appear in multiple tables and could cause ambiguity in joins:

### High Priority (Most Frequently Used)
1. **template_id** - Appears in:
   - images
   - products
   - prompt_element_value_order
   - prompt_elements_usage
   - template_status_history
   - events_templates
   - templates_collections
   - templates_product_types

2. **product_id** - Appears in:
   - events_products
   - marketing_content
   - products_product_types
   - publishing_history
   - sales

3. **agent_id** - Appears in:
   - app_logs
   - marketing_campaigns
   - marketing_content
   - products
   - publishing_history
   - templates

### Medium Priority
4. **user_id** - Appears in:
   - app_users
   - user_filters

5. **collection_id** - Appears in:
   - products
   - templates_collections

6. **element_id** - Appears in:
   - prompt_element_value_order
   - prompt_elements_usage

7. **event_id** - Appears in:
   - events_products
   - events_templates

### Lower Priority
8. **image_id** - Appears in:
   - products

9. **product_type_id** - Appears in:
   - products_product_types
   - templates_product_types

## 2. Phased Implementation Plan

### Phase 1: High-Priority Fields (template_id, product_id, agent_id)

#### 1.1 template_id Renaming
- Rename to table-specific prefixes:
  - images.img_template_id
  - products.prod_template_id
  - prompt_element_value_order.pevo_template_id
  - prompt_elements_usage.peu_template_id
  - template_status_history.tsh_template_id
  - events_templates.et_template_id
  - templates_collections.tc_template_id
  - templates_product_types.tpt_template_id

#### 1.2 product_id Renaming
- Rename to table-specific prefixes:
  - events_products.ep_product_id
  - marketing_content.mc_product_id
  - products_product_types.ppt_product_id
  - publishing_history.ph_product_id
  - sales.sale_product_id

#### 1.3 agent_id Renaming
- Rename to table-specific prefixes:
  - app_logs.al_agent_id
  - marketing_campaigns.mc_agent_id
  - marketing_content.mct_agent_id (using mct to distinguish from marketing_campaigns)
  - products.prod_agent_id
  - publishing_history.ph_agent_id
  - templates.tmpl_agent_id

### Phase 2: Medium-Priority Fields

#### 2.1 user_id Renaming
- Rename to table-specific prefixes:
  - user_filters.uf_user_id
  - (Keep app_users.user_id as is since it's the primary reference)

#### 2.2 collection_id Renaming
- Rename to table-specific prefixes:
  - products.prod_collection_id
  - templates_collections.tc_collection_id

#### 2.3 element_id Renaming
- Rename to table-specific prefixes:
  - prompt_element_value_order.pevo_element_id
  - prompt_elements_usage.peu_element_id

#### 2.4 event_id Renaming
- Rename to table-specific prefixes:
  - events_products.ep_event_id
  - events_templates.et_event_id

### Phase 3: Lower-Priority Fields

#### 3.1 image_id Renaming
- Rename to table-specific prefixes:
  - products.prod_image_id

#### 3.2 product_type_id Renaming
- Rename to table-specific prefixes:
  - products_product_types.ppt_product_type_id
  - templates_product_types.tpt_product_type_id

## 3. Implementation Process for Each Field

For each field, follow this process:

1. **Preparation**:
   - Create a script to identify all views, functions, and client code that reference the field
   - Back up the database before making changes
   - Create a test environment to validate changes

2. **Database Changes**:
   - Add the new column with the prefixed name
   - Copy data from the old column to the new column
   - Update all views and functions to use the new column name
   - Add a trigger to keep both columns in sync during a transition period
   - Test database functionality

3. **Client Code Updates**:
   - Update all client code to use the new column names
   - Test client functionality

4. **Finalization**:
   - Once all code is updated and tested, remove the old column
   - Remove the synchronization trigger
   - Verify all functionality

## 4. Example Implementation for template_id

Here's a detailed example for implementing the changes for `template_id` in the `images` table:

```sql
-- 1. Add new column
ALTER TABLE images ADD COLUMN img_template_id BIGINT;

-- 2. Copy data
UPDATE images SET img_template_id = template_id;

-- 3. Create foreign key constraint
ALTER TABLE images 
  ADD CONSTRAINT fk_images_template 
  FOREIGN KEY (img_template_id) 
  REFERENCES templates(id);

-- 4. Create sync trigger
CREATE OR REPLACE FUNCTION sync_template_id_images()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    NEW.template_id := NEW.img_template_id;
  ELSIF TG_OP = 'UPDATE' THEN
    IF NEW.template_id <> OLD.template_id THEN
      NEW.img_template_id := NEW.template_id;
    ELSIF NEW.img_template_id <> OLD.img_template_id THEN
      NEW.template_id := NEW.img_template_id;
    END IF;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER sync_template_id_images_trigger
BEFORE INSERT OR UPDATE ON images
FOR EACH ROW EXECUTE FUNCTION sync_template_id_images();

-- 5. Update views (example)
CREATE OR REPLACE VIEW template_view AS
SELECT
    -- other fields...
    ( SELECT json_agg(json_build_object('id', i.id, 'title', i.title, 'image_url', i.image_url, 'status', i.status, 'metadata', i.metadata)) AS json_agg
      FROM images i
      WHERE (i.img_template_id = t.id)) AS images,
    -- other fields...
FROM templates t
-- rest of the view definition...
```

## 5. Affected Views and Functions

The following views and functions will need to be updated:

### Views:
- template_view
- template_view_with_collections
- template_performance_view
- template_status_history_view
- agent_performance_view
- designer_performance_view
- prompt_elements_usage_view
- recent_sales_view
- pending_approvals_view

### Functions:
- get_templates
- get_template_elements
- get_template_status_history
- update_template_status
- copy_archived_template
- admin_copy_template
- admin_update_template
- admin_update_template_status
- admin_update_template_fields
- admin_update_template_fields_v2
- get_template_product_types
- get_product_product_types
- reorder_template_elements
- admin_reorder_template_elements

## 6. Risks and Mitigation

### Risks:
1. **Complex Views and Functions**: Many views and functions use these fields, increasing the complexity of changes
2. **Client Code Dependencies**: Client code may have hardcoded references to these field names
3. **Data Integrity**: Ensuring data consistency during the transition period
4. **Performance Impact**: Temporary triggers may impact performance

### Mitigation:
1. **Thorough Testing**: Test each change in a non-production environment
2. **Phased Approach**: Implement changes one field at a time
3. **Synchronization Triggers**: Keep old and new columns in sync during transition
4. **Comprehensive Documentation**: Document all changes for future reference
5. **Rollback Plan**: Have a clear rollback strategy for each change

## 7. Timeline Estimation

- **Phase 1 (High Priority)**: 2-3 weeks
  - template_id: 1 week
  - product_id: 3-4 days
  - agent_id: 3-4 days

- **Phase 2 (Medium Priority)**: 1-2 weeks
  - user_id, collection_id, element_id, event_id: 2-3 days each

- **Phase 3 (Low Priority)**: 3-5 days
  - image_id, product_type_id: 1-2 days each

Total estimated time: 4-6 weeks, depending on complexity and testing requirements.

## 8. SQL Scripts for Finding References

### Find all views referencing a specific field:
```sql
SELECT 
    v.viewname,
    v.definition
FROM 
    pg_views v
WHERE 
    v.schemaname = 'public'
    AND v.definition ILIKE '%template_id%'
ORDER BY 
    v.viewname;
```

### Find all functions referencing a specific field:
```sql
SELECT
    p.proname AS function_name,
    pg_get_functiondef(p.oid) AS function_definition
FROM
    pg_proc p
    JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE
    n.nspname = 'public'
    AND pg_get_functiondef(p.oid) ILIKE '%template_id%'
ORDER BY
    p.proname;
```

## 9. Conclusion

Addressing these field name ambiguities will significantly improve the maintainability and reliability of the database. By following a phased approach and carefully testing each change, we can minimize disruption while making the database more robust against ambiguity errors in joins and queries.

The successful resolution of the `company_id` ambiguity provides a proven template for addressing these additional field ambiguities. By applying the same methodical approach, we can ensure a smooth transition to a more maintainable database structure.
