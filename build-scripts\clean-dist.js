const fs = require('fs');
const path = require('path');

// Disable asar handling
process.noAsar = true;

function cleanDist() {
    const distPath = path.join(__dirname, '..', 'dist');
    
    try {
        if (fs.existsSync(distPath)) {
            try {
                // First try to remove without killing VSCode
                fs.rmSync(distPath, { recursive: true, force: true });
                console.log('✓ Successfully cleaned dist folder');
                return true;
            } catch (e) {
                // If removal failed, it's likely due to locked files
                console.error('Could not delete dist folder - it may be locked by VSCode');
                console.log('Please:');
                console.log('1. Close VSCode');
                console.log('2. Delete the dist folder manually');
                console.log('3. Run "w publish" again');
                return false;
            }
        } else {
            console.log('✓ No dist folder found, proceeding with build');
            return true;
        }
    } catch (err) {
        console.error('Failed to clean dist folder:', err);
        console.log('Please close VSCode and delete the dist folder manually');
        return false;
    }
}

if (require.main === module) {
    // If run directly
    const success = cleanDist();
    process.exit(success ? 0 : 1);
} else {
    // If required as a module
    module.exports = cleanDist;
}

