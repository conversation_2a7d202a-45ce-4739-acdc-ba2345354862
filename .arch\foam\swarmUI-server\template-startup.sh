#!/bin/bash
# SwarmUI Template Startup Script
# This script automatically installs Swarm<PERSON>, downloads models, and configures the backend
# It is designed to be used as the startup command for a RunPod template

# Set environment variables
export DOTNET_ROOT=$HOME/.dotnet
export PATH=$PATH:$HOME/.dotnet:$HOME/.dotnet/tools
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:128
export CUDA_LAUNCH_BLOCKING=0
export CUDA_VISIBLE_DEVICES=0
export HF_HUB_ENABLE_HF_TRANSFER=1

# Log file for debugging
LOGFILE="/workspace/template-startup.log"
echo "Starting SwarmUI template setup at $(date)" > $LOGFILE

# Function to log messages
log() {
  echo "[$(date +%T)] $1" | tee -a $LOGFILE
}

# Install dependencies
log "Installing dependencies..."
apt-get update && \
apt-get install -y git wget curl aria2 libgl1-mesa-glx libglib2.0-0 python3-pip python3-dev build-essential >> $LOGFILE 2>&1

# Upgrade pip
log "Upgrading pip..."
pip install --root-user-action=ignore --upgrade pip >> $LOGFILE 2>&1

# Install pip packages for Hugging Face downloads
log "Installing Hugging Face packages..."
cd /workspace
pip install --root-user-action=ignore huggingface_hub ipywidgets hf_transfer >> $LOGFILE 2>&1
pip install --root-user-action=ignore --upgrade huggingface_hub >> $LOGFILE 2>&1

# Install ComfyUI
log "Installing ComfyUI..."
git clone https://github.com/comfyanonymous/ComfyUI.git >> $LOGFILE 2>&1
cd ComfyUI
pip install --root-user-action=ignore torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118 >> $LOGFILE 2>&1
pip install --root-user-action=ignore -r requirements.txt >> $LOGFILE 2>&1

# Create model directories
log "Creating model directories..."
mkdir -p models/checkpoints
cd models/checkpoints

# Download a small model for testing
log "Downloading small test model..."
python -c "from huggingface_hub import hf_hub_download; hf_hub_download('runwayml/stable-diffusion-v1-5', 'v1-5-pruned-emaonly.safetensors', local_dir='.', local_dir_use_symlinks=False)" >> $LOGFILE 2>&1
mv v1-5-pruned-emaonly.safetensors sd_v1_5.safetensors

# Create model directories for main models
log "Creating main model directories..."
cd /workspace
mkdir -p models/diffusion_models models/clip models/vae

# Download models using Hugging Face
log "Downloading Flux model..."
cd /workspace/models/diffusion_models
python -c "from huggingface_hub import hf_hub_download; hf_hub_download('Kijai/flux-fp8', 'flux1-schnell-fp8-e4m3fn.safetensors', local_dir='.', local_dir_use_symlinks=False)" >> $LOGFILE 2>&1
mv flux1-schnell-fp8-e4m3fn.safetensors FLUX1-Schnell-FP8.safetensors

log "Downloading CLIP model..."
cd ../clip
python -c "from huggingface_hub import hf_hub_download; hf_hub_download('OwlMaster/SD3New', 't5xxl_fp8_e4m3fn_scaled.safetensors', local_dir='.', local_dir_use_symlinks=False)" >> $LOGFILE 2>&1
mv t5xxl_fp8_e4m3fn_scaled.safetensors t5xxl_enconly.safetensors

log "Downloading VAE model..."
cd ../vae
aria2c --file-allocation=none --continue=true -x 16 -s 16 -k 1M "https://huggingface.co/stabilityai/sdxl-vae/resolve/main/sdxl_vae.safetensors" -o "ae.safetensors" >> $LOGFILE 2>&1

# Install SwarmUI
log "Installing SwarmUI..."
cd /workspace
git clone https://github.com/mcmonkeyprojects/SwarmUI >> $LOGFILE 2>&1
cd SwarmUI/launchtools
rm -f dotnet-install.sh
wget https://dot.net/v1/dotnet-install.sh -O dotnet-install.sh >> $LOGFILE 2>&1
chmod +x dotnet-install.sh
./dotnet-install.sh --channel 8.0 --runtime aspnetcore >> $LOGFILE 2>&1
./dotnet-install.sh --channel 8.0 >> $LOGFILE 2>&1
echo 'export DOTNET_ROOT=$HOME/.dotnet' >> ~/.bashrc
echo 'export PATH=$PATH:$HOME/.dotnet:$HOME/.dotnet/tools' >> ~/.bashrc

# Create SwarmUI directories
log "Creating SwarmUI directories..."
cd /workspace
mkdir -p swarmui_data outputs
mkdir -p SwarmUI/Models/Stable-Diffusion
mkdir -p SwarmUI/Models/clip
mkdir -p SwarmUI/Models/vae

# Create symbolic links for models
log "Creating symbolic links for models..."
ln -sf /workspace/models/diffusion_models/FLUX1-Schnell-FP8.safetensors /workspace/SwarmUI/Models/Stable-Diffusion/flux1-schnell-fp8.safetensors
ln -sf /workspace/models/vae/ae.safetensors /workspace/SwarmUI/Models/Stable-Diffusion/flux1-schnell-fp8.vae.safetensors
ln -sf /workspace/models/clip/t5xxl_enconly.safetensors /workspace/SwarmUI/Models/Stable-Diffusion/flux1-schnell-fp8.clip.safetensors
ln -sf /workspace/models/clip/t5xxl_enconly.safetensors /workspace/SwarmUI/Models/clip/t5xxl_enconly.safetensors
ln -sf /workspace/ComfyUI/models/checkpoints/sd_v1_5.safetensors /workspace/SwarmUI/Models/Stable-Diffusion/sd_v1_5.safetensors
ln -sf /workspace/models/clip/t5xxl_enconly.safetensors /workspace/ComfyUI/models/clip/t5xxl_enconly.safetensors
ln -sf /workspace/models/vae/ae.safetensors /workspace/ComfyUI/models/vae/ae.safetensors

# Generate API key
log "Generating API key..."
mkdir -p ~/.swarmui
API_KEY=${SWARMUI_API_KEY:-$(openssl rand -hex 16)}
echo "Your SwarmUI API key is: $API_KEY" | tee -a $LOGFILE

# Create config.json with backend pre-configured
log "Creating config.json with backend pre-configured..."
cat > ~/.swarmui/config.json << EOL
{
  "BackendUrl": "http://0.0.0.0:7801",
  "FrontendUrl": "http://0.0.0.0:7800",
  "ModelRoot": "/workspace/models",
  "OutputsPath": "/workspace/outputs",
  "DisableUpdateCheck": true,
  "AutoInstall": {
    "Torch": false,
    "Comfy": false,
    "Models": false
  },
  "Auth": {
    "Enabled": true,
    "ApiKey": "${API_KEY}"
  },
  "ComfyUI": {
    "Path": "/workspace/ComfyUI",
    "Url": "http://127.0.0.1:7860",
    "EnableWorkflows": true,
    "ModelPaths": {
      "checkpoints": "/workspace/ComfyUI/models/checkpoints",
      "clip": "/workspace/models/clip",
      "vae": "/workspace/models/vae"
    }
  },
  "Backend": {
    "Type": "ComfyUI-SelfStarting",
    "ComfyUIPath": "/workspace/ComfyUI",
    "ComfyUIPort": 7860,
    "EnableWorkflows": true
  }
}
EOL

# Create models.json file
log "Creating models.json file..."
cat > ~/.swarmui/models.json << EOL
{
  "checkpoints": [
    {
      "name": "Flux Schnell",
      "path": "/workspace/models/diffusion_models/FLUX1-Schnell-FP8.safetensors",
      "vae": "/workspace/models/vae/ae.safetensors",
      "clip": "/workspace/models/clip/t5xxl_enconly.safetensors",
      "config": "configs/stable-diffusion/sdxl/sdxl_1_0.yaml",
      "default": true
    }
  ]
}
EOL

# Update the backend URL if RUNPOD_HTTP_HOST is set
if [ ! -z "$RUNPOD_HTTP_HOST" ]; then
  PUBLIC_URL="https://$RUNPOD_HTTP_HOST"
  sed -i "s|\"BackendUrl\": \".*\"|\"BackendUrl\": \"$PUBLIC_URL\"|" ~/.swarmui/config.json
  sed -i "s|\"FrontendUrl\": \".*\"|\"FrontendUrl\": \"$PUBLIC_URL\"|" ~/.swarmui/config.json
  log "Updated SwarmUI URLs to use $PUBLIC_URL"
fi

# Start ComfyUI in the background
log "Starting ComfyUI in the background..."
cd /workspace/ComfyUI
python -c "import torch; torch.backends.cuda.matmul.allow_tf32 = True; torch.backends.cudnn.allow_tf32 = True" >> $LOGFILE 2>&1
nohup python main.py --listen 0.0.0.0 --port 7860 > /workspace/comfyui.log 2>&1 &

# Wait for ComfyUI to start
log "Waiting for ComfyUI to initialize..."
sleep 15

# Start SwarmUI with ComfyUI backend
log "Starting SwarmUI with ComfyUI backend..."
cd /workspace/SwarmUI
./launch-linux.sh --launch_mode none --host 0.0.0.0 --comfy_path /workspace/ComfyUI >> $LOGFILE 2>&1
