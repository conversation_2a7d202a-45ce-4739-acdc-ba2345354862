# Integrating SwarmUI with Your Application

This document explains how to integrate the automated SwarmUI backend setup with your application, allowing you to use SwarmUI for image generation without requiring manual configuration through the web interface.

## Overview

The integration involves:

1. Setting up RunPod templates with the automated backend configuration
2. Creating Supabase Edge Functions to manage RunPod instances
3. Implementing client-side code to interact with SwarmUI via its API

## 1. RunPod Template Setup

### 1.1 Create a Custom RunPod Template

1. Start a new RunPod instance with the desired GPU
2. Install SwarmUI and ComfyUI as described in your existing documentation
3. Copy the `auto-backend-startup.sh` script to the instance
4. Make it executable: `chmod +x auto-backend-startup.sh`
5. Add the script to the pod's startup command
6. Create a template from this pod

### 1.2 Template Configuration

When creating the template, set:

- Container Disk: `50GB` (or the size you chose)
- Volume Size: `100GB` (or the size you chose)
- Expose HTTP Port: `7801` (the SwarmUI API port)
- Start Script: `/workspace/auto-backend-startup.sh`

## 2. Supabase Edge Functions

### 2.1 Create the SwarmUI Server Management Edge Function

Create a Supabase Edge Function to manage SwarmUI servers:

```typescript
// supabase/functions/swarmui-server/index.ts
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const RUNPOD_API_KEY = Deno.env.get('RUNPOD_API_KEY') || ''
const TEMPLATE_ID = Deno.env.get('SWARMUI_TEMPLATE_ID') || ''

serve(async (req) => {
  // CORS headers
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, GET, DELETE',
        'Access-Control-Allow-Headers': 'Authorization, Content-Type',
      }
    })
  }

  // Create Supabase client
  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
  )

  // Get user from request
  const { data: { user } } = await supabaseClient.auth.getUser()
  if (!user) {
    return new Response(JSON.stringify({ error: 'Unauthorized' }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    })
  }

  // Parse URL to get action
  const url = new URL(req.url)
  const action = url.searchParams.get('action')

  // Handle different actions
  if (req.method === 'POST' && action === 'start-server') {
    // Start a new server
    const body = await req.json()
    const { gpuTypeId } = body

    // Call RunPod API to start a pod
    const response = await fetch('https://api.runpod.io/v2/pods', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${RUNPOD_API_KEY}`
      },
      body: JSON.stringify({
        name: `SwarmUI-${user.id}`,
        templateId: TEMPLATE_ID,
        gpuCount: 1,
        gpuTypeId: gpuTypeId || 'NVIDIA RTX A5000',
        containerDiskSizeGB: 50,
        volumeSizeGB: 100,
        env: {
          SWARMUI_API_KEY: crypto.randomUUID()
        }
      })
    })

    const data = await response.json()

    // Store server info in database
    if (data.id) {
      await supabaseClient
        .from('swarmui_servers')
        .insert({
          user_id: user.id,
          pod_id: data.id,
          api_endpoint: `https://${data.id}-7801.proxy.runpod.net/api`,
          api_key: data.env.SWARMUI_API_KEY,
          status: 'starting',
          gpu_type: gpuTypeId || 'NVIDIA RTX A5000'
        })
    }

    return new Response(JSON.stringify(data), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    })
  } else if (req.method === 'DELETE' && action === 'stop-server') {
    // Stop a server
    const podId = url.searchParams.get('podId')
    
    if (!podId) {
      return new Response(JSON.stringify({ error: 'Missing podId parameter' }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      })
    }

    // Call RunPod API to stop the pod
    const response = await fetch(`https://api.runpod.io/v2/pods/${podId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${RUNPOD_API_KEY}`
      }
    })

    const data = await response.json()

    // Update server status in database
    await supabaseClient
      .from('swarmui_servers')
      .update({ status: 'stopped', stopped_at: new Date().toISOString() })
      .eq('pod_id', podId)
      .eq('user_id', user.id)

    return new Response(JSON.stringify(data), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    })
  } else if (req.method === 'GET' && action === 'server-status') {
    // Get server status
    const { data: servers, error } = await supabaseClient
      .from('swarmui_servers')
      .select('*')
      .eq('user_id', user.id)
      .eq('status', 'running')
      .order('created_at', { ascending: false })
      .limit(1)

    if (error) {
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      })
    }

    if (servers.length === 0) {
      return new Response(JSON.stringify({ status: 'no-server' }), {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      })
    }

    const server = servers[0]

    // Check actual pod status from RunPod API
    const response = await fetch(`https://api.runpod.io/v2/pods/${server.pod_id}`, {
      headers: {
        'Authorization': `Bearer ${RUNPOD_API_KEY}`
      }
    })

    const podData = await response.json()

    // Update server status if needed
    if (podData.status !== server.status) {
      await supabaseClient
        .from('swarmui_servers')
        .update({ status: podData.status })
        .eq('id', server.id)
      
      server.status = podData.status
    }

    return new Response(JSON.stringify(server), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    })
  }

  return new Response(JSON.stringify({ error: 'Invalid action' }), {
    status: 400,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*'
    }
  })
})
```

### 2.2 Create the SwarmUI Generation Edge Function

Create a Supabase Edge Function to handle image generation:

```typescript
// supabase/functions/swarmui-generation/index.ts
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

serve(async (req) => {
  // CORS headers
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, GET',
        'Access-Control-Allow-Headers': 'Authorization, Content-Type',
      }
    })
  }

  // Create Supabase client
  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
  )

  // Get user from request
  const { data: { user } } = await supabaseClient.auth.getUser()
  if (!user) {
    return new Response(JSON.stringify({ error: 'Unauthorized' }), {
      status: 401,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    })
  }

  // Get active server for the user
  const { data: servers, error: serverError } = await supabaseClient
    .from('swarmui_servers')
    .select('*')
    .eq('user_id', user.id)
    .eq('status', 'running')
    .order('created_at', { ascending: false })
    .limit(1)

  if (serverError || servers.length === 0) {
    return new Response(JSON.stringify({ error: 'No active server found' }), {
      status: 400,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    })
  }

  const server = servers[0]
  const apiEndpoint = server.api_endpoint
  const apiKey = server.api_key

  // Update server activity timestamp
  await supabaseClient
    .from('swarmui_servers')
    .update({ last_activity: new Date().toISOString() })
    .eq('id', server.id)

  // Handle different actions based on HTTP method
  if (req.method === 'POST') {
    // Generate image
    const generationParams = await req.json()

    // Forward request to SwarmUI API
    const response = await fetch(`${apiEndpoint}/generations`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(generationParams)
    })

    const data = await response.json()

    return new Response(JSON.stringify(data), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    })
  } else if (req.method === 'GET') {
    // Get generation status or other API endpoints
    const url = new URL(req.url)
    const path = url.searchParams.get('path') || 'info'

    // Forward request to SwarmUI API
    const response = await fetch(`${apiEndpoint}/${path}`, {
      headers: {
        'Authorization': `Bearer ${apiKey}`
      }
    })

    const data = await response.json()

    return new Response(JSON.stringify(data), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    })
  }

  return new Response(JSON.stringify({ error: 'Invalid method' }), {
    status: 400,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*'
    }
  })
})
```

## 3. Client-Side Implementation

### 3.1 Server Management Service

Implement a service to manage SwarmUI servers:

```typescript
// src/services/serverManagement/ServerManagementService.ts
import { supabase } from '../../boot/supabase';
import { ServerConfig, ServerInstance, ServerStatus, ServerStartOptions, ServerStartResult } from './types';

export class ServerManagementService {
  async getServerConfigurations(): Promise<ServerConfig[]> {
    const { data, error } = await supabase
      .from('swarmui_server_configurations')
      .select('*')
      .order('is_default', { ascending: false });

    if (error) {
      console.error('Error getting server configurations:', error);
      throw new Error('Failed to get server configurations');
    }

    return data || [];
  }

  async getActiveServer(): Promise<ServerInstance | null> {
    try {
      const { data, error } = await supabase.functions.invoke('swarmui-server', {
        method: 'GET',
        query: { action: 'server-status' }
      });

      if (error) {
        console.error('Error getting server status:', error);
        return null;
      }

      if (data.status === 'no-server') {
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error getting active server:', error);
      return null;
    }
  }

  async startServer(options: ServerStartOptions = {}): Promise<ServerStartResult> {
    try {
      const { data, error } = await supabase.functions.invoke('swarmui-server', {
        method: 'POST',
        query: { action: 'start-server' },
        body: {
          gpuTypeId: options.gpuTypeId,
          configurationId: options.configurationId
        }
      });

      if (error) {
        console.error('Error starting server:', error);
        throw new Error('Failed to start server');
      }

      return {
        serverId: data.id,
        status: 'starting',
        message: 'Server is starting'
      };
    } catch (error) {
      console.error('Error starting server:', error);
      throw new Error('Failed to start server');
    }
  }

  async stopServer(podId: string): Promise<void> {
    try {
      const { error } = await supabase.functions.invoke('swarmui-server', {
        method: 'DELETE',
        query: { action: 'stop-server', podId }
      });

      if (error) {
        console.error('Error stopping server:', error);
        throw new Error('Failed to stop server');
      }
    } catch (error) {
      console.error('Error stopping server:', error);
      throw new Error('Failed to stop server');
    }
  }

  async updateServerActivity(serverId: number): Promise<void> {
    try {
      const { error } = await supabase
        .from('swarmui_servers')
        .update({ last_activity: new Date().toISOString() })
        .eq('id', serverId);

      if (error) {
        console.error('Error updating server activity:', error);
      }
    } catch (error) {
      console.error('Error updating server activity:', error);
    }
  }
}

export const serverManagementService = new ServerManagementService();
```

### 3.2 SwarmUI Service

Implement a service to interact with the SwarmUI API:

```typescript
// src/services/swarmUI/SwarmUIService.ts
import { supabase } from '../../boot/supabase';
import {
  SwarmUIGenerationParams,
  SwarmUIGenerationResponse,
  SwarmUIModel,
  SwarmUIServerInfo
} from './types';

export class SwarmUIService {
  async getServerInfo(): Promise<SwarmUIServerInfo> {
    try {
      const { data, error } = await supabase.functions.invoke('swarmui-generation', {
        method: 'GET',
        query: { path: 'info' }
      });

      if (error) {
        console.error('Error getting SwarmUI server info:', error);
        throw new Error('Failed to get SwarmUI server info');
      }

      return data;
    } catch (error) {
      console.error('Error getting SwarmUI server info:', error);
      throw new Error('Failed to get SwarmUI server info');
    }
  }

  async getModels(): Promise<SwarmUIModel[]> {
    try {
      const { data, error } = await supabase.functions.invoke('swarmui-generation', {
        method: 'GET',
        query: { path: 'models' }
      });

      if (error) {
        console.error('Error getting SwarmUI models:', error);
        throw new Error('Failed to get SwarmUI models');
      }

      return data.models || [];
    } catch (error) {
      console.error('Error getting SwarmUI models:', error);
      throw new Error('Failed to get SwarmUI models');
    }
  }

  async generateImage(params: SwarmUIGenerationParams): Promise<SwarmUIGenerationResponse> {
    try {
      const { data, error } = await supabase.functions.invoke('swarmui-generation', {
        method: 'POST',
        body: params
      });

      if (error) {
        console.error('Error generating image:', error);
        throw new Error('Failed to generate image');
      }

      return data;
    } catch (error) {
      console.error('Error generating image:', error);
      throw new Error('Failed to generate image');
    }
  }

  async getGenerationStatus(generationId: string): Promise<SwarmUIGenerationResponse> {
    try {
      const { data, error } = await supabase.functions.invoke('swarmui-generation', {
        method: 'GET',
        query: { path: `generations/${generationId}` }
      });

      if (error) {
        console.error('Error getting generation status:', error);
        throw new Error('Failed to get generation status');
      }

      return data;
    } catch (error) {
      console.error('Error getting generation status:', error);
      throw new Error('Failed to get generation status');
    }
  }
}

export const swarmUIService = new SwarmUIService();
```

## 4. Database Schema

Create the necessary tables in your Supabase database:

```sql
-- Server configurations
CREATE TABLE swarmui_server_configurations (
  id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
  name TEXT NOT NULL,
  description TEXT,
  template_id TEXT NOT NULL,
  gpu_type TEXT NOT NULL,
  gpu_count INTEGER NOT NULL DEFAULT 1,
  memory_gb INTEGER NOT NULL,
  disk_gb INTEGER NOT NULL,
  hourly_cost DECIMAL(10, 2) NOT NULL,
  is_default BOOLEAN NOT NULL DEFAULT FALSE,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Server instances
CREATE TABLE swarmui_servers (
  id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  pod_id TEXT NOT NULL,
  api_endpoint TEXT NOT NULL,
  api_key TEXT NOT NULL,
  status TEXT NOT NULL,
  gpu_type TEXT NOT NULL,
  started_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  stopped_at TIMESTAMP WITH TIME ZONE,
  last_activity TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Auto-shutdown function
CREATE OR REPLACE FUNCTION auto_shutdown_inactive_servers()
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
  v_shutdown_count INTEGER := 0;
  v_inactive_threshold INTERVAL := INTERVAL '30 minutes';
  v_server RECORD;
BEGIN
  FOR v_server IN
    SELECT * FROM swarmui_servers
    WHERE status = 'running'
    AND NOW() - last_activity > v_inactive_threshold
  LOOP
    -- Call RunPod API to stop the pod (this would be handled by the Edge Function)
    -- For now, just update the status
    UPDATE swarmui_servers
    SET status = 'stopping', stopped_at = NOW(), updated_at = NOW()
    WHERE id = v_server.id;
    
    v_shutdown_count := v_shutdown_count + 1;
  END LOOP;
  
  RETURN v_shutdown_count;
END;
$$;

-- Set up a cron job to run the auto-shutdown function
SELECT cron.schedule(
  'auto-shutdown-inactive-servers',
  '*/5 * * * *',  -- Run every 5 minutes
  $$SELECT auto_shutdown_inactive_servers()$$
);
```

## 5. Conclusion

With this integration, your application can:

1. Start SwarmUI servers on-demand
2. Generate images using the SwarmUI API
3. Automatically shut down inactive servers to save costs

The key advantage is that everything is automated - there's no need for manual configuration through the web interface, making it ideal for headless operation via API calls only.
