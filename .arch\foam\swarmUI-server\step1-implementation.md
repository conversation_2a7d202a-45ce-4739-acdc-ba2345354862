# Step 1: SwarmUI API Integration Implementation

This document outlines the implementation of Step 1 of the SwarmUI integration plan, which focuses on running SwarmUI in API mode and testing API access for image generation.

## Overview

The implementation consists of the following components:

1. **UI Components**:
   - Server controls component for starting/stopping SwarmUI servers
   - Image generation form for testing the API
   - Integration with the admin dashboard

2. **Backend Services**:
   - SwarmUI service for interacting with the API
   - Supabase Edge Functions for server management and image generation
   - Database tables for tracking server instances

3. **Workflow**:
   - Start a SwarmUI server on RunPod
   - Generate test images via the API
   - Save generated images to S3 and display in the preview tab

## Implementation Steps

### 1. Database Setup

Run the SQL script to create the necessary tables:

```bash
psql -h your-supabase-db-host -U postgres -d postgres -f scripts/db-updates/swarmui-tables.sql
```

Or execute the SQL commands directly in the Supabase SQL Editor.

### 2. Deploy Edge Functions

Deploy the Supabase Edge Functions:

```bash
# Navigate to the Supabase functions directory
cd supabase/functions

# Deploy the swarmui-server function
supabase functions deploy swarmui-server --no-verify-jwt

# Deploy the swarmui-generation function
supabase functions deploy swarmui-generation --no-verify-jwt

# Deploy the image-management function
supabase functions deploy image-management --no-verify-jwt
```

### 3. Set Environment Variables

Set the required environment variables for the Edge Functions:

```bash
# Set the RunPod API key
supabase secrets set RUNPOD_API_KEY=your-runpod-api-key

# Set the SwarmUI template ID (once created)
supabase secrets set SWARMUI_TEMPLATE_ID=your-template-id
```

### 4. RunPod Setup

1. Create a RunPod instance with the desired GPU
2. SSH into the instance
3. Install SwarmUI and ComfyUI using the provided scripts
4. Configure SwarmUI to use ComfyUI as a backend

#### SSH Command

```bash
ssh root@your-runpod-ip
```

#### Installation Script

Upload the `auto-backend-startup.sh` script to the RunPod instance:

```bash
scp .arch/foam/swarmUI-server/auto-backend-startup.sh root@your-runpod-ip:/workspace/
```

Make it executable and run it:

```bash
chmod +x /workspace/auto-backend-startup.sh
/workspace/auto-backend-startup.sh
```

### 5. Testing

1. Log in to the application as an admin
2. Navigate to the Admin Dashboard
3. Use the SwarmUI Server Controls to start a server
4. Once the server is running, test the connection
5. Generate a test image
6. Save the generated image to S3
7. Verify that the image appears in the preview tab

## Troubleshooting

### Server Not Starting

If the server fails to start:

1. Check the RunPod API key
2. Verify that the RunPod instance is created correctly
3. Check the RunPod logs for any errors

### API Connection Issues

If you can't connect to the SwarmUI API:

1. Verify that the server is in the "Running" state
2. Check that the API endpoint is correct
3. Ensure the API key is properly set

### Image Generation Failures

If image generation fails:

1. Check that ComfyUI is properly configured as a backend
2. Verify that the models are correctly installed
3. Check the SwarmUI logs for any errors

## Next Steps

After successfully implementing and testing Step 1:

1. Create a RunPod template with SwarmUI and ComfyUI pre-installed
2. Configure the template to use the auto-backend-startup.sh script
3. Update the SWARMUI_TEMPLATE_ID environment variable with the new template ID
4. Test template deployment from the application
