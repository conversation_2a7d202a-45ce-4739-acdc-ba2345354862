<template>
  <div id="q-app" :class="platformClass">
    <router-view />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, watch } from 'vue';
import { useQuasar } from 'quasar';
import PlatformUtil from 'src/utils/platform';
import { userSettingsService } from 'src/services/userSettingsService';

const $q = useQuasar();

// Get platform class for styling
const platformClass = computed(() => PlatformUtil.getPlatformClass());

// Initialize dark mode based on system preference or saved setting
onMounted(async () => {
  // Try to get theme setting from database first
  let dbTheme = null;
  try {
    dbTheme = await userSettingsService.getSetting('theme');
  } catch (error) {
    console.error('Error loading theme setting from database:', error);
  }

  // Check for saved theme preference (database or localStorage)
  const savedTheme = dbTheme || localStorage.getItem('dreambox-theme');
  let isDark = false;

  // If running in Electron, try to get theme from Electron first
  if (PlatformUtil.isElectron() && typeof window.electronAPI !== 'undefined') {
    try {
      // Get current theme from Electron
      const electronTheme = await window.electronAPI.getTheme();
      isDark = electronTheme === 'dark';

      // Set Electron theme based on saved preference
      if (savedTheme === 'dark') {
        await window.electronAPI.setTheme('dark');
        isDark = true;
      } else if (savedTheme === 'light') {
        await window.electronAPI.setTheme('light');
        isDark = false;
      } else {
        // Use system preference
        await window.electronAPI.setTheme('system');
      }
    } catch (error) {
      console.error('Error initializing Electron theme:', error);
      // Fall back to web implementation
      isDark = initializeWebTheme(savedTheme);
    }
  } else {
    // Standard web implementation
    isDark = initializeWebTheme(savedTheme);
  }

  // If we got the theme from the database, save it to localStorage for consistency
  if (dbTheme) {
    localStorage.setItem('dreambox-theme', dbTheme);
  }

  // Set Quasar dark mode
  $q.dark.set(isDark);

  // Add platform class to body
  document.body.classList.add(platformClass.value);
});

// Initialize theme for web context
function initializeWebTheme(savedTheme: string | null): boolean {
  if (savedTheme === 'dark') {
    return true;
  } else if (savedTheme === 'light') {
    return false;
  } else {
    // Use system preference
    return window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
  }
}

// Watch for system color scheme changes
watch(() => $q.dark.isActive, (isDark) => {
  // Apply appropriate class to body
  if (isDark) {
    document.body.classList.add('body--dark');
    document.body.classList.remove('body--light');
  } else {
    document.body.classList.add('body--light');
    document.body.classList.remove('body--dark');
  }

  // Save theme setting to localStorage
  localStorage.setItem('dreambox-theme', isDark ? 'dark' : 'light');

  // Save theme setting to database - use void to handle the promise
  void (async () => {
    try {
      await userSettingsService.saveSetting('theme', isDark ? 'dark' : 'light');
    } catch (error) {
      console.error('Error saving theme setting to database:', error);
    }
  })();
});
</script>

<style lang="scss">
// Global styles
body {
  transition: background-color 0.3s ease, color 0.3s ease;
}

// Platform-specific global styles
.platform-electron {
  // Add any Electron-specific global styles here
}

.platform-mobile {
  // Add any mobile-specific global styles here
  touch-action: manipulation; // Improves touch response
  -webkit-tap-highlight-color: transparent; // Removes tap highlight on iOS
}
</style>
