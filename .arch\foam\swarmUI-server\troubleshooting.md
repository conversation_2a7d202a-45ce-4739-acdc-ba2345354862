# SwarmUI Integration Troubleshooting Guide

This document provides solutions for common issues encountered during the SwarmUI integration.

## CORS Issues

### Symptoms
- Console errors like: `Access to fetch at 'https://ppzaqhuaqzjofsorlmbn.supabase.co/functions/v1/swarmui-server' from origin 'http://localhost:9000' has been blocked by CORS policy`
- Network requests failing with status code 0 or preflight errors

### Solutions

1. **Check Edge Function CORS Headers**
   
   Make sure all Edge Functions include proper CORS headers in their responses:

   ```typescript
   // For OPTIONS requests
   if (req.method === 'OPTIONS') {
     return new Response('ok', {
       headers: {
         'Access-Control-Allow-Origin': '*',
         'Access-Control-Allow-Methods': 'POST, GET, DELETE, OPTIONS',
         'Access-Control-Allow-Headers': 'Authorization, Content-Type',
       }
     })
   }

   // For all other responses
   return new Response(JSON.stringify(data), {
     headers: {
       'Content-Type': 'application/json',
       'Access-Control-Allow-Origin': '*',
       'Access-Control-Allow-Methods': 'POST, GET, DELETE, OPTIONS',
       'Access-Control-Allow-Headers': 'Authorization, Content-Type'
     }
   })
   ```

2. **Verify Edge Function Deployment**
   
   Check that the Edge Functions are properly deployed:
   
   - Go to the Supabase dashboard
   - Navigate to "Edge Functions"
   - Verify that all three functions (swarmui-server, swarmui-generation, image-management) are listed and deployed
   - Check the logs for any errors

3. **Test with a Simple Request**
   
   Create a simple test request to verify the Edge Function is working:
   
   ```javascript
   const { data, error } = await supabase.functions.invoke('swarmui-server', {
     method: 'GET',
     headers: { 'Content-Type': 'application/json' },
     body: { action: 'server-status' }
   });
   console.log('Response:', data, error);
   ```

## Authentication Issues

### Symptoms
- 401 Unauthorized errors
- "User not authenticated" errors in Edge Function logs

### Solutions

1. **Check User Authentication**
   
   Verify that the user is properly authenticated:
   
   ```javascript
   const { data: { user } } = await supabase.auth.getUser();
   console.log('Current user:', user);
   ```

2. **Verify JWT Token**
   
   If you're using `--no-verify-jwt` for development, make sure to remove it for production:
   
   ```bash
   supabase functions deploy swarmui-server
   ```

3. **Check RLS Policies**
   
   Verify that the Row Level Security policies are correctly set up for the `swarmui_servers` table.

## Database Issues

### Symptoms
- SQL errors in Edge Function logs
- Missing tables or columns
- Permission denied errors

### Solutions

1. **Verify Table Creation**
   
   Check that all required tables exist:
   
   ```sql
   SELECT * FROM information_schema.tables 
   WHERE table_schema = 'public' 
   AND table_name = 'swarmui_servers';
   ```

2. **Check Table Structure**
   
   Verify the table structure:
   
   ```sql
   SELECT column_name, data_type 
   FROM information_schema.columns 
   WHERE table_schema = 'public' 
   AND table_name = 'swarmui_servers';
   ```

3. **Test RLS Policies**
   
   Test the RLS policies:
   
   ```sql
   -- As the authenticated user
   SELECT * FROM swarmui_servers;
   
   -- As a different user (should return no rows)
   SET LOCAL ROLE anon;
   SELECT * FROM swarmui_servers;
   RESET ROLE;
   ```

## RunPod API Issues

### Symptoms
- "Failed to start server" errors
- "RunPod API error" messages
- Servers not starting or stopping

### Solutions

1. **Check API Key**
   
   Verify that the RunPod API key is correctly set:
   
   ```bash
   supabase secrets list
   ```

2. **Test RunPod API Directly**
   
   Test the RunPod API directly to verify it's working:
   
   ```javascript
   const response = await fetch('https://api.runpod.io/v2/me', {
     headers: {
       'Authorization': `Bearer ${RUNPOD_API_KEY}`
     }
   });
   const data = await response.json();
   console.log('RunPod API response:', data);
   ```

3. **Check RunPod Dashboard**
   
   Check the RunPod dashboard to see if pods are being created and destroyed correctly.

## Image Generation Issues

### Symptoms
- "Failed to generate image" errors
- Timeouts during image generation
- Empty or corrupted images

### Solutions

1. **Check SwarmUI Server Status**
   
   Verify that the SwarmUI server is running:
   
   ```javascript
   const status = await swarmUIService.checkServerStatus();
   console.log('Server status:', status);
   ```

2. **Test API Endpoint Directly**
   
   Test the SwarmUI API endpoint directly:
   
   ```javascript
   const response = await fetch(`${apiEndpoint}/api/info`, {
     headers: {
       'Authorization': `Bearer ${apiKey}`
     }
   });
   const data = await response.json();
   console.log('SwarmUI API response:', data);
   ```

3. **Check SwarmUI Logs**
   
   SSH into the RunPod instance and check the SwarmUI logs:
   
   ```bash
   ssh root@your-runpod-ip
   cat /workspace/comfyui.log
   cat /workspace/SwarmUI/logs/swarm.log
   ```

## Storage Issues

### Symptoms
- "Failed to upload image" errors
- Missing images in the storage bucket
- Permission denied errors when accessing images

### Solutions

1. **Check Storage Bucket**
   
   Verify that the storage bucket exists and is accessible:
   
   ```javascript
   const { data, error } = await supabase.storage.getBucket('images');
   console.log('Bucket info:', data, error);
   ```

2. **Test Image Upload**
   
   Test uploading a small image:
   
   ```javascript
   const { data, error } = await supabase.storage
     .from('images')
     .upload(`test/${user.id}/test.png`, imageBlob, {
       contentType: 'image/png'
     });
   console.log('Upload result:', data, error);
   ```

3. **Check RLS Policies**
   
   Verify the RLS policies for the storage bucket:
   
   ```sql
   SELECT * FROM storage.policies 
   WHERE bucket_id = 'images';
   ```

## TypeScript/ESLint Issues

### Symptoms
- TypeScript compilation errors
- ESLint warnings about promises, types, etc.

### Solutions

1. **Fix Type Imports**
   
   Use `import type` for type-only imports:
   
   ```typescript
   import type { MyType } from './types';
   ```

2. **Handle Floating Promises**
   
   Use `void` to explicitly ignore promise results:
   
   ```typescript
   void somePromiseFunction();
   ```

3. **Fix setTimeout with Promises**
   
   Wrap promise-returning functions in an arrow function:
   
   ```typescript
   setTimeout(() => { void somePromiseFunction(); }, 1000);
   ```

4. **Avoid `any` Types**
   
   Replace `any` with more specific types:
   
   ```typescript
   // Instead of
   function process(data: any): void {}
   
   // Use
   function process(data: Record<string, unknown>): void {}
   ```

## General Debugging Tips

1. **Check Browser Console**
   
   Always check the browser console for errors and warnings.

2. **Enable Verbose Logging**
   
   Add more console.log statements to track the flow of execution.

3. **Use Network Tab**
   
   Use the browser's Network tab to inspect API requests and responses.

4. **Check Edge Function Logs**
   
   Check the Edge Function logs in the Supabase dashboard for server-side errors.

5. **Isolate the Problem**
   
   Test each component separately to isolate the source of the problem.
