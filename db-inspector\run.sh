#!/bin/bash

# Change to the script directory
cd "$(dirname "$0")"

# Check if bun is installed
if ! command -v bun &> /dev/null; then
    echo "Error: bun is not installed. Please install bun first."
    echo "Visit https://bun.sh/ for installation instructions."
    exit 1
fi

# Run setup if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "Running setup..."
    bun setup
fi

# Parse command line arguments
case "$1" in
    setup)
        bun setup
        ;;
    start)
        bun start
        ;;
    generate)
        bun generate
        ;;
    query)
        bun query
        ;;
    browse)
        shift
        bun browse "$@"
        ;;
    export)
        shift
        bun export "$@"
        ;;
    *)
        echo "Enhanced Database Inspector"
        echo ""
        echo "Usage: ./run.sh [command] [options]"
        echo ""
        echo "Commands:"
        echo "  setup                 Run setup"
        echo "  start                 Fetch database structure"
        echo "  generate              Generate documentation"
        echo "  query                 Run interactive SQL queries"
        echo "  browse [options]      Browse table data"
        echo "  export [options]      Export data"
        echo ""
        echo "For more information on a command, run:"
        echo "  ./run.sh [command] --help"
        ;;
esac
