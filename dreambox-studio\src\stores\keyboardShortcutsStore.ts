import { defineStore } from 'pinia';
import { useLocalStorage } from '@vueuse/core';
import { ref, watch } from 'vue';
import { userSettingsService } from 'src/services/userSettingsService';
import { commandRegistry } from 'src/services/commands/registry';

export interface ShortcutDefinition {
  id: string;
  command: string;
  keys: string;
  description: string;
  context?: string | undefined; // Optional context where the shortcut is applicable
}

export const useKeyboardShortcutsStore = defineStore('keyboardShortcuts', () => {
  // Store shortcuts in local storage for persistence
  const shortcuts = useLocalStorage<ShortcutDefinition[]>('dreambox-keyboard-shortcuts', []);

  // Loading state
  const isLoading = ref(false);
  const isInitialized = ref(false);

  // Default shortcuts that can be restored
  const defaultShortcuts: ShortcutDefinition[] = [
    { id: '1', command: 'toggleDarkMode', keys: 'Ctrl+Shift+D', description: 'Toggle dark mode' },
    { id: '2', command: 'toggleLeftDrawer', keys: 'Ctrl+B', description: 'Toggle left drawer' },
    { id: '3', command: 'toggleRightDrawer', keys: 'Ctrl+Shift+B', description: 'Toggle right drawer' },
    { id: '4', command: 'openDashboard', keys: 'Ctrl+Home', description: 'Go to dashboard' },
    { id: '5', command: 'logout', keys: 'Ctrl+Shift+L', description: 'Log out' },
    { id: '6', command: 'openSettings', keys: '', description: 'Open settings page' },
    { id: '7', command: 'help', keys: '', description: 'Show available commands' },
    // Role-specific commands will be added dynamically based on user role
  ];

  // Initialize with default shortcuts if empty
  if (shortcuts.value.length === 0) {
    shortcuts.value = [...defaultShortcuts];
  }

  // Load shortcuts from database
  async function loadFromDatabase() {
    isLoading.value = true;
    try {
      const settingValue = await userSettingsService.getSetting('keyboard_shortcuts');
      if (settingValue) {
        try {
          const dbShortcuts = JSON.parse(settingValue) as ShortcutDefinition[];
          shortcuts.value = dbShortcuts;
        } catch (e) {
          console.error('Error parsing keyboard shortcuts from database:', e);
        }
      } else {
        // If no shortcuts in database, save the current ones
        await saveToDatabase();
      }
      isInitialized.value = true;
    } catch (e) {
      console.error('Error loading keyboard shortcuts from database:', e);
    } finally {
      isLoading.value = false;
    }
  }

  // Save shortcuts to database
  async function saveToDatabase() {
    try {
      const settingValue = JSON.stringify(shortcuts.value);
      await userSettingsService.saveSetting('keyboard_shortcuts', settingValue);
    } catch (e) {
      console.error('Error saving keyboard shortcuts to database:', e);
    }
  }

  // Watch for changes to shortcuts and save to database
  watch(shortcuts, async () => {
    if (isInitialized.value) {
      await saveToDatabase();
    }
  }, { deep: true });

  // Add a new shortcut
  function addShortcut(shortcut: Omit<ShortcutDefinition, 'id'>) {
    const id = Date.now().toString();
    shortcuts.value.push({ ...shortcut, id });
  }

  // Update an existing shortcut
  function updateShortcut(id: string, shortcut: { keys?: string; command?: string; description?: string; context?: string | undefined }) {
    const index = shortcuts.value.findIndex(s => s.id === id);
    if (index !== -1) {
      const existingShortcut = shortcuts.value[index];
      if (existingShortcut) {
        // Create a new object with the updated properties
        // TypeScript needs us to be explicit about required properties
        shortcuts.value[index] = {
          id: existingShortcut.id,
          command: shortcut.command !== undefined ? shortcut.command : existingShortcut.command,
          keys: shortcut.keys !== undefined ? shortcut.keys : existingShortcut.keys,
          description: shortcut.description !== undefined ? shortcut.description : existingShortcut.description,
          context: shortcut.context
        };
      }
    }
  }

  // Remove a shortcut
  function removeShortcut(id: string) {
    shortcuts.value = shortcuts.value.filter(s => s.id !== id);
  }

  // Reset to default shortcuts
  async function resetToDefaults() {
    shortcuts.value = [...defaultShortcuts];
    await saveToDatabase();
  }

  // Check if a key combination is already used
  function isShortcutUsed(keys: string, excludeId?: string): boolean {
    return shortcuts.value.some(s => s.keys === keys && s.id !== excludeId);
  }

  // Get shortcut by command
  function getShortcutByCommand(command: string): ShortcutDefinition | undefined {
    return shortcuts.value.find(s => s.command === command);
  }

  // Get shortcut by keys
  function getShortcutByKeys(keys: string): ShortcutDefinition | undefined {
    return shortcuts.value.find(s => s.keys === keys);
  }

  // Sync shortcuts with available commands
  async function syncWithAvailableCommands() {
    try {
      // Get all available commands from the registry
      const availableCommands = commandRegistry.getAvailableCommands();

      // Create a map of existing shortcuts for quick lookup
      const existingShortcutsMap = new Map(
        shortcuts.value.map(shortcut => [shortcut.command, shortcut])
      );

      // Create a new array to hold updated shortcuts
      const updatedShortcuts: ShortcutDefinition[] = [];

      // First, add all existing shortcuts that are still valid commands
      shortcuts.value.forEach(shortcut => {
        if (availableCommands.some(cmd => cmd.name === shortcut.command)) {
          updatedShortcuts.push(shortcut);
        }
      });

      // Then, add any new commands that don't have shortcuts yet
      availableCommands.forEach(command => {
        if (!existingShortcutsMap.has(command.name)) {
          updatedShortcuts.push({
            id: Date.now().toString() + Math.random().toString(36).substring(2, 9),
            command: command.name,
            keys: '', // No shortcut assigned by default
            description: command.description
          });
        }
      });

      // Update the shortcuts array
      shortcuts.value = updatedShortcuts;

      // Save to database
      await saveToDatabase();

      return true;
    } catch (error) {
      console.error('Error syncing shortcuts with available commands:', error);
      return false;
    }
  }

  // Initialize by loading from database
  loadFromDatabase()
    .then(() => syncWithAvailableCommands())
    .catch(e => console.error('Failed to initialize keyboard shortcuts:', e));

  return {
    shortcuts,
    isLoading,
    isInitialized,
    addShortcut,
    updateShortcut,
    removeShortcut,
    resetToDefaults,
    isShortcutUsed,
    getShortcutByCommand,
    getShortcutByKeys,
    loadFromDatabase,
    saveToDatabase,
    syncWithAvailableCommands
  };
});
