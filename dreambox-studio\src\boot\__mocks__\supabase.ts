// Mock Supabase client
export const supabase = {
  auth: {
    getSession: () => Promise.resolve({
      data: { session: null },
      error: null
    }),
    getUser: () => Promise.resolve({
      data: {
        user: {
          id: 'test-user-id',
          email: '<EMAIL>',
          user_metadata: {
            first_name: 'Test',
            last_name: 'User'
          }
        }
      },
      error: null
    }),
    signInWithPassword: () => Promise.resolve({
      data: {
        user: {
          id: 'test-user-id',
          email: '<EMAIL>'
        },
        session: {
          access_token: 'test-token'
        }
      },
      error: null
    }),
    signOut: () => Promise.resolve({ error: null }),
    onAuthStateChange: () => ({
      data: { subscription: { unsubscribe: () => {} } },
      error: null
    })
  },
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  from: (table: string) => ({
    select: () => ({
      eq: () => ({
        single: () => Promise.resolve({
          data: {
            id: 1,
            user_id: 'test-user-id',
            first_name: 'Test',
            last_name: 'User',
            is_active: true
          },
          error: null
        }),
        limit: () => Promise.resolve({
          data: [
            {
              app_user_id: 1,
              user_id: 'test-user-id',
              first_name: 'Test',
              last_name: 'User',
              company_id: 1,
              company_name: 'Test Company',
              role_id: 1,
              role_name: 'admin'
            }
          ],
          error: null
        })
      })
    })
  })
};
