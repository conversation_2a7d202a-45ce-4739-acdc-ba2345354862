

# System Description

## Introduction

This document provides an overview of the overall system architecture for our application. It covers the high-level components, their interactions, and the key technologies used.

App purpose: 
    - automate creation of POD (print on demand) designs
    - automate publishing POD products on shopify
    - automate market analysis
    - automate product creation and management
    - automate product sales and fulfillment
    - automate customer support and communication
    - automate financial management and reporting
    - automate legal compliance and documentation
    - automate marketing and advertising
    - automate customer relationship management (CRM)
    - automate quality control and assurance
    - automate customer feedback and reviews
    - automate product research and development

Initial idea: app has two modes: designer and administrator. When user opens app, he gets login screen, if his role is designer, he gets designer mode, if his role is administrator, he gets administrator mode. This is important, because designers are not supposed to see sales data etc, they are supposed to create designs. Administrator app mode is used to manage the products, sales, etc, designer app mode is used to create designs. Administrator mode has access to all features, designer mode has access to limited features (crete designs, publish designs, etc.). Administrator mode has access to all data, designer mode has access to limited data. 
There is also super-admin, which has access to all features and data, managing all teams, users, etc. This role is actually developer role, because he supervises servers, database, develops and manages app, etc.

App is used by many companies (planned are 7 companies), so each user (designer or administrator) belongs to one company. Company has its own data, users, etc. 

When user log in (via supabase), app gets information about user, his company, his team, his role, etc. If he is designer, he gets designer app mode, if he is administrator, he gets administrator app mode. 

App has many phases:

Phase 1: first designer opens app in designer mode and is working on design template. Templates are blueprints for POD images, which are later created as products. Template can be made by user or by AI via n8n Designer Agent or combined (designer & AI). During design phase, template can be tested on various products (t-shirts, mugs, etc.) and in template is written, which products can be created from it. When template is ready, it is published and can be used to create products. So template can have two states: draft and published. When draft is published, it is visible and can be used by administrator app mode, so it is not possible to unpublish template. Designer has access to all templates created by him, administrator has access to all templates in his company. Templates, which are in templates table, are basically prompts (or part of the prompt), which will be used to create images for POD products. So templates have many fields like this:

Table templates {
  angles array
  artistic_reference array
  author_id bigint [ref: > authors.id]
  blend_concepts array
  camera_settings array
  colors array
  composition array
  contrast_juxtaposition array
  created_at timestamp
  description text
  emotional_gradients array
  id bigint [pk]
  layer_back array
  layer_front array
  layer_middle array
  light array
  mood_atmosphere array
  published boolean
  style array
  style_fusion array
  temporal_narrative array
  topics array
  unusual_perspective array
}

There will be also other fields in template, which are specific for swarmUI and flux model, which are used to create images, for example seedId.

About companies:
Every company has it's own design guidelines, it philosophy, it's unique style, branding, etc. This is part of design template and can not be changed by designer, only by administrator. Administrator can change design guidelines, philosophy, branding, etc. but not in reverse, only for future templates. App is intended to serve limited number of companies. It is important that these companies doesn't completely overlap with style, philosophy, branding, etc.

About login: we will use supabase for login, so user can login with email and password, google, github, etc. In users table will alredy be users with their roles, companies, teams, etc. If someone is not in users table, he can't login. So we will have to create users in users table manually. Every page in app will first check if user is logged-in, otherwise it will show login screen.

Phase 2: administrator opens app in administrator mode and is working on products. Products are created from templates. Product are created by AI via n8n Product Maker Agent. We will have table with all events. This events table will be populated with all available events. Event has it's name, it has date, it has description, it has location, maybe also some other fields. Important is date, so we can target and prepaire for example products for New Year celebration or some other event in advance. Event also need time span, so we know how much in advance we need to prepaire for event. Location is also importans, so if we are targetting Chinese New Year, we will choose templates, which have this event in it's list of events, it has appropriate style, etc.

We also have products table (all POD products). In template designer already marked which products (he thinks) can be created from it and which events can be targeted (designer can also make more general templates, which can cover multiple events). Additionally, template also have field (array) with locations, so for example if one designer is specialized for chinese market, he will mark this in template. So n8n Product Maker Agent will create products from templates and each template already have list of possible products, marked by designer (for example - t-shirts, hoodies, etc.). Selection of products is done by AI and by administrator, so administrator selects templates and assign AI a task, to create products from selected templates. He can also ask AI what products AI propose for some template and to check if designers selection of products was correct and then create products.

Phase 3: When we decided (administrator and AI), which products will be created, then AI (n8n Product Maker Agent) starts to create images for these products. Images are created by AI via server which has swarmUI installed and is specifically tasked for this (strong GPU).
n8n Product Maker Agent orders image from ImageServer via API and when he get's these images, he saves them in supabase. He also updates products table with link to image (amazon s3 bucket).

Phase 4: When images are created, he calls another n8n agent, which will publish these products on shopify (Publisher Agent). Publisher Agent first creates product mocs (he puts image on t-shirt, hoodie, etc.), so administrator can check finished products. When administrator is satisfied and checks products, which are ready for publishing, he calls Publisher Agent, which will publish these products on shopify.
Publisher Agent then via API calls shopify and creates product on shopify. He also updates products table with link to product on shopify.

Phase 5: When product is published on shopify, Marketing Agent starts to create marketing material for social media posts for this product. So Marketing agent again calls ImageServer via API and creates images and videos for social media posts. He also creates text for social media posts. When he is done, he updates products table with link to social media posts. Marketing agent is responsible for all social networks.
He connects with social networks via API and creates posts, he follows social networks and responds to comments, etc. He likes and comments on social media posts of other users, followers, etc.

Phase 6: When product is sold, Communicator Agent starts to communicate with customer. He sends email to customer with information about product, when it will be delivered, etc. He is responsible for customers table in supabase. He will regularly send to customers emails with information about new products, etc. and he will check product page on shopify, etsy, ebay and amazon, it there are some complaints, comments, etc. and he will communicate with customer. He will also check company email account for any new messages from customers and reply to them.

Phase 7: There is also Statistics Agent, which will regularly collect statistics about products, customers, etc. and will create reports for administrator. He will save all statistics in supabase product table, so administrator when looking on products table will be informed about all statistics: sales, regions, times, complains, etc. This data will be used by administrator and by Product Maker Agent to create better products or to change something in targetting decisions, etc. The same data will be used by Marketing Agent to create better marketing material, etc. and also by Communicator Agent to communicate with customers better, because all these Agents and also administrator will see which products are selling better, which are not selling at all, which are selling in which regions, etc. so they can create good targetting decisions for future products and future events. Statistics agent will also make a daily report about all sales, products, etc. so administrator can see what is happening in his business and what needs to be done better.

Extra: there is one more agent, which is Command Agent. User can use app like normal app with mouse and keyboard or touch and keyboard on the phone. But can also use command mode, which is microphone. His voice command is managed by Command Agent, which calls (11labs) for speech to text and then calls command in the app. So user can say something lik: "Create product from template 123" and app will create product from template 123. Or user can say: "Show me procucts for event 123" and app will show him products for event 123. So everything what can user do with the mouse and keyboard, can also do with the voice. But for this to work, we need to expose our app functions as API to be used by Command Agent, which is located in the cloud (n8n). Command Agent can also call another Agents for various tasks.

Summary: for now we have only three types of users: designer, administrator and super-admin. Administrator can be also designer so company can be one-man-band. In administrator mode we will have all UI which is in designer mode, so basically designer mode is just heavily stripped version of administrator mode. But later we may add more types of users like financial manager, etc. which will have access to specific parts of app. Probably the best way is to use roles, which is standard way how users are managed. So we have companies and users. One user can be in multiple companies and have different roles in each company.  
