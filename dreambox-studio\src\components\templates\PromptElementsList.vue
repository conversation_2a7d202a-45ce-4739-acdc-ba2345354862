<template>
  <div class="prompt-elements-list">
    <div v-if="loading" class="text-center q-pa-md">
      <q-spinner color="primary" size="2em" />
      <div class="q-mt-sm">Loading elements...</div>
    </div>

    <div v-else-if="error" class="text-center q-pa-md text-negative">
      <q-icon name="error" size="2em" />
      <div class="q-mt-sm">{{ error }}</div>
    </div>

    <div v-else-if="elements.length === 0" class="text-center q-pa-md text-grey">
      <q-icon name="info" size="2em" />
      <div class="q-mt-sm">No prompt elements selected for this template.</div>
    </div>

    <div v-else>
      <!-- Desktop view: Cards by type -->
      <div class="row q-col-gutter-md gt-xs">
        <div
          v-for="(elements, typeName) in groupedElements"
          :key="typeName"
          class="col-12 col-md-6 col-lg-4"
        >
          <q-card flat bordered>
            <q-card-section class="q-pb-xs">
              <div class="text-subtitle1">{{ formatElementTypeName(typeName) }}</div>
            </q-card-section>

            <q-card-section class="q-pt-none">
              <q-list dense>
                <q-item v-for="element in elements" :key="element.id">
                  <q-item-section>
                    <q-item-label>{{ element.value }}</q-item-label>
                    <q-item-label caption v-if="element.description">
                      {{ element.description }}
                    </q-item-label>
                  </q-item-section>
                  <q-item-section side v-if="!element.is_system">
                    <q-badge color="primary" label="Custom" />
                  </q-item-section>
                </q-item>
                <q-item v-if="elements.length === 0">
                  <q-item-section>
                    <q-item-label class="text-grey">No values selected</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </q-card-section>
          </q-card>
        </div>
      </div>

      <!-- Mobile view: Tree -->
      <div class="xs-only">
        <q-tree
          :nodes="elementTreeData"
          node-key="id"
          no-connectors
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { usePromptElementsService, type PromptElement } from 'src/services/promptElementsService';
import type { QTreeNode } from 'quasar';

const props = defineProps({
  templateId: {
    type: Number,
    required: true
  }
});

// Services
const promptElementsService = usePromptElementsService();
const { loading, error, getTemplateElements } = promptElementsService;

// State
const elements = ref<PromptElement[]>([]);

// Computed
const groupedElements = computed(() => {
  const grouped: Record<string, PromptElement[]> = {};

  elements.value.forEach(element => {
    // Use type_name if available, otherwise use 'Other'
    const typeName = (element as PromptElement & { type_name?: string }).type_name || 'Other';
    if (!grouped[typeName]) {
      grouped[typeName] = [];
    }
    grouped[typeName].push(element);
  });

  return grouped;
});

const elementTreeData = computed<QTreeNode[]>(() => {
  return Object.entries(groupedElements.value).map(([typeName, typeElements]) => {
    return {
      id: typeName,
      label: formatElementTypeName(typeName),
      children: typeElements.map(element => ({
        id: `${typeName}-${element.id}`,
        label: element.value,
        caption: element.description,
        icon: element.is_system ? '' : 'star',
        iconColor: element.is_system ? '' : 'primary'
      })) as QTreeNode[]
    } as QTreeNode;
  });
});

// Lifecycle
onMounted(async () => {
  await fetchElements();
});

// Watch for template ID changes
watch(() => props.templateId, async () => {
  await fetchElements();
});

// Methods
async function fetchElements() {
  try {
    elements.value = await getTemplateElements(props.templateId);
  } catch (err) {
    console.error('Error fetching template elements:', err);
  }
}

function formatElementTypeName(name: string): string {
  return name
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}
</script>

<style lang="scss" scoped>
.prompt-elements-list {
  width: 100%;
}
</style>
