// Import design tokens
@import './design-tokens.scss';

// Apply design tokens to Quasar variables
:root {
  // Colors
  --q-primary: #{$primary};
  --q-secondary: #{$secondary};
  --q-accent: #{$accent};
  --q-positive: #{$positive};
  --q-negative: #{$negative};
  --q-info: #{$info};
  --q-warning: #{$warning};
  
  // Typography
  --q-font-family: #{$typography-font-family};
  
  // Spacing
  --q-space-base: #{$space-md};
  --q-space-xs: #{$space-xs};
  --q-space-sm: #{$space-sm};
  --q-space-md: #{$space-md};
  --q-space-lg: #{$space-lg};
  --q-space-xl: #{$space-xl};
}

// Light mode specific variables
.body--light {
  --q-primary-light: #{$primary-light};
  --q-primary-dark: #{$primary-dark};
  --q-secondary-light: #{$secondary-light};
  --q-secondary-dark: #{$secondary-dark};
  
  --app-background: #{$light-page};
  --app-surface: #{$light};
  --app-text-primary: rgba(0, 0, 0, 0.87);
  --app-text-secondary: rgba(0, 0, 0, 0.6);
  --app-text-hint: rgba(0, 0, 0, 0.38);
  --app-divider: rgba(0, 0, 0, 0.12);
  
  // Card styles
  --card-background: white;
  --card-shadow: #{$shadow-1};
  --card-hover-shadow: #{$shadow-2};
}

// Dark mode specific variables
.body--dark {
  --q-primary-light: #{$primary-dark};
  --q-primary-dark: #{$primary-light};
  --q-secondary-light: #{$secondary-dark};
  --q-secondary-dark: #{$secondary-light};
  
  --app-background: #{$dark-page};
  --app-surface: #{$dark};
  --app-text-primary: rgba(255, 255, 255, 0.87);
  --app-text-secondary: rgba(255, 255, 255, 0.6);
  --app-text-hint: rgba(255, 255, 255, 0.38);
  --app-divider: rgba(255, 255, 255, 0.12);
  
  // Card styles
  --card-background: #{$dark};
  --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  --card-hover-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

// Platform-specific styles
.platform-electron {
  // Add extra padding at the top for the window frame
  .q-layout {
    --electron-titlebar-height: #{$electron-window-frame-height};
  }
  
  .q-header {
    padding-top: var(--electron-titlebar-height);
  }
}

.platform-mobile {
  // Adjust for mobile devices
  --q-space-base: #{$space-sm};
  
  // Make touch targets larger
  .q-btn {
    min-height: 44px;
  }
  
  // Adjust card padding
  .q-card {
    padding: #{$space-sm};
  }
}

// Common component styles
.q-card {
  background-color: var(--card-background);
  box-shadow: var(--card-shadow);
  transition: box-shadow #{$transition-medium};
  
  &:hover {
    box-shadow: var(--card-hover-shadow);
  }
}

// Typography styles
h1, h2, h3, h4, h5, h6 {
  font-family: $typography-heading-font-family;
  margin-top: 0;
  margin-bottom: $space-md;
  font-weight: $font-weight-medium;
  line-height: $line-height-sm;
}

h1 {
  font-size: $font-size-xxl;
  
  @media (max-width: $breakpoint-sm) {
    font-size: $font-size-xl;
  }
}

h2 {
  font-size: $font-size-xl;
  
  @media (max-width: $breakpoint-sm) {
    font-size: $font-size-lg;
  }
}

h3 {
  font-size: $font-size-lg;
}

h4 {
  font-size: $font-size-md;
}

p {
  margin-top: 0;
  margin-bottom: $space-md;
  line-height: $line-height-md;
}

// Utility classes
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.elevation-1 { box-shadow: $shadow-1; }
.elevation-2 { box-shadow: $shadow-2; }
.elevation-3 { box-shadow: $shadow-3; }
.elevation-4 { box-shadow: $shadow-4; }
.elevation-5 { box-shadow: $shadow-5; }

// Responsive container
.container {
  width: 100%;
  padding-right: $space-md;
  padding-left: $space-md;
  margin-right: auto;
  margin-left: auto;
  
  @media (min-width: $breakpoint-sm) {
    max-width: 540px;
  }
  
  @media (min-width: $breakpoint-md) {
    max-width: 720px;
  }
  
  @media (min-width: $breakpoint-lg) {
    max-width: 960px;
  }
  
  @media (min-width: $breakpoint-xl) {
    max-width: 1140px;
  }
}

// Fluid container (full width with padding)
.container-fluid {
  width: 100%;
  padding-right: $space-md;
  padding-left: $space-md;
  margin-right: auto;
  margin-left: auto;
  
  @media (max-width: $breakpoint-sm) {
    padding-right: $space-sm;
    padding-left: $space-sm;
  }
}
