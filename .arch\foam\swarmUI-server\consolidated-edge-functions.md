# Consolidated Edge Functions for SwarmUI and Image Management

This document outlines the consolidated approach for Supabase Edge Functions to work within the 10 function limit of the free plan while supporting both SwarmUI server management and image storage.

## Overview

Instead of creating separate Edge Functions for each operation, we'll consolidate related functionality into a smaller number of versatile Edge Functions:

1. **`image-management`**: Handles all S3 image operations (already documented in [Image Organization](../image-organization/04-edge-functions.md))
2. **`swarmui-server`**: Manages RunPod server lifecycle (start, stop, status)
3. **`swarmui-generation`**: Handles image generation requests and responses

This approach reduces the number of Edge Functions from potentially 10+ down to just 3, while maintaining all the functionality.

## 1. SwarmUI Server Management Function

This Edge Function handles all RunPod server lifecycle operations:

```typescript
// supabase/functions/swarmui-server/index.ts
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import axios from 'https://esm.sh/axios@1.3.4'

serve(async (req) => {
  // CORS headers
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Authorization, Content-Type',
      }
    })
  }

  // Create Supabase client
  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
  )
  
  // Get user from request
  const { data: { user } } = await supabaseClient.auth.getUser()
  if (!user) {
    return new Response(JSON.stringify({ error: 'Unauthorized' }), { 
      status: 401, 
      headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' } 
    })
  }
  
  try {
    const url = new URL(req.url)
    const action = url.searchParams.get('action')
    
    // RunPod API configuration
    const RUNPOD_API_KEY = Deno.env.get('RUNPOD_API_KEY')
    const RUNPOD_API_URL = 'https://api.runpod.io/v2'
    
    if (!RUNPOD_API_KEY) {
      throw new Error('RunPod API key not configured')
    }
    
    // Handle different actions
    switch (action) {
      case 'start-server':
        return handleStartServer(req, supabaseClient, RUNPOD_API_KEY, RUNPOD_API_URL)
      
      case 'stop-server':
        return handleStopServer(req, supabaseClient, RUNPOD_API_KEY, RUNPOD_API_URL)
      
      case 'get-server-status':
        return handleGetServerStatus(req, supabaseClient, RUNPOD_API_KEY, RUNPOD_API_URL)
      
      case 'list-templates':
        return handleListTemplates(req, supabaseClient, RUNPOD_API_KEY, RUNPOD_API_URL)
        
      default:
        return new Response(JSON.stringify({ error: 'Invalid action' }), { 
          status: 400, 
          headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' } 
        })
    }
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), { 
      status: 500, 
      headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' } 
    })
  }
})

// Handler for starting a server
async function handleStartServer(req, supabaseClient, apiKey, apiUrl) {
  const { templateId, gpuTypeId } = await req.json()
  
  // Check if user has permission to start servers
  const { data: appUser, error: userError } = await supabaseClient
    .from('app_users')
    .select('id')
    .eq('user_id', user.id)
    .single()
  
  if (userError || !appUser) {
    throw new Error('User not found or not authorized')
  }
  
  // Check if user has admin role
  const { data: userRoles, error: roleError } = await supabaseClient
    .from('companies_users_roles')
    .select('role_id')
    .eq('app_user_id', appUser.id)
    .eq('role_id', 1) // Assuming 1 is the admin role ID
  
  if (roleError || userRoles.length === 0) {
    throw new Error('User does not have permission to start servers')
  }
  
  // Start the server using RunPod API
  const response = await axios.post(
    `${apiUrl}/pod/run`,
    {
      cloudType: 'ALL',
      gpuCount: 1,
      templateId,
      gpuTypeId,
      name: `SwarmUI-${new Date().toISOString()}`,
      containerDiskInGb: 30,
      volumeInGb: 100,
      volumeMountPath: '/workspace'
    },
    {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    }
  )
  
  if (response.status !== 200) {
    throw new Error(`Failed to start server: ${response.data.message || 'Unknown error'}`)
  }
  
  const podId = response.data.id
  
  // Create a record in the gpu_servers table
  const { data: serverRecord, error: dbError } = await supabaseClient
    .from('gpu_servers')
    .insert({
      pod_id: podId,
      user_id: appUser.id,
      status: 'starting',
      template_id: templateId,
      gpu_type_id: gpuTypeId,
      api_endpoint: null, // Will be updated when server is ready
      created_at: new Date().toISOString()
    })
    .select()
    .single()
  
  if (dbError) {
    throw new Error(`Failed to create server record: ${dbError.message}`)
  }
  
  return new Response(JSON.stringify({
    success: true,
    message: 'Server starting',
    server: {
      id: serverRecord.id,
      podId,
      status: 'starting'
    }
  }), { 
    headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' } 
  })
}

// Additional handler functions for other actions...
```

## 2. SwarmUI Generation Function

This Edge Function handles image generation requests:

```typescript
// supabase/functions/swarmui-generation/index.ts
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import axios from 'https://esm.sh/axios@1.3.4'

serve(async (req) => {
  // CORS and authentication code similar to above...
  
  try {
    const url = new URL(req.url)
    const action = url.searchParams.get('action') || 'generate'
    
    switch (action) {
      case 'generate':
        return handleGenerateImage(req, supabaseClient)
      
      case 'check-status':
        return handleCheckStatus(req, supabaseClient)
      
      case 'get-models':
        return handleGetModels(req, supabaseClient)
        
      default:
        return new Response(JSON.stringify({ error: 'Invalid action' }), { 
          status: 400, 
          headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' } 
        })
    }
  } catch (error) {
    // Error handling...
  }
})

// Handler for generating images
async function handleGenerateImage(req, supabaseClient) {
  const generationParams = await req.json()
  
  // Get the active server for this user
  const { data: server, error: serverError } = await supabaseClient
    .from('gpu_servers')
    .select('*')
    .eq('user_id', appUser.id)
    .eq('status', 'running')
    .order('created_at', { ascending: false })
    .limit(1)
    .single()
  
  if (serverError || !server) {
    throw new Error('No active server found. Please start a server first.')
  }
  
  // Create a record in the database
  const { data: generationRecord, error: dbError } = await supabaseClient
    .from('image_generations')
    .insert({
      user_id: appUser.id,
      server_id: server.id,
      prompt: generationParams.prompt,
      negative_prompt: generationParams.negative_prompt || '',
      width: generationParams.width,
      height: generationParams.height,
      num_images: generationParams.batch_size || 1,
      seed: generationParams.seed,
      model: generationParams.model,
      sampler: generationParams.sampler,
      steps: generationParams.steps,
      cfg_scale: generationParams.cfg_scale,
      status: 'pending'
    })
    .select()
    .single()
  
  if (dbError) {
    throw new Error(`Failed to create generation record: ${dbError.message}`)
  }
  
  // Send the request to SwarmUI
  const response = await axios.post(
    `${server.api_endpoint}/api/generate`,
    { params: generationParams },
    { 
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${server.api_key}`
      }
    }
  )
  
  // Update the generation record
  await supabaseClient
    .from('image_generations')
    .update({
      status: 'processing',
      updated_at: new Date().toISOString()
    })
    .eq('id', generationRecord.id)
  
  return new Response(JSON.stringify({
    success: true,
    generationId: generationRecord.id,
    ...response.data
  }), { 
    headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' } 
  })
}

// Additional handler functions...
```

## Client-Side Usage

To use these consolidated Edge Functions from the frontend:

```typescript
// src/services/swarmUIService.ts

/**
 * Start a SwarmUI server
 */
async startServer(templateId, gpuTypeId) {
  const { data, error } = await supabase.functions.invoke('swarmui-server', {
    method: 'POST',
    query: { action: 'start-server' },
    body: { templateId, gpuTypeId }
  })
  
  if (error) throw error
  return data
},

/**
 * Generate an image using SwarmUI
 */
async generateImage(params) {
  const { data, error } = await supabase.functions.invoke('swarmui-generation', {
    method: 'POST',
    body: params
  })
  
  if (error) throw error
  return data
}
```

## Benefits of the Consolidated Approach

1. **Efficiency**: Uses only 3 Edge Function slots instead of 10+
2. **Organization**: Groups related functionality together
3. **Maintainability**: Easier to update and maintain
4. **Flexibility**: Easy to add new operations without deploying new functions

## Implementation Plan

1. Create the consolidated Edge Functions
2. Update the frontend services to use the new API
3. Test all functionality thoroughly
4. Deploy to production

This approach allows you to implement all the required functionality while staying within the 10 Edge Function limit of Supabase's free plan.
