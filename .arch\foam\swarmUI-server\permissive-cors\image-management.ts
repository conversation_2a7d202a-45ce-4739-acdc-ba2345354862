// Image Management Edge Function with permissive CORS
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// Extend Request type to include parsedBody
declare global {
  interface Request {
    parsedBody?: any;
  }
}

// CORS headers for all responses
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': '*',
  'Access-Control-Allow-Headers': '*',
  'Access-Control-Max-Age': '86400',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  // Create Supabase client
  const supabaseClient = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
  )

  // Get user from request
  const { data: { user } } = await supabaseClient.auth.getUser()
  if (!user) {
    return new Response(JSON.stringify({ error: 'Unauthorized' }), {
      status: 401,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }

  // Get action from body or URL
  let action: string | null = null;
  let body: any = {};
  
  try {
    if (req.headers.get('content-type')?.includes('application/json')) {
      body = await req.json();
      action = body.action;
      req.parsedBody = body;
    }
  } catch (e) {
    // If JSON parsing fails, try URL params
    console.error('Error parsing JSON body:', e);
  }
  
  // If action not in body, try URL params
  if (!action) {
    const url = new URL(req.url);
    action = url.searchParams.get('action');
  }

  // Handle different actions
  if (req.method === 'POST' && action === 'save-generated-image') {
    // Save a generated image to S3
    try {
      const { imageUrl, metadata } = req.parsedBody || body;

      if (!imageUrl) {
        return new Response(JSON.stringify({ error: 'Image URL is required' }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        })
      }

      // Fetch the image from the URL
      const imageResponse = await fetch(imageUrl)
      if (!imageResponse.ok) {
        return new Response(JSON.stringify({ 
          error: `Failed to fetch image: ${imageResponse.statusText}` 
        }), {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        })
      }

      const imageBlob = await imageResponse.blob()
      
      // Generate a unique filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
      const randomId = Math.random().toString(36).substring(2, 10)
      const filename = `generated-${timestamp}-${randomId}.png`
      
      // Upload to S3
      const { data: uploadData, error: uploadError } = await supabaseClient
        .storage
        .from('images')
        .upload(`generated/${user.id}/${filename}`, imageBlob, {
          contentType: 'image/png',
          cacheControl: '3600'
        })

      if (uploadError) {
        return new Response(JSON.stringify({ 
          error: `Failed to upload image: ${uploadError.message}` 
        }), {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        })
      }

      // Get the public URL
      const { data: { publicUrl } } = supabaseClient
        .storage
        .from('images')
        .getPublicUrl(uploadData.path)

      // Save record in S3_images table
      const { data: imageRecord, error: insertError } = await supabaseClient
        .from('S3_images')
        .insert({
          user_id: user.id,
          company_id: user.currentCompany?.id,
          bucket_name: 'images',
          file_path: uploadData.path,
          file_name: filename,
          file_size: imageBlob.size,
          mime_type: 'image/png',
          width: metadata?.width || 1024,
          height: metadata?.height || 1024,
          public_url: publicUrl,
          metadata: {
            source: 'swarmui',
            prompt: metadata?.prompt,
            negative_prompt: metadata?.negative_prompt,
            model: metadata?.model,
            steps: metadata?.steps,
            cfg_scale: metadata?.cfg_scale,
            sampler: metadata?.sampler,
            seed: metadata?.seed
          }
        })
        .select()

      if (insertError) {
        return new Response(JSON.stringify({ 
          error: `Failed to save image record: ${insertError.message}` 
        }), {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        })
      }

      return new Response(JSON.stringify({
        success: true,
        imageId: imageRecord[0].id,
        publicUrl
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    } catch (error) {
      return new Response(JSON.stringify({ 
        error: `Failed to save generated image: ${error.message}` 
      }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
  } else if (req.method === 'GET' && action === 'list-images') {
    // List images for the user
    try {
      const { data: images, error } = await supabaseClient
        .from('S3_images')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })

      if (error) {
        return new Response(JSON.stringify({ error: error.message }), {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        })
      }

      return new Response(JSON.stringify(images), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    } catch (error) {
      return new Response(JSON.stringify({ 
        error: `Failed to list images: ${error.message}` 
      }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
  } else if (req.method === 'DELETE' && action === 'delete-image') {
    // Delete an image
    try {
      const url = new URL(req.url);
      let imageId = url.searchParams.get('imageId');
      
      // If imageId not in URL, try body
      if (!imageId && body.imageId) {
        imageId = body.imageId;
      }
      
      if (!imageId) {
        return new Response(JSON.stringify({ error: 'Image ID is required' }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        })
      }

      // Get the image record
      const { data: images, error: selectError } = await supabaseClient
        .from('S3_images')
        .select('*')
        .eq('id', imageId)
        .eq('user_id', user.id)
        .limit(1)

      if (selectError) {
        return new Response(JSON.stringify({ error: selectError.message }), {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        })
      }

      if (images.length === 0) {
        return new Response(JSON.stringify({ error: 'Image not found' }), {
          status: 404,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        })
      }

      const image = images[0]

      // Delete from S3
      const { error: deleteError } = await supabaseClient
        .storage
        .from(image.bucket_name)
        .remove([image.file_path])

      if (deleteError) {
        return new Response(JSON.stringify({ 
          error: `Failed to delete image file: ${deleteError.message}` 
        }), {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        })
      }

      // Delete record
      const { error: deleteRecordError } = await supabaseClient
        .from('S3_images')
        .delete()
        .eq('id', imageId)
        .eq('user_id', user.id)

      if (deleteRecordError) {
        return new Response(JSON.stringify({ 
          error: `Failed to delete image record: ${deleteRecordError.message}` 
        }), {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        })
      }

      return new Response(JSON.stringify({
        success: true,
        message: 'Image deleted successfully'
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    } catch (error) {
      return new Response(JSON.stringify({ 
        error: `Failed to delete image: ${error.message}` 
      }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
  }

  return new Response(JSON.stringify({ error: 'Invalid action' }), {
    status: 400,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  })
})
