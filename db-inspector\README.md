# Enhanced Database Inspector

This tool connects to your Supabase database and provides a comprehensive set of features for inspecting, querying, and documenting your database structure.

## Features

- **Database Structure Documentation**
  - Fetches complete database structure (tables, views, functions, etc.)
  - Generates JSON representation of the database structure
  - Creates Markdown documentation with:
    - Entity Relationship Diagram (Mermaid)
    - Class Diagram (Mermaid)
    - Detailed table information

- **Interactive Query Execution**
  - Run SQL queries against your database
  - View results in a formatted table
  - Save query results to files

- **Table Data Browsing**
  - Browse data in any table
  - Filter and sort data
  - Paginate through large datasets

- **Data Export**
  - Export table data to CSV or JSON
  - Export query results to various formats

- **Schema Comparison**
  - Compare schema between different databases
  - Identify differences in structure

## Prerequisites

- Node.js or Bun installed
- Supabase project
- `.env` file with Supabase credentials

## Installation

```bash
# Install dependencies
cd db-inspector-new
bun install
```

## Usage

### Setup

```bash
# Run the setup script
bun setup
```

### Fetch Database Structure

```bash
# Run the main script to fetch the database structure
bun start
```

### Generate Documentation

```bash
# Generate Markdown documentation with Mermaid diagrams
bun generate
```

### Interactive Query Execution

```bash
# Start the interactive query tool
bun query
```

### Browse Table Data

```bash
# Browse data in tables
bun browse

# Browse a specific table
bun browse --table=table_name
```

### Export Data

```bash
# Export table data
bun export --table=table_name --format=csv

# Export query results
bun export --query="SELECT * FROM table_name" --format=json
```

## Output Files

- `db-structure.json`: Raw JSON data of the database structure
- `database-structure.md`: Markdown documentation with Mermaid diagrams
- `exports/`: Directory containing exported data files

## For AI Assistants

This tool is designed to be particularly useful for AI assistants working with the codebase. The documentation and data export features allow AI assistants to:

1. Understand the database schema without needing direct database access
2. View sample data to understand the data model
3. Generate accurate code that interacts with the database
4. Provide better recommendations based on the actual database structure

## Customizing

You can modify any of the scripts to customize the behavior:

- `index.js`: Main script for fetching database structure
- `generate-docs.js`: Documentation generation
- `query.js`: Interactive query execution
- `browse.js`: Table data browsing
- `export.js`: Data export functionality
