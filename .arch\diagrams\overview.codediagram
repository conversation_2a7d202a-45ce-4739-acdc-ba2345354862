{"id": -1, "name": "FROM_FILE", "userId": -1, "createdAt": "", "updatedAt": "", "content": {"items": [{"uid": "xOex03sD5o", "position": {"x": 140, "y": 0}, "sizes": {"width": 153, "height": 84.5}, "autoheight": true, "blockContent": {"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "context manager"}]}]}, "nodeType": "block"}, {"uid": "dJcdXIb746", "position": {"x": 310, "y": 0}, "sizes": {"width": 140, "height": 84.5}, "autoheight": true, "blockContent": {"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "filter engine"}]}]}, "nodeType": "block"}, {"uid": "wBdbe51kgf", "position": {"x": 470, "y": 0}, "sizes": {"width": 160, "height": 84.5}, "autoheight": true, "blockContent": {"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "command registry"}]}]}, "nodeType": "block"}, {"uid": "dKgYcu5eQg", "position": {"x": 100, "y": 260}, "sizes": {"width": 125, "height": 84.5}, "autoheight": true, "blockContent": {"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "templates"}]}]}, "nodeType": "block"}, {"uid": "b1dK-AYUbf", "position": {"x": 350, "y": 260}, "sizes": {"width": 101, "height": 84.5}, "autoheight": true, "blockContent": {"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "products"}]}]}, "nodeType": "block"}, {"uid": "hxQHKfz3SO", "position": {"x": 100, "y": 370}, "sizes": {"width": 85, "height": 84.5}, "autoheight": true, "blockContent": {"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "users"}]}]}, "nodeType": "block"}, {"uid": "OfQK0am8o6", "position": {"x": 650, "y": 0}, "sizes": {"width": 130, "height": 84.5}, "autoheight": true, "blockContent": {"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "translations"}]}]}, "nodeType": "block"}, {"uid": "Y8IassHVLt", "position": {"x": 470, "y": 260}, "sizes": {"width": 110, "height": 84.5}, "autoheight": true, "blockContent": {"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "marketing"}]}]}, "nodeType": "block"}, {"uid": "sYysJQsnMK", "position": {"x": 600, "y": 260}, "sizes": {"width": 90, "height": 84.5}, "autoheight": true, "blockContent": {"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "sales"}]}]}, "nodeType": "block"}, {"uid": "tRY-fCWC2b", "position": {"x": 710, "y": 260}, "sizes": {"width": 120, "height": 84.5}, "autoheight": true, "blockContent": {"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "customers"}]}]}, "nodeType": "block"}, {"uid": "e0pL_wBLM3", "position": {"x": 240, "y": 260}, "sizes": {"width": 94, "height": 84.5}, "autoheight": true, "blockContent": {"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "images"}]}]}, "nodeType": "block"}, {"uid": "3Hvv_mLAkk", "position": {"x": 350, "y": 360}, "sizes": {"width": 90, "height": 84.5}, "autoheight": true, "blockContent": {"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "quotes"}]}]}, "nodeType": "block"}, {"uid": "7FKBEhw1PC", "position": {"x": 1070, "y": 260}, "sizes": {"width": 107, "height": 84.5}, "autoheight": true, "blockContent": {"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "company"}]}]}, "nodeType": "block"}, {"uid": "U3GBbTsZuH", "position": {"x": 1210, "y": 260}, "sizes": {"width": 101, "height": 84.5}, "autoheight": true, "blockContent": {"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "branding"}]}]}, "nodeType": "block"}, {"uid": "sW0TIm-eng", "position": {"x": -270, "y": -80}, "sizes": {"width": 130, "height": 91.5}, "autoheight": true, "blockContent": {"type": "doc", "content": [{"type": "heading", "attrs": {"level": 2}, "content": [{"type": "text", "text": "Agents"}]}]}, "nodeType": "block"}], "configs": {"centerX": 55, "centerY": 72, "zoomLevel": 1}, "arrowData": {"arrowsMap": {}, "pointsMap": {}, "edgesMap": {}}}}