export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      templates: {
        Row: {
          id: number
          name: string
          description: string | null
          version: number
          created_at: string
          updated_at: string
          tmpl_company_id: number
          designer_id: number
          approved_by: number | null
          approval_date: string | null
          agent_id: number | null
          status: string
          published: boolean
          image_server_data: Json | null
        }
        Insert: {
          id?: number
          name: string
          description?: string | null
          version?: number
          created_at?: string
          updated_at?: string
          tmpl_company_id: number
          designer_id: number
          approved_by?: number | null
          approval_date?: string | null
          agent_id?: number | null
          status?: string
          published?: boolean
          image_server_data?: Json | null
        }
        Update: {
          id?: number
          name?: string
          description?: string | null
          version?: number
          created_at?: string
          updated_at?: string
          tmpl_company_id?: number
          designer_id?: number
          approved_by?: number | null
          approval_date?: string | null
          agent_id?: number | null
          status?: string
          published?: boolean
          image_server_data?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "templates_tmpl_company_id_fkey"
            columns: ["tmpl_company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "templates_designer_id_fkey"
            columns: ["designer_id"]
            isOneToOne: false
            referencedRelation: "app_users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "templates_approved_by_fkey"
            columns: ["approved_by"]
            isOneToOne: false
            referencedRelation: "app_users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "templates_agent_id_fkey"
            columns: ["agent_id"]
            isOneToOne: false
            referencedRelation: "agents"
            referencedColumns: ["id"]
          }
        ]
      }
      prompt_elements: {
        Row: {
          id: number
          type_id: number
          value: string
          description: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: number
          type_id: number
          value: string
          description?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: number
          type_id?: number
          value?: string
          description?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "prompt_elements_type_id_fkey"
            columns: ["type_id"]
            isOneToOne: false
            referencedRelation: "prompt_element_types"
            referencedColumns: ["id"]
          }
        ]
      }
      prompt_element_types: {
        Row: {
          id: number
          name: string
          description: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: number
          name: string
          description?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: number
          name?: string
          description?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      prompt_element_value_order: {
        Row: {
          id: number
          template_id: number
          element_type_id: number
          element_id: number
          order_index: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: number
          template_id: number
          element_type_id: number
          element_id: number
          order_index: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: number
          template_id?: number
          element_type_id?: number
          element_id?: number
          order_index?: number
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "prompt_element_value_order_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "templates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "prompt_element_value_order_element_type_id_fkey"
            columns: ["element_type_id"]
            isOneToOne: false
            referencedRelation: "prompt_element_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "prompt_element_value_order_element_id_fkey"
            columns: ["element_id"]
            isOneToOne: false
            referencedRelation: "prompt_elements"
            referencedColumns: ["id"]
          }
        ]
      },
      prompt_elements_usage: {
        Row: {
          id: number
          template_id: number
          element_id: number
          order: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: number
          template_id: number
          element_id: number
          order: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: number
          template_id?: number
          element_id?: number
          order?: number
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "prompt_elements_usage_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "templates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "prompt_elements_usage_element_id_fkey"
            columns: ["element_id"]
            isOneToOne: false
            referencedRelation: "prompt_elements"
            referencedColumns: ["id"]
          }
        ]
      }
      companies_users_roles: {
        Row: {
          id: number
          app_user_id: number
          role_id: number
          cur_company_id: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: number
          app_user_id: number
          role_id: number
          cur_company_id: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: number
          app_user_id?: number
          role_id?: number
          cur_company_id?: number
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "companies_users_roles_app_user_id_fkey"
            columns: ["app_user_id"]
            isOneToOne: false
            referencedRelation: "app_users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "companies_users_roles_cur_company_id_fkey"
            columns: ["cur_company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "companies_users_roles_role_id_fkey"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["id"]
          }
        ]
      }
      app_users: {
        Row: {
          id: number
          user_id: string
          first_name: string | null
          last_name: string | null
          is_active: boolean
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: number
          user_id: string
          first_name?: string | null
          last_name?: string | null
          is_active?: boolean
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: number
          user_id?: string
          first_name?: string | null
          last_name?: string | null
          is_active?: boolean
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "app_users_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      allowed_emails: {
        Row: {
          email: string
          invited_by: string | null
          company_id: string
          default_role_id: string
          created_at: string | null
          expires_at: string | null
        }
        Insert: {
          email: string
          invited_by?: string | null
          company_id: string
          default_role_id: string
          created_at?: string | null
          expires_at?: string | null
        }
        Update: {
          email?: string
          invited_by?: string | null
          company_id?: string
          default_role_id?: string
          created_at?: string | null
          expires_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "allowed_emails_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "allowed_emails_default_role_id_fkey"
            columns: ["default_role_id"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "allowed_emails_invited_by_fkey"
            columns: ["invited_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      companies: {
        Row: {
          id: string
          name: string
          description: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      roles: {
        Row: {
          id: string
          name: string
          description: string | null
          created_at: string | null
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          created_at?: string | null
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          created_at?: string | null
        }
        Relationships: []
      }
      product_types: {
        Row: {
          id: number
          name: string
          description: string | null
          category: string | null
          print_area_width: number | null
          print_area_height: number | null
          thumbnail_url: string | null
          shopify_product_id: string | null
          active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: number
          name: string
          description?: string | null
          category?: string | null
          print_area_width?: number | null
          print_area_height?: number | null
          thumbnail_url?: string | null
          shopify_product_id?: string | null
          active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: number
          name?: string
          description?: string | null
          category?: string | null
          print_area_width?: number | null
          print_area_height?: number | null
          thumbnail_url?: string | null
          shopify_product_id?: string | null
          active?: boolean
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      templates_product_types: {
        Row: {
          id: number
          template_id: number
          product_type_id: number
          recommended: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: number
          template_id: number
          product_type_id: number
          recommended?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: number
          template_id?: number
          product_type_id?: number
          recommended?: boolean
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "templates_product_types_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "templates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "templates_product_types_product_type_id_fkey"
            columns: ["product_type_id"]
            isOneToOne: false
            referencedRelation: "product_types"
            referencedColumns: ["id"]
          }
        ]
      }
      products_product_types: {
        Row: {
          id: number
          product_id: number
          product_type_id: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: number
          product_id: number
          product_type_id: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: number
          product_id?: number
          product_type_id?: number
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "products_product_types_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "products_product_types_product_type_id_fkey"
            columns: ["product_type_id"]
            isOneToOne: false
            referencedRelation: "product_types"
            referencedColumns: ["id"]
          }
        ]
      }
      user_roles: {
        Row: {
          id: string
          user_id: string
          role_id: string
          company_id: string | null
          created_at: string | null
        }
        Insert: {
          id?: string
          user_id: string
          role_id: string
          company_id?: string | null
          created_at?: string | null
        }
        Update: {
          id?: string
          user_id?: string
          role_id?: string
          company_id?: string | null
          created_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_roles_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_roles_role_id_fkey"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_roles_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      users: {
        Row: {
          id: string
          email: string
          first_name: string | null
          last_name: string | null
          is_active: boolean
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id: string
          email: string
          first_name?: string | null
          last_name?: string | null
          is_active?: boolean
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          email?: string
          first_name?: string | null
          last_name?: string | null
          is_active?: boolean
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "users_id_fkey"
            columns: ["id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      prompt_elements_usage_view: {
        Row: {
          id: number
          template_id: number
          element_id: number
          order: number
          created_at: string
          pe_id: number
          pe_type_id: number
          value: string
          element_description: string | null
          is_system: boolean
          designer_id: number | null
          element_created_at: string
          element_updated_at: string
          pet_id: number
          type_name: string
          type_description: string | null
        }
        Relationships: []
      },
      template_view: {
        Row: {
          id: number
          name: string
          description: string | null
          version: number
          created_at: string
          updated_at: string
          tmpl_company_id: number
          company_name: string
          initiated_by: number
          initiated_by_name: string
          approved_by: number | null
          approved_by_name: string | null
          approval_date: string | null
          agent_id: number | null
          agent_name: string | null
          status: string
          published: boolean
          image_server_data: Json | null
          elements: Json | null
          images: Json | null
          events: Json | null
          products: Json | null
          collections: Json | null
        }
        Relationships: []
      },
      user_roles_view: {
        Row: {
          app_user_id: number
          user_id: string
          first_name: string | null
          last_name: string | null
          cur_company_id: number
          company_name: string
          role_id: number
          role_name: string
        }
        Relationships: [
          {
            foreignKeyName: "app_users_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      },
      user_emails_view: {
        Row: {
          app_user_id: number
          user_id: string
          first_name: string | null
          last_name: string | null
          email: string
        }
        Relationships: [
          {
            foreignKeyName: "app_users_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Functions: {
      admin_delete_prompt_element_usage: {
        Args: {
          p_template_id: number
          p_element_id: number
        }
        Returns: undefined
      },
      admin_reorder_template_elements: {
        Args: {
          p_template_id: number
          p_element_orders: Array<{ elementId: number; order: number }>
        }
        Returns: boolean
      },
      admin_insert_prompt_element_usage: {
        Args: {
          p_template_id: number
          p_element_id: number
          p_order?: number | null
        }
        Returns: boolean
      },
      delete_element_direct: {
        Args: {
          p_template_id: number
          p_element_id: number
        }
        Returns: undefined
      },
      insert_element_direct: {
        Args: {
          p_template_id: number
          p_element_id: number
          p_order: number
        }
        Returns: undefined
      },
      check_element_exists: {
        Args: {
          p_template_id: number
          p_element_id: number
        }
        Returns: boolean
      },
      get_next_available_order: {
        Args: {
          p_template_id: number
        }
        Returns: number
      },
      insert_prompt_element_usage: {
        Args: {
          p_template_id: number
          p_element_id: number
          p_order: number
        }
        Returns: undefined
      },
      delete_element_from_template: {
        Args: {
          p_template_id: number
          p_element_id: number
        }
        Returns: undefined
      },
      get_max_order_for_template: {
        Args: {
          p_template_id: number
        }
        Returns: number
      },
      delete_prompt_element_usage: {
        Args: {
          p_template_id: number
          p_element_id: number
        }
        Returns: undefined
      },
      get_highest_order_for_template: {
        Args: {
          p_template_id: number
        }
        Returns: number
      },
      get_highest_value_order_for_element_type: {
        Args: {
          p_template_id: number
          p_element_type_id: number
        }
        Returns: number
      },
      add_prompt_element_to_template: {
        Args: {
          p_template_id: number
          p_element_id: number
          p_order: number
        }
        Returns: undefined
      },
      reorder_template_elements: {
        Args: {
          p_template_id: number
          p_element_orders: Json
        }
        Returns: undefined
      },
      reorder_element_values: {
        Args: {
          p_template_id: number
          p_element_type_id: number
          p_value_orders: Json
        }
        Returns: undefined
      },
      get_ordered_element_values: {
        Args: {
          p_template_id: number
          p_element_type_id: number
        }
        Returns: {
          element_id: number
          value: string
          description: string | null
          is_system: boolean
          designer_id: number | null
          order_index: number
        }[]
      },
      filter_templates: {
        Args: {
          p_company_id?: number
          p_designer_id?: number
          p_status?: string
          p_published?: boolean
          p_search?: string
          p_limit?: number
          p_offset?: number
          p_template_id?: number
          p_sort_by?: string
          p_sort_desc?: boolean
          p_element_angles?: number[]
          p_element_artistic_reference?: number[]
          p_element_blend_concepts?: number[]
          p_element_camera_settings?: number[]
          p_element_color_scheme?: number[]
          p_element_composition?: number[]
          p_element_lighting?: number[]
          p_element_mood?: number[]
          p_element_style?: number[]
          p_element_subject?: number[]
        }
        Returns: {
          id: number
          name: string
          description: string | null
          version: number
          created_at: string
          updated_at: string
          tmpl_company_id: number
          company_name: string
          initiated_by: number
          initiated_by_name: string
          approved_by: number | null
          approved_by_name: string | null
          approval_date: string | null
          agent_id: number | null
          agent_name: string | null
          status: string
          published: boolean
          image_server_data: Json | null
          elements: Json | null
          images: Json | null
          events: Json | null
          products: Json | null
          collections: Json | null
          total_count: number
        }[]
      },
      // Keeping get_templates type definition for backward compatibility
      // This will be removed after the database function is dropped
      get_templates: {
        Args: {
          p_company_id?: number
          p_designer_id?: number
          p_status?: string
          p_published?: boolean
          p_search?: string
          p_limit?: number
          p_offset?: number
          p_template_id?: number
          p_product_type_id?: number
        }
        Returns: {
          id: number
          name: string
          description: string | null
          version: number
          created_at: string
          updated_at: string
          tmpl_company_id: number
          company_name: string
          initiated_by: number
          initiated_by_name: string
          approved_by: number | null
          approved_by_name: string | null
          approval_date: string | null
          agent_id: number | null
          agent_name: string | null
          status: string
          published: boolean
          image_server_data: Json | null
          elements: Json | null
          images: Json | null
          events: Json | null
          products: Json | null
          collections: Json | null
          product_types: Json | null
          total_count: number
        }[]
      },
      get_user_setting: {
        Args: {
          p_app_user_id: number
          p_setting_key: string
        }
        Returns: string | null
      },
      save_user_setting: {
        Args: {
          p_app_user_id: number
          p_setting_key: string
          p_setting_value: string
        }
        Returns: boolean
      },
      delete_user_setting: {
        Args: {
          p_app_user_id: number
          p_setting_key: string
        }
        Returns: boolean
      },
      get_template_product_types: {
        Args: {
          p_template_id: number
        }
        Returns: {
          id: number
          name: string
          description: string | null
          category: string | null
          print_area_width: number | null
          print_area_height: number | null
          thumbnail_url: string | null
          recommended: boolean
        }[]
      },
      get_product_product_types: {
        Args: {
          p_product_id: number
        }
        Returns: {
          id: number
          name: string
          description: string | null
          category: string | null
          print_area_width: number | null
          print_area_height: number | null
          thumbnail_url: string | null
        }[]
      },
      get_template_elements_with_types: {
        Args: {
          p_template_id: number
        }
        Returns: {
          type_id: number
          type_name: string
          type_description: string | null
          type_order: number
          element_id: number | null
          element_value: string | null
          element_description: string | null
          element_order: number | null
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
