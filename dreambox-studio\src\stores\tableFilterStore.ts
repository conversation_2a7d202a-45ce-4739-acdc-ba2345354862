import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { supabase } from 'src/boot/supabase';
import type { SavedFilter, DatabaseWithFilters } from 'src/types/supabase-filters';
import { useAuthStore } from './auth';
import { useQuasar } from 'quasar';
import { notify } from 'src/boot/notifications';

export interface ColumnDefinition {
  id: string;
  name: string;
  field: string;
  visible: boolean;
  section: 'main' | 'expanded';
  order: number;
  filterValue?: string | null;
  filterType?: 'text' | 'number' | 'date' | 'select' | 'boolean' | 'multiselect';
  filterOptions?: Array<{ label: string; value: string | number | boolean }>;
}

// SavedFilter interface is now imported from 'src/types/supabase-filters'

export const useTableFilterStore = defineStore('tableFilter', () => {
  // State
  const columns = ref<ColumnDefinition[]>([]);
  const currentTable = ref<string | null>(null);
  const savedFilters = ref<SavedFilter[]>([]);
  const currentFilterId = ref<number | null>(null);
  const isLoading = ref(false);
  const hasUnsavedChanges = ref(false);
  const $q = useQuasar();

  // Getters
  const mainTableColumns = computed(() =>
    columns.value
      .filter(col => col.section === 'main')
      .sort((a, b) => a.order - b.order)
  );

  const expandedViewColumns = computed(() =>
    columns.value
      .filter(col => col.section === 'expanded')
      .sort((a, b) => a.order - b.order)
  );

  const activeFilters = computed(() => {
    const filters: Record<string, string | number | null> = {};
    columns.value.forEach(col => {
      if (col.filterValue) {
        filters[col.field] = col.filterValue;
      }
    });
    return filters;
  });

  const visibleMainColumns = computed(() =>
    columns.value
      .filter(col => col.section === 'main' && col.visible)
      .sort((a, b) => a.order - b.order)
  );

  const visibleExpandedColumns = computed(() =>
    columns.value
      .filter(col => col.section === 'expanded' && col.visible)
      .sort((a, b) => a.order - b.order)
  );

  const currentFilter = computed(() => {
    return savedFilters.value.find((f: SavedFilter) => f.id === currentFilterId.value) || null;
  });

  const filtersByTable = computed(() => {
    console.log('Computing filtersByTable');
    console.log('Current table:', currentTable.value);
    console.log('All saved filters:', savedFilters.value);

    const filteredFilters = savedFilters.value.filter((f: SavedFilter) => {
      // Check both table_name and context fields to handle legacy filters
      const matchesTable = f.table_name === currentTable.value ||
                          (f.table_name === undefined && f.context === currentTable.value);

      console.log(`Filter ${f.id} (${f.name}) table_name: "${f.table_name}", context: "${f.context}", current table: "${currentTable.value}", matches: ${matchesTable}`);

      return matchesTable;
    });

    console.log('Filtered filters:', filteredFilters);
    return filteredFilters;
  });

  // Actions
  async function setTable(tableName: string) {
    if (currentTable.value === tableName) return;

    currentTable.value = tableName;
    hasUnsavedChanges.value = false;

    // Load saved filters for this table
    await loadSavedFilters();

    // Try to load the default filter for this table
    const defaultFilter = savedFilters.value.find(
      (f: SavedFilter) => f.table_name === tableName && f.is_default
    );

    if (defaultFilter) {
      loadFilter(defaultFilter.id);
    } else {
      // Load base configuration if no default
      loadBaseColumnsForTable(tableName);
    }
  }

  function toggleColumnVisibility(columnId: string, visible: boolean) {
    const column = columns.value.find(col => col.id === columnId);
    if (column) {
      column.visible = visible;
      markUnsavedChanges();
    }
  }

  function setColumnFilter(columnId: string, value: string | number | null) {
    const column = columns.value.find(col => col.id === columnId);
    if (column) {
      column.filterValue = value !== null && typeof value === 'number' ? String(value) : value;
      markUnsavedChanges();
    }
  }

  function updateColumnOrder(columnId: string, section: 'main' | 'expanded', order: number) {
    const column = columns.value.find(col => col.id === columnId);
    if (column) {
      column.order = order;
      markUnsavedChanges();
    }
  }

  function moveColumnToSection(columnId: string, section: 'main' | 'expanded') {
    const column = columns.value.find(col => col.id === columnId);
    if (column) {
      column.section = section;

      // Reorder columns in the section
      const sectionColumns = columns.value.filter(col => col.section === section);
      sectionColumns.forEach((col, index) => {
        col.order = index;
      });

      markUnsavedChanges();
    }
  }

  async function loadSavedFilters() {
    // Get the app_user.id (BIGINT) from the user_id (UUID)
    const appUserId = await getAppUserId();
    if (!appUserId) {
      console.warn('Cannot load filters: No valid app_user.id found');
      savedFilters.value = [];
      return;
    }

    console.log('Found app_user.id:', appUserId);

    // Check if the user_filters table exists
    try {
      // First check if the table exists by trying to get a single row
      const { error: checkError } = await supabase
        .from('user_filters' as unknown as keyof DatabaseWithFilters['public']['Tables'])
        .select('id')
        .limit(1);

      if (checkError) {
        console.warn('user_filters table may not exist:', checkError.message);
        // Return empty array to prevent further errors
        savedFilters.value = [];
        // Don't show error notification to user for missing table
        return;
      }
    } catch (err) {
      console.warn('Error checking user_filters table:', err);
      // Return empty array to prevent further errors
      savedFilters.value = [];
      // Don't show error notification to user for missing table
      return;
    }

    isLoading.value = true;
    try {
      // Use type assertion to allow querying the user_filters table
      const { data, error } = await supabase
        .from('user_filters' as unknown as keyof DatabaseWithFilters['public']['Tables'])
        .select('*')
        .eq('user_id', appUserId);

      if (error) throw error;

      console.log('Loaded filters from database:', data);

      // Cast data to SavedFilter[] to ensure proper typing
      const typedData = (data || []) as SavedFilter[];

      // Check if any filters have null or undefined table_name
      const filtersWithMissingTableName = typedData.filter(f => !f.table_name);
      if (filtersWithMissingTableName.length > 0) {
        console.warn('Found filters with missing table_name:', filtersWithMissingTableName);

        // Fix filters with missing table_name by using the context field
        const fixedData = typedData.map(f => {
          if (!f.table_name && f.context) {
            console.log(`Fixing filter ${f.id} (${f.name}) by setting table_name to context: ${f.context}`);
            return { ...f, table_name: f.context };
          }
          return f;
        });

        // Update the database for each filter with missing table_name
        // Use Promise.all to properly handle async operations in a loop
        void Promise.all(
          filtersWithMissingTableName.map(async (filter) => {
            if (filter.context) {
              try {
                console.log(`Updating filter ${filter.id} in database to set table_name = ${filter.context}`);
                const { error: updateError } = await supabase
                  .from('user_filters' as unknown as keyof DatabaseWithFilters['public']['Tables'])
                  .update({ table_name: filter.context })
                  .eq('id', filter.id);

                if (updateError) {
                  console.error(`Error updating filter ${filter.id}:`, updateError);
                }
              } catch (updateErr) {
                console.error(`Error updating filter ${filter.id}:`, updateErr);
              }
            }
          })
        );

        savedFilters.value = fixedData;
      } else {
        savedFilters.value = typedData;
      }
    } catch (err) {
      console.error('Error loading saved filters:', err);
      // Only show error notification if it's not a table existence issue
      if (!(err instanceof Error && err.message.includes('does not exist'))) {
        void notify({
          color: 'warning',
          message: 'Could not load saved filters. This feature may not be available yet.',
          icon: 'warning',
          timeout: 5000
        });
      }
    } finally {
      isLoading.value = false;
    }
  }

  async function saveCurrentFilter(name: string, description = '', makeDefault = false, combinedFilters: Record<string, unknown> = {}) {
    if (!currentTable.value) return false;

    // Get the app_user.id (BIGINT) from the user_id (UUID)
    const appUserId = await getAppUserId();
    if (!appUserId) {
      console.error('Cannot save filter: No valid app_user.id found');
      void notify({
        color: 'negative',
        message: 'Failed to save filter: User ID not found',
        icon: 'error'
      });
      return false;
    }

    // Create configuration object from current state with proper typing
    const configuration: {
      columns: Array<{
        id: string;
        visible: boolean;
        section: 'main' | 'expanded';
        order: number;
        filterValue: string | null;
      }>;
      additionalFilters?: Record<string, unknown>;
    } = {
      columns: columns.value.map(col => ({
        id: col.id,
        visible: col.visible,
        section: col.section,
        order: col.order,
        filterValue: col.filterValue || null
      }))
    };

    // Add any additional filters from combinedFilters
    // This allows us to include prompt element filters and other special filters
    if (Object.keys(combinedFilters).length > 0) {
      console.log('Adding combined filters to configuration:', combinedFilters);
      configuration.additionalFilters = combinedFilters;
    }

    try {
      // If editing existing filter
      if (currentFilterId.value) {
        const { error } = await supabase
          .from('user_filters' as unknown as keyof DatabaseWithFilters['public']['Tables'])
          .update({
            name,
            description,
            table_name: currentTable.value, // Ensure table_name is set correctly
            context: currentTable.value, // Use the same value as table_name
            configuration,
            is_default: makeDefault,
            updated_at: new Date().toISOString()
          })
          .eq('id', currentFilterId.value);

        if (error) throw error;

        // Update local state
        const index = savedFilters.value.findIndex((f: SavedFilter) => f.id === currentFilterId.value);
        if (index !== -1) {
          // Create a partial filter first, then cast it to SavedFilter
          const updatedFilter = {
            ...savedFilters.value[index],
            name,
            description,
            table_name: currentTable.value, // Ensure table_name is set correctly
            context: currentTable.value, // Use the same value as table_name
            configuration,
            is_default: makeDefault,
            updated_at: new Date().toISOString()
          };
          savedFilters.value[index] = updatedFilter as SavedFilter;
        }
      } else {
        // Creating new filter
        const { data, error } = await supabase
          .from('user_filters' as unknown as keyof DatabaseWithFilters['public']['Tables'])
          .insert({
            user_id: appUserId,
            name,
            description,
            table_name: currentTable.value,
            context: currentTable.value, // Use the same value as table_name for now
            configuration,
            is_default: makeDefault
          })
          .select();

        if (error) throw error;

        if (data && data.length > 0) {
          // Use the data directly with type checking
          const newFilter = data[0];
          if (newFilter && typeof newFilter === 'object' && 'id' in newFilter) {
            // Ensure id is a number
            const filterId = typeof newFilter.id === 'string' ? parseInt(newFilter.id, 10) : newFilter.id;
            currentFilterId.value = filterId;
            savedFilters.value.push(newFilter as SavedFilter);
          }
        }
      }

      // If making this the default, update other filters
      if (makeDefault) {
        await updateDefaultFilter(currentFilterId.value as number);
      }

      hasUnsavedChanges.value = false;
      return true;
    } catch (err) {
      console.error('Error saving filter:', err);
      void notify({
        color: 'negative',
        message: 'Failed to save filter',
        icon: 'error'
      });
      return false;
    }
  }

  function loadFilter(filterId: number) {
    const filter = savedFilters.value.find((f: SavedFilter) => f.id === filterId);
    if (!filter) return false;

    try {
      // Apply the configuration
      // First load the base columns for this table
      loadBaseColumnsForTable(filter.table_name);

      // Then apply the saved configuration
      filter.configuration.columns.forEach((savedCol: { id: string; visible?: boolean; section?: 'main' | 'expanded'; order?: number; filterValue?: string | null }) => {
        const column = columns.value.find(col => col.id === savedCol.id);
        if (column) {
          if (savedCol.visible !== undefined) column.visible = savedCol.visible;
          if (savedCol.section !== undefined) column.section = savedCol.section;
          if (savedCol.order !== undefined) column.order = savedCol.order;
          if (savedCol.filterValue !== undefined) column.filterValue = savedCol.filterValue;
        }
      });

      // Check for additional filters (like prompt element filters)
      const additionalFilters = filter.configuration.additionalFilters;
      if (additionalFilters) {
        console.log('Found additional filters in configuration:', additionalFilters);

        // For templates table, dispatch events for special filters
        if (filter.table_name === 'templates') {
          // Extract prompt element filters
          const promptElementFilters: Record<string, unknown> = {};

          Object.keys(additionalFilters).forEach(key => {
            if (key.startsWith('element_')) {
              promptElementFilters[key] = additionalFilters[key];

              // If the value is a JSON string, try to parse it
              if (typeof additionalFilters[key] === 'string') {
                try {
                  // TypeScript should know this is a string based on the if condition
                  const parsedValue = JSON.parse(additionalFilters[key]);
                  if (Array.isArray(parsedValue)) {
                    promptElementFilters[key] = parsedValue;
                  }
                } catch (err) {
                  // If parsing fails, keep the original value
                  console.warn(`Failed to parse JSON for ${key}:`, err);
                }
              }
            }
          });

          // If we found prompt element filters, dispatch an event to load them
          if (Object.keys(promptElementFilters).length > 0) {
            console.log('Dispatching load-prompt-element-filters event with:', promptElementFilters);
            document.dispatchEvent(new CustomEvent('load-prompt-element-filters', {
              detail: {
                filters: promptElementFilters
              }
            }));
          }
        }
      }

      currentFilterId.value = filterId;
      hasUnsavedChanges.value = false;
      return true;
    } catch (err) {
      console.error('Error loading filter:', err);
      void notify({
        color: 'negative',
        message: 'Failed to load filter',
        icon: 'error'
      });
      return false;
    }
  }

  async function deleteFilter(filterId: number) {
    try {
      const { error } = await supabase
        .from('user_filters' as unknown as keyof DatabaseWithFilters['public']['Tables'])
        .delete()
        .eq('id', filterId);

      if (error) throw error;

      // Update local state
      savedFilters.value = savedFilters.value.filter((f: SavedFilter) => f.id !== filterId);

      if (currentFilterId.value === filterId) {
        currentFilterId.value = null;
        // Load default filter for this table if available
        const defaultFilter = savedFilters.value.find(
          (f: SavedFilter) => f.table_name === currentTable.value && f.is_default
        );

        if (defaultFilter) {
          loadFilter(defaultFilter.id);
        } else {
          // Reset to base configuration
          loadBaseColumnsForTable(currentTable.value as string);
        }
      }

      return true;
    } catch (err) {
      console.error('Error deleting filter:', err);
      $q.notify({
        color: 'negative',
        message: 'Failed to delete filter',
        icon: 'error'
      });
      return false;
    }
  }

  // Load base column configuration for a table
  function loadBaseColumnsForTable(tableName: string) {
    // This would load the default column configuration for a given table
    // Could be from a static configuration or from an API

    switch(tableName) {
      case 'templates':
        columns.value = [
          { id: 'name', name: 'Name', field: 'name', visible: true, section: 'main', order: 0, filterType: 'text' },
          { id: 'description', name: 'Description', field: 'description', visible: true, section: 'main', order: 1, filterType: 'text' },
          { id: 'status', name: 'Status', field: 'status', visible: true, section: 'main', order: 2, filterType: 'multiselect', filterOptions: [
            { label: 'Draft', value: 'draft' },
            { label: 'Pending', value: 'pending' },
            { label: 'Active', value: 'active' },
            { label: 'Archived', value: 'archived' }
          ]},
          { id: 'version', name: 'Version', field: 'version', visible: true, section: 'expanded', order: 0, filterType: 'number' },
          { id: 'created_at', name: 'Created At', field: 'created_at', visible: true, section: 'expanded', order: 1, filterType: 'date' },
          { id: 'updated_at', name: 'Updated At', field: 'updated_at', visible: true, section: 'expanded', order: 2, filterType: 'date' },
          { id: 'company_name', name: 'Company', field: 'company_name', visible: true, section: 'expanded', order: 3, filterType: 'text' },
          { id: 'initiated_by_name', name: 'Created By', field: 'initiated_by_name', visible: true, section: 'expanded', order: 4, filterType: 'text' },
          { id: 'approved_by_name', name: 'Approved By', field: 'approved_by_name', visible: true, section: 'expanded', order: 5, filterType: 'text' },
          { id: 'approval_date', name: 'Approval Date', field: 'approval_date', visible: true, section: 'expanded', order: 6, filterType: 'date' },
          { id: 'agent_name', name: 'Agent', field: 'agent_name', visible: true, section: 'expanded', order: 7, filterType: 'text' }
        ];
        break;
      case 'products':
        columns.value = [
          { id: 'name', name: 'Name', field: 'name', visible: true, section: 'main', order: 0, filterType: 'text' },
          { id: 'sku', name: 'SKU', field: 'sku', visible: true, section: 'main', order: 1, filterType: 'text' },
          { id: 'base_price', name: 'Base Price', field: 'base_price', visible: true, section: 'main', order: 2, filterType: 'number' },
          { id: 'active', name: 'Active', field: 'active', visible: true, section: 'main', order: 3, filterType: 'boolean' },
          { id: 'created_at', name: 'Created At', field: 'created_at', visible: true, section: 'expanded', order: 0, filterType: 'date' },
          { id: 'updated_at', name: 'Updated At', field: 'updated_at', visible: true, section: 'expanded', order: 1, filterType: 'date' }
        ];
        break;
      case 'collections':
        columns.value = [
          { id: 'name', name: 'Name', field: 'name', visible: true, section: 'main', order: 0, filterType: 'text' },
          { id: 'description', name: 'Description', field: 'description', visible: true, section: 'main', order: 1, filterType: 'text' },
          { id: 'created_at', name: 'Created At', field: 'created_at', visible: true, section: 'expanded', order: 0, filterType: 'date' },
          { id: 'updated_at', name: 'Updated At', field: 'updated_at', visible: true, section: 'expanded', order: 1, filterType: 'date' }
        ];
        break;
      default:
        columns.value = [];
        break;
    }

    currentFilterId.value = null;
    hasUnsavedChanges.value = false;
  }

  // Track changes to mark unsaved changes
  function markUnsavedChanges() {
    hasUnsavedChanges.value = true;
  }

  // Dismiss unsaved changes notification without saving or reverting
  function dismissUnsavedChanges() {
    // Just hide the notification without changing any filter settings
    hasUnsavedChanges.value = false;
  }

  // Update which filter is the default
  async function updateDefaultFilter(filterId: number) {
    if (!currentTable.value) return false;

    // Get the app_user.id (BIGINT) from the user_id (UUID)
    const appUserId = await getAppUserId();
    if (!appUserId) {
      console.error('Cannot update default filter: No valid app_user.id found');
      void notify({
        color: 'negative',
        message: 'Failed to update default filter: User ID not found',
        icon: 'error'
      });
      return false;
    }

    try {
      // First, unset any existing default
      const { error: error1 } = await supabase
        .from('user_filters' as unknown as keyof DatabaseWithFilters['public']['Tables'])
        .update({ is_default: false })
        .eq('user_id', appUserId)
        .eq('table_name', currentTable.value)
        .neq('id', filterId);

      if (error1) throw error1;

      // Then set the new default
      const { error: error2 } = await supabase
        .from('user_filters' as unknown as keyof DatabaseWithFilters['public']['Tables'])
        .update({ is_default: true })
        .eq('id', filterId);

      if (error2) throw error2;

      // Update local state
      savedFilters.value.forEach((f: SavedFilter) => {
        if (f.table_name === currentTable.value) {
          f.is_default = (f.id === filterId);
        }
      });

      return true;
    } catch (err) {
      console.error('Error updating default filter:', err);
      void notify({
        color: 'negative',
        message: 'Failed to update default filter',
        icon: 'error'
      });
      return false;
    }
  }

  // Helper function to get app_user.id from user_id
  async function getAppUserId() {
    const authStore = useAuthStore();
    if (!authStore.state.user?.id) {
      console.warn('Cannot get app_user.id: User not logged in or user ID not available');
      return null;
    }

    try {
      const { data: userData, error: userError } = await supabase
        .from('app_users')
        .select('id')
        .eq('user_id', authStore.state.user.id)
        .single();

      if (userError) {
        console.warn('Error fetching app_user id:', userError.message);
        return null;
      }

      if (!userData) {
        console.warn('No app_user found for user_id:', authStore.state.user.id);
        return null;
      }

      return userData.id;
    } catch (err) {
      console.warn('Error getting app_user id:', err);
      return null;
    }
  }

  // Apply filters to data
  function applyFilters(data: Record<string, unknown>[]) {
    console.log('applyFilters called with data:', data);
    console.log('activeFilters:', activeFilters.value);

    if (!data || !data.length) {
      console.log('No data to filter, returning empty array');
      return [];
    }

    // If no active filters, return all data
    if (Object.keys(activeFilters.value).length === 0) {
      console.log('No active filters, returning all data');
      return data;
    }

    return data.filter(item => {
      // Check each active filter
      for (const [field, value] of Object.entries(activeFilters.value)) {
        const column = columns.value.find(col => col.field === field);
        if (!column) continue;

        const itemValue = item[field];

        // Skip if item doesn't have this field or value is null/empty
        if (itemValue === undefined || value === null || value === '') continue;

        // Apply filter based on filter type
        switch (column.filterType) {
          case 'text':
            if (typeof value === 'string' && typeof itemValue === 'string') {
              if (!itemValue.toLowerCase().includes(value.toLowerCase())) {
                return false;
              }
            }
            break;
          case 'number':
            if (typeof value === 'string' && !isNaN(Number(value)) && typeof itemValue === 'number') {
              if (itemValue !== Number(value)) {
                return false;
              }
            }
            break;
          case 'date':
            // If no filter value, it's a match
            if (!value) {
              break;
            }

            // Date range comparison with support for multiple ranges
            if (typeof value === 'string') {
              try {
                // Parse the item date first
                let itemDate: Date;
                if (typeof itemValue === 'string') {
                  itemDate = new Date(itemValue);
                } else if (itemValue instanceof Date) {
                  itemDate = itemValue;
                } else {
                  // If we can't parse the item value as a date, it doesn't match
                  return false;
                }

                // Ensure item date is valid
                if (isNaN(itemDate.getTime())) {
                  return false;
                }

                // Try to parse the filter value
                console.log('Filtering date with value:', value);
                const parsedValue = JSON.parse(value);
                console.log('Parsed date filter value:', parsedValue);

                // Handle multiple ranges (array of ranges)
                if (Array.isArray(parsedValue)) {
                  // If there are no ranges, it's not a match
                  if (parsedValue.length === 0) {
                    return false;
                  }

                  // For debugging
                  console.log('Item date to check:', itemDate);

                  // Check if the item date is within any of the ranges
                  const isInAnyRange = parsedValue.some(range => {
                    // Skip invalid ranges
                    if (!range || typeof range !== 'object') {
                      return false;
                    }

                    const fromDate = range.from ? new Date(range.from) : null;
                    const toDate = range.to ? new Date(range.to) : null;

                    // For debugging
                    console.log('Checking range:', { from: fromDate, to: toDate });

                    // If neither date is specified, skip this range
                    if (!fromDate && !toDate) {
                      return false;
                    }

                    if (fromDate) fromDate.setHours(0, 0, 0, 0);
                    if (toDate) toDate.setHours(23, 59, 59, 999); // End of day

                    // If both dates are specified, check if item is within range
                    if (fromDate && toDate) {
                      const result = itemDate >= fromDate && itemDate <= toDate;
                      console.log(`Date ${itemDate.toISOString()} in range ${fromDate.toISOString()} - ${toDate.toISOString()}: ${result}`);
                      return result;
                    }

                    // If only from date is specified
                    if (fromDate) {
                      const result = itemDate >= fromDate;
                      console.log(`Date ${itemDate.toISOString()} >= ${fromDate.toISOString()}: ${result}`);
                      return result;
                    }

                    // If only to date is specified
                    if (toDate) {
                      const result = itemDate <= toDate;
                      console.log(`Date ${itemDate.toISOString()} <= ${toDate.toISOString()}: ${result}`);
                      return result;
                    }

                    return false; // No valid dates in this range
                  });

                  console.log('Is in any range:', isInAnyRange);

                  if (!isInAnyRange) {
                    return false;
                  }
                }
                // Handle single range object
                else if (typeof parsedValue === 'object') {
                  console.log('Handling single range object:', parsedValue);

                  let fromDate: Date | null = null;
                  let toDate: Date | null = null;

                  if (parsedValue.from) {
                    fromDate = new Date(parsedValue.from);
                    fromDate.setHours(0, 0, 0, 0);
                  }

                  if (parsedValue.to) {
                    toDate = new Date(parsedValue.to);
                    toDate.setHours(23, 59, 59, 999); // End of day
                  }

                  console.log('Item date:', itemDate);
                  console.log('From date:', fromDate);
                  console.log('To date:', toDate);

                  // If neither date is specified, it's not a match
                  if (!fromDate && !toDate) {
                    console.log('No dates specified in range');
                    return false;
                  }

                  // Check if the item date is within the range
                  if (fromDate && !isNaN(fromDate.getTime())) {
                    const result = itemDate >= fromDate;
                    console.log(`Date ${itemDate.toISOString()} >= ${fromDate.toISOString()}: ${result}`);
                    if (!result) return false;
                  }

                  if (toDate && !isNaN(toDate.getTime())) {
                    const result = itemDate <= toDate;
                    console.log(`Date ${itemDate.toISOString()} <= ${toDate.toISOString()}: ${result}`);
                    if (!result) return false;
                  }

                  // If we get here, it's a match
                  console.log('Single range match!');
                }
                // Handle legacy single date string (should not happen with new code)
                else if (typeof parsedValue === 'string') {
                  const filterDate = new Date(parsedValue);
                  filterDate.setHours(0, 0, 0, 0);

                  const itemDateNoTime = new Date(itemDate);
                  itemDateNoTime.setHours(0, 0, 0, 0);

                  if (filterDate.getTime() !== itemDateNoTime.getTime()) {
                    return false;
                  }
                }
                else {
                  return false; // Unrecognized format
                }
              } catch (error) {
                console.error('Error comparing dates:', error);
                return false;
              }
            }
            break;
          case 'boolean':
            if (typeof value === 'string') {
              const boolValue = value.toLowerCase() === 'true';
              if (itemValue !== boolValue) {
                return false;
              }
            }
            break;
          case 'select':
            if (typeof value === 'string' && value && itemValue !== value) {
              return false;
            }
            break;
          case 'multiselect':
            console.log('Applying multiselect filter:', { field, value, itemValue });
            if (typeof value === 'string') {
              try {
                // Parse the JSON string into an array
                const selectedValues = JSON.parse(value);
                console.log('Parsed multiselect values:', selectedValues);

                // Check if the item's value is in the selected values
                if (Array.isArray(selectedValues) && selectedValues.length > 0) {
                  if (!selectedValues.includes(itemValue)) {
                    console.log('Item value', itemValue, 'not in selected values', selectedValues);
                    return false;
                  }
                }
              } catch (error) {
                console.error('Error parsing multiselect filter:', error);
                return false;
              }
            }
            break;
          default:
            // Default string comparison
            if (typeof value === 'string' && typeof itemValue === 'string') {
              if (!itemValue.toLowerCase().includes(value.toLowerCase())) {
                return false;
              }
            }
        }
      }

      return true;
    });
  }

  return {
    // State
    columns,
    currentTable,
    savedFilters,
    // Make currentFilterId writable from outside the store
    get currentFilterId() {
      return currentFilterId.value;
    },
    set currentFilterId(value: number | null) {
      currentFilterId.value = value;
    },
    isLoading,
    hasUnsavedChanges,

    // Getters
    mainTableColumns,
    expandedViewColumns,
    activeFilters,
    visibleMainColumns,
    visibleExpandedColumns,
    currentFilter,
    filtersByTable,

    // Actions
    setTable,
    toggleColumnVisibility,
    setColumnFilter,
    updateColumnOrder,
    moveColumnToSection,
    loadSavedFilters,
    saveCurrentFilter,
    loadFilter,
    deleteFilter,
    loadBaseColumnsForTable,
    markUnsavedChanges,
    dismissUnsavedChanges,
    updateDefaultFilter,
    applyFilters
  };
});
