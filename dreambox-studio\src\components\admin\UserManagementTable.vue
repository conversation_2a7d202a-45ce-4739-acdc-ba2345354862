<template>
  <div class="user-management-table">
    <!-- Show skeleton loader when loading -->
    <div v-if="loading" class="skeleton-container">
      <table-skeleton-loader
        :columns="columns"
        :row-count="pagination.rowsPerPage"
      />
    </div>

    <!-- Show actual table when not loading -->
    <div v-else>
        <!-- Table with smooth loading state -->
        <q-table
          :rows="userRoles"
          :columns="columns"
          row-key="id"
          :filter="filter"
          :filter-method="customFilter"
          v-model:pagination="pagination"
          class="no-default-spinner"
        >
      <template v-slot:top>
        <div class="q-table__title">{{ t('nav.users') }}</div>
        <q-space />
        <q-btn
          color="primary"
          icon="add"
          label="Add User Role"
          @click="openAddDialog"
        />
      </template>

      <!-- Role column with dropdown -->
      <template v-slot:body-cell-role="props">
        <q-td :props="props">
          <q-select
            v-model="props.row.role_id"
            :options="roleOptions"
            option-label="name"
            option-value="id"
            map-options
            emit-value
            dense
            outlined
            @update:model-value="updateUserRole(props.row, 'role_id', $event)"
            :display-value="props.row.role_name"
          />
        </q-td>
      </template>

      <!-- Company column with dropdown -->
      <template v-slot:body-cell-company="props">
        <q-td :props="props">
          <q-select
            v-model="props.row.cur_company_id"
            :options="companyOptions"
            option-label="name"
            option-value="id"
            map-options
            emit-value
            dense
            outlined
            @update:model-value="updateUserRole(props.row, 'cur_company_id', $event)"
            :display-value="props.row.company_name"
          />
        </q-td>
      </template>

      <!-- Actions column -->
      <template v-slot:body-cell-actions="props">
        <q-td :props="props">
          <q-btn
            flat
            round
            color="negative"
            icon="delete"
            @click="confirmDelete(props.row)"
          />
        </q-td>
      </template>
    </q-table>

    <!-- Add User Role Dialog -->
    <q-dialog v-model="addDialog" persistent>
      <q-card style="min-width: 350px">
        <q-card-section class="bg-primary text-white">
          <div class="text-h6">Add User Role</div>
        </q-card-section>

        <q-card-section>
          <!-- User selection first -->
          <q-select
            v-model="newUserRole.user_id"
            :options="userOptions"
            option-label="display_name"
            option-value="id"
            label="User"
            map-options
            emit-value
            outlined
            class="q-mb-md"
            @update:model-value="updateSelections"
          />

          <!-- Company selection second -->
          <q-select
            v-model="newUserRole.cur_company_id"
            :options="filteredCompanyOptions"
            option-label="name"
            option-value="id"
            label="Company"
            map-options
            emit-value
            outlined
            class="q-mb-md"
            @update:model-value="updateSelections"
            :disable="!newUserRole.user_id"
          />

          <!-- Role selection last, filtered based on user and company -->
          <div v-if="!newUserRole.user_id || !newUserRole.cur_company_id">
            <q-select
              v-model="newUserRole.role_id"
              :options="[]"
              option-label="name"
              option-value="id"
              label="Role"
              map-options
              emit-value
              outlined
              disable
              hint="Select a user and company first"
            />
          </div>
          <div v-else-if="hasAvailableRoles">
            <q-select
              v-model="newUserRole.role_id"
              :options="filteredRoleOptions"
              option-label="name"
              option-value="id"
              label="Role"
              map-options
              emit-value
              outlined
            />
          </div>
          <div v-else class="text-positive q-pa-md text-weight-bold">
            <q-icon name="info" class="q-mr-sm" />
            User has all available roles
          </div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="Cancel" color="primary" v-close-popup />
          <q-btn
            label="Add"
            color="primary"
            @click="addUserRole"
            :disable="!isNewUserRoleValid"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- Delete Confirmation Dialog -->
    <q-dialog v-model="deleteDialog" persistent>
      <q-card>
        <q-card-section class="bg-negative text-white">
          <div class="text-h6">Confirm Delete</div>
        </q-card-section>

        <q-card-section>
          Are you sure you want to delete this user role?
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="Cancel" color="primary" v-close-popup />
          <q-btn
            flat
            label="Delete"
            color="negative"
            @click="deleteUserRole"
            v-close-popup
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useQuasar } from 'quasar';
import { supabase } from 'src/boot/supabase';
import type { PostgrestError } from 'src/types/supabase-client';
import TableSkeletonLoader from 'src/components/common/TableSkeletonLoader.vue';
import { useI18n } from 'vue-i18n';

const $q = useQuasar();
const { t } = useI18n();

// Define types
interface UserRole {
  id: string;
  user_id: string;
  role_id: string;
  cur_company_id: string | null;
  user_name: string;
  user_email: string;
  role: string;
  role_name: string;
  company: string | null;
  company_name: string;
}

interface User {
  id: string;
  email: string;
  display_name: string;
}

interface Role {
  id: string;
  name: string;
}

interface Company {
  id: string;
  name: string;
}

// Table data
const userRoles = ref<UserRole[]>([]);
const loading = ref(false);
const filter = ref('');
const pagination = ref({
  rowsPerPage: 10
});

// Listen for global search events
onMounted(() => {
  document.addEventListener('global-search-changed', handleGlobalSearch as EventListener);
});

onUnmounted(() => {
  document.removeEventListener('global-search-changed', handleGlobalSearch as EventListener);
});

// Handle global search events
function handleGlobalSearch(event: Event) {
  const customEvent = event as CustomEvent;
  const { value, category } = customEvent.detail;

  // Only apply the filter if the category is 'All' or 'Users'
  if (!category || category === 'All' || category === 'Users') {
    filter.value = value;
  }
}

// Custom filter function to search across all fields
// eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any
function customFilter(rows: readonly any[], terms: string, cols: readonly any[], getCellValue: (col: any, row: any) => any): readonly any[] {
  const lowerTerms = terms.toLowerCase();

  return rows.filter(row => {
    // Search in user name
    if (typeof row.user_name === 'string' && row.user_name.toLowerCase().includes(lowerTerms)) return true;

    // Search in email
    if (typeof row.user_email === 'string' && row.user_email.toLowerCase().includes(lowerTerms)) return true;

    // Search in role name
    if (typeof row.role_name === 'string' && row.role_name.toLowerCase().includes(lowerTerms)) return true;

    // Search in company name
    if (typeof row.company_name === 'string' && row.company_name.toLowerCase().includes(lowerTerms)) return true;

    // No match found
    return false;
  });
}

// Options for dropdowns
const userOptions = ref<User[]>([]);
const roleOptions = ref<Role[]>([]);
const companyOptions = ref<Company[]>([]);

// Dialog controls
const addDialog = ref(false);
const deleteDialog = ref(false);
const userRoleToDelete = ref<UserRole | null>(null);

// New user role form
const newUserRole = ref({
  user_id: '',
  role_id: '',
  cur_company_id: ''
});

// Filtered options for the company and role dropdowns
const filteredCompanyOptions = ref<Company[]>([]);
const filteredRoleOptions = ref<Role[]>([]);

// Computed properties
const hasAvailableRoles = computed(() => filteredRoleOptions.value.length > 0);

// Check if the new user role form is valid
const isNewUserRoleValid = computed(() => {
  return (
    newUserRole.value.user_id &&
    newUserRole.value.cur_company_id &&
    newUserRole.value.role_id &&
    hasAvailableRoles.value
  );
});

// Table columns
const columns = [
  {
    name: 'user',
    required: true,
    label: t('columns.user'),
    align: 'left' as const,
    field: (row: UserRole) => row.user_name,
    sortable: true
  },
  {
    name: 'email',
    required: true,
    label: t('columns.email'),
    align: 'left' as const,
    field: (row: UserRole) => row.user_email,
    sortable: true
  },
  {
    name: 'role',
    required: true,
    label: t('columns.role'),
    align: 'left' as const,
    field: (row: UserRole) => row.role_name, // Display role_name instead of role_id
    sortable: true
  },
  {
    name: 'company',
    required: true,
    label: t('columns.company'),
    align: 'left' as const,
    field: (row: UserRole) => row.company_name, // Display company_name instead of company_id
    sortable: true
  },
  {
    name: 'actions',
    required: true,
    label: t('columns.actions'),
    align: 'center' as const,
    field: 'actions'
  }
];

// Note: isNewUserRoleValid is already defined above

// Lifecycle hooks
onMounted(async () => {
  await fetchUserRoles();
  await fetchUsers();
  await fetchRoles();
  await fetchCompanies();
});

// Methods
async function fetchUserRoles() {
  loading.value = true;
  try {
    // Clear the current data first
    userRoles.value = [];

    // Fetch user roles from the view
    const { data: permissionsData, error: permissionsError } = await supabase
      .from('user_roles_view')
      .select(`
        app_user_id,
        user_id,
        first_name,
        last_name,
        role_id,
        role_name,
        cur_company_id,
        company_name
      `)
      .order('user_id');

    if (permissionsError) throw permissionsError;

    // Get emails from user_emails_view
    const { data: userEmails, error: userEmailsError } = await supabase
      .from('user_emails_view')
      .select('user_id, email');

    if (userEmailsError) {
      console.error('Error fetching user emails:', userEmailsError);
    }

    // Create a map of user_id to email
    const emailMap = new Map();

    // Add emails from user_emails_view to the map
    if (userEmails) {
      userEmails.forEach((user: { user_id: string, email: string }) => {
        emailMap.set(user.user_id, user.email);
      });
    }

    // Get the current user's email as a fallback
    const { data: { user } } = await supabase.auth.getUser();
    if (user?.email) {
      emailMap.set(user.id, user.email);
    }

    if (permissionsData) {
      // Transform the data to a more usable format
      // Create a unique ID for each user-role-company combination
      userRoles.value = permissionsData.map((item: {
        app_user_id: number | null,
        user_id: string | null,
        first_name: string | null,
        last_name: string | null,
        role_id: number | null,
        role_name: string | null,
        cur_company_id: number | null,
        company_name: string | null
      }) => {
        // Make sure we have valid values for all fields
        const appUserId = item.app_user_id !== null ? item.app_user_id.toString() : '0';
        const roleId = item.role_id !== null ? item.role_id.toString() : '0';
        const companyId = item.cur_company_id !== null ? item.cur_company_id.toString() : '0';
        const userId = item.user_id || '';

        return {
          id: `${appUserId}-${roleId}-${companyId}`,
          user_id: userId,
          role_id: roleId,
          cur_company_id: companyId,
          user_name: `${item.first_name || ''} ${item.last_name || ''}`.trim() || 'Unknown',
          user_email: emailMap.get(userId) || user?.email || 'Email not available',
          role: roleId,
          role_name: item.role_name || '',
          company: companyId,
          company_name: item.company_name || ''
        };
      });

      // Force a UI refresh
      userRoles.value = [...userRoles.value];
    }
  } catch (error: unknown) {
    const pgError = error as PostgrestError;
    console.error('Error fetching user roles:', pgError);
    // Use a more subtle notification without warning icon
    $q.notify({
      color: 'blue-grey-7',
      message: 'Unable to load user roles. Please try again.',
      icon: 'refresh',
      actions: [{
        label: 'Retry',
        color: 'white',
        handler: () => { void fetchUserRoles(); }
      }]
    });
  } finally {
    loading.value = false;
  }
}

async function fetchUsers() {
  try {
    // Fetch app_users from the database
    const { data: appUsers, error: appUsersError } = await supabase
      .from('app_users')
      .select('id, user_id, first_name, last_name, is_active');

    if (appUsersError) throw appUsersError;

    // Get emails from user_emails_view
    const { data: userEmails, error: userEmailsError } = await supabase
      .from('user_emails_view')
      .select('user_id, email');

    if (userEmailsError) {
      console.error('Error fetching user emails:', userEmailsError);
    }

    // Create a map of user_id to email
    const emailMap = new Map();

    // Add emails from user_emails_view to the map
    if (userEmails) {
      userEmails.forEach((user: { user_id: string, email: string }) => {
        emailMap.set(user.user_id, user.email);
      });
    }

    // Get the current user's email as a fallback
    const { data: { user } } = await supabase.auth.getUser();
    if (user?.email) {
      emailMap.set(user.id, user.email);
    }

    if (appUsers) {
      // For each app_user, we'll use their first and last name and email from the map
      userOptions.value = appUsers.map((appUser: {
        id: number,
        user_id: string,
        first_name: string | null,
        last_name: string | null,
        is_active: boolean
      }) => {
        const email = emailMap.get(appUser.user_id) || 'Email not available';

        // Create a display name that prioritizes email
        const displayName = email || `${appUser.first_name || ''} ${appUser.last_name || ''}`.trim() || 'User';

        return {
          id: appUser.user_id,
          email: email,
          display_name: displayName
        };
      });
    }
  } catch (error: unknown) {
    const pgError = error as PostgrestError;
    console.error('Error fetching users:', pgError);
  }
}

async function fetchRoles() {
  try {
    const { data, error } = await supabase
      .from('roles')
      .select('id, name');

    if (error) throw error;

    if (data) {
      roleOptions.value = data;
    }
  } catch (error: unknown) {
    const pgError = error as PostgrestError;
    console.error('Error fetching roles:', pgError);
  }
}

async function fetchCompanies() {
  try {
    const { data, error } = await supabase
      .from('companies')
      .select('id, name');

    if (error) throw error;

    if (data) {
      companyOptions.value = data;
    }
  } catch (error: unknown) {
    const pgError = error as PostgrestError;
    console.error('Error fetching companies:', pgError);
  }
}

async function updateUserRole(row: UserRole, field: string, value: string) {
  try {
    // Parse the ID to get the app_user_id, role_id, and company_id
    const idParts = row.id.split('-');
    if (idParts.length !== 3) {
      throw new Error('Invalid ID format');
    }

    const appUserId = idParts[0];
    const oldRoleId = idParts[1];
    const oldCompanyId = idParts[2];

    if (!appUserId || !oldRoleId || !oldCompanyId) {
      throw new Error('Missing ID components');
    }

    // For companies_users_roles table, we need to delete the old record and insert a new one
    // First, delete the old record
    const { error: deleteError } = await supabase
      .from('companies_users_roles')
      .delete()
      .eq('app_user_id', parseInt(appUserId))
      .eq('role_id', parseInt(oldRoleId))
      .eq('cur_company_id', parseInt(oldCompanyId));

    if (deleteError) throw deleteError;

    // Then, insert the new record with the updated field
    const newRecord = {
      app_user_id: parseInt(appUserId),
      role_id: field === 'role_id' ? parseInt(value) : parseInt(oldRoleId),
      cur_company_id: field === 'company_id' ? parseInt(value) : parseInt(oldCompanyId),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { error: insertError } = await supabase
      .from('companies_users_roles')
      .insert([newRecord]);

    if (insertError) throw insertError;

    // Get the updated field name for the notification
    let fieldName = '';
    if (field === 'role_id') {
      const role = roleOptions.value.find(r => r.id === value);
      fieldName = `Role to ${role?.name || 'Unknown'}`;
    } else if (field === 'company_id') {
      const company = companyOptions.value.find(c => c.id === value);
      fieldName = `Company to ${company?.name || 'Unknown'}`;
    }

    $q.notify({
      type: 'positive',
      color: 'positive',
      textColor: 'white',
      message: `Updated ${fieldName} for ${row.user_name}`,
      icon: 'check_circle',
      position: 'top',
      timeout: 5000,
      multiLine: true
    });

    // Also log to console for debugging
    console.log(`Updated ${fieldName} for ${row.user_name}`);

    // Update the row in the table instead of refreshing all data
    // Find the role and company names for the updated record
    const updatedRole = roleOptions.value.find(r => r.id === newRecord.role_id.toString());
    const updatedCompany = companyOptions.value.find(c => c.id === newRecord.cur_company_id.toString());

    // Create a new ID for the updated row
    const newId = `${newRecord.app_user_id}-${newRecord.role_id}-${newRecord.cur_company_id}`;

    // Find the index of the row in the userRoles array
    const rowIndex = userRoles.value.findIndex(r => r.id === row.id);

    if (rowIndex !== -1) {
      // Create an updated row
      const updatedRow = {
        ...row,
        id: newId,
        role_id: newRecord.role_id.toString(),
        cur_company_id: newRecord.cur_company_id.toString(),
        role: newRecord.role_id.toString(),
        role_name: updatedRole?.name || '',
        company: newRecord.cur_company_id.toString(),
        company_name: updatedCompany?.name || ''
      };

      // Update the row in the userRoles array
      userRoles.value.splice(rowIndex, 1, updatedRow);

      // Force a UI refresh
      userRoles.value = [...userRoles.value];
    }
  } catch (error: unknown) {
    const pgError = error as PostgrestError;
    console.error('Error updating user role:', pgError);
    $q.notify({
      type: 'info',
      color: 'blue-grey-7',
      textColor: 'white',
      message: 'Unable to update user role. Please try again.',
      icon: 'info',
      position: 'top',
      timeout: 5000,
      multiLine: true,
      actions: [{
        label: 'Dismiss',
        color: 'white'
      }]
    });
  }
}

function openAddDialog() {
  // Reset the form
  newUserRole.value = {
    user_id: '',
    role_id: '',
    cur_company_id: ''
  };

  // Reset filtered options
  filteredCompanyOptions.value = [];
  filteredRoleOptions.value = [];

  // Open the dialog
  addDialog.value = true;
}

// Update selections based on user and company
function updateSelections() {
  // Reset role selection when user or company changes
  newUserRole.value.role_id = '';

  // Update filtered companies
  filterCompanyOptions();

  // Update filtered roles
  filterRoleOptions();
}

// Filter company options based on selected user
function filterCompanyOptions() {
  if (!newUserRole.value.user_id) {
    filteredCompanyOptions.value = [];
    return;
  }

  // Start with all companies
  filteredCompanyOptions.value = [...companyOptions.value];
}

// Filter role options based on selected user and company
function filterRoleOptions() {
  if (!newUserRole.value.user_id || !newUserRole.value.cur_company_id) {
    filteredRoleOptions.value = [];
    return;
  }

  // Start with all roles
  filteredRoleOptions.value = [...roleOptions.value];

  // The issue is that we're comparing different types of IDs
  // Let's convert everything to strings for comparison

  // Get the selected user ID and company ID as strings
  const selectedUserId = newUserRole.value.user_id.toString();
  const selectedCompanyId = newUserRole.value.cur_company_id.toString();

  // Find roles that the user already has for this company
  // We need to check if the user has any roles for this company
  const userRolesForCompany: string[] = [];

  // Loop through all user roles to find matches
  for (const ur of userRoles.value) {
    // Convert to strings for comparison
    const urUserId = ur.user_id ? ur.user_id.toString() : '';
    const urCompanyId = ur.cur_company_id ? ur.cur_company_id.toString() : '';

    if (urUserId === selectedUserId && urCompanyId === selectedCompanyId) {
      // Convert role_id to string to ensure consistent comparison
      userRolesForCompany.push(ur.role_id.toString());
    }
  }

  // Filter out roles that the user already has
  if (userRolesForCompany.length > 0) {
    filteredRoleOptions.value = roleOptions.value.filter(role => {
      // Check if this role is already assigned
      // Convert role.id to string for consistent comparison
      return !userRolesForCompany.includes(role.id.toString());
    });
  }
}

async function addUserRole() {
  try {
    // First, get the app_user_id for the selected user_id
    const { data: appUserData, error: appUserError } = await supabase
      .from('app_users')
      .select('id, first_name, last_name')
      .eq('user_id', newUserRole.value.user_id)
      .single();

    if (appUserError) throw appUserError;

    if (!appUserData) {
      throw new Error('User not found in app_users table');
    }

    // Create the new record for companies_users_roles
    const newRecord = {
      app_user_id: appUserData.id,
      role_id: parseInt(newUserRole.value.role_id),
      cur_company_id: parseInt(newUserRole.value.cur_company_id),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { error } = await supabase
      .from('companies_users_roles')
      .insert([newRecord]);

    if (error) throw error;

    // Get user, role, and company names for the notification
    const user = userOptions.value.find(u => u.id === newUserRole.value.user_id);
    const role = roleOptions.value.find(r => r.id === newUserRole.value.role_id);
    const company = companyOptions.value.find(c => c.id === newUserRole.value.cur_company_id);

    // Use a dialog notification instead of toast
    $q.dialog({
      title: 'Success',
      message: `Role added: ${user?.display_name || 'User'} as ${role?.name || 'Role'} for ${company?.name || 'Company'}`,
      color: 'positive',
      persistent: false,
      ok: {
        label: 'OK',
        flat: true,
        color: 'positive'
      }
    });

    // Also log to console for debugging
    console.log(`Role added: ${user?.display_name || 'User'} as ${role?.name || 'Role'} for ${company?.name || 'Company'}`);

    // Get the email for this user from user_emails_view
    const { data: userEmail, error: userEmailError } = await supabase
      .from('user_emails_view')
      .select('email')
      .eq('user_id', newUserRole.value.user_id)
      .single();

    if (userEmailError) {
      console.error('Error fetching user email:', userEmailError);
    }

    // Create a new row
    const newRow = {
      id: `${appUserData.id}-${newUserRole.value.role_id}-${newUserRole.value.cur_company_id}`,
      user_id: newUserRole.value.user_id,
      role_id: newUserRole.value.role_id,
      cur_company_id: newUserRole.value.cur_company_id,
      user_name: `${appUserData.first_name || ''} ${appUserData.last_name || ''}`.trim() || user?.display_name || 'Unknown',
      user_email: userEmail?.email || user?.email || 'Email not available',
      role: newUserRole.value.role_id,
      role_name: role?.name || '',
      company: newUserRole.value.cur_company_id,
      company_name: company?.name || ''
    };

    // Add the new row to the userRoles array
    userRoles.value.push(newRow);

    // Force a UI refresh
    userRoles.value = [...userRoles.value];

    // Close the dialog
    addDialog.value = false;
  } catch (error: unknown) {
    const pgError = error as PostgrestError;
    console.error('Error adding user role:', pgError);

    // Check if this is a duplicate role error
    if (pgError.message && pgError.message.includes('duplicate key value violates unique constraint')) {
      // Show a user-friendly message
      $q.notify({
        type: 'info',
        color: 'blue-grey-7',
        textColor: 'white',
        message: 'This user already has this role for the selected company.',
        icon: 'info',
        position: 'top',
        timeout: 5000,
        multiLine: true
      });
    } else {
      // Show a generic error message
      $q.notify({
        type: 'info',
        color: 'blue-grey-7',
        textColor: 'white',
        message: 'Unable to add user role. Please try again.',
        icon: 'info',
        position: 'top',
        timeout: 5000,
        multiLine: true,
        actions: [{
          label: 'Dismiss',
          color: 'white'
        }]
      });
    }
  }
}

function confirmDelete(row: UserRole) {
  userRoleToDelete.value = row;
  deleteDialog.value = true;
}

async function deleteUserRole() {
  if (!userRoleToDelete.value) return;

  try {
    // Store the user role details before deletion for the notification
    const deletedRole = {
      role_name: userRoleToDelete.value.role_name,
      user_name: userRoleToDelete.value.user_name,
      company_name: userRoleToDelete.value.company_name
    };

    // Parse the ID to get the app_user_id, role_id, and company_id
    const idParts = userRoleToDelete.value.id.split('-');
    if (idParts.length !== 3) {
      throw new Error('Invalid ID format');
    }

    const appUserId = idParts[0];
    const roleId = idParts[1];
    const companyId = idParts[2];

    if (!appUserId || !roleId || !companyId) {
      throw new Error('Missing ID components');
    }

    const { error } = await supabase
      .from('companies_users_roles')
      .delete()
      .eq('app_user_id', parseInt(appUserId))
      .eq('role_id', parseInt(roleId))
      .eq('cur_company_id', parseInt(companyId));

    if (error) throw error;

    // Remove the row from the table instead of refreshing all data
    if (userRoleToDelete.value) {
      // Find the index of the row in the userRoles array
      const rowIndex = userRoles.value.findIndex(r => r.id === userRoleToDelete.value?.id);

      if (rowIndex !== -1) {
        // Remove the row from the userRoles array
        userRoles.value.splice(rowIndex, 1);

        // Force a UI refresh
        userRoles.value = [...userRoles.value];
      }

      // Clear the userRoleToDelete reference after using it
      userRoleToDelete.value = null;
    }

    // Show notification after data is refreshed
    $q.notify({
      type: 'positive',
      color: 'positive',
      textColor: 'white',
      message: `Removed ${deletedRole.role_name} role for ${deletedRole.user_name} at ${deletedRole.company_name}`,
      icon: 'check_circle',
      position: 'top',
      timeout: 5000,
      multiLine: true
    });

    // Also log to console for debugging
    console.log(`Removed ${deletedRole.role_name} role for ${deletedRole.user_name} at ${deletedRole.company_name}`);
  } catch (error: unknown) {
    const pgError = error as PostgrestError;
    console.error('Error deleting user role:', pgError);
    $q.notify({
      type: 'info',
      color: 'blue-grey-7',
      textColor: 'white',
      message: 'Unable to delete user role. Please try again.',
      icon: 'info',
      position: 'top',
      timeout: 5000,
      multiLine: true,
      actions: [{
        label: 'Dismiss',
        color: 'white'
      }]
    });
  }
}
</script>

<style>
/* Global styles for notifications */
.notification-toast {
  font-size: 16px !important;
  font-weight: bold !important;
  padding: 12px !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
  min-width: 300px !important;
}

/* Loading animation styles */
.loading-container {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
</style>

<style scoped>
.user-management-table {
  width: 100%;
}

/* Skeleton loader styles */
.table-skeleton-loader {
  width: 100%;
  padding: 8px;
  background-color: var(--q-card-background, #fff);
}

.skeleton-row {
  display: flex;
  padding: 8px 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.header-row {
  background-color: rgba(0, 0, 0, 0.02);
  font-weight: 500;
}

.skeleton-cell {
  flex: 1;
  padding: 8px;
  min-width: 100px;
}

/* Skeleton loader styles */
.skeleton-container {
  width: 100%;
  padding: 8px;
  background-color: var(--q-table-bgcolor, transparent);
  min-height: 400px; /* Adjust based on your table's typical height */
}

/* Mobile-specific styles */
@media (max-width: 599px) {
  .user-management-table :deep(.q-table__container) {
    border-radius: 0;
  }

  .user-management-table :deep(.q-table) {
    border-left: 0;
    border-right: 0;
  }

  /* Adjust padding for mobile */
  .user-management-table :deep(.q-table__top),
  .user-management-table :deep(.q-table__bottom),
  .user-management-table :deep(th),
  .user-management-table :deep(td) {
    padding: 4px 8px;
  }

  /* Stack the top controls on mobile */
  .user-management-table :deep(.q-table__top) {
    flex-direction: column;
    align-items: stretch;
  }

  .user-management-table :deep(.q-table__top > *) {
    margin-bottom: 8px;
  }

  .user-management-table :deep(.q-table__top .q-space) {
    display: none;
  }
}
</style>
