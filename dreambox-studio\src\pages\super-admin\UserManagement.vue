<template>
  <q-page :padding="!$q.platform.is.mobile">
    <div :class="{ 'q-pa-md': !$q.platform.is.mobile, 'mobile-container': $q.platform.is.mobile }">
      <user-management-table />
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';
import UserManagementTable from 'src/components/admin/UserManagementTable.vue';

const $q = useQuasar();
</script>

<style scoped>
.mobile-container {
  padding: 0.5rem 0;
}

.mobile-container h1,
.mobile-container p {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
</style>
