# Step 1: Core Architecture

## Overview
In this step, we'll establish the foundation of our filter engine by creating the core architectural components:
1. Filter Registry
2. Filter State Store
3. Filter Engine

## Tasks

### 1.1 Create the Filter Registry

The Filter Registry will be the central repository for all filter type definitions.

```typescript
// src/lib/filters/FilterRegistry.ts

export interface FilterTypeDefinition {
  id: string;
  name: string;
  section: 'main' | 'expanded' | 'specialized';
  component: any; // Will be a Vue component
  tableTypes: string[]; // Which tables this filter type applies to
  stateExtractor: (state: any) => any;
  stateApplier: (state: any, filterValue: any) => boolean;
  clearHandler: (state: any) => any;
  serializeValue?: (value: any) => any; // For saving to database
  deserializeValue?: (value: any) => any; // For loading from database
}

export class FilterRegistry {
  private static instance: FilterRegistry;
  private filterTypes: Map<string, FilterTypeDefinition> = new Map();

  private constructor() {
    // Private constructor to enforce singleton
  }

  public static getInstance(): FilterRegistry {
    if (!FilterRegistry.instance) {
      FilterRegistry.instance = new FilterRegistry();
    }
    return FilterRegistry.instance;
  }

  public registerFilterType(definition: FilterTypeDefinition): void {
    this.filterTypes.set(definition.id, definition);
    console.log(`Registered filter type: ${definition.id}`);
  }

  public getFilterTypesForTable(tableName: string): FilterTypeDefinition[] {
    return Array.from(this.filterTypes.values())
      .filter(def => def.tableTypes.includes(tableName));
  }

  public getFilterType(id: string): FilterTypeDefinition | undefined {
    return this.filterTypes.get(id);
  }

  public getAllFilterTypes(): FilterTypeDefinition[] {
    return Array.from(this.filterTypes.values());
  }
}

// Export a singleton instance
export const filterRegistry = FilterRegistry.getInstance();
```

### 1.2 Implement the Filter State Store

Create a Pinia store to manage all filter-related state:

```typescript
// src/stores/filterStore.ts

import { defineStore } from 'pinia';
import { supabase } from '@/boot/supabase';
import { useAuthStore } from '@/stores/auth';
import { filterRegistry } from '@/lib/filters/FilterRegistry';
import type { FilterTypeDefinition } from '@/lib/filters/FilterRegistry';

export interface FilterDefinition {
  id: string;
  name: string;
  field: string;
  filterType: string;
  section: 'main' | 'expanded';
  order: number;
  visible: boolean;
  filterValue: any;
  filterOptions?: Array<{ label: string; value: any }>;
}

export interface SavedFilter {
  id: number;
  user_id: number;
  name: string;
  description: string | null;
  table_name: string;
  context: string;
  configuration: {
    columns: Array<{
      id: string;
      visible?: boolean;
      section?: 'main' | 'expanded';
      order?: number;
      filterValue?: any;
    }>;
    specializedFilters?: Record<string, any>;
  };
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

export interface FilterState {
  currentTable: string | null;
  currentFilterId: number | null;
  savedFilters: SavedFilter[];
  columns: FilterDefinition[];
  activeFilters: Record<string, any>;
  specializedFilters: Record<string, any>;
  isLoading: boolean;
  hasUnsavedChanges: boolean;
}

export const useFilterStore = defineStore('filter', {
  state: (): FilterState => ({
    currentTable: null,
    currentFilterId: null,
    savedFilters: [],
    columns: [],
    activeFilters: {},
    specializedFilters: {},
    isLoading: false,
    hasUnsavedChanges: false
  }),
  
  getters: {
    filtersByTable: (state) => 
      state.savedFilters.filter(f => f.table_name === state.currentTable),
    
    currentFilter: (state) => 
      state.savedFilters.find(f => f.id === state.currentFilterId),
    
    mainTableColumns: (state) => 
      state.columns.filter(col => col.section === 'main')
        .sort((a, b) => a.order - b.order),
    
    expandedViewColumns: (state) => 
      state.columns.filter(col => col.section === 'expanded')
        .sort((a, b) => a.order - b.order),
    
    specializedFilterTypes: (state) => {
      if (!state.currentTable) return [];
      return filterRegistry.getFilterTypesForTable(state.currentTable)
        .filter(def => def.section === 'specialized');
    }
  },
  
  actions: {
    async setTable(tableName: string) {
      this.currentTable = tableName;
      this.currentFilterId = null;
      this.activeFilters = {};
      this.specializedFilters = {};
      
      // Load base columns for this table
      this.loadBaseColumnsForTable(tableName);
      
      // Load saved filters for this table
      await this.loadSavedFilters();
      
      // Try to load default filter
      const defaultFilter = this.savedFilters.find(
        f => f.table_name === tableName && f.is_default
      );
      
      if (defaultFilter) {
        this.loadFilter(defaultFilter.id);
      }
    },
    
    async loadSavedFilters() {
      this.isLoading = true;
      
      try {
        const authStore = useAuthStore();
        if (!authStore.state.user?.id) {
          console.warn('Cannot load filters: User not logged in');
          return;
        }
        
        // Get the app_user.id from the user_id
        const { data: userData, error: userError } = await supabase
          .from('app_users')
          .select('id')
          .eq('user_id', authStore.state.user.id)
          .single();
        
        if (userError) {
          console.error('Error fetching app_user id:', userError);
          return;
        }
        
        const appUserId = userData.id;
        
        // Load saved filters
        const { data, error } = await supabase
          .from('user_filters')
          .select('*')
          .eq('user_id', appUserId);
        
        if (error) {
          console.error('Error loading saved filters:', error);
          return;
        }
        
        this.savedFilters = data as SavedFilter[];
      } catch (err) {
        console.error('Error in loadSavedFilters:', err);
      } finally {
        this.isLoading = false;
      }
    },
    
    async saveCurrentFilter(name: string, description = '', makeDefault = false) {
      if (!this.currentTable) return false;
      
      // Get the app_user.id from the user_id
      const authStore = useAuthStore();
      if (!authStore.state.user?.id) {
        console.warn('Cannot save filter: User not logged in');
        return false;
      }
      
      const { data: userData, error: userError } = await supabase
        .from('app_users')
        .select('id')
        .eq('user_id', authStore.state.user.id)
        .single();
      
      if (userError) {
        console.error('Error fetching app_user id:', userError);
        return false;
      }
      
      const appUserId = userData.id;
      
      // Create configuration object
      const configuration = {
        columns: this.columns.map(col => ({
          id: col.id,
          visible: col.visible,
          section: col.section,
          order: col.order,
          filterValue: col.filterValue || null
        })),
        specializedFilters: { ...this.specializedFilters }
      };
      
      try {
        // If editing existing filter
        if (this.currentFilterId) {
          const { error } = await supabase
            .from('user_filters')
            .update({
              name,
              description,
              table_name: this.currentTable,
              context: this.currentTable,
              configuration,
              is_default: makeDefault,
              updated_at: new Date().toISOString()
            })
            .eq('id', this.currentFilterId);
          
          if (error) throw error;
          
          // Update local state
          const index = this.savedFilters.findIndex(f => f.id === this.currentFilterId);
          if (index !== -1) {
            this.savedFilters[index] = {
              ...this.savedFilters[index],
              name,
              description,
              table_name: this.currentTable,
              context: this.currentTable,
              configuration,
              is_default: makeDefault,
              updated_at: new Date().toISOString()
            };
          }
        } else {
          // Creating new filter
          const { data, error } = await supabase
            .from('user_filters')
            .insert({
              user_id: appUserId,
              name,
              description,
              table_name: this.currentTable,
              context: this.currentTable,
              configuration,
              is_default: makeDefault
            })
            .select();
          
          if (error) throw error;
          
          if (data && data.length > 0) {
            this.currentFilterId = data[0].id;
            this.savedFilters.push(data[0] as SavedFilter);
          }
        }
        
        // If making this the default, update other filters
        if (makeDefault) {
          await this.updateDefaultFilter(this.currentFilterId as number);
        }
        
        this.hasUnsavedChanges = false;
        return true;
      } catch (err) {
        console.error('Error saving filter:', err);
        return false;
      }
    },
    
    loadFilter(filterId: number) {
      const filter = this.savedFilters.find(f => f.id === filterId);
      if (!filter) return false;
      
      try {
        // First load the base columns for this table
        this.loadBaseColumnsForTable(filter.table_name);
        
        // Apply column configuration
        filter.configuration.columns.forEach(savedCol => {
          const column = this.columns.find(col => col.id === savedCol.id);
          if (column) {
            if (savedCol.visible !== undefined) column.visible = savedCol.visible;
            if (savedCol.section !== undefined) column.section = savedCol.section;
            if (savedCol.order !== undefined) column.order = savedCol.order;
            if (savedCol.filterValue !== undefined) column.filterValue = savedCol.filterValue;
          }
        });
        
        // Update active filters
        this.activeFilters = {};
        this.columns.forEach(col => {
          if (col.filterValue) {
            this.activeFilters[col.field] = col.filterValue;
          }
        });
        
        // Apply specialized filters
        if (filter.configuration.specializedFilters) {
          this.specializedFilters = { ...filter.configuration.specializedFilters };
          
          // Process specialized filters
          const specializedFilterTypes = filterRegistry.getFilterTypesForTable(filter.table_name)
            .filter(def => def.section === 'specialized');
          
          specializedFilterTypes.forEach(filterType => {
            const filterValue = this.specializedFilters[filterType.id];
            if (filterValue && filterType.deserializeValue) {
              this.specializedFilters[filterType.id] = filterType.deserializeValue(filterValue);
            }
          });
        } else {
          this.specializedFilters = {};
        }
        
        this.currentFilterId = filterId;
        this.hasUnsavedChanges = false;
        return true;
      } catch (err) {
        console.error('Error loading filter:', err);
        return false;
      }
    },
    
    async deleteFilter(filterId: number) {
      try {
        const { error } = await supabase
          .from('user_filters')
          .delete()
          .eq('id', filterId);
        
        if (error) throw error;
        
        // Update local state
        this.savedFilters = this.savedFilters.filter(f => f.id !== filterId);
        
        if (this.currentFilterId === filterId) {
          this.currentFilterId = null;
          
          // Load default filter if available
          const defaultFilter = this.savedFilters.find(
            f => f.table_name === this.currentTable && f.is_default
          );
          
          if (defaultFilter) {
            this.loadFilter(defaultFilter.id);
          } else {
            // Reset to base configuration
            this.loadBaseColumnsForTable(this.currentTable as string);
          }
        }
        
        return true;
      } catch (err) {
        console.error('Error deleting filter:', err);
        return false;
      }
    },
    
    async updateDefaultFilter(filterId: number) {
      if (!this.currentTable) return false;
      
      // Get the app_user.id from the user_id
      const authStore = useAuthStore();
      if (!authStore.state.user?.id) {
        console.warn('Cannot update default filter: User not logged in');
        return false;
      }
      
      const { data: userData, error: userError } = await supabase
        .from('app_users')
        .select('id')
        .eq('user_id', authStore.state.user.id)
        .single();
      
      if (userError) {
        console.error('Error fetching app_user id:', userError);
        return false;
      }
      
      const appUserId = userData.id;
      
      try {
        // First, unset any existing default
        const { error: error1 } = await supabase
          .from('user_filters')
          .update({ is_default: false })
          .eq('user_id', appUserId)
          .eq('table_name', this.currentTable)
          .neq('id', filterId);
        
        if (error1) throw error1;
        
        // Then set the new default
        const { error: error2 } = await supabase
          .from('user_filters')
          .update({ is_default: true })
          .eq('id', filterId);
        
        if (error2) throw error2;
        
        // Update local state
        this.savedFilters.forEach(f => {
          if (f.table_name === this.currentTable) {
            f.is_default = (f.id === filterId);
          }
        });
        
        return true;
      } catch (err) {
        console.error('Error updating default filter:', err);
        return false;
      }
    },
    
    setColumnFilter(columnId: string, value: any) {
      const column = this.columns.find(col => col.id === columnId);
      if (column) {
        column.filterValue = value;
        
        // Update active filters
        if (value === null || value === '') {
          delete this.activeFilters[column.field];
        } else {
          this.activeFilters[column.field] = value;
        }
        
        this.hasUnsavedChanges = true;
      }
    },
    
    setSpecializedFilter(filterId: string, value: any) {
      const filterType = filterRegistry.getFilterType(filterId);
      if (filterType) {
        // If there's a serializer, use it
        const serializedValue = filterType.serializeValue ? 
          filterType.serializeValue(value) : value;
        
        this.specializedFilters[filterId] = serializedValue;
        this.hasUnsavedChanges = true;
      }
    },
    
    clearFilter(columnId: string) {
      this.setColumnFilter(columnId, null);
    },
    
    clearSpecializedFilter(filterId: string) {
      const filterType = filterRegistry.getFilterType(filterId);
      if (filterType && filterType.clearHandler) {
        filterType.clearHandler(this.specializedFilters);
        this.hasUnsavedChanges = true;
      }
    },
    
    clearAllFilters() {
      // Clear column filters
      this.columns.forEach(col => {
        col.filterValue = null;
      });
      
      // Clear active filters
      this.activeFilters = {};
      
      // Clear specialized filters
      this.specializedFilters = {};
      
      // Reset current filter ID
      this.currentFilterId = null;
      
      this.hasUnsavedChanges = true;
    },
    
    toggleColumnVisibility(columnId: string, visible: boolean) {
      const column = this.columns.find(col => col.id === columnId);
      if (column) {
        column.visible = visible;
        this.hasUnsavedChanges = true;
      }
    },
    
    updateColumnOrder(columnId: string, section: 'main' | 'expanded', order: number) {
      const column = this.columns.find(col => col.id === columnId);
      if (column) {
        column.section = section;
        column.order = order;
        this.hasUnsavedChanges = true;
      }
    },
    
    moveColumnToSection(columnId: string, section: 'main' | 'expanded') {
      const column = this.columns.find(col => col.id === columnId);
      if (column) {
        column.section = section;
        
        // Update order to be at the end of the section
        const sectionColumns = this.columns.filter(col => col.section === section);
        const maxOrder = sectionColumns.reduce((max, col) => Math.max(max, col.order), -1);
        column.order = maxOrder + 1;
        
        this.hasUnsavedChanges = true;
      }
    },
    
    markUnsavedChanges() {
      this.hasUnsavedChanges = true;
    },
    
    dismissUnsavedChanges() {
      this.hasUnsavedChanges = false;
    },
    
    // Load base column configuration for a table
    loadBaseColumnsForTable(tableName: string) {
      // This would load the default column configuration for a given table
      switch(tableName) {
        case 'templates':
          this.columns = [
            { id: 'name', name: 'Name', field: 'name', visible: true, section: 'main', order: 0, filterType: 'text', filterValue: null },
            { id: 'description', name: 'Description', field: 'description', visible: true, section: 'main', order: 1, filterType: 'text', filterValue: null },
            { id: 'status', name: 'Status', field: 'status', visible: true, section: 'main', order: 2, filterType: 'multiselect', filterValue: null, filterOptions: [
              { label: 'Draft', value: 'draft' },
              { label: 'Pending', value: 'pending' },
              { label: 'Active', value: 'active' },
              { label: 'Archived', value: 'archived' }
            ]},
            { id: 'version', name: 'Version', field: 'version', visible: true, section: 'expanded', order: 0, filterType: 'number', filterValue: null },
            { id: 'created_at', name: 'Created At', field: 'created_at', visible: true, section: 'expanded', order: 1, filterType: 'date', filterValue: null },
            { id: 'updated_at', name: 'Updated At', field: 'updated_at', visible: true, section: 'expanded', order: 2, filterType: 'date', filterValue: null },
            { id: 'company_name', name: 'Company', field: 'company_name', visible: true, section: 'expanded', order: 3, filterType: 'text', filterValue: null },
            { id: 'initiated_by_name', name: 'Created By', field: 'initiated_by_name', visible: true, section: 'expanded', order: 4, filterType: 'text', filterValue: null },
            { id: 'approved_by_name', name: 'Approved By', field: 'approved_by_name', visible: true, section: 'expanded', order: 5, filterType: 'text', filterValue: null },
            { id: 'approval_date', name: 'Approval Date', field: 'approval_date', visible: true, section: 'expanded', order: 6, filterType: 'date', filterValue: null },
            { id: 'agent_name', name: 'Agent', field: 'agent_name', visible: true, section: 'expanded', order: 7, filterType: 'text', filterValue: null }
          ];
          break;
        case 'products':
          this.columns = [
            { id: 'name', name: 'Name', field: 'name', visible: true, section: 'main', order: 0, filterType: 'text', filterValue: null },
            { id: 'sku', name: 'SKU', field: 'sku', visible: true, section: 'main', order: 1, filterType: 'text', filterValue: null },
            { id: 'base_price', name: 'Base Price', field: 'base_price', visible: true, section: 'main', order: 2, filterType: 'number', filterValue: null },
            { id: 'active', name: 'Active', field: 'active', visible: true, section: 'main', order: 3, filterType: 'boolean', filterValue: null },
            { id: 'created_at', name: 'Created At', field: 'created_at', visible: true, section: 'expanded', order: 0, filterType: 'date', filterValue: null },
            { id: 'updated_at', name: 'Updated At', field: 'updated_at', visible: true, section: 'expanded', order: 1, filterType: 'date', filterValue: null }
          ];
          break;
        case 'collections':
          this.columns = [
            { id: 'name', name: 'Name', field: 'name', visible: true, section: 'main', order: 0, filterType: 'text', filterValue: null },
            { id: 'description', name: 'Description', field: 'description', visible: true, section: 'main', order: 1, filterType: 'text', filterValue: null },
            { id: 'created_at', name: 'Created At', field: 'created_at', visible: true, section: 'expanded', order: 0, filterType: 'date', filterValue: null },
            { id: 'updated_at', name: 'Updated At', field: 'updated_at', visible: true, section: 'expanded', order: 1, filterType: 'date', filterValue: null }
          ];
          break;
        default:
          this.columns = [];
          break;
      }
      
      this.currentFilterId = null;
      this.activeFilters = {};
      this.specializedFilters = {};
      this.hasUnsavedChanges = false;
    }
  }
});
```

### 1.3 Develop the Filter Engine

Create a class to handle the application of filters to data:

```typescript
// src/lib/filters/FilterEngine.ts

import { filterRegistry } from './FilterRegistry';
import type { FilterTypeDefinition } from './FilterRegistry';

export class FilterEngine {
  private static instance: FilterEngine;

  private constructor() {
    // Private constructor to enforce singleton
  }

  public static getInstance(): FilterEngine {
    if (!FilterEngine.instance) {
      FilterEngine.instance = new FilterEngine();
    }
    return FilterEngine.instance;
  }

  public applyFilters(data: any[], tableName: string, activeFilters: Record<string, any>, specializedFilters: Record<string, any> = {}): any[] {
    if (!data || data.length === 0) {
      return [];
    }

    // If no filters are active, return all data
    if (Object.keys(activeFilters).length === 0 && Object.keys(specializedFilters).length === 0) {
      return data;
    }

    // Get specialized filter types for this table
    const specializedFilterTypes = filterRegistry.getFilterTypesForTable(tableName)
      .filter(def => def.section === 'specialized');

    // Apply filters
    return data.filter(item => {
      // Apply standard column filters
      for (const [field, value] of Object.entries(activeFilters)) {
        if (value === null || value === undefined || value === '') continue;

        const itemValue = item[field];
        if (itemValue === undefined) continue;

        // Apply filter based on value type
        if (typeof value === 'string') {
          // Text filter
          if (typeof itemValue === 'string' && !itemValue.toLowerCase().includes(value.toLowerCase())) {
            return false;
          }
        } else if (typeof value === 'number') {
          // Number filter
          if (typeof itemValue === 'number' && itemValue !== value) {
            return false;
          }
        } else if (Array.isArray(value)) {
          // Multi-select filter
          if (!value.includes(itemValue)) {
            return false;
          }
        } else if (typeof value === 'object' && value !== null) {
          // Date range filter
          try {
            const itemDate = new Date(itemValue);
            if (isNaN(itemDate.getTime())) return false;

            if (value.from) {
              const fromDate = new Date(value.from);
              fromDate.setHours(0, 0, 0, 0);
              if (itemDate < fromDate) return false;
            }

            if (value.to) {
              const toDate = new Date(value.to);
              toDate.setHours(23, 59, 59, 999);
              if (itemDate > toDate) return false;
            }
          } catch (err) {
            console.error('Error applying date filter:', err);
            return false;
          }
        }
      }

      // Apply specialized filters
      for (const filterType of specializedFilterTypes) {
        const filterValue = specializedFilters[filterType.id];
        if (!filterValue) continue;

        // Apply the filter using the filter type's stateApplier
        if (!filterType.stateApplier(item, filterValue)) {
          return false;
        }
      }

      return true;
    });
  }
}

// Export a singleton instance
export const filterEngine = FilterEngine.getInstance();
```

### 1.4 Create a Filter Event Bus

Implement a simple event bus for filter-related communication:

```typescript
// src/lib/filters/FilterEventBus.ts

type EventCallback = (payload: any) => void;

export class FilterEventBus {
  private static instance: FilterEventBus;
  private events: Map<string, EventCallback[]> = new Map();

  // Event types
  public static readonly EVENTS = {
    FILTER_CHANGED: 'filter:changed',
    FILTER_CLEARED: 'filter:cleared',
    FILTER_SAVED: 'filter:saved',
    FILTER_LOADED: 'filter:loaded',
    FILTER_DELETED: 'filter:deleted',
    SPECIALIZED_FILTER_CHANGED: 'specialized-filter:changed',
    SPECIALIZED_FILTER_CLEARED: 'specialized-filter:cleared'
  };

  private constructor() {
    // Private constructor to enforce singleton
  }

  public static getInstance(): FilterEventBus {
    if (!FilterEventBus.instance) {
      FilterEventBus.instance = new FilterEventBus();
    }
    return FilterEventBus.instance;
  }

  public on(event: string, callback: EventCallback): void {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }
    this.events.get(event)?.push(callback);
  }

  public off(event: string, callback: EventCallback): void {
    const callbacks = this.events.get(event);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index !== -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  public emit(event: string, payload: any): void {
    const callbacks = this.events.get(event);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(payload);
        } catch (err) {
          console.error(`Error in event handler for ${event}:`, err);
        }
      });
    }
  }
}

// Export a singleton instance
export const filterEventBus = FilterEventBus.getInstance();
```

## Expected Outcome

After completing this step, we will have:

1. A central registry for all filter types
2. A comprehensive state store for managing filters
3. A filter engine for applying filters to data
4. An event bus for filter-related communication

These components form the foundation of our filter engine and will be used by all other parts of the system.

## Next Steps

In the next step, we'll implement the base filter components that will be used across the application.
