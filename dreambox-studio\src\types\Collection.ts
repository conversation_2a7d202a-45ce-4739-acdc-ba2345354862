/**
 * Collection interface
 */
export interface Collection {
  id: number;
  name: string;
  description: string | null;
  col_company_id: number; // Changed from company_id
  active: boolean;
  created_at: string;
  updated_at: string;
}

/**
 * Collection filter interface
 */
export interface CollectionFilter {
  col_company_id?: number; // Changed from company_id
  active?: boolean;
  search?: string;
}

/**
 * Collection pagination interface
 */
export interface CollectionPagination {
  page: number;
  rowsPerPage: number;
  rowsNumber: number;
  sortBy?: string;
  descending?: boolean;
}
