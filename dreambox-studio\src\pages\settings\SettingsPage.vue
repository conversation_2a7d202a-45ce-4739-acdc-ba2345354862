<template>
  <q-page padding>
    <div class="q-pa-md">
      <h1 class="text-h4 q-mb-lg">{{ $t('settings.title') }}</h1>
      <settings-panel />
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import SettingsPanel from 'src/components/settings/SettingsPanel.vue';
import { useContextStore } from 'src/stores/contextStore';

const contextStore = useContextStore();

onMounted(() => {
  // Set the context to settings when this page is loaded
  contextStore.setContext('settings');
});
</script>
