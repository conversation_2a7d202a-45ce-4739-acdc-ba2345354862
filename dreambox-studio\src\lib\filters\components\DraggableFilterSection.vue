<template>
  <div class="draggable-filter-section">
    <div class="section-header q-mb-sm">
      <div class="text-subtitle1">{{ title }}</div>
      <div class="text-caption text-grey">{{ columns.length }} columns</div>
    </div>

    <draggable
      v-model="internalColumns"
      group="columns"
      item-key="id"
      handle=".drag-handle"
      ghost-class="ghost"
      chosen-class="chosen"
      @change="handleChange"
    >
      <template #item="{ element }">
        <div class="column-item q-pa-sm q-mb-xs">
          <div class="row items-center no-wrap">
            <q-icon name="drag_indicator" class="drag-handle q-mr-sm cursor-move" />
            <div class="column-name">{{ element.name }}</div>
            <q-space />
            <q-btn
              flat
              dense
              round
              size="md"
              :icon="element.visible ? 'visibility' : 'visibility_off'"
              :color="element.visible ? 'primary' : 'grey'"
              @click="toggleVisibility(element)"
              class="visibility-toggle"
            />
          </div>
        </div>
      </template>
    </draggable>

    <div v-if="columns.length === 0" class="empty-section q-pa-md text-center text-grey">
      Drag columns here
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useFilterStore } from '../../../stores/filterStore';
import type { ColumnDefinition } from '../../../stores/filterStore';
import draggable from 'vuedraggable';

// Props
const props = defineProps<{
  title: string;
  section: 'main' | 'expanded';
  columns: ColumnDefinition[];
}>();

// Emits
const emit = defineEmits<{
  (e: 'update:columns', columns: ColumnDefinition[]): void;
  (e: 'column-moved', columnId: string, fromSection: string, toSection: string): void;
}>();

// Store
const filterStore = useFilterStore();

// Computed
const internalColumns = computed({
  get: () => props.columns,
  set: (value) => {
    emit('update:columns', value);
  }
});

// Methods
function handleChange(event: {
  added?: {
    element: {
      id: string;
      section: string;
      [key: string]: unknown
    }
  }
}) {
  // Handle added items (moved from another section)
  if (event.added) {
    const column = event.added.element;
    emit('column-moved', column.id, column.section, props.section);

    // Update the column's section in the store
    filterStore.moveColumnToSection(column.id, props.section);
  }

  // Update the order of columns
  internalColumns.value.forEach((column, index) => {
    filterStore.updateColumnOrder(column.id, index);
  });
}

function toggleVisibility(column: ColumnDefinition) {
  // Toggle the visibility
  const newVisibility = !column.visible;

  // Update the column visibility in the store
  filterStore.updateColumnVisibility(
    column.id,
    newVisibility,
    column.section
  );
}
</script>

<style scoped>
.draggable-filter-section {
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  padding: 12px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.column-item {
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  background-color: white;
}

.drag-handle {
  cursor: move;
  opacity: 0.6;
}

.column-name {
  font-weight: 500;
  font-size: 0.9rem;
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}

.chosen {
  background: #eaf5fe;
}

.visibility-toggle {
  opacity: 0.8;
  transition: all 0.2s;
  min-width: 36px;
  min-height: 36px;
  font-size: 1.2rem;
}

.visibility-toggle :deep(.q-icon) {
  font-size: 1.5rem;
  width: 100%;
  height: 100%;
}

.visibility-toggle:hover {
  opacity: 1;
  transform: scale(1.05);
}

.empty-section {
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  margin-top: 8px;
}

/* Make the icon more touch-friendly on mobile */
@media (max-width: 600px) {
  .visibility-toggle {
    min-width: 44px;
    min-height: 44px;
  }

  .visibility-toggle :deep(.q-icon) {
    font-size: 1.8rem;
  }
}
</style>
