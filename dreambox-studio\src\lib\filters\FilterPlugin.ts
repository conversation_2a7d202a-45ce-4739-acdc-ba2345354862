/**
 * FilterPlugin.ts
 *
 * This module provides a Vue plugin for the filter engine.
 * It registers all filter components and initializes the filter system.
 */

import type { App } from 'vue';
import { registerAllFilterTypes } from './FilterRegistration';
import FilterPanel from './components/FilterPanel.vue';
import FilterSection from './components/FilterSection.vue';
import FilterControl from './components/FilterControl.vue';
import SavedFilterSelector from './components/SavedFilterSelector.vue';
import FilterActionBar from './components/FilterActionBar.vue';
import DraggableSection from './components/DraggableSection.vue';
import PromptElementsFilter from './specialized/PromptElementsFilter.vue';
import RelatedItemsFilter from './specialized/RelatedItemsFilter.vue';

/**
 * Filter plugin
 */
export const FilterPlugin = {
  /**
   * Install the plugin
   */
  install(app: App) {
    // Register components
    app.component('FilterPanel', FilterPanel);
    app.component('FilterSection', FilterSection);
    app.component('FilterControl', FilterControl);
    app.component('SavedFilterSelector', SavedFilterSelector);
    app.component('FilterActionBar', FilterActionBar);
    app.component('DraggableSection', DraggableSection);

    // Register specialized filter components
    app.component('PromptElementsFilter', PromptElementsFilter);
    app.component('RelatedItemsFilter', RelatedItemsFilter);

    // Register all filter types
    registerAllFilterTypes();

    console.log('Filter plugin installed');
  }
};

// Export default for module imports
export default FilterPlugin;
