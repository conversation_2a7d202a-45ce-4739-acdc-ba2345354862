<template>
  <div class="saved-filter-selector">
    <div class="row items-center no-wrap filter-header">
      <div class="col filter-dropdown-container">
        <q-select
          v-model="selectedFilter"
          :options="filterOptions"
          :label="t('filters.savedFilters')"
          outlined
          dense
          emit-value
          map-options
          :loading="loading"
          :disable="loading"
          class="filter-dropdown"
          @update:model-value="handleFilterSelect"
        >
          <template v-slot:no-option>
            <q-item>
              <q-item-section class="text-grey">
                {{ t('filters.noSavedFilters') }}
              </q-item-section>
            </q-item>
          </template>

          <template v-slot:option="scope">
            <q-item v-bind="scope.itemProps">
              <q-item-section>
                <q-item-label>{{ scope.opt.label }}</q-item-label>
                <q-item-label caption v-if="scope.opt.description">
                  {{ scope.opt.description }}
                </q-item-label>
              </q-item-section>

              <q-item-section side v-if="scope.opt.isDefault">
                <q-badge color="primary" :label="t('common.default')" />
              </q-item-section>

              <q-item-section side>
                <q-btn
                  flat
                  round
                  dense
                  icon="delete"
                  color="negative"
                  @click.stop="confirmDeleteFilter(scope.opt.value)"
                />
              </q-item-section>
            </q-item>
          </template>
        </q-select>
      </div>

      <div class="filter-actions">
        <q-btn
          round
          flat
          color="primary"
          icon="save"
          :disable="!hasUnsavedChanges && currentFilterId !== null"
          @click="showSaveDialog = true"
          class="filter-action-btn q-ml-sm"
        >
          <q-tooltip>{{ currentFilterId ? t('filters.updateFilter') : t('filters.saveFilter') }}</q-tooltip>
        </q-btn>
        <q-btn
          round
          flat
          color="negative"
          icon="clear_all"
          @click="clearFilter"
          class="filter-action-btn q-ml-sm q-mr-sm"
        >
          <q-tooltip>{{ t('filters.clearFilters') }}</q-tooltip>
        </q-btn>
      </div>
    </div>

    <!-- Save Filter Dialog -->
    <q-dialog v-model="showSaveDialog">
      <q-card style="min-width: 350px">
        <q-card-section>
          <div class="text-h6">{{ currentFilterId ? t('filters.updateFilter') : t('filters.saveFilter') }}</div>
        </q-card-section>

        <q-card-section>
          <q-input
            v-model="filterName"
            :label="t('filters.filterName')"
            dense
            outlined
            :rules="[val => !!val || t('filters.nameRequired')]"
          />

          <q-input
            v-model="filterDescription"
            :label="t('filters.description')"
            dense
            outlined
            type="textarea"
            class="q-mt-sm"
          />

          <q-checkbox
            v-model="makeDefault"
            :label="t('filters.setAsDefaultFilter')"
            class="q-mt-sm"
          />
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="t('common.cancel')" color="primary" v-close-popup />
          <q-btn
            flat
            :label="t('common.save')"
            color="primary"
            :disable="!filterName"
            @click="saveFilter"
            v-close-popup
          />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- Delete Confirmation Dialog -->
    <q-dialog v-model="showDeleteDialog">
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar icon="delete" color="negative" text-color="white" />
          <span class="q-ml-sm">{{ t('filters.deleteFilterConfirm') }}</span>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="t('common.cancel')" color="primary" v-close-popup />
          <q-btn
            flat
            :label="t('common.delete')"
            color="negative"
            @click="deleteFilter"
            v-close-popup
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import type { SavedFilter } from '../../../stores/filterStore';
import { useI18n } from 'vue-i18n';

// Props
const props = defineProps<{
  filters: SavedFilter[];
  currentFilterId: number | null;
  hasUnsavedChanges: boolean;
  loading?: boolean;
}>();

// Emits
const emit = defineEmits<{
  (e: 'select', filterId: number): void;
  (e: 'save', filterData: { name: string, description: string, makeDefault: boolean }): void;
  (e: 'delete', filterId: number): void;
  (e: 'clear'): void;
}>();

// Initialize i18n
const { t } = useI18n();

// State
const selectedFilter = ref<number | null>(props.currentFilterId);
const showSaveDialog = ref(false);
const showDeleteDialog = ref(false);
const filterToDelete = ref<number | null>(null);
const filterName = ref('');
const filterDescription = ref('');
const makeDefault = ref(false);

// Watch for changes in currentFilterId
watch(() => props.currentFilterId, (newId) => {
  selectedFilter.value = newId;

  // Update filter name and description if a filter is selected
  if (newId) {
    const filter = props.filters.find(f => f.id === newId);
    if (filter) {
      filterName.value = filter.name;
      filterDescription.value = filter.description || '';
      makeDefault.value = filter.is_default;
    }
  }
});

// Computed
const filterOptions = computed(() => {
  return props.filters.map(filter => ({
    label: filter.name,
    value: filter.id,
    description: filter.description,
    isDefault: filter.is_default
  }));
});

// Methods
function handleFilterSelect(filterId: number) {
  if (filterId) {
    emit('select', filterId);
  }
}

function clearFilter() {
  // Reset the selected filter
  selectedFilter.value = null;

  // Emit the clear event to clear all filters and reset the current filter ID
  emit('clear');
}

function confirmDeleteFilter(filterId: number) {
  filterToDelete.value = filterId;
  showDeleteDialog.value = true;
}

function deleteFilter() {
  if (filterToDelete.value) {
    emit('delete', filterToDelete.value);

    // If we deleted the selected filter, clear the selection
    if (selectedFilter.value === filterToDelete.value) {
      selectedFilter.value = null;
    }

    filterToDelete.value = null;
  }
}

function saveFilter() {
  // Emit the save event with the filter details
  emit('save', {
    name: filterName.value,
    description: filterDescription.value,
    makeDefault: makeDefault.value
  });

  // Reset the dialog state
  showSaveDialog.value = false;
}
</script>

<style scoped>
.saved-filter-selector {
  width: 100%;
}

.filter-header {
  padding: 8px 0;
}

.filter-dropdown-container {
  padding: 0 0 0 8px;
}

/* Ensure dropdown has consistent height in all states */
:deep(.q-field) {
  height: 36px;
}

:deep(.q-field__control) {
  height: 36px !important;
  min-height: 36px !important;
  padding: 0 8px;
}

/* Override Quasar's focus styling to maintain consistent height */
:deep(.q-field--focused) {
  .q-field__control {
    height: 36px !important;
    min-height: 36px !important;
  }
}

/* Fix border styling */
:deep(.q-field__control:before) {
  border-width: 1px !important;
}

:deep(.q-field__control:after) {
  border-width: 2px !important; /* Match Quasar's default focus border width */
  transform: scale3d(1, 1, 1) !important; /* Prevent transform animations */
}

/* Ensure text ellipsis for long filter names */
:deep(.q-field__native) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Style for filter actions */
.filter-actions {
  display: flex;
  align-items: center;
  white-space: nowrap;
}

/* Style for action buttons */
.filter-action-btn {
  height: 36px;
  width: 36px;
}

/* Ensure proper icon size */
:deep(.q-icon) {
  font-size: 18px;
}
</style>
