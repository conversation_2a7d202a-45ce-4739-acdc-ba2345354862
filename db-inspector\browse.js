import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import chalk from 'chalk';
import inquirer from 'inquirer';
import Table from 'cli-table3';
import { Parser } from 'json2csv';
import { Command } from 'commander';

// Get the directory name of the current module
const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Load environment variables from the parent directory's .env file
dotenv.config({ path: path.join(__dirname, '..', '.env') });

// Directory to save exports
const exportsDir = path.join(__dirname, 'exports');

// Create Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error(chalk.red('Supabase credentials not found in environment variables'));
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Parse command line arguments
const program = new Command();
program
  .option('-t, --table <name>', 'Specify table to browse')
  .option('-l, --limit <number>', 'Number of rows to fetch', '10')
  .option('-p, --page <number>', 'Page number', '1')
  .parse(process.argv);

const options = program.opts();

/**
 * Formats and displays table data in a table
 * @param {Array} data - The table data
 * @param {string} tableName - The name of the table
 */
function displayTableData(data, tableName) {
  if (!data || data.length === 0) {
    console.log(chalk.yellow(`No data found in table ${tableName}`));
    return;
  }

  // Get column names from the first row
  const columns = Object.keys(data[0]);
  
  // Create a new table
  const table = new Table({
    head: columns.map(col => chalk.cyan(col)),
    wordWrap: true,
    wrapOnWordBoundary: true,
    truncate: '…'
  });
  
  // Add rows to the table
  data.forEach(row => {
    const rowData = columns.map(col => {
      const value = row[col];
      if (value === null) return chalk.gray('NULL');
      if (typeof value === 'object') return JSON.stringify(value);
      return String(value);
    });
    table.push(rowData);
  });
  
  console.log(table.toString());
}

/**
 * Exports table data to a file
 * @param {Array} data - The table data
 * @param {string} tableName - The name of the table
 * @param {string} format - The output format (json or csv)
 */
function exportTableData(data, tableName, format) {
  if (!data || data.length === 0) {
    console.log(chalk.yellow(`No data to export from table ${tableName}`));
    return;
  }
  
  // Create the exports directory if it doesn't exist
  if (!fs.existsSync(exportsDir)) {
    fs.mkdirSync(exportsDir, { recursive: true });
  }
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  let filePath;
  
  if (format === 'csv') {
    // Convert to CSV
    const parser = new Parser();
    const csv = parser.parse(data);
    
    filePath = path.join(exportsDir, `${tableName}_${timestamp}.csv`);
    fs.writeFileSync(filePath, csv);
  } else {
    // Save as JSON
    filePath = path.join(exportsDir, `${tableName}_${timestamp}.json`);
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
  }
  
  console.log(chalk.green(`Data exported to ${filePath}`));
}

/**
 * Fetches data from a table
 * @param {string} tableName - The name of the table
 * @param {number} limit - The maximum number of rows to fetch
 * @param {number} page - The page number
 * @returns {Promise<Array>} The table data
 */
async function fetchTableData(tableName, limit = 10, page = 1) {
  try {
    console.log(chalk.blue(`Fetching data from table ${tableName}...`));
    
    const offset = (page - 1) * limit;
    
    const { data, error, count } = await supabase
      .from(tableName)
      .select('*', { count: 'exact' })
      .range(offset, offset + limit - 1);
    
    if (error) {
      throw new Error(`Error fetching data: ${error.message}`);
    }
    
    console.log(chalk.green(`Fetched ${data.length} rows (page ${page} of ${Math.ceil(count / limit)})`));
    
    return { data, count };
  } catch (error) {
    console.error(chalk.red(`Error: ${error.message}`));
    return { data: [], count: 0 };
  }
}

/**
 * Fetches all tables in the database
 * @returns {Promise<Array>} The list of tables
 */
async function fetchTables() {
  try {
    console.log(chalk.blue('Fetching tables...'));
    
    const { data, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .order('table_name');
    
    if (error) {
      throw new Error(`Error fetching tables: ${error.message}`);
    }
    
    return data.map(table => table.table_name);
  } catch (error) {
    console.error(chalk.red(`Error: ${error.message}`));
    return [];
  }
}

/**
 * Interactive table browser
 */
async function interactiveTableBrowser() {
  // Fetch all tables
  const tables = await fetchTables();
  
  if (tables.length === 0) {
    console.log(chalk.red('No tables found in the database'));
    return;
  }
  
  console.log(chalk.green(`Found ${tables.length} tables`));
  
  // If table is specified in command line arguments, browse that table
  if (options.table) {
    if (!tables.includes(options.table)) {
      console.log(chalk.red(`Table ${options.table} not found`));
      return;
    }
    
    await browseTable(options.table, parseInt(options.limit), parseInt(options.page));
    return;
  }
  
  // Otherwise, show table selection menu
  let browsing = true;
  
  while (browsing) {
    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: 'What would you like to do?',
        choices: [
          { name: 'Browse a table', value: 'browse' },
          { name: 'Export a table', value: 'export' },
          { name: 'Exit', value: 'exit' }
        ]
      }
    ]);
    
    if (action === 'exit') {
      browsing = false;
      continue;
    }
    
    // Select a table
    const { tableName } = await inquirer.prompt([
      {
        type: 'list',
        name: 'tableName',
        message: 'Select a table:',
        choices: tables
      }
    ]);
    
    if (action === 'browse') {
      await browseTable(tableName);
    } else if (action === 'export') {
      await exportTable(tableName);
    }
  }
  
  console.log(chalk.green('Goodbye!'));
}

/**
 * Browse a table interactively
 * @param {string} tableName - The name of the table
 * @param {number} limit - The maximum number of rows to fetch
 * @param {number} page - The page number
 */
async function browseTable(tableName, limit = 10, page = 1) {
  let browsing = true;
  let currentPage = page;
  let pageSize = limit;
  
  while (browsing) {
    // Fetch data for the current page
    const { data, count } = await fetchTableData(tableName, pageSize, currentPage);
    
    // Display the data
    displayTableData(data, tableName);
    
    // Calculate total pages
    const totalPages = Math.ceil(count / pageSize);
    
    // Show pagination info
    console.log(chalk.gray(`Page ${currentPage} of ${totalPages} (${count} total rows)`));
    
    // Ask for next action
    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: 'What would you like to do?',
        choices: [
          { name: 'Next page', value: 'next', disabled: currentPage >= totalPages },
          { name: 'Previous page', value: 'prev', disabled: currentPage <= 1 },
          { name: 'Go to page...', value: 'goto' },
          { name: 'Change page size', value: 'size' },
          { name: 'Export current page', value: 'export' },
          { name: 'Back to table selection', value: 'back' }
        ]
      }
    ]);
    
    if (action === 'next') {
      currentPage++;
    } else if (action === 'prev') {
      currentPage--;
    } else if (action === 'goto') {
      const { page } = await inquirer.prompt([
        {
          type: 'number',
          name: 'page',
          message: `Enter page number (1-${totalPages}):`,
          default: currentPage,
          validate: input => input >= 1 && input <= totalPages
        }
      ]);
      currentPage = page;
    } else if (action === 'size') {
      const { size } = await inquirer.prompt([
        {
          type: 'number',
          name: 'size',
          message: 'Enter page size:',
          default: pageSize,
          validate: input => input >= 1 && input <= 100
        }
      ]);
      pageSize = size;
      currentPage = 1; // Reset to first page when changing page size
    } else if (action === 'export') {
      const { format } = await inquirer.prompt([
        {
          type: 'list',
          name: 'format',
          message: 'Export format:',
          choices: [
            { name: 'JSON', value: 'json' },
            { name: 'CSV', value: 'csv' }
          ]
        }
      ]);
      exportTableData(data, tableName, format);
    } else if (action === 'back') {
      browsing = false;
    }
  }
}

/**
 * Export a table
 * @param {string} tableName - The name of the table
 */
async function exportTable(tableName) {
  // Ask for export options
  const { limit, format } = await inquirer.prompt([
    {
      type: 'number',
      name: 'limit',
      message: 'How many rows to export? (0 for all)',
      default: 100
    },
    {
      type: 'list',
      name: 'format',
      message: 'Export format:',
      choices: [
        { name: 'JSON', value: 'json' },
        { name: 'CSV', value: 'csv' }
      ]
    }
  ]);
  
  try {
    console.log(chalk.blue(`Exporting data from table ${tableName}...`));
    
    let query = supabase.from(tableName).select('*');
    
    if (limit > 0) {
      query = query.limit(limit);
    }
    
    const { data, error } = await query;
    
    if (error) {
      throw new Error(`Error exporting data: ${error.message}`);
    }
    
    exportTableData(data, tableName, format);
  } catch (error) {
    console.error(chalk.red(`Error: ${error.message}`));
  }
}

// Run the interactive table browser
interactiveTableBrowser();
