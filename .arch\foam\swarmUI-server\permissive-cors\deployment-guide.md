# Deploying Edge Functions with Permissive CORS

This guide provides instructions for deploying Edge Functions with permissive CORS headers to resolve CORS issues.

## Overview

The Edge Functions in this directory have been updated with permissive CORS headers to allow requests from any origin, with any method, and with any headers. This is useful for development and testing, but should be restricted in production.

## Deployment Steps

### 1. Deploy swarmui-server

1. Go to your Supabase dashboard: https://app.supabase.com/
2. Select your project (ppzaqhuaqzjofsorlmbn)
3. Navigate to "Edge Functions" in the left sidebar
4. Find the "swarmui-server" function and click on it
5. Click "Edit"
6. Replace the entire code with the content of `swarmui-server.ts` from this directory
7. Click "Deploy"

### 2. Deploy swarmui-generation

1. Find the "swarmui-generation" function and click on it
2. Click "Edit"
3. Replace the entire code with the content of `swarmui-generation.ts` from this directory
4. Click "Deploy"

### 3. Deploy image-management

1. Find the "image-management" function and click on it
2. Click "Edit"
3. Replace the entire code with the content of `image-management.ts` from this directory
4. Click "Deploy"

## Verification

After deploying the updated Edge Functions, verify that they are working correctly:

1. Refresh your application
2. Check the browser console for CORS errors
3. If there are no CORS errors, the deployment was successful

## Troubleshooting

If you're still experiencing CORS issues:

1. Check the browser console for specific error messages
2. Verify that the Edge Functions were deployed successfully
3. Check the Edge Function logs in the Supabase dashboard for any errors
4. Try accessing the Edge Functions directly using a tool like Postman or curl

## Security Considerations

The CORS headers in these Edge Functions are intentionally permissive to resolve development issues. For production, you should restrict the CORS headers to only allow requests from your application's domain.

To do this, replace:

```typescript
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': '*',
  'Access-Control-Allow-Headers': '*',
  'Access-Control-Max-Age': '86400',
};
```

With:

```typescript
const corsHeaders = {
  'Access-Control-Allow-Origin': 'https://your-app-domain.com',
  'Access-Control-Allow-Methods': 'POST, GET, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Authorization, Content-Type, x-client-info',
  'Access-Control-Max-Age': '86400',
};
```

Replace `https://your-app-domain.com` with your application's domain.
