<template>
  <div class="prompt-elements-filter">
    <!-- One dropdown for each element type -->
    <div
      v-for="type in elementTypes"
      :key="type.id"
      class="q-mb-md"
    >
      <q-select
        v-model="selectedElementsByType[type.id]"
        :options="getElementsForType(type.id)"
        :label="getTypeLabel(type)"
        outlined
        dense
        multiple
        use-chips
        use-input
        hide-selected
        fill-input
        input-debounce="300"
        option-label="label"
        option-value="value"
        emit-value
        map-options
        clearable
        @filter="(val, update) => filterOptions(val, update, type.id)"
        @update:model-value="updateElementsForType(type.id, $event)"
      >
        <template v-slot:no-option>
          <q-item>
            <q-item-section class="text-grey">
              No elements found
            </q-item-section>
          </q-item>
        </template>
      </q-select>
    </div>

    <!-- Match type selector -->
    <div class="row q-col-gutter-md q-mt-md">
      <div class="col-12">
        <q-option-group
          v-model="matchType"
          :options="matchOptions"
          inline
          @update:model-value="updateFilter"
        />
      </div>
    </div>

    <!-- Selected elements summary (optional) -->
    <div v-if="allSelectedElements.length > 0" class="row q-col-gutter-md q-mt-md">
      <div class="col-12">
        <q-list bordered separator>
          <q-item-label header>Selected Elements</q-item-label>
          <q-item v-for="element in selectedElementsDetails" :key="element.id">
            <q-item-section>
              <q-item-label>{{ element.name }}</q-item-label>
              <q-item-label caption>{{ formatTypeName(element.typeName) }}</q-item-label>
            </q-item-section>

            <q-item-section side>
              <q-btn
                flat
                round
                dense
                icon="close"
                @click="removeElement(element.id)"
              />
            </q-item-section>
          </q-item>
        </q-list>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { supabase } from '../../../boot/supabase';
import { useI18n } from 'vue-i18n';

// Props
const props = defineProps<{
  modelValue?: { elements?: number[], matchType?: 'all' | 'any' } | null;
}>();

// Emits
const emit = defineEmits<{
  (e: 'update:modelValue', value: { elements: number[], matchType: string } | null): void;
}>();

// Store - removed unused import
// const promptElementsStore = usePromptElementsStore();

// i18n
const { t } = useI18n();

// Interfaces
interface ElementType {
  id: number;
  name: string;
}

interface ElementOption {
  label: string;
  value: number;
  typeId: number;
}

// State
const elementTypes = ref<ElementType[]>([]);
const elementsByType = ref<Record<number, ElementOption[]>>({});
const filteredElementsByType = ref<Record<number, ElementOption[]>>({});
const selectedElementsByType = ref<Record<number, number[]>>({});
const matchType = ref('any');
const loading = ref(false);

// Match options
const matchOptions = [
  { label: t('common.matchAny'), value: 'any' },
  { label: t('common.matchAll'), value: 'all' }
];

// Computed
const allSelectedElements = computed(() => {
  const allElements: number[] = [];

  // Combine all selected elements from all types
  for (const typeId in selectedElementsByType.value) {
    const elements = selectedElementsByType.value[typeId] || [];
    allElements.push(...elements);
  }

  return allElements;
});

const selectedElementsDetails = computed(() => {
  const details: { id: number, name: string, typeName: string }[] = [];

  // For each element type
  for (const type of elementTypes.value) {
    // Get selected elements for this type
    const selectedIds = selectedElementsByType.value[type.id] || [];

    // Find the element details
    const typeElements = elementsByType.value[type.id] || [];

    // Add details for each selected element
    for (const id of selectedIds) {
      const element = typeElements.find(el => el.value === id);
      if (element) {
        details.push({
          id: element.value,
          name: element.label,
          typeName: type.name
        });
      }
    }
  }

  return details;
});

// Methods
function getElementsForType(typeId: number) {
  return filteredElementsByType.value[typeId] || [];
}

function updateElementsForType(typeId: number, elements: number[]) {
  selectedElementsByType.value[typeId] = elements;
  updateFilter();
}

function updateFilter() {
  const elements = allSelectedElements.value;

  console.log('Updating prompt elements filter with elements:', elements);

  if (elements.length === 0) {
    // If we had a previous value with elements, emit null to clear the filter
    if (props.modelValue && props.modelValue.elements && props.modelValue.elements.length > 0) {
      console.log('Clearing prompt elements filter');
      emit('update:modelValue', null);
    } else {
      console.log('No elements selected and no previous filter, not emitting');
    }
    return;
  }

  // Make sure elements is an array of numbers
  const elementIds = elements.map(el => {
    if (typeof el === 'number') {
      return el;
    } else if (typeof el === 'object' && el !== null && 'id' in el) {
      return (el as { id: number }).id;
    } else {
      return Number(el);
    }
  });

  const filterValue = {
    elements: elementIds,
    matchType: matchType.value
  };

  console.log('Emitting prompt elements filter value:', filterValue);
  emit('update:modelValue', filterValue);
}

function removeElement(elementId: number) {
  // Find which type this element belongs to
  for (const typeId in selectedElementsByType.value) {
    const elements = selectedElementsByType.value[typeId] || [];
    const index = elements.indexOf(elementId);
    if (index !== -1) {
      // Remove the element from this type
      const newElements = [...elements];
      newElements.splice(index, 1);
      selectedElementsByType.value[typeId] = newElements;
      break;
    }
  }

  updateFilter();
}

function filterOptions(val: string, update: (callback: () => void) => void, typeId: number) {
  update(() => {
    // If search is empty, show all options for this type
    if (val === '') {
      filteredElementsByType.value[typeId] = [...elementsByType.value[typeId] || []];
      return;
    }

    const needle = val.toLowerCase();
    // Filter the options for this type based on the input value
    const filteredOptions = elementsByType.value[typeId]?.filter(
      (element) => element.label.toLowerCase().indexOf(needle) > -1
    ) || [];

    // Update the filtered options for this type
    filteredElementsByType.value[typeId] = filteredOptions;
  });
}

/**
 * Format a type name from snake_case to Title Case with spaces
 * e.g., "artistic_reference" -> "Artistic Reference"
 */
function formatTypeName(name: string): string {
  if (!name) return '';

  // Split by underscore and capitalize each word
  return name
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

/**
 * Get the label for a type, including the count of selected elements
 * e.g., "Artistic Reference (3)"
 */
function getTypeLabel(type: ElementType): string {
  const formattedName = formatTypeName(type.name);
  const selectedCount = selectedElementsByType.value[type.id]?.length || 0;

  return selectedCount > 0
    ? `${formattedName} (${selectedCount})`
    : formattedName;
}

// Initialize from model value
function initializeFromModelValue() {
  console.log('Initializing from model value:', props.modelValue);

  if (props.modelValue) {
    // Set match type
    matchType.value = props.modelValue.matchType || 'any';

    // Set selected elements
    if (props.modelValue.elements && props.modelValue.elements.length > 0) {
      const elementIds = props.modelValue.elements;
      console.log('Loading element IDs from filter:', elementIds);

      // Reset all selections
      for (const typeId in selectedElementsByType.value) {
        selectedElementsByType.value[typeId] = [];
      }

      // For each element type
      for (const type of elementTypes.value) {
        const typeElements = elementsByType.value[type.id] || [];

        // Find elements of this type that are in the selected elements
        const selectedForType = typeElements
          .filter(el => elementIds.includes(el.value))
          .map(el => el.value);

        // Set the selected elements for this type
        if (selectedForType.length > 0) {
          console.log(`Setting ${selectedForType.length} selected elements for type ${type.id}`);
          selectedElementsByType.value[type.id] = selectedForType;
        }
      }
    } else {
      // Clear all selections if no elements are provided
      console.log('No elements in filter, clearing selections');
      for (const typeId in selectedElementsByType.value) {
        selectedElementsByType.value[typeId] = [];
      }
    }
  } else {
    // Clear all selections if no model value is provided
    console.log('No model value, clearing all selections');
    for (const typeId in selectedElementsByType.value) {
      selectedElementsByType.value[typeId] = [];
    }
    matchType.value = 'any'; // Reset to default
  }
}

// Load element types and elements
async function loadElementTypesAndElements() {
  loading.value = true;

  try {
    // Load element types
    const { data: typesData, error: typesError } = await supabase
      .from('prompt_element_types')
      .select('id, name')
      .order('name');

    if (typesError) {
      console.error('Error loading element types');
      return;
    }

    elementTypes.value = typesData || [];

    // Initialize selectedElementsByType with empty arrays for each type
    elementTypes.value.forEach(type => {
      selectedElementsByType.value[type.id] = [];
      elementsByType.value[type.id] = [];
      filteredElementsByType.value[type.id] = [];
    });

    // Load elements for each type
    for (const type of elementTypes.value) {
      const { data: elementsData, error: elementsError } = await supabase
        .from('prompt_elements')
        .select('id, value')
        .eq('type_id', type.id)
        .order('value');

      if (elementsError) {
        console.error(`Error loading elements for type ${type.id}`);
        continue;
      }

      // Transform the data into the format needed for the dropdown
      const options = (elementsData || []).map(el => ({
        label: el.value,
        value: el.id,
        typeId: type.id
      }));

      elementsByType.value[type.id] = options;
      filteredElementsByType.value[type.id] = [...options];
    }

    // Initialize from model value after data is loaded
    initializeFromModelValue();
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error) {
    console.error('Error in loadElementTypesAndElements');
  } finally {
    loading.value = false;
  }
}

// Watch for changes in model value
watch(() => props.modelValue, () => {
  initializeFromModelValue();
}, { deep: true });

// Initialize
onMounted(async () => {
  await loadElementTypesAndElements();
});
</script>

<style scoped>
.prompt-elements-filter {
  width: 100%;
}
</style>
