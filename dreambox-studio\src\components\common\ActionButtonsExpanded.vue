<template>
  <div class="action-buttons-expanded q-px-md">
    <div class="text-subtitle1 q-mb-sm">Actions</div>
    <div class="row q-col-gutter-md">
      <!-- Bottom bar actions removed -->
      <div
        v-for="action in [] as Array<{id?: string; icon?: string; label?: string}>"
        :key="String(action.id || 'action')"
        class="col-6 col-sm-4 col-md-3"
      >
        <q-btn
          :color="$q.dark.isActive ? 'dark' : 'white'"
          :text-color="$q.dark.isActive ? 'white' : 'grey-8'"
          flat
          :icon="String(action.icon || '')"
          :label="String(action.label || '')"
          no-caps
          class="full-width"
          :disable="false"
          @click="() => console.log('Action clicked:', action)"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';
// import { useContextStore } from 'src/stores/contextStore';

const $q = useQuasar();
// Bottom bar removed, so we don't need the context store
// const contextStore = useContextStore();
</script>

<style scoped>
.action-buttons-expanded {
  width: 100%;
}
</style>
