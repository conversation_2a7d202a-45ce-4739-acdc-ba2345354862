
# Context Manager
## Context Mapping

| Component         | Area         | Context                               | Role            | Device  | Properties                                        | Default Active |
| :---------------- | :----------- | :------------------------------------ | :-------------- | :------ | :------------------------------------------------ | :------------- |
| Navigation        | Left Drawer  | ALL                                   | ALL             | ALL     | { items:{/*admin, designer,...*/}, expanded:true} | Yes            |
| Chat              | Right Drawer | ALL                                   | ALL             | ALL     | { assistant (/*templates, products,... */) }      | Yes            |
| Search            | Top Bar      | *.table_view, *.grid_view             | ALL             | ALL     | { placeholder: "Search..." }                      | N/A            |
| Actions           | Bottom Bar   | *.table_view, *.grid_view             | ALL             | Mobile  | { }                                               | N/A            |
| Filter            | Left Drawer  | *.table_view, *.grid_view             | ALL             | ALL     | { targetCollection: [context] }                   | No             |
| Templates Filter  | Left Drawer  | templates_table.*                     | ALL             | ALL     | { targetCollection: templates }                   | No             |
| Element Values    | Left Drawer  | templates.*.expanded.elements.element | Designer, Admin | ALL     | { mode: "edit", elementId: [elementId] }          | No             |
| Preview           | Right Drawer | ALL                                   | ALL             | ALL     | { templateId: [templateId] }                      | No             |
| Table/Grid Toggle | Top Bar      | *.table_view, *.grid_view             | ALL             | Desktop | { currentView: [view] }                           | N/A            |
| New Button        | Bottom Bar   | *.table_view, *.grid_view             | Admin           | Mobile  | { action: "new", label: "New", icon: "add" }      | N/A            |
| Edit Button       | Bottom Bar   | *.table.selected                      | Admin           | Mobile  | { action: "edit", label: "Edit" }                 | N/A            |
| Delete Button     | Bottom Bar   | *.table.selected                      | Admin           | Mobile  | { action: "delete", label: "Delete" }             | N/A            |


| Component       | Area         | Context                                    | Role            | Device  | Properties                                                                     | Default Active |
| :-------------- | :----------- | :----------------------------------------- | :-------------- | :------ | :----------------------------------------------------------------------------- | :------------- |
| Navigation      | Left Drawer  | ALL                                        | [role]          | ALL     | { items: "[role]_navigation_items", expanded: true, initiallySelected: true }  |                |
| Chat            | Right Drawer | ALL                                        | ALL             | ALL     | { initiallySelected: true, placeholder: "Type a message..." }                  |                |
| Search          | Top Bar      | [table_type].*_view                        | ALL             | ALL     | { placeholder: "Search [table_type]...", targetCollection: "[table_type]" }    |                |
| Filter          | Left Drawer  | [table_type].*_view                        | ALL             | ALL     | { targetCollection: "[table_type]", initiallySelected: false }                 |                |
| View Toggle     | Top Bar      | [table_type].*_view                        | ALL             | Desktop | { currentView: "[wildcard0]", views: ["table_view", "grid_view"] }             |                |
| Prompt Elements | Left Drawer  | templates.*_view.expanded.elements         | Designer, Admin | ALL     | { mode: "selection", targetTemplate: "[templateId]", initiallySelected: true } |                |
| Element Values  | Left Drawer  | templates.*_view.expanded.elements.element | Designer, Admin | ALL     | { mode: "edit", elementId: "[elementId]", templateId: "[templateId]" }         |                |


