<template>
  <div class="action-buttons-container row q-col-gutter-xs">
    <div
      v-for="action in visibleActions"
      :key="String(action.id || 'action')"
      class="col-auto"
    >
      <q-btn
        :color="$q.dark.isActive ? 'dark' : 'white'"
        :text-color="$q.dark.isActive ? 'white' : 'grey-8'"
        flat
        :icon="String(action.icon || '')"
        :label="showLabels ? String(action.label || '') : undefined"
        no-caps
        dense
        class="full-width"
        :disable="false"
        @click="() => console.log('Action clicked:', action)"
      >
        <q-tooltip>{{ String(action.label || '') }}</q-tooltip>
      </q-btn>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';
// import { useContextStore } from 'src/stores/contextStore';
import { computed, ref, onMounted, onUnmounted } from 'vue';

const props = defineProps<{
  expanded?: boolean;
  maxButtons?: number;
  showLabels?: boolean;
}>();

const $q = useQuasar();
// Bottom bar removed, so we don't need the context store
// const contextStore = useContextStore();

// Determine how many buttons can fit in the container
const containerWidth = ref(0);
const buttonWidth = ref(56); // Estimated width of a button in pixels

// Calculate how many buttons can fit
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const maxVisibleButtons = computed(() => {
  if (props.expanded) {
    // In expanded mode, show all buttons
    return 0; // Bottom bar actions removed
  }

  // If maxButtons prop is provided, use that
  if (props.maxButtons) {
    return props.maxButtons;
  }

  // Otherwise calculate based on container width
  if (containerWidth.value > 0) {
    return Math.floor(containerWidth.value / buttonWidth.value);
  }

  // Default to 4 buttons if we can't calculate
  return 4;
});

// Filter actions to only show the ones that fit
const visibleActions = computed(() => {
  // Bottom bar actions removed
  return [] as Array<{id?: string; icon?: string; label?: string}>;
});

// Determine if labels should be shown
const showLabels = computed(() => {
  if (props.showLabels !== undefined) {
    return props.showLabels;
  }
  return $q.screen.gt.xs;
});

// Measure container on mount and window resize
function updateContainerWidth() {
  const container = document.querySelector('.action-buttons-container');
  if (container) {
    containerWidth.value = container.clientWidth;
  }
}

onMounted(() => {
  updateContainerWidth();
  window.addEventListener('resize', updateContainerWidth);
});

onUnmounted(() => {
  window.removeEventListener('resize', updateContainerWidth);
});
</script>

<style scoped>
.action-buttons-container {
  width: 100%;
}
</style>
