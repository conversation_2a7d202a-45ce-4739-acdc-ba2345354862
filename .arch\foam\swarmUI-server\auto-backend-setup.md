# Automatic Backend Setup for SwarmUI

This document explains how to automatically configure SwarmUI with ComfyUI as a backend without needing to use the web interface. This is useful for headless setups or when you want to use SwarmUI only via its API.

## Overview

SwarmUI supports multiple backend types, including ComfyUI. When using the web interface, you would typically:

1. Start SwarmUI
2. Navigate to the web interface
3. Go to Settings > Backend
4. Select "ComfyUI Self-Starting" as the backend type
5. Configure the ComfyUI path and other settings
6. Save the configuration

This manual process is not ideal for automated deployments or when you want to use SwarmUI only via its API. The solution provided here automates this process by:

1. Pre-configuring SwarmUI with ComfyUI as a backend in the config.json file
2. Starting ComfyUI before launching SwarmUI
3. Using the `--comfy_path` parameter when launching SwarmUI to ensure proper integration

## Files

1. `auto-backend-config.json`: A template for the SwarmUI configuration file with ComfyUI backend pre-configured
2. `auto-backend-startup.sh`: A script that automatically configures and starts SwarmUI with Comfy<PERSON> as a backend

## How It Works

The key to this solution is the `Backend` section in the config.json file:

```json
"Backend": {
  "Type": "ComfyUI-SelfStarting",
  "ComfyUIPath": "/workspace/ComfyUI",
  "ComfyUIPort": 7860,
  "EnableWorkflows": true
}
```

This configuration tells SwarmUI to use ComfyUI as a backend without requiring manual setup through the web interface. The `ComfyUI-SelfStarting` backend type means that SwarmUI will manage the ComfyUI process itself.

Additionally, the startup script:

1. Generates a secure API key for SwarmUI
2. Creates the config.json file with ComfyUI backend pre-configured
3. Starts ComfyUI in the background
4. Launches SwarmUI with the `--comfy_path` parameter pointing to the ComfyUI installation

## Usage

1. Copy the `auto-backend-startup.sh` script to your server
2. Make it executable: `chmod +x auto-backend-startup.sh`
3. Run the script: `./auto-backend-startup.sh`

The script will:
- Generate a new API key and display it (save this key for API access)
- Configure SwarmUI with ComfyUI as a backend
- Start ComfyUI and SwarmUI

## API Access

Once SwarmUI is running with ComfyUI as a backend, you can access its API using the generated API key:

```bash
curl -X GET "http://localhost:7801/api/models" \
  -H "Authorization: Bearer YOUR_API_KEY"
```

Replace `YOUR_API_KEY` with the API key displayed when running the startup script.

## Integration with RunPod

For RunPod deployments, the script automatically detects the `RUNPOD_HTTP_HOST` environment variable and updates the SwarmUI URLs accordingly. This ensures that SwarmUI is accessible from outside the pod.

## Troubleshooting

If you encounter issues:

1. Check the ComfyUI log: `cat /workspace/comfyui.log`
2. Check the SwarmUI log: `cat /workspace/SwarmUI/logs/swarm.log`
3. Verify that ComfyUI is running: `ps aux | grep ComfyUI`
4. Verify that SwarmUI is running: `ps aux | grep SwarmUI`

## Conclusion

This solution allows you to use SwarmUI with ComfyUI as a backend without needing to use the web interface. This is particularly useful for headless setups or when you want to use SwarmUI only via its API, as in your application's case.
