<template>
  <div class="grid-skeleton-loader" :class="{ 'dark-mode': $q.dark.isActive }">
    <div class="row q-col-gutter-md">
      <div
        v-for="n in cardCount"
        :key="n"
        class="col-12 col-sm-6 col-md-4 col-lg-3"
      >
        <q-card class="skeleton-card">
          <!-- Card header -->
          <q-card-section class="skeleton-header">
            <div class="row items-center no-wrap">
              <div class="col">
                <q-skeleton type="text" class="skeleton-title" />
                <q-skeleton type="text" width="60%" class="q-mt-xs" />
              </div>
              <div class="col-auto">
                <q-skeleton type="QBtn" size="sm" />
              </div>
            </div>
          </q-card-section>

          <!-- Card content -->
          <q-card-section>
            <q-skeleton type="text" class="q-mb-md" />
            <q-skeleton type="text" width="80%" class="q-mb-md" />
            <q-skeleton type="text" width="70%" class="q-mb-md" />
          </q-card-section>

          <!-- Card stats -->
          <q-card-section>
            <div class="row q-col-gutter-sm">
              <div v-for="i in 4" :key="i" class="col-3 text-center">
                <q-skeleton type="QBtn" size="xs" class="q-mb-xs" />
                <q-skeleton type="text" width="80%" class="q-mb-xs" />
                <q-skeleton type="text" width="40%" class="q-mx-auto" />
              </div>
            </div>
          </q-card-section>

          <!-- Card footer -->
          <q-card-section class="q-pt-none">
            <div class="row items-center justify-between">
              <div>
                <q-skeleton type="text" width="40%" />
              </div>
              <div>
                <q-skeleton type="QBtn" size="sm" class="q-mr-sm" />
                <q-skeleton type="QBtn" size="sm" />
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useQuasar } from 'quasar';

// Get access to Quasar's dark mode
const $q = useQuasar();

// Props definition
defineProps({
  cardCount: {
    type: Number,
    default: 12
  }
});
</script>

<style scoped>
.grid-skeleton-loader {
  width: 100%;
}

.skeleton-card {
  height: 100%;
  min-height: 450px;
  transition: transform 0.2s, box-shadow 0.2s;
  display: flex;
  flex-direction: column;
}

.skeleton-header {
  background-color: var(--q-primary, #1976d2);
  opacity: 0.7;
}

.skeleton-title {
  height: 24px;
}

:deep(.q-skeleton) {
  --q-skeleton-dark-color: var(--q-dark-page);
  --q-skeleton-light-color: var(--q-light-page);
  animation: pulse 1.5s ease-in-out infinite;
}

:deep(.skeleton-header .q-skeleton) {
  --q-skeleton-dark-color: rgba(255, 255, 255, 0.2);
  --q-skeleton-light-color: rgba(255, 255, 255, 0.3);
}

/* Dark mode specific styles */
.dark-mode :deep(.q-skeleton) {
  --q-skeleton-dark-color: rgba(255, 255, 255, 0.05) !important;
  --q-skeleton-light-color: rgba(255, 255, 255, 0.1) !important;
  opacity: 0.8;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}
</style>
