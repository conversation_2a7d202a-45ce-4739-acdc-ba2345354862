<template>
  <div class="inline-edit-container">
    <!-- Display mode -->
    <div
      v-if="!isEditing"
      class="inline-display q-pa-xs"
      :class="{ 'cursor-pointer': !disabled, 'inline-display-disabled': disabled }"
      @click="startEditing"
    >
      <slot name="display" :value="modelValue">
        <div class="inline-display-content">
          <span v-if="modelValue">{{ modelValue }}</span>
          <span v-else class="text-grey">{{ placeholder }}</span>
          <q-icon v-if="!disabled" name="edit" size="xs" class="edit-icon q-ml-xs" />
        </div>
      </slot>
    </div>

    <!-- Edit mode -->
    <div v-else class="inline-edit">
      <slot name="edit" :value="editValue" :update="updateEditValue" :save="save" :cancel="cancel">
        <q-input
          v-if="type === 'text'"
          ref="inputRef"
          v-model="editValue"
          :type="type"
          :placeholder="placeholder"
          :rules="rules"
          :autogrow="autogrow"
          dense
          outlined
          @keyup.enter="save"
          @keyup.esc="cancel"
          @blur="onBlur"
        />
        <q-input
          v-else-if="type === 'textarea'"
          ref="inputRef"
          v-model="editValue"
          :placeholder="placeholder"
          :rules="rules"
          type="textarea"
          autogrow
          dense
          outlined
          @keyup.esc="cancel"
          @blur="onBlur"
        />
      </slot>

      <!-- Action buttons -->
      <div class="inline-edit-actions q-mt-xs" v-if="showButtons">
        <q-btn
          flat
          dense
          color="primary"
          icon="check"
          size="sm"
          @click="save"
          :disable="!isValid"
        />
        <q-btn
          flat
          dense
          color="negative"
          icon="close"
          size="sm"
          @click="cancel"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue';
import type { PropType } from 'vue';

const props = defineProps({
  modelValue: {
    type: [String, Number],
    required: true
  },
  type: {
    type: String,
    default: 'text',
    validator: (value: string) => ['text', 'textarea', 'number'].includes(value)
  },
  placeholder: {
    type: String,
    default: 'Click to edit'
  },
  rules: {
    type: Array as PropType<Array<(val: unknown) => boolean | string>>,
    default: () => []
  },
  autogrow: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  showButtons: {
    type: Boolean,
    default: true
  },
  blurOnSave: {
    type: Boolean,
    default: true
  },
  saveOnBlur: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits<{
  (e: 'update:modelValue', value: string | number): void;
  (e: 'save', value: string | number): void;
  (e: 'cancel'): void;
  (e: 'edit-start'): void;
}>();

const isEditing = ref(false);
const editValue = ref<string | number>('');
const inputRef = ref<HTMLElement | null>(null);
const isValid = ref(true);

// Start editing mode
function startEditing() {
  if (props.disabled) return;

  isEditing.value = true;
  editValue.value = props.modelValue;
  emit('edit-start');

  // Focus the input after the DOM updates
  void nextTick(() => {
    if (inputRef.value) {
      inputRef.value.focus();
    }
  });
}

// Update the edit value (used in slot)
function updateEditValue(value: string | number) {
  editValue.value = value;
}

// Save the changes
function save() {
  if (!isValid.value) return;

  emit('update:modelValue', editValue.value);
  emit('save', editValue.value);
  isEditing.value = false;

  if (props.blurOnSave && inputRef.value) {
    inputRef.value.blur();
  }
}

// Cancel editing
function cancel() {
  isEditing.value = false;
  emit('cancel');
}

// Handle blur event
function onBlur() {
  if (props.saveOnBlur) {
    save();
  }
}

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  if (!isEditing.value) {
    editValue.value = newValue;
  }
});
</script>

<style lang="scss" scoped>
.inline-edit-container {
  position: relative;
}

.inline-display {
  min-height: 24px;
  border-radius: 4px;
  transition: background-color 0.3s;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);

    .edit-icon {
      opacity: 1;
    }
  }
}

.inline-display-content {
  display: flex;
  align-items: center;
}

.inline-display-disabled {
  cursor: default !important;

  &:hover {
    background-color: transparent;
  }
}

.edit-icon {
  opacity: 0;
  transition: opacity 0.3s;
}

.inline-edit {
  position: relative;
}

.inline-edit-actions {
  display: flex;
  justify-content: flex-end;
  gap: 4px;
}
</style>
