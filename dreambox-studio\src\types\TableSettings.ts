/**
 * Enum for table text wrapping modes
 */
export enum TableTextWrapMode {
  WRAP = 'wrap',
  ELLIPSIS = 'ellipsis'
}

/**
 * Interface for table settings
 */
export interface TableSettings {
  textWrapMode: TableTextWrapMode;
}

/**
 * Default table settings
 */
export const DEFAULT_TABLE_SETTINGS: TableSettings = {
  textWrapMode: TableTextWrapMode.WRAP // Default to wrapping text
};

/**
 * User setting keys
 */
export const TABLE_SETTINGS_KEY = 'table_settings';
