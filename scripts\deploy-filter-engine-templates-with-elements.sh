#!/bin/bash

# Deploy the filter_engine_templates_with_elements function to the database
# This script should be run from the root directory of the project

# Get the database connection string from the environment
DB_CONNECTION_STRING=$(grep -o 'SUPABASE_DB_CONNECTION_STRING=.*' .env | cut -d '=' -f2)

if [ -z "$DB_CONNECTION_STRING" ]; then
  echo "Error: SUPABASE_DB_CONNECTION_STRING not found in .env file"
  exit 1
fi

echo "Deploying filter_engine_templates_with_elements function to the database..."

# Run the SQL script
psql "$DB_CONNECTION_STRING" -f scripts/db-updates/filter-engine-templates-with-elements.sql

if [ $? -eq 0 ]; then
  echo "Deployment successful!"
else
  echo "Error: Deployment failed"
  exit 1
fi

echo "Done!"
