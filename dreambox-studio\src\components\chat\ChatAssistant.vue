<template>
  <!-- Unified Chat & Voice Commands Section -->
  <div class="chat-container">
    <!-- Messages area with scroll -->
    <div class="chat-messages-scroll">
      <div class="chat-messages" ref="chatMessagesContainer">
        <q-chat-message
          v-for="(message, index) in chatMessages"
          :key="index"
          :name="message.name"
          :avatar="message.avatar"
          :text="message.text"
          :sent="message.sent"
        />
      </div>
    </div>

    <!-- Fixed input at the bottom -->
    <div class="chat-input">
      <q-select
        ref="chatInput"
        v-model="commandText"
        :options="filteredCommands"
        :placeholder="$t('voiceCommands.enterCommand')"
        outlined
        dense
        use-input
        hide-selected
        fill-input
        input-debounce="0"
        @filter="filterCommands"
        @keyup.enter="executeCommand"
        class="command-select"
        popup-content-class="command-dropdown custom-scrollbar"
        :bg-color="$q.dark.isActive ? 'grey-9' : 'white'"
        :color="$q.dark.isActive ? 'white' : 'primary'"
        :input-style="{ color: $q.dark.isActive ? 'white' : 'black' }"
      >
        <template v-slot:no-option>
          <q-item>
            <q-item-section class="text-grey">
              {{ $t('voiceCommands.noCommandDetected') }}
            </q-item-section>
          </q-item>
        </template>

        <template v-slot:option="scope">
          <q-item v-bind="scope.itemProps">
            <q-item-section>
              <q-item-label>{{ scope.opt }}</q-item-label>
              <q-item-label caption v-if="getCommandDescription(scope.opt)">
                {{ getCommandDescription(scope.opt) }}
              </q-item-label>
              <q-item-label caption v-if="getCommandExample(scope.opt)">
                {{ $t('voiceCommands.examples') }}: "{{ getCommandExample(scope.opt) }}"
              </q-item-label>
            </q-item-section>
          </q-item>
        </template>

        <template v-slot:after>
          <q-btn
            round
            dense
            flat
            :icon="isListening ? 'mic_off' : 'mic'"
            :color="isListening ? 'negative' : ($q.dark.isActive ? 'white' : 'primary')"
            @click="simulateVoiceInput"
          />
          <q-btn
            round
            dense
            flat
            :color="$q.dark.isActive ? 'white' : 'primary'"
            icon="send"
            @click="executeCommand"
          />
        </template>
      </q-select>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, onMounted, onUnmounted } from 'vue';
import { useQuasar, QSelect } from 'quasar';
import { useI18n } from 'vue-i18n';
import { notify } from 'src/boot/notifications';
import { commandRegistry } from 'src/services/commands/registry';
import { mockWebSocket } from 'src/services/commands/mock-websocket';
import type { CommandResult } from 'src/services/commands/types';

// Import images for avatars
import milanAvatar from '../../assets/milan.jpeg';
import robotAvatar from '../../assets/robot-thumbnail.png';

// Define props for future context-aware functionality
defineProps<{
  contextAware?: boolean;
}>();

const $q = useQuasar();
const { t } = useI18n();

// Chat messages
const chatMessages = ref([
  {
    name: 'Assistant',
    text: ['How can I help you?'],
    sent: true,
    avatar: robotAvatar // Robot avatar
  }
]);

// Reference to chat messages container for scrolling
const chatMessagesContainer = ref<HTMLElement | null>(null);

// Reference to chat input for focusing
const chatInput = ref<QSelect | null>(null);

// Command text and history
const commandText = ref<string>('');
const commandHistory = ref<string[]>([]);
const historyIndex = ref(-1);

// Voice command state
const isListening = ref(false);
const availableCommands = ref<{ name: string; description?: string | undefined; examples?: string[] | undefined }[]>([]);
const filteredCommands = ref<string[]>([]);
const lastResult = ref<Record<string, unknown>>({});

// Function to scroll chat to bottom
function scrollToBottom() {
  void nextTick(() => {
    const scrollArea = document.querySelector('.chat-messages-scroll');
    if (scrollArea) {
      scrollArea.scrollTop = scrollArea.scrollHeight;
    }
  });
}

// Watch for changes to chat messages and scroll to bottom
watch(chatMessages, () => {
  scrollToBottom();
}, { deep: true });

// Refresh available commands
function refreshAvailableCommands() {
  const commands = commandRegistry.getAvailableCommands();
  availableCommands.value = commands.map((cmd) => ({
    name: cmd.name,
    description: cmd.description,
    examples: cmd.examples
  }));
}

// Get command description
function getCommandDescription(commandName: string) {
  const command = availableCommands.value.find(cmd => cmd.name === commandName);
  return command?.description || '';
}

// Get command example
function getCommandExample(commandName: string) {
  const command = availableCommands.value.find(cmd => cmd.name === commandName);
  return command?.examples?.[0] || '';
}

// Filter commands based on input
function filterCommands(val: string, update: (callback: () => void) => void) {
  if (val === '') {
    update(() => {
      filteredCommands.value = availableCommands.value.map(cmd => cmd.name);
    });
    return;
  }

  update(() => {
    const needle = val.toLowerCase();
    filteredCommands.value = availableCommands.value
      .filter(cmd => {
        // Check command name
        if (cmd.name.toLowerCase().includes(needle)) return true;

        // Check examples
        if (cmd.examples?.some(ex => ex.toLowerCase().includes(needle))) return true;

        // Check description
        return cmd.description?.toLowerCase().includes(needle) || false;
      })
      .map(cmd => cmd.name);
  });
}

// Normalize command name
function normalizeCommandName(command: string) {
  return command.toLowerCase().replace(/\s+/g, '');
}

// Add command to history
function addToHistory(command: string) {
  // Don't add empty commands or duplicates at the end
  if (!command || (commandHistory.value.length > 0 && commandHistory.value[0] === command)) {
    return;
  }

  // Add to beginning of history
  commandHistory.value.unshift(command);

  // Limit history size
  if (commandHistory.value.length > 20) {
    commandHistory.value.pop();
  }

  // Reset history index
  historyIndex.value = -1;
}

// Navigate command history
function navigateHistory(direction: 'up' | 'down') {
  if (commandHistory.value.length === 0) return;

  if (direction === 'up') {
    // Move up in history (older commands)
    if (historyIndex.value < commandHistory.value.length - 1) {
      historyIndex.value++;
    }
  } else {
    // Move down in history (newer commands)
    if (historyIndex.value > -1) {
      historyIndex.value--;
    }
  }

  // Set command text from history or clear if at beginning
  if (historyIndex.value === -1) {
    commandText.value = '';
  } else {
    const historyCommand = commandHistory.value[historyIndex.value];
    if (historyCommand) {
      commandText.value = historyCommand;
    }
  }
}

// Simulate voice input
function simulateVoiceInput() {
  isListening.value = !isListening.value;

  if (isListening.value) {
    // Show notification
    void notify({
      message: t('voiceCommands.listening'),
      color: 'info',
      icon: 'mic',
      position: 'top'
    });

    // Simulate voice recognition after a delay
    setTimeout(() => {
      isListening.value = false;

      // Generate a random command for demonstration
      const demoCommands = [
        'open left drawer',
        'close right drawer',
        'go to dashboard',
        'show templates'
      ];

      const randomIndex = Math.floor(Math.random() * demoCommands.length);
      const randomCommand = demoCommands[randomIndex] || 'open left drawer';
      commandText.value = randomCommand;

      // Execute the command
      executeCommand();
    }, 2000);
  } else {
    // Show notification
    void notify({
      message: t('voiceCommands.stoppedListening'),
      color: 'info',
      icon: 'mic_off',
      position: 'top'
    });
  }
}

// Execute command
function executeCommand() {
  const trimmedCommand = commandText.value.trim();
  if (!trimmedCommand) return;

  try {
    // Log the command being executed
    console.log('Executing command:', trimmedCommand);
    console.log('Available commands:', availableCommands.value);

    // Add user's command to chat messages
    chatMessages.value.push({
      name: 'You',
      text: [trimmedCommand],
      sent: false,
      avatar: milanAvatar // User avatar
    });

    // Special case for toggleDarkMode
    if (trimmedCommand.toLowerCase() === 'toggledarkmode' ||
        trimmedCommand.toLowerCase() === 'toggle dark mode') {
      console.log('Executing toggleDarkMode command');
      mockWebSocket.simulateCommand('toggleDarkMode');
      addToHistory('toggleDarkMode');
      commandText.value = '';
      return;
    }

    // Check if the command is in the available commands list
    const isExactCommand = availableCommands.value.some(cmd => cmd.name === trimmedCommand);

    if (isExactCommand) {
      // Execute exact command
      console.log('Executing exact command:', trimmedCommand);
      mockWebSocket.simulateCommand(trimmedCommand);
      addToHistory(trimmedCommand);
      commandText.value = '';
      return;
    }

    // Try to find a matching command by normalizing
    const normalizedInput = normalizeCommandName(trimmedCommand);
    const matchingCommand = availableCommands.value.find(cmd =>
      normalizeCommandName(cmd.name) === normalizedInput
    );

    if (matchingCommand) {
      // Execute matching command
      console.log('Executing matching command:', matchingCommand.name);
      mockWebSocket.simulateCommand(matchingCommand.name);
      addToHistory(matchingCommand.name);
      commandText.value = '';
      return;
    }

    // Try to find a command by example
    const matchingExample = availableCommands.value.find(cmd =>
      cmd.examples?.some(ex => normalizeCommandName(ex) === normalizedInput)
    );

    if (matchingExample) {
      // Execute command matched by example
      console.log('Executing command matched by example:', matchingExample.name);
      mockWebSocket.simulateCommand(matchingExample.name);
      addToHistory(matchingExample.name);
      commandText.value = '';
      return;
    }

    // No exact match found, try to parse as natural language
    console.log('No exact match found, trying natural language parsing');
    mockWebSocket.simulateCommand(trimmedCommand);
    addToHistory(trimmedCommand);
    commandText.value = '';
  } catch (error) {
    console.error('Error executing command:', error);

    // Add error message to chat
    chatMessages.value.push({
      name: 'Assistant',
      text: [`Error: ${error instanceof Error ? error.message : 'Unknown error'}`],
      sent: true,
      avatar: robotAvatar // Robot avatar
    });

    void notify({
      color: 'negative',
      message: error instanceof Error ? error.message : 'Unknown error',
      icon: 'error'
    });
  }
}

// Define a type for command result data
type CommandResultData = {
  result?: CommandResult;
  error?: string;
  command?: string;
  params?: Record<string, unknown>;
};

// WebSocket result handler
function webSocketResultHandler(data: unknown) {
  const resultData = data as CommandResultData;
  lastResult.value = resultData as Record<string, unknown>;

  // Format the result as a natural language message
  let responseMessage = '';

  if (resultData.result?.success) {
    // Check if this is a silent success (no notification needed)
    const isSilent = resultData.result.silent === true;

    if (!isSilent) {
      // Success message
      responseMessage = resultData.result.message || t('voiceCommands.commandExecuted');

      // Add additional data if available
      if (resultData.result.data) {
        const dataEntries = Object.entries(resultData.result.data);
        if (dataEntries.length > 0) {
          responseMessage += '. ' + dataEntries.map(([key, value]) => {
            return `${key.charAt(0).toUpperCase() + key.slice(1)}: ${String(value)}`;
          }).join(', ');
        }
      }

      // Show notification
      void notify({
        color: 'positive',
        message: responseMessage,
        icon: 'check_circle',
        position: 'top'
      });
    }
  } else if (resultData.error) {
    // Error message
    responseMessage = `Error executing command: ${resultData.error}`;

    // Show notification
    void notify({
      color: 'negative',
      message: String(resultData.error),
      icon: 'error'
    });
  }

  // Add the response to chat messages
  if (responseMessage) {
    chatMessages.value.push({
      name: 'Assistant',
      text: [responseMessage],
      sent: true,
      avatar: robotAvatar // Robot avatar
    });
  }
}

// Command history keyboard handler
const commandHistoryKeyHandler = (e: KeyboardEvent) => {
  if (e.key === 'ArrowUp') {
    navigateHistory('up');
    e.preventDefault();
  } else if (e.key === 'ArrowDown') {
    navigateHistory('down');
    e.preventDefault();
  }
};

// Initialize
onMounted(() => {
  // Initialize voice commands
  refreshAvailableCommands();
  console.log('Available commands on mount:', availableCommands.value.map(cmd => ({
    name: cmd.name,
    normalized: normalizeCommandName(cmd.name),
    examples: cmd.examples
  })));

  // Set up keyboard shortcuts for command history navigation
  window.addEventListener('keydown', commandHistoryKeyHandler);

  // Listen for WebSocket results
  mockWebSocket.on('result', webSocketResultHandler);

  // Focus the chat input
  void nextTick(() => {
    const inputEl = chatInput.value?.$el.querySelector('input');
    if (inputEl) {
      inputEl.focus();
    }
  });

  // Scroll to bottom
  scrollToBottom();
});

// Clean up
onUnmounted(() => {
  // Remove command history keyboard handler
  window.removeEventListener('keydown', commandHistoryKeyHandler);

  // Clean up WebSocket listener
  mockWebSocket.off('result', webSocketResultHandler);
});
</script>

<style lang="scss" scoped>
// Chat panel styling
.chat-container {
  display: flex;
  flex-direction: column;
  height: calc(100% - 16px); // Account for padding
  background-color: rgba(0, 0, 0, 0.02);
  padding: 8px;

  .body--dark & {
    background-color: rgba(255, 255, 255, 0.03);
  }
}

.chat-messages-scroll {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 8px;

  // Quasar scrollbar styling
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
  }

  .body--dark &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
  }

  .body--dark &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
  }
}

.chat-input {
  flex-shrink: 0;
  background-color: inherit;
}

.chat-messages {
  padding: 8px;
  display: flex;
  flex-direction: column;

  // Scroll to bottom by default
  .q-message:last-child {
    margin-top: auto;
  }

  // Quasar scrollbar styling
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
  }

  .body--dark &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
  }

  .body--dark &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
  }
}

.chat-input {
  .q-input, .q-select {
    border-radius: 4px;
  }

  .command-select {
    margin-bottom: 0 !important;
  }

  // Style the command dropdown
  .command-dropdown {
    max-height: 300px;
    overflow-y: auto;
    // Scrollbar styling is now handled by the global .custom-scrollbar class
  }
}
</style>
