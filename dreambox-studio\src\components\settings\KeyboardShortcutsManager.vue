<template>
  <div class="keyboard-shortcuts-manager">
    <div class="q-mb-md">
      <div class="text-h6">{{ $t('settings.shortcuts.title') }}</div>
      <p>{{ $t('settings.shortcuts.description') }}</p>

      <div class="row q-col-gutter-md">
        <div class="col-auto">
          <q-btn
            color="primary"
            :label="$t('settings.shortcuts.resetDefaults')"
            icon="restore"
            @click="confirmReset"
            :disable="isLoading"
          />
        </div>

        <div class="col-auto">
          <q-btn
            color="secondary"
            icon="refresh"
            flat
            :loading="isLoading"
            @click="refreshShortcuts"
          >
            <q-tooltip>Refresh from database</q-tooltip>
          </q-btn>
        </div>

        <div class="col-auto">
          <q-btn
            color="info"
            icon="sync"
            flat
            :loading="isLoading"
            @click="syncCommands"
          >
            <q-tooltip>Sync with available commands</q-tooltip>
          </q-btn>
        </div>
      </div>
    </div>

    <q-inner-loading :showing="isLoading">
      <q-spinner-dots size="50px" color="primary" />
    </q-inner-loading>

    <div class="q-mb-md">
      <q-input
        v-model="searchText"
        outlined
        dense
        clearable
        :placeholder="$t('settings.shortcuts.searchPlaceholder')"
        class="q-mb-md"
      >
        <template v-slot:prepend>
          <q-icon name="search" />
        </template>
        <template v-slot:append v-if="searchText">
          <q-icon name="close" class="cursor-pointer" @click="searchText = ''" />
        </template>
      </q-input>

      <div class="row q-mb-sm">
        <div class="col">
          <q-chip
            clickable
            :color="filterEmpty ? 'primary' : 'grey-9'"
            text-color="white"
            @click="filterEmpty = !filterEmpty"
          >
            <q-icon name="filter_alt" />
            {{ $t('settings.shortcuts.showUnassignedOnly') }}
          </q-chip>
        </div>
      </div>
    </div>

    <q-list bordered separator>
      <q-item v-for="shortcut in filteredShortcuts" :key="shortcut.id">
        <q-item-section>
          <q-item-label>{{ getTranslatedDescription(shortcut) }}</q-item-label>
          <q-item-label caption>{{ shortcut.command }}</q-item-label>
        </q-item-section>

        <q-item-section side>
          <shortcut-recorder
            v-model="shortcut.keys"
            :exclude-id="shortcut.id"
            @update:model-value="updateShortcut(shortcut.id, $event)"
            :disable="isLoading"
          />
        </q-item-section>

        <q-item-section side>
          <q-btn
            flat
            round
            dense
            color="grey-7"
            icon="clear"
            @click="clearShortcut(shortcut.id)"
            :disable="!shortcut.keys"
          >
            <q-tooltip>{{ $t('settings.shortcuts.clearShortcut') }}</q-tooltip>
          </q-btn>
        </q-item-section>
      </q-item>

      <q-item v-if="filteredShortcuts.length === 0">
        <q-item-section class="text-center text-grey">
          <q-icon name="search_off" size="2rem" />
          <div class="q-mt-sm">{{ $t('settings.shortcuts.noShortcutsFound') }}</div>
        </q-item-section>
      </q-item>
    </q-list>

    <q-dialog v-model="resetConfirmDialog">
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar icon="warning" color="warning" text-color="white" />
          <span class="q-ml-sm">{{ $t('settings.shortcuts.resetConfirm') }}</span>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat :label="$t('common.cancel')" color="primary" v-close-popup />
          <q-btn flat :label="$t('common.confirm')" color="negative" @click="resetToDefaults" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useKeyboardShortcutsStore } from 'src/stores/keyboardShortcutsStore';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import ShortcutRecorder from './ShortcutRecorder.vue';
import type { ShortcutDefinition } from 'src/stores/keyboardShortcutsStore';

const $q = useQuasar();
const { t } = useI18n();
const shortcutsStore = useKeyboardShortcutsStore();
const shortcuts = computed(() => shortcutsStore.shortcuts);
const isLoading = computed(() => shortcutsStore.isLoading);
const resetConfirmDialog = ref(false);

// Get translated description for a shortcut
function getTranslatedDescription(shortcut: ShortcutDefinition): string {
  // Check if there's a translation for this command
  const translationKey = `settings.shortcuts.commands.${shortcut.command}`;
  const translation = t(translationKey);

  // If the translation key exists and is not the same as the key itself, use it
  if (translation !== translationKey) {
    return translation;
  }

  // Otherwise, fall back to the description from the shortcut
  return shortcut.description;
}

// Search and filter
const searchText = ref('');
const filterEmpty = ref(false);

// Filtered shortcuts based on search and filter
const filteredShortcuts = computed(() => {
  return shortcuts.value.filter(shortcut => {
    // Apply text search
    const matchesSearch = searchText.value === '' ||
      shortcut.description.toLowerCase().includes(searchText.value.toLowerCase()) ||
      shortcut.command.toLowerCase().includes(searchText.value.toLowerCase());

    // Apply empty filter
    const matchesEmptyFilter = !filterEmpty.value || shortcut.keys === '';

    return matchesSearch && matchesEmptyFilter;
  }).sort((a, b) => {
    // Sort by whether they have a shortcut assigned, then alphabetically
    if (a.keys && !b.keys) return -1;
    if (!a.keys && b.keys) return 1;
    return a.description.localeCompare(b.description);
  });
});

// Update a shortcut
function updateShortcut(id: string, keys: string) {
  shortcutsStore.updateShortcut(id, { keys });
  $q.notify({
    message: t('settings.shortcuts.notifications.updated'),
    color: 'positive',
    icon: 'check_circle',
    position: 'top',
    timeout: 2000
  });
}

// Clear a shortcut
function clearShortcut(id: string) {
  shortcutsStore.updateShortcut(id, { keys: '' });
  $q.notify({
    message: t('settings.shortcuts.notifications.cleared'),
    color: 'info',
    icon: 'clear',
    position: 'top',
    timeout: 2000
  });
}

// Show reset confirmation dialog
function confirmReset() {
  resetConfirmDialog.value = true;
}

// Reset shortcuts to defaults
async function resetToDefaults() {
  try {
    await shortcutsStore.resetToDefaults();
    $q.notify({
      message: t('settings.shortcuts.notifications.resetToDefaults'),
      color: 'positive',
      icon: 'restore',
      position: 'top',
      timeout: 2000
    });
  } catch (err) {
    console.error('Error resetting shortcuts:', err);
    $q.notify({
      message: t('settings.shortcuts.notifications.failedToReset'),
      color: 'negative',
      icon: 'error',
      position: 'top',
      timeout: 2000
    });
  }
}

// Refresh shortcuts from database
async function refreshShortcuts() {
  try {
    await shortcutsStore.loadFromDatabase();
    $q.notify({
      message: t('settings.shortcuts.notifications.refreshed'),
      color: 'positive',
      icon: 'refresh',
      position: 'top',
      timeout: 2000
    });
  } catch (err) {
    console.error('Error refreshing shortcuts:', err);
    $q.notify({
      message: t('settings.shortcuts.notifications.failedToRefresh'),
      color: 'negative',
      icon: 'error',
      position: 'top',
      timeout: 2000
    });
  }
}

// Sync with available commands
async function syncCommands() {
  try {
    await shortcutsStore.syncWithAvailableCommands();
    $q.notify({
      message: t('settings.shortcuts.notifications.synced'),
      color: 'info',
      icon: 'sync',
      position: 'top',
      timeout: 2000
    });
  } catch (err) {
    console.error('Error syncing shortcuts:', err);
    $q.notify({
      message: t('settings.shortcuts.notifications.failedToSync'),
      color: 'negative',
      icon: 'error',
      position: 'top',
      timeout: 2000
    });
  }
}
</script>

<style lang="scss" scoped>
.keyboard-shortcuts-manager {
  max-width: 800px;
  margin: 0 auto;
}
</style>
