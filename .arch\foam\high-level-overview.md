# System Overview

## Purpose

This system automates the entire Print-on-Demand (POD) business lifecycle, providing end-to-end solutions for:

- Design creation and management
- Product generation and publishing
- Market analysis and targeting
- Sales and fulfillment automation
- Customer support and communication
- Marketing and social media management
- Analytics and reporting

## Core Architecture

The application is built on a modern tech stack featuring:

- **Client**: Quasar framework providing both PWA (web) and Electron (desktop) experiences
- **Backend**: Supabase for database, authentication, and storage
- **Workflow Automation**: n8n for orchestrating AI agents and external integrations
- **Image Generation**: SwarmUI with GPU acceleration for AI-powered design creation
- **E-commerce**: Shopify integration for product publishing and sales

## User Roles

The system supports three primary user roles:

1. **Designer**: Creates design templates that serve as blueprints for POD products
   - Limited access to design-related features only
   - Cannot access sales data or administrative functions

2. **Administrator**: Manages the entire business operation
   - Full access to all features including design tools
   - Controls product creation, publishing, and marketing
   - Reviews analytics and makes strategic decisions

3. **Super-Admin**: Developer role with complete system access
   - Manages all companies, users, and technical infrastructure
   - Responsible for system development and maintenance

## Multi-Company Support

The system is designed to support multiple companies (planned for 7), with:

- Company-specific data isolation
- Unique branding and design guidelines per company
- Role-based access control within each company
- Users can belong to different companies with different roles

## Workflow Phases

### Phase 1: Design Creation
Designers create templates using a combination of manual design and AI assistance. Templates contain comprehensive metadata including style parameters, product compatibility, and target markets.

### Phase 2: Product Planning
Administrators select templates and work with AI to determine which products to create based on upcoming events, market trends, and template suitability.

### Phase 3: Image Generation
The system generates high-quality product images using AI via SwarmUI running on dedicated GPU hardware, with results stored in Supabase.

### Phase 4: Product Publishing
Products are prepared for market with mockups (images on actual products), then published to Shopify after administrator approval.

### Phase 5: Marketing
Marketing materials including social media posts, images, and videos are automatically generated and distributed across platforms.

### Phase 6: Customer Communication
Automated customer communication handles order confirmations, shipping updates, and responds to customer inquiries and feedback.

### Phase 7: Analytics
The system collects and analyzes sales data, customer behavior, and product performance to inform future business decisions.

## AI Agents

The system employs several specialized AI agents:

- **Designer Agent**: Assists with template creation and refinement
- **Product Maker Agent**: Creates products from templates and generates images
- **Publisher Agent**: Prepares and publishes products to Shopify
- **Marketing Agent**: Creates and manages social media content
- **Communicator Agent**: Handles customer interactions and support
- **Statistics Agent**: Collects and analyzes business performance data
- **Command Agent**: Enables voice control of the application

## Voice Command Interface

In addition to traditional UI interaction, the system supports voice commands via:
- Speech-to-text processing (using 11labs)
- Natural language understanding to execute application functions
- Integration with all system capabilities for hands-free operation

## Data Architecture

Key data entities include:
- Templates with comprehensive design parameters
- Products linked to templates and images
- Events for targeted product creation
- Companies with specific branding guidelines
- Users with role-based permissions

## Integration Points

The system connects with multiple external services:
- Shopify for e-commerce
- Social media platforms for marketing
- Email services for customer communication
- Cloud storage for image assets
- AI services for various automation tasks

---

This system represents a comprehensive approach to automating the POD business, from initial design concept through to sales, marketing, and customer support, with AI-driven processes throughout the entire workflow.