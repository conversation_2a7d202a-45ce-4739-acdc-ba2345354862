<template>
  <q-card
    :class="[
      'base-card',
      elevated ? `elevation-${elevationLevel}` : '',
      flat ? 'flat' : '',
      bordered ? 'bordered' : '',
      square ? 'square' : '',
      dense ? 'dense' : '',
      platformClass
    ]"
    :style="cardStyle"
  >
    <q-card-section v-if="$slots.header || title" :class="['header', headerClass]">
      <div class="row items-center justify-between">
        <div v-if="title" class="col">
          <div class="text-h6">{{ title }}</div>
          <div v-if="subtitle" class="text-subtitle2 text-grey-7">{{ subtitle }}</div>
        </div>
        <slot name="header"></slot>
      </div>
    </q-card-section>

    <q-separator v-if="($slots.header || title) && showDivider" />

    <q-card-section :class="['content', contentClass, { 'q-pa-none': noPadding }]">
      <slot></slot>
    </q-card-section>

    <q-separator v-if="$slots.footer && showDivider" />

    <q-card-actions v-if="$slots.footer" :class="['footer', footerClass]">
      <slot name="footer"></slot>
    </q-card-actions>
  </q-card>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import PlatformUtil from 'src/utils/platform';

// Props
interface Props {
  title?: string;
  subtitle?: string;
  elevated?: boolean;
  elevationLevel?: 1 | 2 | 3 | 4 | 5 | string;
  flat?: boolean;
  bordered?: boolean;
  square?: boolean;
  dense?: boolean;
  noPadding?: boolean;
  showDivider?: boolean;
  width?: string;
  height?: string;
  maxWidth?: string;
  maxHeight?: string;
  headerClass?: string;
  contentClass?: string;
  footerClass?: string;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  subtitle: '',
  elevated: true,
  elevationLevel: 1,
  flat: false,
  bordered: false,
  square: false,
  dense: false,
  noPadding: false,
  showDivider: true,
  width: '',
  height: '',
  maxWidth: '',
  maxHeight: '',
  headerClass: '',
  contentClass: '',
  footerClass: ''
});

// Platform detection
const platformClass = computed(() => PlatformUtil.getPlatformClass());

// Card style based on props
const cardStyle = computed(() => {
  const style: Record<string, string> = {};

  if (props.width) style.width = props.width;
  if (props.height) style.height = props.height;
  if (props.maxWidth) style.maxWidth = props.maxWidth;
  if (props.maxHeight) style.maxHeight = props.maxHeight;

  return style;
});
</script>

<style lang="scss" scoped>
.base-card {
  transition: box-shadow 0.3s ease, transform 0.3s ease;

  &.elevation-1 { box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24); }
  &.elevation-2 { box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23); }
  &.elevation-3 { box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23); }
  &.elevation-4 { box-shadow: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22); }
  &.elevation-5 { box-shadow: 0 19px 38px rgba(0,0,0,0.30), 0 15px 12px rgba(0,0,0,0.22); }

  &.bordered {
    border: 1px solid rgba(0, 0, 0, 0.12);
  }

  &.dense {
    .q-card__section {
      padding: 8px 16px;
    }

    .q-card__actions {
      padding: 4px 8px;
    }
  }
}

// Platform-specific styles
.platform-mobile {
  .q-card__section {
    padding: 12px;
  }

  .q-card__actions {
    padding: 8px 12px;
  }
}

// Dark mode adjustments
:deep(.body--dark) {
  .base-card.bordered {
    border-color: rgba(255, 255, 255, 0.12);
  }
}
</style>
