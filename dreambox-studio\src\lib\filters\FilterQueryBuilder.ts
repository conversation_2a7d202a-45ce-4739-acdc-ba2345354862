/**
 * FilterQueryBuilder.ts
 *
 * This module converts filter definitions to SQL query parameters.
 * It handles both standard and specialized filters.
 */

/**
 * Filter query parameters interface for the filter_engine_templates function
 */
export interface FilterQueryParams {
  search_text?: string;       // Keep for backward compatibility
  name_filter?: string;       // New parameter for name filtering
  description_filter?: string; // New parameter for description filtering
  status_filter?: string;
  created_from?: string;
  created_to?: string;
  updated_from?: string;
  updated_to?: string;
  designer_id_filter?: string;
  prompt_elements_filter?: unknown;
  prompt_element_values?: number[];  // New parameter for element value filtering
  prompt_element_match_type?: 'all' | 'any'; // Match type for element values
  related_items_filter?: unknown;
  sort_by?: string;
  sort_desc?: boolean;
  limit_val?: number;
  offset_val?: number;
}

/**
 * Filter Query Builder class
 */
export class FilterQueryBuilder {
  /**
   * Build query parameters for templates
   *
   * @param standardFilters Standard column filters
   * @param specializedFilters Specialized filters
   * @param pagination Pagination parameters
   * @param sorting Sorting parameters
   * @returns Query parameters for the filter_engine_templates function
   */
  static buildTemplatesQueryParams(
    standardFilters: Record<string, unknown> = {},
    specializedFilters: Record<string, unknown> = {},
    pagination: { limit: number; offset: number } = { limit: 10, offset: 0 },
    sorting: { sort_by: string; sort_desc: boolean } = { sort_by: 'updated_at', sort_desc: true }
  ): FilterQueryParams {
    // Removed console.log

    console.log('FilterQueryBuilder: Building query params with pagination:', pagination);

    const params: FilterQueryParams = {
      limit_val: pagination.limit,
      offset_val: pagination.offset,
      sort_by: sorting.sort_by,
      sort_desc: sorting.sort_desc
    };

    // Process standard filters
    if ('search' in standardFilters && typeof standardFilters.search === 'string') {
      // Keep for backward compatibility
      params.search_text = standardFilters.search;
    }

    // Handle name filter - now maps to name_filter instead of search_text
    if ('name' in standardFilters && standardFilters.name) {
      const nameValue = standardFilters.name;
      if (typeof nameValue === 'string' && nameValue.trim() !== '') {
        params.name_filter = nameValue;
      }
    }

    // Handle description filter - now maps to description_filter instead of search_text
    if ('description' in standardFilters && standardFilters.description) {
      const descValue = standardFilters.description;
      if (typeof descValue === 'string' && descValue.trim() !== '') {
        params.description_filter = descValue;
      }
    }

    if ('status' in standardFilters && standardFilters.status) {
      const status = standardFilters.status;
      if (Array.isArray(status)) {
        params.status_filter = status[0];
      } else if (typeof status === 'string') {
        params.status_filter = status;
      } else if (typeof status === 'number') {
        params.status_filter = String(status);
      } else if (status && typeof status === 'object' && 'value' in status) {
        // Handle case where status might be an object with a value property
        const statusValue = (status as { value: string | number }).value;
        params.status_filter = typeof statusValue === 'string' ? statusValue : String(statusValue);
      }
    }

    if ('created_at' in standardFilters &&
        standardFilters.created_at &&
        typeof standardFilters.created_at === 'object' &&
        standardFilters.created_at !== null) {

      const createdAt = standardFilters.created_at as { from?: string, to?: string };

      if (createdAt.from) {
        params.created_from = createdAt.from;
      }

      if (createdAt.to) {
        params.created_to = createdAt.to;
      }
    }

    if ('updated_at' in standardFilters &&
        standardFilters.updated_at &&
        typeof standardFilters.updated_at === 'object' &&
        standardFilters.updated_at !== null) {

      const updatedAt = standardFilters.updated_at as { from?: string, to?: string };

      if (updatedAt.from) {
        params.updated_from = updatedAt.from;
      }

      if (updatedAt.to) {
        params.updated_to = updatedAt.to;
      }
    }

    if ('designer_id' in standardFilters && standardFilters.designer_id) {
      // Use the selected designer ID
      const designerId = standardFilters.designer_id;
      if (typeof designerId === 'number' || typeof designerId === 'string') {
        params.designer_id_filter = String(designerId);
      } else if (designerId && typeof designerId === 'object' && 'id' in designerId) {
        // Handle case where designer_id might be an object with an id property
        params.designer_id_filter = String((designerId as { id: number | string }).id);
      }
    }

    // Process specialized filters
    if ('prompt_elements_filter' in specializedFilters && specializedFilters.prompt_elements_filter) {
      params.prompt_elements_filter = specializedFilters.prompt_elements_filter;
    } else if ('promptElements' in specializedFilters && specializedFilters.promptElements) {
      // Handle promptElements filter
      const promptElementsFilter = specializedFilters.promptElements as {
        elements?: number[],
        matchType?: 'all' | 'any'
      };

      if (promptElementsFilter.elements && promptElementsFilter.elements.length > 0) {
        // For type filtering (backward compatibility)
        params.prompt_elements_filter = JSON.stringify(promptElementsFilter.elements);

        // For element value filtering (new functionality)
        // Make sure we're passing an array of numbers, not objects
        params.prompt_element_values = promptElementsFilter.elements.map(el => {
          if (typeof el === 'number') {
            return el;
          } else if (typeof el === 'object' && el !== null && 'id' in el) {
            return (el as { id: number }).id;
          } else {
            return Number(el);
          }
        });
        params.prompt_element_match_type = promptElementsFilter.matchType || 'any';
      }
    }

    if ('related_items_filter' in specializedFilters && specializedFilters.related_items_filter) {
      params.related_items_filter = specializedFilters.related_items_filter;
    } else if ('relatedItems' in specializedFilters && specializedFilters.relatedItems) {
      // Handle relatedItems filter
      const relatedItemsFilter = specializedFilters.relatedItems as {
        type?: 'collections' | 'products',
        items?: number[],
        matchType?: 'all' | 'any'
      };

      if (relatedItemsFilter.items && relatedItemsFilter.items.length > 0 && relatedItemsFilter.type) {
        // Convert to the format expected by the SQL function
        params.related_items_filter = {
          [relatedItemsFilter.type]: relatedItemsFilter.items
        };
      }
    }

    return params;
  }

  /**
   * Build query parameters for any table
   *
   * @param tableName Table name
   * @param standardFilters Standard column filters
   * @param specializedFilters Specialized filters
   * @param pagination Pagination parameters
   * @param sorting Sorting parameters
   * @returns Query parameters for the appropriate filter function
   */
  static buildQueryParams(
    tableName: string,
    standardFilters: Record<string, unknown> = {},
    specializedFilters: Record<string, unknown> = {},
    pagination: { limit: number; offset: number } = { limit: 10, offset: 0 },
    sorting: { sort_by: string; sort_desc: boolean } = { sort_by: 'updated_at', sort_desc: true }
  ): FilterQueryParams {
    // Call the appropriate builder based on table name
    if (tableName === 'templates') {
      return this.buildTemplatesQueryParams(
        standardFilters,
        specializedFilters,
        pagination,
        sorting
      );
    }

    // Default to a generic builder for other tables
    return {
      ...standardFilters,
      ...pagination,
      sort_by: sorting.sort_by,
      sort_desc: sorting.sort_desc
    };
  }
}

// Export default for module imports
export default FilterQueryBuilder;
