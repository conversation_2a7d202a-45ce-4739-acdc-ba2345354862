import { describe, test, expect, beforeEach, afterEach, mock, spyOn } from 'bun:test';
import { setActive<PERSON>inia, createPinia } from 'pinia';
import { useAuthStore } from '../../../../src/stores/auth';

// Mock localStorage
const mockLocalStorage = {
  store: {} as Record<string, string>,
  getItem: function(key: string) {
    return this.store[key] || null;
  },
  setItem: function(key: string, value: string) {
    this.store[key] = value;
  },
  removeItem: function(key: string) {
    delete this.store[key];
  },
  clear: function() {
    this.store = {};
  }
};

// Mock console methods to avoid cluttering test output
const originalConsoleLog = console.log;
const originalConsoleWarn = console.warn;
const originalConsoleError = console.error;

describe('Auth Store - Helper Methods', () => {
  beforeEach(() => {
    // Create a fresh pinia instance
    setActivePinia(createPinia());

    // Reset localStorage mock
    mockLocalStorage.clear();

    // Replace global localStorage with our mock
    Object.defineProperty(globalThis, 'localStorage', {
      value: mockLocalStorage,
      writable: true
    });

    // Silence console output during tests
    console.log = mock(() => {});
    console.warn = mock(() => {});
    console.error = mock(() => {});
  });

  afterEach(() => {
    // Restore console methods
    console.log = originalConsoleLog;
    console.warn = originalConsoleWarn;
    console.error = originalConsoleError;
  });

  test.skip('processUserRoles correctly processes user roles', async () => {
    const authStore = useAuthStore();

    // Create test data
    const userPermissions = [
      {
        app_user_id: 1,
        user_id: 'test-user-id',
        first_name: 'Test',
        last_name: 'User',
        company_id: 1,
        company_name: 'Company A',
        role_id: 1,
        role_name: 'admin'
      },
      {
        app_user_id: 1,
        user_id: 'test-user-id',
        first_name: 'Test',
        last_name: 'User',
        company_id: 1,
        company_name: 'Company A',
        role_id: 2,
        role_name: 'designer'
      },
      {
        app_user_id: 1,
        user_id: 'test-user-id',
        first_name: 'Test',
        last_name: 'User',
        company_id: 2,
        company_name: 'Company B',
        role_id: 1,
        role_name: 'admin'
      }
    ];

    // Call the method
    const result = (authStore as any).processUserRoles(userPermissions);

    // Verify the result
    expect(result.companies.length).toBe(2);
    expect(result.companies[0].id).toBe('1');
    expect(result.companies[0].name).toBe('Company A');
    expect(result.companies[1].id).toBe('2');
    expect(result.companies[1].name).toBe('Company B');

    expect(result.roles.length).toBe(2);
    expect(result.roles).toContain('admin');
    expect(result.roles).toContain('designer');

    expect(result.userRoles.length).toBe(3);

    // Verify the first company and role are set as current
    expect(result.currentCompany.id).toBe('1');
    expect(result.currentCompany.name).toBe('Company A');
    expect(result.currentRole).toBe('admin');
  });

  test.skip('processUserRoles handles empty permissions', async () => {
    const authStore = useAuthStore();

    // Call the method with empty array
    const result = (authStore as any).processUserRoles([]);

    // Verify the result
    expect(result.companies.length).toBe(0);
    expect(result.roles.length).toBe(0);
    expect(result.userRoles.length).toBe(0);
    expect(result.currentCompany).toBeNull();
    expect(result.currentRole).toBeNull();
  });

  test.skip('hasRole correctly identifies if user has a specific role', async () => {
    const authStore = useAuthStore();

    // Set up user state
    authStore.state.user = {
      id: 'test-user-id',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      companies: [
        { id: '1', name: 'Company A' },
        { id: '2', name: 'Company B' }
      ],
      roles: ['admin', 'designer'],
      userRoles: [
        {
          role: { id: '1', name: 'admin' },
          company: { id: '1', name: 'Company A' }
        },
        {
          role: { id: '2', name: 'designer' },
          company: { id: '1', name: 'Company A' }
        },
        {
          role: { id: '1', name: 'admin' },
          company: { id: '2', name: 'Company B' }
        }
      ],
      currentCompany: { id: '1', name: 'Company A' },
      currentRole: 'admin'
    };

    // Test with current company
    expect(authStore.hasRole('admin')).toBe(true);
    expect(authStore.hasRole('designer')).toBe(true);
    expect(authStore.hasRole('super-admin')).toBe(false);

    // Test with specific company
    expect(authStore.hasRole('admin', '2')).toBe(true);
    expect(authStore.hasRole('designer', '2')).toBe(false);

    // Test with non-existent company
    expect(authStore.hasRole('admin', '3')).toBe(false);
  });

  test.skip('hasRole returns false when user is null', async () => {
    const authStore = useAuthStore();

    // Ensure user is null
    authStore.state.user = null;

    expect(authStore.hasRole('admin')).toBe(false);
  });

  test.skip('isSuperAdmin correctly identifies super-admin role', async () => {
    const authStore = useAuthStore();

    // Set up user state with super-admin role
    authStore.state.user = {
      id: 'test-user-id',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      companies: [{ id: '1', name: 'Company A' }],
      roles: ['super-admin'],
      userRoles: [
        {
          role: { id: '3', name: 'super-admin' },
          company: { id: '1', name: 'Company A' }
        }
      ],
      currentCompany: { id: '1', name: 'Company A' },
      currentRole: 'super-admin'
    };

    expect(authStore.isSuperAdmin).toBe(true);

    // Change role to non-super-admin
    authStore.state.user.currentRole = 'admin';

    expect(authStore.isSuperAdmin).toBe(false);
  });

  test.skip('isSuperAdmin returns false when user is null', async () => {
    const authStore = useAuthStore();

    // Ensure user is null
    authStore.state.user = null;

    expect(authStore.isSuperAdmin).toBe(false);
  });

  test.skip('isAdmin correctly identifies admin role', async () => {
    const authStore = useAuthStore();

    // Set up user state with admin role
    authStore.state.user = {
      id: 'test-user-id',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      companies: [{ id: '1', name: 'Company A' }],
      roles: ['admin'],
      userRoles: [
        {
          role: { id: '1', name: 'admin' },
          company: { id: '1', name: 'Company A' }
        }
      ],
      currentCompany: { id: '1', name: 'Company A' },
      currentRole: 'admin'
    };

    expect(authStore.isAdmin).toBe(true);

    // Change role to non-admin
    authStore.state.user.currentRole = 'designer';

    expect(authStore.isAdmin).toBe(false);
  });

  test.skip('isAdmin returns false when user is null', async () => {
    const authStore = useAuthStore();

    // Ensure user is null
    authStore.state.user = null;

    expect(authStore.isAdmin).toBe(false);
  });

  test.skip('isDesigner correctly identifies designer role', async () => {
    const authStore = useAuthStore();

    // Set up user state with designer role
    authStore.state.user = {
      id: 'test-user-id',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      companies: [{ id: '1', name: 'Company A' }],
      roles: ['designer'],
      userRoles: [
        {
          role: { id: '2', name: 'designer' },
          company: { id: '1', name: 'Company A' }
        }
      ],
      currentCompany: { id: '1', name: 'Company A' },
      currentRole: 'designer'
    };

    expect(authStore.isDesigner).toBe(true);

    // Change role to non-designer
    authStore.state.user.currentRole = 'admin';

    expect(authStore.isDesigner).toBe(false);
  });

  test.skip('isDesigner returns false when user is null', async () => {
    const authStore = useAuthStore();

    // Ensure user is null
    authStore.state.user = null;

    expect(authStore.isDesigner).toBe(false);
  });

  test('hasMultipleCompanies correctly identifies multiple companies', async () => {
    const authStore = useAuthStore();

    // Set up user state with multiple companies
    authStore.state.user = {
      id: 'test-user-id',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      companies: [
        { id: '1', name: 'Company A' },
        { id: '2', name: 'Company B' }
      ],
      roles: ['admin'],
      userRoles: [
        {
          role: { id: '1', name: 'admin' },
          company: { id: '1', name: 'Company A' }
        },
        {
          role: { id: '1', name: 'admin' },
          company: { id: '2', name: 'Company B' }
        }
      ],
      currentCompany: { id: '1', name: 'Company A' },
      currentRole: 'admin'
    };

    expect(authStore.hasMultipleCompanies).toBe(true);

    // Change to single company
    authStore.state.user.companies = [{ id: '1', name: 'Company A' }];

    expect(authStore.hasMultipleCompanies).toBe(false);
  });

  test('hasMultipleCompanies returns false when user is null', async () => {
    const authStore = useAuthStore();

    // Ensure user is null
    authStore.state.user = null;

    expect(authStore.hasMultipleCompanies).toBe(false);
  });

  test.skip('hasMultipleRoles correctly identifies multiple roles', async () => {
    const authStore = useAuthStore();

    // Set up user state with multiple roles for the current company
    authStore.state.user = {
      id: 'test-user-id',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      companies: [{ id: '1', name: 'Company A' }],
      roles: ['admin', 'designer'],
      userRoles: [
        {
          role: { id: '1', name: 'admin' },
          company: { id: '1', name: 'Company A' }
        },
        {
          role: { id: '2', name: 'designer' },
          company: { id: '1', name: 'Company A' }
        }
      ],
      currentCompany: { id: '1', name: 'Company A' },
      currentRole: 'admin'
    };

    expect(authStore.hasMultipleRoles).toBe(true);

    // Change to single role
    authStore.state.user.userRoles = [
      {
        role: { id: '1', name: 'admin' },
        company: { id: '1', name: 'Company A' }
      }
    ];

    expect(authStore.hasMultipleRoles).toBe(false);
  });

  test('hasMultipleRoles returns false when user is null', async () => {
    const authStore = useAuthStore();

    // Ensure user is null
    authStore.state.user = null;

    expect(authStore.hasMultipleRoles).toBe(false);
  });

  test.skip('getRolesForCurrentCompany returns roles for current company', async () => {
    const authStore = useAuthStore();

    // Set up user state with multiple roles across companies
    authStore.state.user = {
      id: 'test-user-id',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      companies: [
        { id: '1', name: 'Company A' },
        { id: '2', name: 'Company B' }
      ],
      roles: ['admin', 'designer'],
      userRoles: [
        {
          role: { id: '1', name: 'admin' },
          company: { id: '1', name: 'Company A' }
        },
        {
          role: { id: '2', name: 'designer' },
          company: { id: '1', name: 'Company A' }
        },
        {
          role: { id: '1', name: 'admin' },
          company: { id: '2', name: 'Company B' }
        }
      ],
      currentCompany: { id: '1', name: 'Company A' },
      currentRole: 'admin'
    };

    const roles = authStore.getRolesForCurrentCompany();

    expect(roles.length).toBe(2);
    expect(roles[0]).toBe('admin');
    expect(roles[1]).toBe('designer');

    // Change current company
    authStore.state.user.currentCompany = { id: '2', name: 'Company B' };

    const rolesForCompanyB = authStore.getRolesForCurrentCompany();

    expect(rolesForCompanyB.length).toBe(1);
    expect(rolesForCompanyB[0]).toBe('admin');
  });

  test.skip('getRolesForCurrentCompany returns empty array when user is null', async () => {
    const authStore = useAuthStore();

    // Ensure user is null
    authStore.state.user = null;

    const roles = authStore.getRolesForCurrentCompany();

    expect(roles.length).toBe(0);
  });

  test.skip('getUserFullName returns formatted full name', async () => {
    const authStore = useAuthStore();

    // Set up user state
    authStore.state.user = {
      id: 'test-user-id',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      companies: [{ id: '1', name: 'Company A' }],
      roles: ['admin'],
      userRoles: [
        {
          role: { id: '1', name: 'admin' },
          company: { id: '1', name: 'Company A' }
        }
      ],
      currentCompany: { id: '1', name: 'Company A' },
      currentRole: 'admin'
    };

    expect(authStore.getUserFullName()).toBe('Test User');

    // Test with only first name
    authStore.state.user.lastName = '';
    expect(authStore.getUserFullName()).toBe('Test');

    // Test with only last name
    authStore.state.user.firstName = '';
    authStore.state.user.lastName = 'User';
    expect(authStore.getUserFullName()).toBe('User');

    // Test with no name
    authStore.state.user.firstName = '';
    authStore.state.user.lastName = '';
    expect(authStore.getUserFullName()).toBe('');
  });

  test.skip('getUserFullName returns empty string when user is null', async () => {
    const authStore = useAuthStore();

    // Ensure user is null
    authStore.state.user = null;

    expect(authStore.getUserFullName()).toBe('');
  });
});
