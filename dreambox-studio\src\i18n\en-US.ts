export default {
  // General
  app: {
    name: 'Dreambox Studio',
    loading: 'Loading...',
    error: 'An error occurred',
    success: 'Success!',
    confirm: 'Are you sure?',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    create: 'Create',
    search: 'Search',
    filter: 'Filter',
    sort: 'Sort',
    language: 'Language',
    theme: {
      light: 'Light Mode',
      dark: 'Dark Mode',
      toggle: 'Toggle Dark Mode'
    },
    new: 'New',
    view: 'View',
    clear: 'Clear',
    clearAll: 'Clear All',
    hideAll: 'Hide All',
    showAll: 'Show All'
  },

  // Column Names
  columns: {
    name: 'Name',
    description: 'Description',
    status: 'Status',
    createdDate: 'Created Date',
    updatedDate: 'Updated Date',
    designer: 'Designer',
    type: 'Type',
    category: 'Category',
    value: 'Value',
    actions: 'Actions',
    // User Management Table
    user: 'User',
    email: 'Email',
    role: 'Role',
    company: 'Company'
  },

  // Common
  common: {
    cancel: 'Cancel',
    confirm: 'Confirm',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    create: 'Create',
    apply: 'Apply',
    reset: 'Reset',
    close: 'Close',
    hideColumn: 'Hide column',
    showColumn: 'Show column',
    clearFilter: 'Clear filter',
    dismissNotification: 'Dismiss notification',
    saveCurrentFilter: 'Save current filter',
    createNewFilter: 'Create new filter',
    noSavedFilters: 'No saved filters available',
    setAsDefault: 'Set as Default',
    default: 'Default',
    loading: 'Loading...',
    optional: 'optional',
    matchAny: 'Match Any',
    matchAll: 'Match All',
    on: 'On',
    off: 'Off'
  },

  // Auth
  auth: {
    login: 'Login',
    logout: 'Logout',
    email: 'Email',
    password: 'Password',
    forgotPassword: 'Forgot Password?',
    resetPassword: 'Reset Password',
    register: 'Register',
    loginWith: 'Login with {provider}',
    loginSuccess: 'Login successful',
    loginError: 'Login failed',
    logoutSuccess: 'Logout successful',
    invalidCredentials: 'Invalid email or password',
    passwordReset: 'Password reset email sent',
    passwordResetError: 'Failed to send password reset email',
    selectRole: 'Select Role',
    selectCompany: 'Select Company'
  },

  // Navigation
  nav: {
    title: 'Navigation',
    dashboard: 'Dashboard',
    settings: 'Settings',
    profile: 'Profile',
    designs: 'Designs',
    templates: 'Templates',
    projects: 'Projects',
    users: 'Users',
    admin: 'Admin',
    home: 'Home',
    browse: 'Browse',
    builder: 'Builder',
    products: 'Products',
    images: 'Images',
    marketing: 'Marketing',
    sales: 'Sales',
    customers: 'Customers',
    statistics: 'Statistics',
    utilities: 'Utilities',
    events: 'Events',
    collections: 'Collections',
    company: 'Company',
    branding: 'Branding',
    app: 'Application',
    companies: 'Companies',
    servers: 'Servers',
    database: 'Database',
    deviceType: 'Device Type',
    logout: 'Logout'
  },

  // Settings
  settings: {
    title: 'Settings',
    appearance: 'Appearance',
    account: 'Account',
    notifications: 'Notifications',
    security: 'Security',
    language: 'Language',
    theme: 'Theme',
    uiTheme: 'UI Theme',
    darkMode: 'Dark Mode',
    lightMode: 'Light Mode',
    systemDefault: 'System Default',
    tableTextWrapMode: 'Table Text Display',
    wrap: 'Wrap',
    ellipsis: 'Ellipsis',
    tableTextWrapModeChanged: 'Table text display changed to {mode}',
    toastNotifications: 'Enable toast notifications',
    toastNotificationsEnabled: 'Toast notifications enabled',
    toastNotificationsDisabled: 'Toast notifications disabled',
    imageServerConfiguration: 'Image Server Configuration',
    terminateServerOnReload: 'Terminate Server on Page Reload',
    keepRunning: 'Keep Running',
    terminate: 'Terminate',
    serverWillTerminateOnReload: 'Server will be terminated when you reload or leave the page',
    serverWillKeepRunningOnReload: 'Server will keep running when you reload or leave the page',
    errorLoadingSettings: 'Error loading settings',
    errorSavingSettings: 'Error saving settings',
    themeSaved: 'Theme saved successfully',
    languageSaved: 'Language saved successfully',
    tabs: {
      general: 'General',
      appearance: 'Appearance',
      shortcuts: 'Keyboard Shortcuts'
    },
    shortcuts: {
      title: 'Keyboard Shortcuts',
      description: 'Customize keyboard shortcuts for frequently used commands.',
      resetDefaults: 'Reset to Defaults',
      resetConfirm: 'Are you sure you want to reset all shortcuts to their default values?',
      pressKeys: 'Press keys...',
      needsModifier: 'Shortcut must include a modifier key (Ctrl, Alt, Shift, or Meta)',
      alreadyUsed: 'This shortcut is already assigned to another command',
      searchPlaceholder: 'Search shortcuts',
      showUnassignedOnly: 'Show unassigned only',
      noShortcutsFound: 'No shortcuts found',
      clearShortcut: 'Clear shortcut',
      commands: {
        toggleDarkMode: 'Toggle dark mode',
        toggleLeftDrawer: 'Toggle left drawer',
        toggleRightDrawer: 'Toggle right drawer',
        openDashboard: 'Go to dashboard',
        logout: 'Log out',
        openSettings: 'Open settings page',
        help: 'Show available commands',
        openProjects: 'Navigate to the projects page',
        openChat: 'Open the chat panel'
      },
      notifications: {
        updated: 'Shortcut updated',
        cleared: 'Shortcut cleared',
        resetToDefaults: 'Shortcuts reset to defaults',
        failedToReset: 'Failed to reset shortcuts',
        refreshed: 'Shortcuts refreshed from database',
        failedToRefresh: 'Failed to refresh shortcuts',
        synced: 'Shortcuts synced with available commands',
        failedToSync: 'Failed to sync shortcuts'
      }
    }
  },

  // Voice Commands
  voiceCommands: {
    title: 'Voice Commands',
    listening: 'Listening...',
    commandExecuted: 'Command executed',
    commandFailed: 'Command failed',
    noCommandDetected: 'No command detected',
    availableCommands: 'Available Commands',
    enterCommand: 'Type or Select',
    execute: 'Execute',
    lastResult: 'Last Result',
    help: 'Help',
    examples: 'Examples',
    selectCommand: 'Select Command',
    toggleDarkMode: {
      name: 'Toggle Dark Mode',
      description: 'Toggle between light and dark mode',
      examples: ['Toggle dark mode', 'Switch theme', 'Change to dark mode', 'Change to light mode']
    },
    openDashboard: {
      name: 'Open Dashboard',
      description: 'Navigate to the dashboard',
      examples: ['Open dashboard', 'Go to dashboard', 'Show dashboard']
    },
    openSettings: {
      name: 'Open Settings',
      description: 'Navigate to settings',
      examples: ['Open settings', 'Go to settings', 'Show settings']
    },
    toggleLeftDrawer: {
      name: 'Toggle Left Drawer',
      description: 'Open or close the navigation drawer',
      examples: ['Open menu', 'Close menu', 'Toggle navigation', 'Show navigation']
    },
    toggleRightDrawer: {
      name: 'Toggle Right Drawer',
      description: 'Open or close the preview drawer',
      examples: ['Open preview', 'Close preview', 'Toggle preview', 'Show preview']
    },
    logout: {
      name: 'Logout',
      description: 'Log out of the application',
      examples: ['Log out', 'Sign out', 'Logout']
    },
    helpCommand: {
      name: 'Help',
      description: 'Show available commands',
      examples: ['Help', 'What can I say?', 'Show commands', 'Available commands']
    }
  },

  // Roles
  roles: {
    designer: 'Designer',
    admin: 'Administrator',
    superAdmin: 'Super Administrator'
  },

  // Errors
  errors: {
    notFound: 'Page not found',
    unauthorized: 'Unauthorized',
    forbidden: 'Forbidden',
    serverError: 'Server error',
    networkError: 'Network error',
    unknown: 'Unknown error'
  },

  // SwarmUI (legacy)
  swarmUI: {
    createRunPod: 'Create SwarmUI Server',
    createSwarmUIServer: 'Create SwarmUI Server',
    selectGpu: 'Select GPU',
    cancel: 'Cancel',
    create: 'Create',
    serverCreating: 'Server is being created. This may take a few minutes.',
    errorCreatingServer: 'Error creating server',
    serverStatus: 'Server Status',
    running: 'Server Running',
    starting: 'Server Starting',
    stopping: 'Server Stopping',
    stopped: 'Server Stopped',
    'no-server': 'No Server',
    error: 'Server Error',
    stopServer: 'Stop Server',
    testConnection: 'Test Connection',
    generateImage: 'Generate Image',
    prompt: 'Prompt',
    negativePrompt: 'Negative Prompt',
    model: 'Model',
    width: 'Width',
    height: 'Height',
    steps: 'Steps',
    cfgScale: 'CFG Scale',
    sampler: 'Sampler',
    seed: 'Seed',
    randomSeed: 'Random Seed',
    scheduler: 'Scheduler',
    generating: 'Generating...',
    generationError: 'Generation Error',
    saveToS3: 'Save to S3',
    saving: 'Saving...',
    imageSaved: 'Image saved to S3',
    failedToSave: 'Failed to save image',
    reset: 'Reset'
  },

  // ComfyUI
  comfyUI: {
    createRunPod: 'Create ComfyUI Server',
    createComfyUIServer: 'Create ComfyUI Server',
    selectGpu: 'Select GPU',
    cancel: 'Cancel',
    create: 'Create',
    serverCreating: 'Server is being created. This may take a few minutes.',
    errorCreatingServer: 'Error creating server',
    serverStatus: 'Server Status',
    running: 'Server Running',
    starting: 'Server Starting',
    stopping: 'Server Stopping',
    stopped: 'Server Stopped',
    'no-server': 'No Server',
    error: 'Server Error',
    success: 'Success',
    stopServer: 'Stop Server',
    terminateServer: 'Terminate Server',
    testConnection: 'Test Connection',
    generateImage: 'Generate Image',
    imageGenerated: 'Image generated successfully',
    imageGenerationFailed: 'Failed to generate image',
    connectionSuccessful: 'Connection successful',
    connectionFailed: 'Connection failed',
    serverStopping: 'Server is stopping',
    serverTerminating: 'Server is being terminated',
    errorStoppingServer: 'Error stopping server',
    errorTerminatingServer: 'Error terminating server',
    terminateServerConfirm: 'Are you sure you want to terminate the server? This action cannot be undone.',
    serverNotRunning: 'Server is not running',
    serverNotConnected: 'Server is not connected yet',
    serverReady: 'Server is ready for image generation',
    prompt: 'Prompt',
    negativePrompt: 'Negative Prompt',
    model: 'Model',
    width: 'Width',
    height: 'Height',
    steps: 'Steps',
    cfgScale: 'CFG Scale',
    sampler: 'Sampler',
    seed: 'Seed',
    randomSeed: 'Random Seed',
    scheduler: 'Scheduler',
    generating: 'Generating...',
    generationError: 'Generation Error',
    saveToS3: 'Save to S3',
    saving: 'Saving...',
    imageSaved: 'Image saved to S3',
    failedToSave: 'Failed to save image',
    reset: 'Reset'
  },

  // Filters
  filters: {
    savedFilters: 'Saved Filters',
    visibleColumns: 'Visible Columns',
    detailsColumns: 'Details Columns',
    expanded: 'Expanded',
    main: 'Main',
    promptElements: 'Prompt Elements',
    relatedItems: 'Related Items',
    noFiltersAvailable: 'No filters available',
    filterName: 'Filter Name',
    description: 'Description (optional)',
    setAsDefault: 'Set as default filter for this view',
    setAsDefaultFilter: 'Set as default filter',
    editFilter: 'Edit Filter',
    saveFilter: 'Save Filter',
    updateFilter: 'Update Filter',
    deleteFilter: 'Delete Filter',
    deleteFilterConfirm: 'Are you sure you want to delete this filter?',
    clearAllPromptElementFilters: 'Clear all prompt element filters',
    clearFilters: 'Clear Filters',
    filtersWillAppear: 'Filters will appear here based on the current view.',
    search: 'Search...',
    min: 'Min',
    max: 'Max',
    selectDateOrRange: 'Select date or range',
    nameRequired: 'Name is required',
    filterApplied: 'Filter applied',
    filterCleared: 'Filter cleared',
    filterSaved: 'Filter saved successfully',
    filterUpdated: 'Filter updated successfully',
    filterDeleted: 'Filter deleted successfully',
    defaultFilterUpdated: 'Default filter updated',
    failedToInitialize: 'Failed to initialize filters. Please try again later.',
    failedToUpdate: 'Failed to update filters for the new context.',
    allPromptElementFiltersCleared: 'All prompt element filters cleared',
    holdAltForDragSelect: 'Hold Alt for drag select'
  },

  // Templates
  templates: {
    elements: 'Elements',
    related: 'Related',
    details: 'Details',
    statusHistory: 'Status History',
    promptElements: 'Prompt Elements',
    collections: 'Collections',
    products: 'Products',
    noCollections: 'No collections associated with this template.',
    noProducts: 'No products',
    noElements: 'No elements',
    templateDetails: 'Template Details',
    selectElementTypes: 'Select Element Types',
    selectValues: 'Select Values',
    noMatchingElementTypes: 'No matching element types',
    noMatchingValues: 'No matching values',
    selectedTypes: 'Selected types: {count}',
    typesShown: 'Types shown: {count}',
    selectedValues: 'Selected values: {count}',
    selectElementTypesPrompt: 'Select element types to add to this template',
    loadStatusHistory: 'Load Status History',
    loadingStatusHistory: 'Loading status history...',
    noTemplatesFound: 'No templates found',
    templatesSelected: '{count} templates selected',
    status: 'Status',
    mixedStatuses: 'Mixed statuses',
    submitForApproval: 'Submit for Approval',
    approve: 'Approve',
    reject: 'Reject',
    archive: 'Archive',
    edit: 'Edit',
    view: 'View',
    restore: 'Restore to Draft',
    copyToNewDraft: 'Copy to New Draft',
    clearSelection: 'Clear Selection',
    batchActions: 'Batch Actions',
    updating: 'Updating {count} templates...',
    updated: 'Updated {count} of {total} templates',
    copying: 'Copying {count} templates...',
    copied: 'Copied {count} of {total} templates',
    failedToUpdate: 'Failed to update templates',
    failedToCopy: 'Failed to copy templates',
    templateCopied: 'Template copied successfully',
    statusUpdated: 'Status updated to {status}',
    statusUpdateFailed: 'Failed to update template status',
    notes: 'Notes',
    newTemplate: '+ NEW TEMPLATE',
    title: 'Templates',
    templatesFound: '{count} templates found',
    recordsSelected: '{count} records selected',
    recordsPerPage: 'Records per page:',
    records: 'Records',
    by: 'By',

    // Template Builder
    builder: {
      title: 'Template Builder',
      newTemplate: 'New Template',
      editTemplate: 'Edit Template',
      viewTemplate: 'View Template',
      backToTemplates: 'Back to Templates',
      loadingTemplate: 'Loading template...',
      basicInformation: 'Basic Information',
      templateName: 'Template Name',
      templateDescription: 'Template Description',
      nameRequired: 'Name is required',
      definePromptElements: 'Define the prompt elements that make up this template',
      productTypes: 'Product Types',
      recommendedProductTypes: 'Recommended Product Types',
      selectProductTypes: 'Select product types for this template',
      selectRecommendedTypes: 'Select recommended product types',
      imageSettings: 'Image Settings',
      configureImageSettings: 'Configure settings for image generation',
      model: 'Model',
      steps: 'Steps',
      aspectRatio: 'Aspect Ratio',
      seed: 'Seed',
      randomSeed: 'Random Seed',
      save: 'Save',
      cancel: 'Cancel',
      back: 'Back',
      saving: 'Saving...',
      templateSaved: 'Template saved successfully',
      errorSaving: 'Error saving template',
      selectAll: 'Select All',
      addCustomValue: 'Add Custom Value',
      customValue: 'Custom Value',
      addValue: 'Add Value',
      preview: 'Preview',
      generatePreview: 'Generate Preview',
      generatingPreview: 'Generating preview...',
      previewError: 'Error generating preview',
      tryAgain: 'Try Again',
      elementValues: 'Element Values',
      selectValuesForEachType: 'Select values for each element type'
    }
  }
};
